package com.mides.mides_skadik

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.widget.Toast
import java.io.IOException
import android.app.admin.DevicePolicyManager
import android.content.ComponentName

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.example/screen_share" // Match Dart side
    private val REQUEST_CODE_MEDIA_PROJECTION = 101
    private val REQUEST_CODE_ENABLE_ADMIN = 201
    private var mediaProjectionResultCode: Int = 0
    private var mediaProjectionResultData: Intent? = null
    private var pendingResult: MethodChannel.Result? = null // To store the result callback

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "startScreenCapture" -> {
                        Log.d("MainActivity", "startScreenCapture called from Dart")
                        pendingResult = result // Store the result callback
                        requestMediaProjectionPermission()
                        // Don't call result.success yet, wait for onActivityResult
                    }
                    "stopScreenCapture" -> {
                        Log.d("MainActivity", "stopScreenCapture called from Dart")
                        stopScreenCaptureService()
                        result.success(null)
                    }
                    "shutdownService" -> {
                        Log.d("MainActivity", "shutdownService called from Dart")
                        val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
                        val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
                        if (devicePolicyManager.isAdminActive(adminComponent)) {
                            shutdownDevice() // Langsung panggil jika sudah admin
                            result.success("Attempting to reboot device.") // Beri feedback ke Dart
                        } else {
                            requestDeviceAdminActivation() // Minta aktivasi jika belum admin
                            // Di sini, Anda mungkin ingin memberi tahu Dart bahwa Anda sedang meminta izin
                            // dan hasil sebenarnya akan asinkron.
                            // Untuk saat ini, kita bisa mengembalikan pesan bahwa izin diminta.
                            result.success("Device Admin permission requested. Please activate.")
                        }
                    }
                    "restartService" -> {
                        Log.d("MainActivity", "restartService called from Dart")
                        val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
                        val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
                        if (devicePolicyManager.isAdminActive(adminComponent)) {
                            restartDevice() // Langsung panggil jika sudah admin
                            result.success("Attempting to reboot device.") // Beri feedback ke Dart
                        } else {
                            requestDeviceAdminActivation() // Minta aktivasi jika belum admin
                            // Di sini, Anda mungkin ingin memberi tahu Dart bahwa Anda sedang meminta izin
                            // dan hasil sebenarnya akan asinkron.
                            // Untuk saat ini, kita bisa mengembalikan pesan bahwa izin diminta.
                            result.success("Device Admin permission requested. Please activate.")
                        }
                    }
                    "requestDeviceAdmin" -> {
                        Log.d("MainActivity", "requestDeviceAdmin called from Dart")
                        requestDeviceAdminActivation()
                        result.success("Device Admin activation requested")
                    }
                    "checkDeviceAdmin" -> {
                        Log.d("MainActivity", "checkDeviceAdmin called from Dart")
                        val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
                        val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
                        val isActive = devicePolicyManager.isAdminActive(adminComponent)
                        result.success(isActive)
                    }
                    else -> result.notImplemented()
                }
            }
    }

    private fun requestMediaProjectionPermission() {
        Log.d("MainActivity", "Requesting media projection permission")
        val mediaProjectionManager =
            getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        startActivityForResult(
            mediaProjectionManager.createScreenCaptureIntent(),
            REQUEST_CODE_MEDIA_PROJECTION
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        Log.d("MainActivity", "onActivityResult: requestCode=$requestCode, resultCode=$resultCode")

        if (requestCode == REQUEST_CODE_MEDIA_PROJECTION) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                Log.d("MainActivity", "Media projection permission GRANTED")
                mediaProjectionResultCode = resultCode
                mediaProjectionResultData = data
                startScreenCaptureService() // Start the service *after* getting permission
                pendingResult?.success(true) // Inform Dart that permission was granted and service started
            } else {
                Log.w("MainActivity", "Media projection permission DENIED or cancelled")
                pendingResult?.error("PERMISSION_DENIED", "User denied screen capture permission.", null)
            }
            pendingResult = null // Clear the pending result
        } else if (requestCode == REQUEST_CODE_ENABLE_ADMIN) {
            if (resultCode == Activity.RESULT_OK) {
                Log.i("MainActivity", "Device Admin permission GRANTED by user.")
                Toast.makeText(this, "Device Admin Activated!", Toast.LENGTH_SHORT).show()
                // Sekarang Anda bisa mencoba lagi operasi yang memerlukan Device Admin,
                // misalnya jika pengguna menekan tombol lagi atau Anda memiliki alur otomatis.
                // Contoh: shutdownDevice() // Panggil jika alurnya adalah langsung shutdown setelah aktivasi
            } else {
                Log.w("MainActivity", "Device Admin permission DENIED or cancelled by user.")
                Toast.makeText(this, "Device Admin activation failed or cancelled.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun startScreenCaptureService() {
        Log.d("MainActivity", "Starting ScreenCaptureService")
        val serviceIntent = Intent(this, ScreenCaptureService::class.java).apply {
            // Optionally pass projection data if needed by the service itself,
            // though flutter_webrtc usually handles this internally when using getDisplayMedia
            // putExtra("RESULT_CODE", mediaProjectionResultCode)
            // putExtra("DATA", mediaProjectionResultData)
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent)
        } else {
            startService(serviceIntent)
        }
    }

    private fun stopScreenCaptureService() {
        Log.d("MainActivity", "Stopping ScreenCaptureService")
        stopService(Intent(this, ScreenCaptureService::class.java))
    }

    private fun shutdownDevice() {
        try {
            val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java) // Ganti MyDeviceAdminReceiver dengan nama kelas Anda

            // Pastikan aplikasi adalah device admin yang aktif sebelum mencoba operasi sensitif
            if (devicePolicyManager.isAdminActive(adminComponent)) {
                devicePolicyManager.lockNow()
            } else {
                Log.w("MainActivity", "App is not an active device admin.")
                Toast.makeText(this, "App is not an active device admin.", Toast.LENGTH_SHORT).show()
            }
        } catch (e: SecurityException) {
            e.printStackTrace()
            Toast.makeText(this, "SecurityException: Failed to reboot device. Ensure app is Device Owner/Profile Owner.", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Exception: Could not reboot device. ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun restartDevice() {
        try {
            val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
            val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java) // Ganti MyDeviceAdminReceiver dengan nama kelas Anda

            // Pastikan aplikasi adalah device admin yang aktif sebelum mencoba operasi sensitif
            if (devicePolicyManager.isAdminActive(adminComponent)) {
                //devicePolicyManager.lockNow() // Mengunci perangkat, ini opsional tergantung kebutuhan

                // Untuk me-reboot perangkat (memerlukan app sebagai Device Owner atau Profile Owner)
                // Perhatikan: Ini akan me-reboot, bukan mematikan (power off).
                // Tidak ada API standar untuk mematikan perangkat bagi aplikasi biasa.
                devicePolicyManager.reboot(adminComponent)
            } else {
                Log.w("MainActivity", "App is not an active device admin.")
                Toast.makeText(this, "App is not an active device admin.", Toast.LENGTH_SHORT).show()
            }
        } catch (e: SecurityException) {
            e.printStackTrace()
            Toast.makeText(this, "SecurityException: Failed to reboot device. Ensure app is Device Owner/Profile Owner.", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Exception: Could not reboot device. ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun requestDeviceAdminActivation() {
        val adminComponent = ComponentName(this, MyDeviceAdminReceiver::class.java)
        val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager

        if (!devicePolicyManager.isAdminActive(adminComponent)) {
            Log.i("MainActivity", "Requesting Device Admin activation.")
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN).apply {
                putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent)
                putExtra(
                    DevicePolicyManager.EXTRA_ADD_EXPLANATION,
                    "Aplikasi ini memerlukan izin Administrator Perangkat untuk melakukan beberapa operasi." // Sesuaikan pesan ini
                )
            }
            startActivityForResult(intent, REQUEST_CODE_ENABLE_ADMIN)
        } else {
            Log.i("MainActivity", "Device Admin is already active.")
            Toast.makeText(this, "Device Admin is already active.", Toast.LENGTH_SHORT).show()
            // Anda bisa langsung mencoba operasi shutdown jika sudah aktif
            // shutdownDevice() // Panggil jika sudah aktif dan itu adalah alur yang diinginkan
        }
    }
}