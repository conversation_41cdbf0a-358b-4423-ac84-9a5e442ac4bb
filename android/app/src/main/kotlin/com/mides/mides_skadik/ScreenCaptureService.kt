package com.mides.mides_skadik

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat

class ScreenCaptureService : Service() {

    companion object {
        const val CHANNEL_ID = "ScreenCaptureServiceChannel"
        const val NOTIFICATION_ID = 1
    }

    override fun onCreate() {
        super.onCreate()
        Log.d("ScreenCaptureService", "onCreate called")
        createNotificationChannel()
        // Start foreground immediately on creation
        startForeground(NOTIFICATION_ID, createNotification())
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d("ScreenCaptureService", "onStartCommand called")
        // Ensure foreground status is maintained
        startForeground(NOTIFICATION_ID, createNotification())
        // Handle projection data if passed via intent (optional here if handled elsewhere)
        return START_NOT_STICKY // Or START_STICKY depending on desired behavior
    }

    override fun onDestroy() {
        Log.d("ScreenCaptureService", "onDestroy called")
        stopForeground(true) // Ensure notification is removed
        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "Screen Capture Service Channel",
                NotificationManager.IMPORTANCE_LOW // Use LOW to avoid sound/vibration
            )
            val manager = getSystemService(NotificationManager::class.java)
            manager?.createNotificationChannel(serviceChannel)
        }
    }

    private fun createNotification(): Notification {
        // Use a simple icon, replace with your app's icon
        val notificationIcon = android.R.drawable.ic_dialog_info

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Screen Sharing Active")
            .setContentText("Your screen is being shared.")
            .setSmallIcon(notificationIcon)
            .setPriority(NotificationCompat.PRIORITY_LOW) // Low priority
            .setOngoing(true) // Makes the notification non-dismissible
            .build()
    }
}
