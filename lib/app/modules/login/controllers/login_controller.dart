import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/auth/user_model.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/profile_model.dart';
import 'package:mides_skadik/app/data/services/auth/login_service.dart';
import 'package:mides_skadik/app/data/services/course/profile/profile_service.dart';
import 'package:mides_skadik/app/modules/choose_screen/views/choose_screen_view.dart';

class LoginController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final RxString email = ''.obs;
  final RxString password = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Add listeners to update RxString values when text changes
    emailController.addListener(() {
      email.value = emailController.text;
    });

    passwordController.addListener(() {
      password.value = passwordController.text;
    });
  }

  @override
  void onClose() {
    // Dispose the controllers when the controller is closed
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  RxBool isLoading = false.obs;
  final userProfile = Rxn<ProfileModel>();

  ProfileService profileService = ProfileService();

  Rxn<UserModel?> user = Rxn<UserModel>();

  RxBool obscureText = true.obs;

  @override
  void onReady() async {
    await getUser();
    super.onReady();
  }

  // void loadUser() async {
  //   StorageService storageService = StorageService();

  //   var res = await storageService.getUser();

  //   user.value = res;
  // }

  Future<void> getUser() async {
    final result = await profileService.getProfile();

    if (result.isSuccess) {
      userProfile.value = result.resultValue;
    } else {
      userProfile.value = null;
    }
  }

  RxString greeting() {
    final hour = DateTime.now().hour;
    if (hour >= 5 && hour < 12) {
      return 'Good Morning'.obs;
    } else if (hour >= 12 && hour < 17) {
      return 'Good Afternoon'.obs;
    } else if (hour >= 17 && hour < 21) {
      return 'Good Evening'.obs;
    } else {
      return 'Good Night'.obs;
    }
  }

  RxBool validated() {
    return (email.isNotEmpty && password.isNotEmpty).obs;
  }

  void submitLogin() async {
    isLoading.value = true;
    var response = await LoginService().login(
        username: emailController.text, password: passwordController.text);
    if (response.isSuccess) {
      email.value = '';
      password.value = '';
      emailController.clear();
      passwordController.clear();

      user.value = response.resultValue;
      await getUser();
      isLoading.value = false;
      Get.off(() => const ChooseScreenView());
    } else {
      isLoading.value = false;
      Get.snackbar('Error', response.errorMessage.toString(),
          backgroundColor: Colors.red, colorText: Colors.white);
    }
  }

  void toggleObscureText() {
    obscureText.value = !obscureText.value;
  }
}
