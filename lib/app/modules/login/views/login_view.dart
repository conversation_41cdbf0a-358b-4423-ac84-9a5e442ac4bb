import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_login.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../controllers/login_controller.dart';
import 'login_landscape.dart';

class LoginView extends GetView<LoginController> {
  const LoginView({super.key});
  @override
  Widget build(BuildContext context) {
    bool isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscape) {
      return const LoginLandscape();
    }

    return CustomScaffold(
      background: "assets/images/login_bg.png",
      body: Stack(
        children: [
          Center(
            child: ListView(
              shrinkWrap: true,
              children: [
                const CustomLogin(),
              ],
            ),
          ),
          Positioned(
              top: Get.window.padding.top,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/images/logo.png',
                height: 100.h,
              )),
          Positioned(
            bottom: Get.window.padding.top,
            left: 0,
            right: 0,
            child: CustomTextWigdet(
              title: "©m-ides education system",
              textAlign: TextAlign.center,
              textColor: whiteColor.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}
