import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_login.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class LoginLandscape extends GetView<LoginController> {
  const LoginLandscape({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      background: "assets/images/login_bg.png",
      body: Stack(
        children: [
          Center(
            child: ListView(
              shrinkWrap: true,
              children: [
                CustomLogin(width: Get.width / 2.5),
              ],
            ),
          ),
          Positioned(
              top: Get.window.padding.top,
              left: 0,
              right: 0,
              child: Image.asset(
                'assets/images/logo.png',
                height: 100.h,
              )),
          Positioned(
            bottom: Get.window.padding.top,
            left: 0,
            right: 0,
            child: CustomTextWigdet(
              title: "©m-ides education system",
              textAlign: TextAlign.center,
              textColor: whiteColor.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
}
