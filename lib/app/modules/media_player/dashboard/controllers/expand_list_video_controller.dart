import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';

class ExpandListVideoController extends GetxController {
  RxList<VodModel> expandListVod = <VodModel>[].obs;

  RxString selectedFilter = ''.obs;
  RxBool isLoading = false.obs;

  final VodService vodService = VodService();

  // TODO: Select Filter
  void selectFilter(String filter) {
    if (selectedFilter.value == filter) {
      selectedFilter.value = '';
      return;
    }
    selectedFilter.value = filter;
  }

  // TODO: Clear Select Filter
  void clearFilter() {
    selectedFilter.value = '';
  }

  // TODO: Get Popular VoD by Filter
  Future<void> getPopularVodFilter() async {
    isLoading.value = true;

    final result = await vodService.getPopularVod(tag: selectedFilter.value);

    if (result.isSuccess) {
      expandListVod.value = result.resultValue ?? [];
    } else {
      expandListVod.value = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Newes VoD by Filter
  Future<void> getNewestVodFilter() async {
    isLoading.value = true;

    final result = await vodService.getNewestVod(tag: selectedFilter.value);

    if (result.isSuccess) {
      expandListVod.value = result.resultValue ?? [];
    } else {
      expandListVod.value = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Untuk Kamu VoD by Filter
  Future<void> getRecommendationVodFilter() async {
    isLoading.value = true;

    final result =
        await vodService.getRecommendationVod(tag: selectedFilter.value);

    if (result.isSuccess) {
      expandListVod.value = result.resultValue ?? [];
    } else {
      expandListVod.value = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Data by Filter
  Future<void> getDataFilter(String viewName) async {
    switch (viewName.toLowerCase()) {
      case 'populer':
        await getPopularVodFilter();
        break;
      case 'terbaru':
        await getNewestVodFilter();
        break;
      case 'untuk kamu':
        await getRecommendationVodFilter();
        break;
    }
  }
}
