import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/broadcast/broadcast_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/user_info/user_info_model.dart';
import 'package:mides_skadik/app/data/services/media_player/broadcast/broadcast_service.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/services/media_player/profile/user_info_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:ntp/ntp.dart';

class DashboardController extends GetxController with WidgetsBindingObserver {
  Rx<UserInfoMediaPlayer?> userProfile = Rx<UserInfoMediaPlayer?>(null);
  RxList<VodModel> listPopularVod = <VodModel>[].obs;
  RxList<VodModel> listNewestVod = <VodModel>[].obs;
  RxList<VodModel> listRecommVod = <VodModel>[].obs;
  RxList<PlaybackModel> listContinueWatchVod = <PlaybackModel>[].obs;
  RxList<VodModel> dataViewAll = <VodModel>[].obs;
  RxList<BroadcastModel> listBroadcast = <BroadcastModel>[].obs;

  RxBool isPlayingVideo = false.obs;
  RxString videoUrlPlay = ''.obs;
  RxString selectedFilter = ''.obs;
  RxInt page = 1.obs;

  var isPlaying = false.obs;
  var isBuffering = false.obs;
  var isSettingUp = false.obs;
  var videoUrl = ''.obs;
  var isInitialized = false.obs;
  var isControllerClosed = false.obs;
  var isLastPage = false.obs;
  var viewOtherButton = false.obs;

  // final _lastPosition = Rx<Duration?>(Duration.zero);
  var timeNow = DateTime.now().obs;
  var vodService = VodService();
  var profileService = UserInfoService();

  //Loading
  RxBool isLoadingViewAll = false.obs;
  RxBool isLoadingUserProfile = false.obs;
  RxBool isLoadingPopularVod = false.obs;
  RxBool isLoadingNewestVod = false.obs;
  RxBool isLoadingRecommVod = false.obs;
  RxBool isLoadingContinueWatchVod = false.obs;
  RxBool isLoadingMoreData = false.obs;

  @override
  void onInit() async {
    super.onInit();
    await getBroadcast();
    await getTimeNow();

    await getPopularVod();
    await getNewestVod();
    await getRecommendationVod();
    await getContinueWatch();

    await getUserProfile();
    await updateLastLogin();
  }

  @override
  void onClose() {
    WidgetsBinding.instance.removeObserver(this);
    selectedFilter.value = '';
    super.onClose();
  }

  void refreshPage() async {
    await getBroadcast();
    await getTimeNow();

    getPopularVod();
    getNewestVod();
    getRecommendationVod();
    getContinueWatch();

    getUserProfile();
  }

  // TODO: Get Time Now from NTP
  Future<void> getTimeNow() async {
    timeNow.value = await NTP.now();
  }

  // TODO: Update Last Login
  Future<void> updateLastLogin() async {
    final result = await profileService.updateLastLogin();

    if (result.isSuccess) {
      SnackbarUtil.showOnce(
          title: 'Sukses', message: result.succesMessage ?? '');
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
  }

  // TODO: Select Filter
  void selectFilter(String filter) {
    dataViewAll.clear();
    if (selectedFilter.value == filter) {
      selectedFilter.value = '';
      return;
    }
    selectedFilter.value = filter;
  }

  // TODO: Clear Select Filter
  void clearFilter() {
    selectedFilter.value = '';
  }

  // TODO: Get User Profile
  Future<void> getUserProfile() async {
    isLoadingUserProfile.value = true;

    final result = await profileService.getUserData();

    if (result.isSuccess) {
      userProfile.value = result.resultValue;
    } else {
      userProfile.value = null;
    }

    isLoadingUserProfile.value = false;
  }

  // TODO: Get All Video for View All
  Future<void> getAllView({required String title, limit = 15}) async {
    final currentPage = page.value;
    isLastPage.value = false;

    if (currentPage == 1) {
      isLoadingViewAll.value = true;
    } else {
      isLoadingMoreData.value = true;
    }

    List<VodModel> newData = [];
    List<PlaybackModel> newDataContinueWatch = [];
    switch (title.toLowerCase()) {
      case 'populer':
        final result = await vodService.getPopularVod(
            page: currentPage, limit: limit, tag: selectedFilter.value);
        newData = result.resultValue ?? [];
        break;
      case 'terbaru':
        final result = await vodService.getNewestVod(
            page: currentPage, limit: limit, tag: selectedFilter.value);
        newData = result.resultValue ?? [];
        break;
      case 'untuk kamu':
        final result = await vodService.getRecommendationVod(
            page: currentPage, limit: limit, tag: selectedFilter.value);
        newData = result.resultValue ?? [];
        break;
      case 'lanjut menonton':
        final result = await vodService.getContinueWatch(
            page: currentPage, limit: limit, isComplete: 'no');
        newDataContinueWatch = result.resultValue ?? [];
        break;
    }

    if (currentPage == 1) {
      dataViewAll.value = newData.isEmpty
          ? newDataContinueWatch.map((e) => e.vod!).toList()
          : newData;
    } else {
      if (newData.isNotEmpty) {
        dataViewAll.addAll(newData);
      } else {
        isLastPage.value = true;
        page.value = currentPage - 1;
      }
    }

    isLoadingViewAll.value = false;
    isLoadingMoreData.value = false;
  }

  // TODO: Get Popular Video
  Future<void> getPopularVod({int? page = 1, int? limit = 10}) async {
    isLoadingPopularVod.value = true;

    final result = await vodService.getPopularVod(page: page, limit: limit);
    if (result.isSuccess) {
      listPopularVod.value = result.resultValue ?? [];
    } else {
      listPopularVod.value = [];
    }

    isLoadingPopularVod.value = false;
    isLoadingViewAll.value = false;
  }

  // TODO: Get Live Broadcast Video
  Future<void> getBroadcast({int? page = 1, int? limit = 10}) async {
    isLoadingPopularVod.value = true;

    final result =
        await BroadcastService().getAllBroadcast(page: page, limit: limit);
    if (result.isSuccess) {
      listBroadcast.value = result.resultValue ?? [];
    } else {
      listBroadcast.value = [];
    }

    isLoadingPopularVod.value = false;
    isLoadingViewAll.value = false;
  }

  // TODO: Get Newest Video
  Future<void> getNewestVod() async {
    isLoadingNewestVod.value = true;

    final result = await vodService.getNewestVod();
    if (result.isSuccess) {
      listNewestVod.value = result.resultValue ?? [];
    } else {
      listNewestVod.value = [];
    }

    isLoadingNewestVod.value = false;
  }

  // TODO: Get Recommendation Video
  Future<void> getRecommendationVod() async {
    final currentPage = page.value;
    final isFirstPage = currentPage == 1;

    (isFirstPage ? isLoadingRecommVod : isLoadingMoreData).value = true;

    final result = await vodService.getRecommendationVod(page: currentPage);
    List<VodModel> newData = result.resultValue ?? [];

    if (result.isSuccess) {
      if (newData.isNotEmpty) {
        if (isFirstPage) {
          listRecommVod.value = newData;
        } else {
          listRecommVod.addAll(newData);
        }
      } else {
        isLastPage.value = true;
        page.value = currentPage - 1;
      }
    } else {
      listRecommVod.value = [];
    }

    isLoadingMoreData.value = false;
    isLoadingRecommVod.value = false;
  }

  // TODO: Get Continue Watch Video
  Future<void> getContinueWatch() async {
    isLoadingContinueWatchVod.value = true;

    final result = await vodService.getContinueWatch();

    if (result.isSuccess) {
      listContinueWatchVod.value = result.resultValue ?? [];
    } else {
      listContinueWatchVod.value = [];
    }

    isLoadingContinueWatchVod.value = false;
  }

  // TODO: Get Data by Filter
  // Future<void> getDataFilter(String viewName) async {
  //   switch (viewName.toLowerCase()) {
  //     case 'populer':
  //       await getPopularVod();
  //       break;
  //     case 'terbaru':
  //       await getNewestVod();
  //       break;
  //     case 'untuk kamu':
  //       await getRecommendationVod();
  //       break;
  //   }
  // }

  // TODO: Display the other button on landscape mode
  void showOtherButton() {
    viewOtherButton.value = !viewOtherButton.value;
  }

  // void initMiniVideo() {
  //   WidgetsBinding.instance.addObserver(this);
  //   debugPrint("initMiniVideo: ${videoUrlPlay.value}");
  //   videoUrl.value = videoUrlPlay.value;
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     initializeVideo(videoUrl.value, _lastPosition.value ?? Duration.zero);
  //   });
  // }

  // Future<void> initializeVideo(String url, Duration duration) async {
  //   if (isSettingUp.value) return;
  //   isSettingUp.value = true;

  //   disposeCurrentController();
  //   Future.delayed(const Duration(seconds: 5));
  //   if (isControllerClosed.value) return;

  //   createControllerVideo(url, duration);
  // }

  // void disposeCurrentController() {
  //   final controller = betterPlayerController.value;
  //   if (controller != null) {
  //     isInitialized.value = false;
  //     isSettingUp.value = false;
  //     try {
  //       controller.dispose();
  //       betterPlayerController.value = null;
  //     } catch (e) {
  //       throw ("Dispose/pause error: $e");
  //     }
  //   }
  // }

  // void createControllerVideo(String url, Duration duration) {
  //   final dataSource = BetterPlayerDataSource(
  //     BetterPlayerDataSourceType.network,
  //     url,
  //   );

  //   const config = BetterPlayerConfiguration(
  //     autoPlay: true,
  //     looping: false,
  //     aspectRatio: 16 / 9,
  //     controlsConfiguration: BetterPlayerControlsConfiguration(
  //       enableFullscreen: true,
  //       enablePlaybackSpeed: true,
  //       enableSubtitles: false,
  //     ),
  //   );

  //   betterPlayerController.value = BetterPlayerController(
  //     config,
  //     betterPlayerDataSource: dataSource,
  //   );

  //   betterPlayerController.value!.addEventsListener((event) async {
  //     switch (event.betterPlayerEventType) {
  //       case BetterPlayerEventType.play:
  //         isPlaying.value = true;
  //         break;
  //       case BetterPlayerEventType.pause:
  //         isPlaying.value = false;
  //         break;
  //       case BetterPlayerEventType.bufferingStart:
  //         isBuffering.value = true;
  //         break;
  //       case BetterPlayerEventType.bufferingEnd:
  //         isBuffering.value = false;
  //         break;
  //       case BetterPlayerEventType.initialized:
  //         seekToLastPosition(duration);
  //         isInitialized.value = true;
  //         isSettingUp.value = true;
  //         break;
  //       default:
  //         break;
  //     }
  //   });
  // }

  // Future<void> seekToLastPosition(Duration duration) async {
  //   final controller = betterPlayerController.value?.videoPlayerController;

  //   if (controller == null) return;

  //   while (!controller.value.initialized) {
  //     await Future.delayed(const Duration(milliseconds: 100));
  //   }

  //   await controller.seekTo(duration);
  //   await controller.play();
  // }

  // void playPauseVideo() {
  //   if (betterPlayerController.value != null &&
  //       betterPlayerController.value!.isPlaying()!) {
  //     betterPlayerController.value!.pause();
  //   } else {
  //     betterPlayerController.value!.play();
  //   }
  // }
}
