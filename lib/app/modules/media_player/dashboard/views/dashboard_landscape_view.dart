import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/views/expand_list_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/bindings/my_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/views/my_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/upload_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/custom_container.dart';
import '../../../../../widgets/components/custom_divider.dart';
import '../../../../../widgets/components/custom_header.dart';
import '../../../../../widgets/components/custom_section.dart';
import '../../../../../widgets/media_player/videos/custom_video_container.dart';
import '../../../../../widgets/media_player/videos/custom_video_feed.dart';
import '../../../../../widgets/media_player/videos/custom_video_item.dart';
import '../../../../../widgets/media_player/videos/custom_video_list.dart';

class DashboardLandscapeView extends GetView<DashboardController> {
  final bool landscape;
  const DashboardLandscapeView({super.key, required this.landscape});
  @override
  Widget build(BuildContext context) {
    Get.put(DashboardController());
    return
        // Stack(
        //   alignment: Alignment.bottomRight,
        //   children: [
        Column(
      children: [
        const CustomDivider(),
        Expanded(
          child: ListView(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: CustomContainer(
                            height: 1250,
                            width: 680,
                            bgColor: secondBlueColor.withOpacity(0.15),
                            radius: 12,
                            widget: Obx(
                              () {
                                // * Popular Vod
                                final datasPopularVod =
                                    controller.listPopularVod;
                                final firstItemPopularVod =
                                    datasPopularVod.isNotEmpty
                                        ? datasPopularVod.first
                                        : null;
                                final isEmpty = datasPopularVod.isEmpty &&
                                    !controller.isLoadingPopularVod.value;

                                return CustomScrollView(
                                  physics: isEmpty
                                      ? const NeverScrollableScrollPhysics()
                                      : const BouncingScrollPhysics(),
                                  slivers: [
                                    SliverAppBar(
                                      automaticallyImplyLeading: false,
                                      expandedHeight: 600.h,
                                      backgroundColor: Colors.transparent,
                                      flexibleSpace: isEmpty
                                          ? SizedBox(
                                              width: 680.w,
                                              height: 300.h,
                                            )
                                          : Skeletonizer(
                                              enabled: controller
                                                  .isLoadingPopularVod.value,
                                              textBoneBorderRadius:
                                                  TextBoneBorderRadius(
                                                BorderRadius.circular(4.r),
                                              ),
                                              child: FlexibleSpaceBar(
                                                background: GestureDetector(
                                                  onTap: () {
                                                    Get.to(VideoPlayerView(),
                                                        binding:
                                                            VideoPlayerBinding(),
                                                        arguments: {
                                                          "videoUrl":
                                                              firstItemPopularVod
                                                                  ?.videoUrl,
                                                          "videoSourceUrl":
                                                              firstItemPopularVod
                                                                  ?.videoSourceUrl,
                                                          "totalLike":
                                                              firstItemPopularVod
                                                                  ?.totalLike,
                                                          "totalDislike":
                                                              firstItemPopularVod
                                                                  ?.totalDislike,
                                                          "totalRating":
                                                              firstItemPopularVod
                                                                  ?.totalRating,
                                                          "totalView":
                                                              firstItemPopularVod
                                                                  ?.totalView,
                                                          "videoId":
                                                              firstItemPopularVod
                                                                  ?.id,
                                                          "videoTitle":
                                                              firstItemPopularVod
                                                                  ?.title,
                                                          "videoTags":
                                                              firstItemPopularVod
                                                                  ?.tagNames,
                                                        });
                                                  },
                                                  child: CustomVideoContainer(
                                                    thumbnailUrl:
                                                        firstItemPopularVod
                                                                ?.thumbnailUrl ??
                                                            '',
                                                    titleVideo:
                                                        firstItemPopularVod
                                                                ?.title ??
                                                            '',
                                                    totalView:
                                                        firstItemPopularVod
                                                                ?.totalView ??
                                                            0,
                                                    uploadDate:
                                                        firstItemPopularVod !=
                                                                null
                                                            ? timeRange(
                                                                controller
                                                                    .timeNow
                                                                    .value,
                                                                firstItemPopularVod
                                                                    .uploadDate!)
                                                            : '',
                                                    duration:
                                                        firstItemPopularVod !=
                                                                null
                                                            ? formatDuration(
                                                                firstItemPopularVod
                                                                    .duration)
                                                            : '',
                                                    tags: firstItemPopularVod
                                                        ?.tagNames,
                                                    uploaderName:
                                                        firstItemPopularVod
                                                            ?.uploader
                                                            ?.pasisName,
                                                    userName:
                                                        firstItemPopularVod
                                                            ?.uploader
                                                            ?.username,
                                                    width: 680,
                                                    heightVideo: 300,
                                                    heightContainerVideo: 300,
                                                    widthUserInfo: 80,
                                                    heightUserInfo: 80,
                                                    heightButton: 65,
                                                    titleSize: 32,
                                                    subTitleSize: 24,
                                                    widthIcon: 30,
                                                    heightIcon: 30,
                                                    isLandscape: landscape,
                                                  ),
                                                ),
                                              ),
                                            ),
                                    ),
                                    SliverAppBar(
                                      automaticallyImplyLeading: false,
                                      elevation: 0,
                                      pinned: true,
                                      bottom: PreferredSize(
                                          preferredSize: Size.fromHeight(-2.h),
                                          child: const SizedBox()),
                                      backgroundColor: baseBlueColor,
                                      toolbarHeight: 125.h,
                                      flexibleSpace: CustomContainerList(
                                        isLandscape: landscape,
                                        title: 'Populer',
                                        width: 680,
                                        height: 700,
                                        heightButton: 70,
                                        onViewClicked: () => Get.to(
                                          () => const ExpandListVideoView(
                                              title: 'Populer'),
                                        ),
                                      ),
                                    ),
                                    isEmpty
                                        ? SliverToBoxAdapter(
                                            child: SizedBox(
                                              height: Get.height * 0.35,
                                              child: const Center(
                                                child: CustomDataEmpty(
                                                    title:
                                                        'Tidak ada data video popular.',
                                                    assetName:
                                                        'assets/icons/no_data_video.svg'),
                                              ),
                                            ),
                                          )
                                        : SliverList(
                                            delegate:
                                                SliverChildBuilderDelegate(
                                                    (it, idx) {
                                              final item =
                                                  datasPopularVod.isNotEmpty
                                                      ? datasPopularVod[idx]
                                                      : null;
                                              return Skeletonizer(
                                                enabled: controller
                                                    .isLoadingPopularVod.value,
                                                textBoneBorderRadius:
                                                    TextBoneBorderRadius(
                                                  BorderRadius.circular(2.r),
                                                ),
                                                child: GestureDetector(
                                                  onTap: () {
                                                    Get.to(VideoPlayerView(),
                                                        binding:
                                                            VideoPlayerBinding(),
                                                        arguments: {
                                                          "videoUrl":
                                                              item?.videoUrl,
                                                          "videoSourceUrl": item
                                                              ?.videoSourceUrl,
                                                          "totalLike":
                                                              item?.totalLike,
                                                          "totalDislike": item
                                                              ?.totalDislike,
                                                          "totalRating":
                                                              item?.totalRating,
                                                          "totalView":
                                                              item?.totalView,
                                                          "videoId": item?.id,
                                                          "videoTitle":
                                                              item?.title,
                                                          "videoTags":
                                                              item?.tagNames,
                                                        });
                                                  },
                                                  child: CustomVideoItem(
                                                    topSpace: 0,
                                                    thumbnailHeight: 150,
                                                    thumbnailWidth: 250,
                                                    isLandscape: landscape,
                                                    bottomRightSpace: 160,
                                                    thumbnailUrl:
                                                        item?.thumbnailUrl ??
                                                            '',
                                                    duration: formatDuration(
                                                        item?.duration),
                                                    titleVideo: item?.title,
                                                    uploaderName: item
                                                        ?.uploader?.pasisName,
                                                    totalView:
                                                        item?.totalView ?? 0,
                                                    uploadDate: item
                                                                ?.uploadDate !=
                                                            null
                                                        ? timeRange(
                                                            controller
                                                                .timeNow.value,
                                                            item!.uploadDate!)
                                                        : 'Hari Ini',
                                                    tags: item?.tagNames,
                                                  ),
                                                ),
                                              );
                                            },
                                                    childCount:
                                                        datasPopularVod.isEmpty
                                                            ? 10
                                                            : datasPopularVod
                                                                .length),
                                          ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                        24.horizontalSpace,
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12.r),
                          child: CustomContainer(
                            height: 1250,
                            width: 680,
                            bgColor: secondBlueColor.withOpacity(0.15),
                            widget: Obx(
                              () {
                                final datasNewestVod = controller.listNewestVod;
                                final firstItem = datasNewestVod.isNotEmpty
                                    ? datasNewestVod.first
                                    : null;
                                final isEmpty = datasNewestVod.isEmpty &&
                                    !controller.isLoadingNewestVod.value;

                                return CustomScrollView(
                                  physics: isEmpty
                                      ? const NeverScrollableScrollPhysics()
                                      : const BouncingScrollPhysics(),
                                  slivers: [
                                    SliverAppBar(
                                      automaticallyImplyLeading: false,
                                      expandedHeight: 600.h,
                                      backgroundColor: Colors.transparent,
                                      flexibleSpace: isEmpty
                                          ? SizedBox(
                                              width: 680.w,
                                              height: 300.h,
                                            )
                                          : Skeletonizer(
                                              enabled: controller
                                                  .isLoadingNewestVod.value,
                                              textBoneBorderRadius:
                                                  TextBoneBorderRadius(
                                                BorderRadius.circular(4.r),
                                              ),
                                              child: FlexibleSpaceBar(
                                                background: GestureDetector(
                                                  onTap: () {
                                                    Get.to(VideoPlayerView(),
                                                        binding:
                                                            VideoPlayerBinding(),
                                                        arguments: {
                                                          "videoUrl": firstItem
                                                              ?.videoUrl,
                                                          "videoSourceUrl":
                                                              firstItem
                                                                  ?.videoSourceUrl,
                                                          "totalLike": firstItem
                                                              ?.totalLike,
                                                          "totalDislike":
                                                              firstItem
                                                                  ?.totalDislike,
                                                          "totalRating":
                                                              firstItem
                                                                  ?.totalRating,
                                                          "totalView": firstItem
                                                              ?.totalView,
                                                          "videoId":
                                                              firstItem?.id,
                                                          "videoTitle":
                                                              firstItem?.title,
                                                          "videoTags": firstItem
                                                              ?.tagNames,
                                                        });
                                                  },
                                                  child: CustomVideoContainer(
                                                    thumbnailUrl: firstItem
                                                            ?.thumbnailUrl ??
                                                        '',
                                                    titleVideo:
                                                        firstItem?.title,
                                                    totalView:
                                                        firstItem?.totalView ??
                                                            0,
                                                    uploadDate: timeRange(
                                                      controller.timeNow.value,
                                                      firstItem?.uploadDate ??
                                                          controller
                                                              .timeNow.value,
                                                    ),
                                                    tags: firstItem?.tagNames,
                                                    duration: formatDuration(
                                                        firstItem?.duration),
                                                    uploaderName: firstItem
                                                        ?.uploader?.username,
                                                    width: 680,
                                                    heightVideo: 300,
                                                    heightContainerVideo: 300,
                                                    widthUserInfo: 80,
                                                    heightUserInfo: 80,
                                                    heightButton: 65,
                                                    titleSize: 32,
                                                    subTitleSize: 24,
                                                    widthIcon: 30,
                                                    heightIcon: 30,
                                                    isLandscape: landscape,
                                                  ),
                                                ),
                                              ),
                                            ),
                                    ),
                                    SliverAppBar(
                                      automaticallyImplyLeading: false,
                                      elevation: 0,
                                      pinned: true,
                                      bottom: PreferredSize(
                                          preferredSize: Size.fromHeight(-2.h),
                                          child: const SizedBox()),
                                      backgroundColor: baseBlueColor,
                                      toolbarHeight: 125.h,
                                      flexibleSpace: CustomContainerList(
                                        isLandscape: landscape,
                                        title: 'Terbaru',
                                        width: 680,
                                        height: 700,
                                        heightButton: 70,
                                        onViewClicked: () => Get.to(
                                          () => const ExpandListVideoView(
                                              title: 'Terbaru'),
                                        ),
                                      ),
                                    ),
                                    isEmpty
                                        ? SliverToBoxAdapter(
                                            child: SizedBox(
                                              height: Get.height * 0.35,
                                              child: const Center(
                                                child: CustomDataEmpty(
                                                    title:
                                                        'Tidak ada data video terbaru.',
                                                    assetName:
                                                        'assets/icons/no_data_video.svg'),
                                              ),
                                            ),
                                          )
                                        : SliverList(
                                            delegate:
                                                SliverChildBuilderDelegate(
                                                    (item, idx) {
                                              final item =
                                                  datasNewestVod.isNotEmpty
                                                      ? datasNewestVod[idx]
                                                      : null;
                                              return Skeletonizer(
                                                enabled: controller
                                                    .isLoadingNewestVod.value,
                                                textBoneBorderRadius:
                                                    TextBoneBorderRadius(
                                                  BorderRadius.circular(2.r),
                                                ),
                                                child: GestureDetector(
                                                  onTap: () {
                                                    Get.to(VideoPlayerView(),
                                                        binding:
                                                            VideoPlayerBinding(),
                                                        arguments: {
                                                          "videoUrl":
                                                              item?.videoUrl,
                                                          "videoSourceUrl": item
                                                              ?.videoSourceUrl,
                                                          "totalLike":
                                                              item?.totalLike,
                                                          "totalDislike": item
                                                              ?.totalDislike,
                                                          "totalRating":
                                                              item?.totalRating,
                                                          "totalView":
                                                              item?.totalView,
                                                          "videoId": item?.id,
                                                          "videoTitle":
                                                              item?.title,
                                                          "videoTags":
                                                              item?.tagNames,
                                                        });
                                                  },
                                                  child: CustomVideoItem(
                                                    topSpace: 0,
                                                    thumbnailHeight: 150,
                                                    thumbnailWidth: 250,
                                                    isLandscape: landscape,
                                                    bottomRightSpace: 160,
                                                    thumbnailUrl:
                                                        item?.thumbnailUrl,
                                                    titleVideo: item?.title,
                                                    uploaderName: item
                                                        ?.uploader?.pasisName,
                                                    totalView: item?.totalView,
                                                    uploadDate: timeRange(
                                                      controller.timeNow.value,
                                                      item?.uploadDate ??
                                                          controller
                                                              .timeNow.value,
                                                    ),
                                                    tags: item?.tagNames,
                                                  ),
                                                ),
                                              );
                                            },
                                                    childCount: datasNewestVod
                                                            .isNotEmpty
                                                        ? datasNewestVod.length
                                                        : 10),
                                          ),
                                  ],
                                );
                              },
                            ),
                          ),
                        ),
                        24.horizontalSpace,
                        Flexible(
                          child: CustomContainer(
                            width: double.infinity,
                            widget: Obx(
                              () => Stack(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const CustomContainer(
                                        widget: CustomDivider(),
                                      ),
                                      Obx(
                                        () {
                                          final data =
                                              controller.userProfile.value;

                                          return Skeletonizer(
                                            enabled: controller
                                                .isLoadingUserProfile.value,
                                            child: CustomHeader(
                                                userName: data?.name,
                                                nameTag: data?.name,
                                                userImageUrl:
                                                    data?.imageProfile,
                                                widthUserInfo: 80,
                                                heightUserInfo: 80,
                                                titleSize: 30,
                                                subTitleSize: 25,
                                                isLandscape: landscape,
                                                onMyVideoPress: () {
                                                  Get.to(
                                                      () => const MyVideoView(),
                                                      binding:
                                                          MyVideoBinding());
                                                },
                                                onUploadPress: () {
                                                  Get.to(
                                                      () =>
                                                          const UploadVideoView(),
                                                      binding:
                                                          UploadVideoBinding());
                                                }),
                                          );
                                        },
                                      ),
                                      const CustomContainer(
                                        widget: CustomDivider(),
                                      ),
                                      24.verticalSpace,
                                      CustomContainerList(
                                        isLandscape: landscape,
                                        title: 'Lanjut Menonton',
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(12.r),
                                            topRight: Radius.circular(12.r),
                                            bottomLeft: Radius.circular(12.r),
                                            bottomRight: Radius.circular(12.r)),
                                        height: 1118,
                                        heightButton: 70,
                                        onViewClicked: () {
                                          () => Get.to(
                                                () => const ExpandListVideoView(
                                                    title: 'Lanjut Menonton'),
                                              );
                                        },
                                        widget: Flexible(
                                          child: Obx(() {
                                            final datas =
                                                controller.listContinueWatchVod;

                                            if (datas.isEmpty &&
                                                !controller
                                                    .isLoadingContinueWatchVod
                                                    .value) {
                                              return const Center(
                                                child: CustomDataEmpty(
                                                    title:
                                                        'Tidak ada data Lanjut Menonton.',
                                                    assetName:
                                                        'assets/icons/no_data_video.svg'),
                                              );
                                            }

                                            return Skeletonizer(
                                              enabled: controller
                                                  .isLoadingContinueWatchVod
                                                  .value,
                                              child: ListView.builder(
                                                itemCount: datas.length,
                                                itemBuilder: (ctx, idx) {
                                                  final item = datas.isNotEmpty
                                                      ? datas[idx]
                                                      : null;
                                                  return GestureDetector(
                                                    onTap: () {
                                                      Get.to(VideoPlayerView(),
                                                          binding:
                                                              VideoPlayerBinding(),
                                                          arguments: {
                                                            "videoUrl": item
                                                                ?.vod?.videoUrl,
                                                            "videoSourceUrl": item
                                                                ?.vod
                                                                ?.videoSourceUrl,
                                                            "totalLike": item
                                                                ?.vod
                                                                ?.totalLike,
                                                            "totalDislike": item
                                                                ?.vod
                                                                ?.totalDislike,
                                                            "totalRating": item
                                                                ?.vod
                                                                ?.totalRating,
                                                            "totalView": item
                                                                ?.vod
                                                                ?.totalView,
                                                            "videoId":
                                                                item?.vod?.id,
                                                            "videoTitle": item
                                                                ?.vod?.title,
                                                            "videoTags": item
                                                                ?.vod?.tagNames,
                                                            "lastWatchTime":
                                                                stringToDuration(
                                                                    item?.lastWatchedTime ??
                                                                        '00:00:00'),
                                                          });
                                                    },
                                                    child: CustomVideoItem(
                                                      thumbnailUrl: item
                                                          ?.vod?.thumbnailUrl,
                                                      duration: formatDuration(
                                                          item?.vod?.duration),
                                                      titleVideo:
                                                          item?.vod?.title,
                                                      uploaderName: item?.vod
                                                          ?.uploader?.pasisName,
                                                      totalView:
                                                          item?.vod?.totalView,
                                                      uploadDate: timeRange(
                                                          controller
                                                              .timeNow.value,
                                                          item?.vod
                                                                  ?.uploadDate ??
                                                              controller.timeNow
                                                                  .value),
                                                      tags: item?.vod?.tagNames,
                                                      thumbnailHeight: 150,
                                                      thumbnailWidth: 300,
                                                      isLandscape: landscape,
                                                      bottomRightSpace: 210,
                                                      lastDuration:
                                                          formatDuration(item
                                                              ?.vod?.duration),
                                                      lastWatchTime: (convertDurationToSeconds(
                                                                  item?.lastWatchedTime ??
                                                                      '00:00:00') /
                                                              convertDurationToSeconds(item
                                                                      ?.vod
                                                                      ?.duration ??
                                                                  '00:00:00'))
                                                          .clamp(0.0, 1.0),
                                                    ),
                                                  );
                                                },
                                              ),
                                            );
                                          }),
                                        ),
                                      ),
                                    ],
                                  ),
                                  controller.viewOtherButton.value
                                      ? Positioned(
                                          top: 65,
                                          left: 362,
                                          child: CustomFilledButtonWidget(
                                            widthButton: 230,
                                            onPressed: () {
                                              controller.viewOtherButton.value =
                                                  false;
                                            },
                                            withIcon: true,
                                            assetName:
                                                'assets/icons/broadcast.svg',
                                            title: 'Broadcast',
                                            fontSize: 18,
                                            widthIcon: 32,
                                            heightIcon: 32,
                                            radius: 8,
                                            bgColor: null,
                                            gradient: LinearGradient(
                                              colors: [redColor, darkFuchsia],
                                              // stops: const [0.5, 2.0],
                                              transform:
                                                  const GradientRotation(0.5),
                                            ),
                                            fontColor: secondWhiteColor,
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 18.w,
                                            ),
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const CustomDivider(),
                  32.verticalSpace,
                  CustomSection(
                    sectionTitle: 'Untuk Kamu',
                    sectionTitleButton: 'Lihat Semua',
                    onPressed: () => Get.to(
                      () => const ExpandListVideoView(title: 'Untuk Kamu'),
                    ),
                  ),
                  32.verticalSpace,
                  SizedBox(
                    height: Get.height * 0.4,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Obx(() {
                        final datas = controller.listRecommVod;
                        // final profile = controller.userProfile;
                        final isEmpty = datas.isEmpty &&
                            !controller.isLoadingRecommVod.value;

                        if (isEmpty) {
                          return const Center(
                            child: CustomDataEmpty(
                                title: 'Tidak ada data For You.',
                                assetName: 'assets/icons/no_data_video.svg'),
                          );
                        }
                        return Skeletonizer(
                          enabled: controller.isLoadingRecommVod.value,
                          textBoneBorderRadius: TextBoneBorderRadius(
                            BorderRadius.circular(4.r),
                          ),
                          child: GridView.builder(
                            cacheExtent: 900,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 4,
                                    childAspectRatio: 19 / 20,
                                    crossAxisSpacing: 24.w,
                                    mainAxisSpacing: 24.h),
                            itemCount: controller.listRecommVod.length > 3
                                ? 4
                                : controller.listRecommVod.length,
                            itemBuilder: (it, idx) {
                              final item = datas[idx];
                              return GestureDetector(
                                onTap: () {
                                  Get.to(VideoPlayerView(),
                                      binding: VideoPlayerBinding(),
                                      arguments: {
                                        "videoUrl": item.videoUrl,
                                        "videoSourceUrl": item.videoSourceUrl,
                                        "totalLike": item.totalLike,
                                        "totalDislike": item.totalDislike,
                                        "totalRating": item.totalRating,
                                        "totalView": item.totalView,
                                        "videoId": item.id,
                                        "videoTitle": item.title,
                                        "videoTags": item.tagNames,
                                      });
                                },
                                child: CustomVideoFeed(
                                  titleVideo: item.title,
                                  totalView: item.totalView ?? 0,
                                  uploadDate: item != null
                                      ? timeRange(controller.timeNow.value,
                                          item.uploadDate!)
                                      : '',
                                  thumbnailUrl: item.thumbnailUrl,
                                  duration: formatDuration(item.duration),
                                  uploaderName: item.uploader?.username,
                                  tags: item.tagNames,
                                  width: double.infinity,
                                  height: 300,
                                ),
                              );
                            },
                          ),
                        );
                      }),
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ],
    );
    // Positioned(
    //   bottom: 70.h,
    //   right: 14.w,
    //   child: GestureDetector(
    //     onTap: () {
    //       Get.toNamed(Routes.VIDEO_PLAYER);
    //     },
    //     child: Column(
    //       children: [
    //         CustomContainer(
    //           width: 550,
    //           height: 330,
    //           borderRadius: const BorderRadius.only(
    //             topLeft: Radius.circular(12),
    //             topRight: Radius.circular(12),
    //           ),
    //           widget: ClipRRect(
    //             borderRadius: const BorderRadius.only(
    //               topLeft: Radius.circular(12),
    //               topRight: Radius.circular(12),
    //             ),
    //             child: Image.asset(
    //               "assets/images/airplane.png",
    //               fit: BoxFit.cover,
    //             ),
    //           ),
    //         ),
    //         ClipRRect(
    //           borderRadius: BorderRadius.only(
    //             bottomLeft: Radius.circular(12.r),
    //             bottomRight: Radius.circular(12.r),
    //           ),
    //           child: BackdropFilter(
    //             filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
    //             child: CustomContainer(
    //               width: 550,
    //               height: 100,
    //               bgColor: darkGreyColor.withOpacity(0.2),
    //               borderRadius: BorderRadius.only(
    //                 bottomLeft: Radius.circular(12.r),
    //                 bottomRight: Radius.circular(12.r),
    //               ),
    //             ),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    // ),
    //   ],
    // );
  }
}
