import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/choose_screen/bindings/choose_screen_binding.dart';
import 'package:mides_skadik/app/modules/choose_screen/views/choose_screen_view.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/views/dashboard_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/views/dashboard_potrait_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import '../controllers/dashboard_controller.dart';

class DashboardView extends GetView<DashboardController> {
  const DashboardView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => WillPopScope(
        onWillPop: () async {
          if (Get.isRegistered<DashboardController>()) {
            Get.delete<DashboardController>();
          }
          Get.offAll(() => const ChooseScreenView(),
              binding: ChooseScreenBinding());
          return true; // Allows the back action in portrait mode
        },
        child: CustomScaffold(
          useAppBar: true,
          titleAppBar: 'Dasboard',
          fontWeight: FontWeight.w200,
          withIcon: true,
          customIconPath: "assets/icons/dashoard.svg",
          isHamburgerIcon: true,
          fontSizeAppBar: 32,
          actions: orientation.isLandscape.value
              ? [
                  Padding(
                    padding: EdgeInsets.only(right: 16.w),
                    child: CustomFilledButtonWidget(
                      onPressed: () {},
                      withIcon: true,
                      assetName: 'assets/icons/search.svg',
                      title: 'Cari',
                      fontSize: 22,
                      heightIcon: 32,
                      heightButton: 64,
                      bgColor: secondBlueColor.withOpacity(0.25),
                      fontColor: whiteColor,
                      radius: 10,
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 20.w),
                    child: CustomFilledButtonWidget(
                      onPressed: () {
                        Get.toNamed('/notification');
                      },
                      withIcon: true,
                      assetName: 'assets/icons/notification.svg',
                      title: 'Notifikasi',
                      fontSize: 22,
                      heightIcon: 32,
                      heightButton: 64,
                      bgColor: secondBlueColor.withOpacity(0.25),
                      fontColor: whiteColor,
                      radius: 10,
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                    ),
                  ),
                ]
              : [
                  IconButton(
                    onPressed: () {
                      // layoutNav.currentTab.value = 1;
                    },
                    icon: SvgPicture.asset(
                      'assets/icons/search.svg',
                      width: 48.w,
                      height: 48.h,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    icon: SvgPicture.asset(
                      'assets/icons/notification.svg',
                      width: 48.w,
                      height: 48.h,
                    ),
                  ),
                ],
          body: orientation.isLandscape.value
              ? DashboardLandscapeView(landscape: orientation.isLandscape.value)
              : const DashboardPotraitView(),
        ),
      ),
    );
  }
}
