import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/expand_list_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/searchdata/controllers/searchdata_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';

class ExpandListVideoView extends StatefulWidget {
  final String title;
  const ExpandListVideoView({super.key, required this.title});

  @override
  State<ExpandListVideoView> createState() => _ExpandListVideoViewState();
}

class _ExpandListVideoViewState extends State<ExpandListVideoView> {
  final DashboardController controller = Get.put(DashboardController());
  final ExpandListVideoController controllerExpand =
      Get.put(ExpandListVideoController());
  final SearchdataController searchController = Get.put(SearchdataController());

  final orientation = Get.find<OrientationController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getAllView(title: widget.title);
      searchController.getListCategory();
    });
  }

  @override
  void dispose() {
    super.dispose();
    controller.dataViewAll.clear();
    controller.page.value = 1;
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      titleAppBar: widget.title,
      withIcon: true,
      customIconPath: 'assets/icons/back.svg',
      iconWithBackground: true,
      heightAppBar: 150.h,
      body: Column(
        children: [
          8.verticalSpace,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              spacing: 10,
              children: [
                Obx(() => CustomFilledButtonWidget(
                      onPressed: () async {
                        controller.clearFilter();
                        await controller.getAllView(title: widget.title);
                      },
                      title: 'All',
                      heightButton: 56,
                      radius: 8,
                      fontColor: whiteColor,
                      bgColor: baseBlueColor.withValues(alpha: .6),
                      bgSelectedColor: whiteColor,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      isSelected: controller.selectedFilter.value.isEmpty,
                    )),
                CustomFilledButtonWidget(
                  onPressed: () {},
                  title: 'Videos',
                  heightButton: 56,
                  radius: 8,
                  fontColor: whiteColor,
                  bgColor: baseBlueColor.withValues(alpha: .6),
                  bgSelectedColor: whiteColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                ),
                CustomFilledButtonWidget(
                  onPressed: () {},
                  title: 'Playlist',
                  heightButton: 56,
                  radius: 8,
                  fontColor: whiteColor,
                  bgColor: baseBlueColor.withValues(alpha: .6),
                  bgSelectedColor: whiteColor,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                ),
                const CustomDivider(
                  isVertical: false,
                  width: 2,
                  height: 35,
                ),
                Obx(
                  () {
                    if (searchController.isLoadingCategory.value) {
                      return const CustomLoadingWidget();
                    }
                    return Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Wrap(
                          spacing: 10,
                          children: [
                            ...searchController.categories.map((e) {
                              return CustomFilledButtonWidget(
                                onPressed: () async {
                                  controller.selectFilter(e.id!);
                                  await controller.getAllView(
                                      title: widget.title);
                                },
                                title: e.name,
                                heightButton: 56,
                                radius: 8,
                                fontColor: whiteColor,
                                bgColor: baseBlueColor.withValues(alpha: .6),
                                bgSelectedColor: whiteColor,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                isSelected:
                                    e.id == controller.selectedFilter.value,
                              );
                            })
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          6.verticalSpace,
          const CustomDivider(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Obx(
                () {
                  final datas = controller.dataViewAll;
                  if (controller.isLoadingViewAll.value && datas.isEmpty) {
                    return const CustomLoadingWidget();
                  }

                  return datas.isEmpty
                      ? const Center(
                          child: CustomDataEmpty(
                              title: 'Tidak ada data video.',
                              assetName: 'assets/icons/no_data_video.svg'),
                        )
                      : SingleChildScrollView(
                          child: Column(
                            children: [
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount:
                                      orientation.isLandscape.value ? 4 : 3,
                                  childAspectRatio:
                                      orientation.isLandscape.value
                                          ? 19 / 21
                                          : 10 / 13,
                                  crossAxisSpacing: 24.w,
                                  mainAxisSpacing: 24.h,
                                ),
                                itemBuilder: (context, idx) {
                                  final item =
                                      datas.isNotEmpty ? datas[idx] : null;
                                  return CustomVideoFeed(
                                    titleVideo: item?.title,
                                    totalView: item?.totalView ?? 0,
                                    uploadDate: item != null
                                        ? timeRange(controller.timeNow.value,
                                            item.uploadDate!)
                                        : '',
                                    thumbnailUrl: item?.thumbnailUrl,
                                    duration: formatDuration(
                                        item?.duration ?? '99:99'),
                                    uploaderName: item?.uploader?.pasisName,
                                    userName: item?.uploader?.username,
                                    tags: item?.tagNames,
                                    width: double.infinity,
                                    height: 300,
                                  );
                                },
                                itemCount: datas.isNotEmpty ? datas.length : 9,
                              ),
                              controller.isLoadingMoreData.value
                                  ? const CircularProgressIndicator()
                                  : controller.isLastPage.value
                                      ? CustomTextWigdet(
                                          title: 'No More Videos',
                                          fontWeight: FontWeight.w600,
                                          textColor: secondWhiteColor,
                                          fontSize: 32,
                                        )
                                      : CustomFilledButtonWidget(
                                          bgColor: secondBlueColor,
                                          widthButton: 300,
                                          heightButton: 50,
                                          radius: 10,
                                          onPressed: () {
                                            controller.page + 1;
                                            controller.getAllView(
                                                title: widget.title);
                                          },
                                          title: 'Load More Video',
                                        )
                            ],
                          ),
                        );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
