import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/controllers/clipping_controller.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/playlist_collection_view.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/views/expand_list_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_item_dialog_more.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../../../../widgets/components/custom_divider.dart';
import '../../../../../widgets/components/custom_header.dart';
import '../../../../../widgets/components/custom_section.dart';
import '../../../../../widgets/media_player/videos/custom_video_container.dart';
import '../../../../../widgets/media_player/videos/custom_video_feed.dart';
import '../../../../../widgets/media_player/videos/custom_video_item.dart';
import '../../../../../widgets/media_player/videos/custom_video_list.dart';
import '../../my_video/bindings/my_video_binding.dart';
import '../../my_video/views/my_video_view.dart';
import '../../upload_video/bindings/upload_video_binding.dart';
import '../../upload_video/views/upload_video_view.dart';

class DashboardPotraitView extends GetView<DashboardController> {
  const DashboardPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    Get.put(DashboardController());
    Get.put(ClippingController());
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 60),
          child: Column(
            children: [
              const CustomDivider(),
              Obx(
                () {
                  final data = controller.userProfile.value;

                  return Skeletonizer(
                    enabled: controller.isLoadingUserProfile.value,
                    child: CustomHeader(
                        userName: data?.name,
                        userImageUrl: data?.imageProfile,
                        widthUserInfo: 80,
                        heightUserInfo: 80,
                        titleSize: 30,
                        subTitleSize: 25,
                        onMyVideoPress: () {
                          Get.to(() => const MyVideoView(),
                              binding: MyVideoBinding());
                        },
                        onUploadPress: () {
                          Get.to(() => const UploadVideoView(),
                              binding: UploadVideoBinding());
                        }),
                  );
                },
              ),
              const CustomDivider(),
              Expanded(
                child: RefreshIndicator(
                  color: baseBlueColor,
                  onRefresh: () async {
                    controller.refreshPage();
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 40.w, vertical: 32.h),
                          child: Obx(
                            () {
                              final datas = controller.listPopularVod;
                              final firstItem =
                                  datas.isNotEmpty ? datas.first : null;
                              final isEmpty =
                                  !controller.isLoadingPopularVod.value &&
                                      datas.isEmpty;

                              return Row(
                                children: [
                                  Skeletonizer(
                                    enabled:
                                        controller.isLoadingPopularVod.value,
                                    child: isEmpty
                                        ? CustomContainer(
                                            width: 580,
                                            height: 470,
                                            radius: 12,
                                            bgColor: baseBlueColor.withValues(
                                                alpha: 0.5),
                                          )
                                        : GestureDetector(
                                            onTap: () {
                                              Get.to(VideoPlayerView(),
                                                  binding: VideoPlayerBinding(),
                                                  arguments: {
                                                    "videoUrl":
                                                        firstItem?.videoUrl,
                                                    "videoId": firstItem?.id,
                                                    "totalLike":
                                                        firstItem?.totalLike,
                                                    "totalDislike":
                                                        firstItem?.totalDislike,
                                                    "totalRating":
                                                        firstItem?.totalRating,
                                                    "totalView":
                                                        firstItem?.totalView,
                                                    "videoSourceUrl": firstItem
                                                        ?.videoSourceUrl,
                                                    "videoTitle":
                                                        firstItem?.title,
                                                    "videoTags":
                                                        firstItem?.tagNames,
                                                  });
                                            },
                                            child: CustomVideoContainer(
                                              thumbnailUrl:
                                                  firstItem?.thumbnailUrl ?? '',
                                              titleVideo: firstItem?.title,
                                              totalView: firstItem?.totalView,
                                              uploadDate: timeRange(
                                                  controller.timeNow.value,
                                                  firstItem?.uploadDate ??
                                                      controller.timeNow.value),
                                              duration: formatDuration(
                                                  firstItem?.duration),
                                              tags: firstItem?.tagNames,
                                              uploaderName: firstItem
                                                  ?.uploader?.pasisName,
                                              userName:
                                                  firstItem?.uploader?.username,
                                              photoUserUrl: firstItem
                                                  ?.uploader?.imageProfile,
                                              onTapMore: () {
                                                _showMoreDialog(
                                                    firstItem?.id ?? '');
                                              },
                                            ),
                                          ),
                                  ),
                                  32.horizontalSpace,
                                  Flexible(
                                    child: CustomContainerList(
                                      title: 'Populer',
                                      onViewClicked: () => Get.to(
                                        () => const ExpandListVideoView(
                                            title: 'Populer'),
                                      ),
                                      widget: Expanded(
                                        child: isEmpty
                                            ? const Center(
                                                child: CustomDataEmpty(
                                                    title:
                                                        'Tidak ada data video Populer.',
                                                    assetName:
                                                        'assets/icons/no_data_video.svg'),
                                              )
                                            : Skeletonizer(
                                                enabled: controller
                                                    .isLoadingPopularVod.value,
                                                child: ListView.builder(
                                                  itemCount: datas.isNotEmpty
                                                      ? datas.length
                                                      : 10,
                                                  itemBuilder: (context, idx) {
                                                    final item =
                                                        datas.isNotEmpty
                                                            ? datas[idx]
                                                            : null;
                                                    return GestureDetector(
                                                      onTap: () {
                                                        Get.to(
                                                            VideoPlayerView(),
                                                            binding:
                                                                VideoPlayerBinding(),
                                                            arguments: {
                                                              "videoUrl": item
                                                                  ?.videoUrl,
                                                              "videoId":
                                                                  item?.id,
                                                              "totalLike": item
                                                                  ?.totalLike,
                                                              "totalDislike": item
                                                                  ?.totalDislike,
                                                              "totalRating": item
                                                                  ?.totalRating,
                                                              "totalView": item
                                                                  ?.totalView,
                                                              "videoSourceUrl":
                                                                  item?.videoSourceUrl,
                                                              "videoTitle":
                                                                  item?.title,
                                                              "videoTags": item
                                                                  ?.tagNames,
                                                            });
                                                      },
                                                      child: CustomVideoItem(
                                                        topSpace: 0,
                                                        thumbnailHeight: 150,
                                                        bottomRightSpace: 150,
                                                        thumbnailUrl:
                                                            item?.thumbnailUrl,
                                                        duration:
                                                            formatDuration(
                                                                item?.duration),
                                                        titleVideo: item?.title,
                                                        uploaderName: item
                                                            ?.uploader
                                                            ?.pasisName,
                                                        totalView:
                                                            item?.totalView,
                                                        uploadDate: timeRange(
                                                            controller
                                                                .timeNow.value,
                                                            item?.uploadDate ??
                                                                controller
                                                                    .timeNow
                                                                    .value),
                                                        tags: item?.tagNames,
                                                        onTapMore: () {
                                                          _showMoreDialog(
                                                              firstItem?.id ??
                                                                  '');
                                                        },
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                      ),
                                    ),
                                  )
                                ],
                              );
                            },
                          ),
                        ),
                        const CustomDivider(),
                        Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 40.w, vertical: 32.h),
                          child: Obx(
                            () {
                              final datas = controller.listNewestVod;
                              final firstItem =
                                  datas.isNotEmpty ? datas.first : null;
                              final isEmpty =
                                  !controller.isLoadingNewestVod.value &&
                                      datas.isEmpty;

                              return Row(
                                children: [
                                  Flexible(
                                    child: CustomContainerList(
                                      title: 'Terbaru',
                                      onViewClicked: () => Get.to(
                                        () => const ExpandListVideoView(
                                            title: 'Terbaru'),
                                      ),
                                      widget: Expanded(
                                        child: isEmpty
                                            ? const Center(
                                                child: CustomDataEmpty(
                                                    title:
                                                        'Tidak ada data video Terbaru.',
                                                    assetName:
                                                        'assets/icons/no_data_video.svg'),
                                              )
                                            : Skeletonizer(
                                                enabled: controller
                                                    .isLoadingNewestVod.value,
                                                child: ListView.builder(
                                                  itemCount: datas.isNotEmpty
                                                      ? datas.length
                                                      : 10,
                                                  itemBuilder: (it, idx) {
                                                    final item =
                                                        datas.isNotEmpty
                                                            ? datas[idx]
                                                            : null;
                                                    return GestureDetector(
                                                      onTap: () {
                                                        // print(item?.videoUrl);
                                                        Get.to(
                                                            VideoPlayerView(),
                                                            binding:
                                                                VideoPlayerBinding(),
                                                            arguments: {
                                                              "videoUrl": item
                                                                  ?.videoUrl,
                                                              "videoId":
                                                                  item?.id,
                                                              "totalLike": item
                                                                  ?.totalLike,
                                                              "totalDislike": item
                                                                  ?.totalDislike,
                                                              "totalRating": item
                                                                  ?.totalRating,
                                                              "totalView": item
                                                                  ?.totalView,
                                                              "videoSourceUrl":
                                                                  item?.videoSourceUrl,
                                                              "videoTitle":
                                                                  item?.title,
                                                              "videoTags": item
                                                                  ?.tagNames,
                                                            });
                                                      },
                                                      child: CustomVideoItem(
                                                        topSpace: 0,
                                                        thumbnailHeight: 150,
                                                        bottomRightSpace: 150,
                                                        thumbnailUrl:
                                                            item?.thumbnailUrl ??
                                                                '',
                                                        duration:
                                                            formatDuration(
                                                                item?.duration),
                                                        titleVideo: item?.title,
                                                        uploaderName: item
                                                            ?.uploader
                                                            ?.pasisName,
                                                        totalView:
                                                            item?.totalView,
                                                        uploadDate: timeRange(
                                                            controller
                                                                .timeNow.value,
                                                            item?.uploadDate ??
                                                                controller
                                                                    .timeNow
                                                                    .value),
                                                        tags: item?.tagNames,
                                                        onTapMore: () {
                                                          _showMoreDialog(
                                                              firstItem?.id ??
                                                                  '');
                                                        },
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                      ),
                                    ),
                                  ),
                                  32.horizontalSpace,
                                  Skeletonizer(
                                    enabled:
                                        controller.isLoadingNewestVod.value,
                                    child: isEmpty
                                        ? CustomContainer(
                                            width: 580,
                                            height: 470,
                                            radius: 12,
                                            bgColor: baseBlueColor.withValues(
                                                alpha: 0.5),
                                          )
                                        : GestureDetector(
                                            onTap: () {
                                              Get.to(VideoPlayerView(),
                                                  binding: VideoPlayerBinding(),
                                                  arguments: {
                                                    "videoUrl":
                                                        firstItem?.videoUrl,
                                                    "videoId": firstItem?.id,
                                                    "totalLike":
                                                        firstItem?.totalLike,
                                                    "totalDislike":
                                                        firstItem?.totalDislike,
                                                    "totalRating":
                                                        firstItem?.totalRating,
                                                    "totalView":
                                                        firstItem?.totalView,
                                                    "videoSourceUrl": firstItem
                                                        ?.videoSourceUrl,
                                                    "videoTitle":
                                                        firstItem?.title,
                                                    "videoTags":
                                                        firstItem?.tagNames,
                                                  });
                                            },
                                            child: CustomVideoContainer(
                                              thumbnailUrl:
                                                  firstItem?.thumbnailUrl ?? '',
                                              titleVideo: firstItem?.title,
                                              totalView: firstItem?.totalView,
                                              uploadDate: timeRange(
                                                  controller.timeNow.value,
                                                  firstItem?.uploadDate ??
                                                      controller.timeNow.value),
                                              duration: formatDuration(
                                                  firstItem?.duration),
                                              tags: firstItem?.tagNames,
                                              uploaderName: firstItem
                                                  ?.uploader?.pasisName,
                                              userName:
                                                  firstItem?.uploader?.username,
                                              photoUserUrl: firstItem
                                                  ?.uploader?.imageProfile,
                                              onTapMore: () {
                                                _showMoreDialog(
                                                    firstItem?.id ?? '');
                                              },
                                            ),
                                          ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        const CustomDivider(),
                        Obx(
                          () => Padding(
                            padding: const EdgeInsets.all(20),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  ...controller.listBroadcast
                                      .where((item) => item.status == 'active')
                                      .map((item) {
                                    return GestureDetector(
                                      onTap: () {
                                        Get.to(VideoPlayerView(),
                                            binding: VideoPlayerBinding(),
                                            arguments: {
                                              "videoUrl": item.broadcastUrl,
                                              "videoId": item.id,
                                              "totalLike": 0,
                                              "totalDislike": 0,
                                              "totalRating": 0,
                                              "totalView": 0,
                                              "videoSourceUrl": '',
                                              "videoTitle": item.title,
                                              "videoTags": '',
                                            });
                                      },
                                      child: Padding(
                                        padding: EdgeInsets.only(
                                          right: controller.listBroadcast
                                                      .where((i) =>
                                                          i.status == 'active')
                                                      .toList()
                                                      .last ==
                                                  item
                                              ? 0
                                              : 30.w,
                                        ),
                                        child: CustomVideoContainer(
                                          thumbnailUrl: item.thumbnailUrl ?? '',
                                          titleVideo: item.title,
                                          totalView: 0,
                                          uploadDate: item.createdAt != null
                                              ? timeRange(
                                                  controller.timeNow.value,
                                                  item.createdAt!)
                                              : '',
                                          duration: '00:00:00',
                                          tags: const [],
                                          uploaderName:
                                              item.createdUser?.pasisName ?? '',
                                          userName:
                                              item.createdUser?.username ?? '',
                                          photoUserUrl: '',
                                          isLive: true,
                                          width: Get.width * 1.04,
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ],
                              ),
                            ),
                          ),
                        ),
                        CustomSection(
                          sectionTitle: 'Lanjut Menonton',
                          sectionTitleButton: 'Lihat Semua',
                          onPressed: () => Get.to(() =>
                              const ExpandListVideoView(
                                  title: 'Lanjut Menonton')),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 40.w, right: 40.w),
                          child: Obx(
                            () {
                              final datas = controller.listContinueWatchVod;
                              final isEmpty = datas.isEmpty &&
                                  !controller.isLoadingContinueWatchVod.value;

                              return SizedBox(
                                height: 471.h,
                                child: isEmpty
                                    ? const Center(
                                        child: CustomDataEmpty(
                                            title:
                                                'Tidak ada data Lanjut Menonton.',
                                            assetName:
                                                'assets/icons/no_data_video.svg'),
                                      )
                                    : Skeletonizer(
                                        enabled: controller
                                            .isLoadingContinueWatchVod.value,
                                        child: ListView.builder(
                                          scrollDirection: Axis.horizontal,
                                          itemCount: datas.isNotEmpty
                                              ? datas.length
                                              : 5,
                                          itemBuilder: (ctx, idx) {
                                            final item = datas.isNotEmpty
                                                ? datas[idx]
                                                : null;
                                            // print('Data Lanjut: ${item.}');
                                            return Row(
                                              children: [
                                                GestureDetector(
                                                  onTap: () {
                                                    Get.to(VideoPlayerView(),
                                                        binding:
                                                            VideoPlayerBinding(),
                                                        arguments: {
                                                          "videoUrl": item
                                                              ?.vod?.videoUrl,
                                                          "videoId":
                                                              item?.vod?.id,
                                                          "totalLike": item
                                                              ?.vod?.totalLike,
                                                          "totalDislike": item
                                                              ?.vod
                                                              ?.totalDislike,
                                                          "totalRating": item
                                                              ?.vod
                                                              ?.totalRating,
                                                          "totalView": item
                                                              ?.vod?.totalView,
                                                          "videoSourceUrl": item
                                                              ?.vod
                                                              ?.videoSourceUrl,
                                                          "videoTitle":
                                                              item?.vod?.title,
                                                          "videoTags": item
                                                              ?.vod?.tagNames,
                                                          "lastWatchTime":
                                                              stringToDuration(
                                                                  item?.lastWatchedTime ??
                                                                      '00:00:00'),
                                                        });
                                                  },
                                                  child: CustomVideoFeed(
                                                    titleVideo:
                                                        item?.vod?.title,
                                                    thumbnailUrl:
                                                        item?.vod?.thumbnailUrl,
                                                    duration: formatDuration(
                                                        item?.vod?.duration),
                                                    totalView:
                                                        item?.vod?.totalView,
                                                    uploadDate: timeRange(
                                                        controller
                                                            .timeNow.value,
                                                        item?.vod?.uploadDate ??
                                                            controller
                                                                .timeNow.value),
                                                    tags: item?.vod?.tagNames,
                                                    uploaderName: item?.vod
                                                        ?.uploader?.pasisName,
                                                    lastDuration:
                                                        formatDuration(item
                                                            ?.vod?.duration),
                                                    lastWatch: (convertDurationToSeconds(
                                                                item?.lastWatchedTime ??
                                                                    '00:00:00') /
                                                            convertDurationToSeconds(item
                                                                    ?.vod
                                                                    ?.duration ??
                                                                '00:00:00'))
                                                        .clamp(0.0, 1.0),
                                                    onTapMore: () {
                                                      _showMoreDialog(
                                                          item?.id ?? '');
                                                    },
                                                  ),
                                                ),
                                                24.horizontalSpace,
                                              ],
                                            );
                                          },
                                        ),
                                      ),
                              );
                            },
                          ),
                        ),
                        32.verticalSpace,
                        CustomSection(
                          sectionTitle: 'Untuk Kamu',
                          sectionTitleButton: 'Lihat Semua',
                          onPressed: () {
                            Get.to(() => const ExpandListVideoView(
                                  title: 'Untuk Kamu',
                                ));
                          },
                        ),
                        32.verticalSpace,
                        SizedBox(
                          height: Get.height * 0.35,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 40.w),
                            child: Obx(
                              () {
                                final datas = controller.listRecommVod;

                                return Skeletonizer(
                                  enabled: controller.isLoadingRecommVod.value,
                                  child: GridView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: 3,
                                            childAspectRatio: 17 / 18,
                                            crossAxisSpacing: 24.w,
                                            mainAxisSpacing: 24.h),
                                    itemCount:
                                        datas.length > 3 ? 3 : datas.length,
                                    itemBuilder: (it, idx) {
                                      final item =
                                          datas.isNotEmpty ? datas[idx] : null;

                                      return GestureDetector(
                                        onTap: () {
                                          Get.to(VideoPlayerView(),
                                              binding: VideoPlayerBinding(),
                                              arguments: {
                                                "videoUrl": item?.videoUrl,
                                                "videoSourceUrl":
                                                    item?.videoSourceUrl,
                                                "totalLike": item?.totalLike,
                                                "totalDislike":
                                                    item?.totalDislike,
                                                "totalRating":
                                                    item?.totalRating,
                                                "totalView": item?.totalView,
                                                "videoId": item?.id,
                                                "videoTitle": item?.title,
                                                "videoTags": item?.tagNames,
                                              });
                                        },
                                        child: CustomVideoFeed(
                                          width: double.infinity,
                                          thumbnailUrl:
                                              item?.thumbnailUrl ?? '',
                                          duration:
                                              formatDuration(item?.duration),
                                          titleVideo: item?.title,
                                          totalView: item?.totalView,
                                          uploadDate: timeRange(
                                              controller.timeNow.value,
                                              item?.uploadDate ??
                                                  controller.timeNow.value),
                                          tags: item?.tagNames,
                                          uploaderName:
                                              item?.uploader?.username,
                                          onTapMore: () {
                                            _showMoreDialog(item?.id ?? '');
                                          },
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        // Positioned(
        //   bottom: 120.h,
        //   right: 40.w,
        //   child: MiniPlayer(
        //     videourl: controller.videoUrlPlay.value,
        //   ),
        // ),
      ],
    );
  }

  void _showMoreDialog(String vodId) {
    Get.dialog(
      CustomDialogMore(
        widgets: [
          const CustomItemDialogMore(
            assetName: 'assets/icons/time.svg',
            title: CustomTextWigdet(
              title: 'Tambah ke Tonton Nanti',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(color: blackColor),
          CustomItemDialogMore(
            assetName: 'assets/icons/add_to_playlist.svg',
            title: const CustomTextWigdet(
              title: 'Tambah atau Hapus dari Playlist',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
            onTap: () {
              Get.to(
                () => PlaylistCollectionView(
                  vodId: vodId,
                  needBackNavigate: true,
                ),
              );
            },
          ),
          Divider(color: blackColor),
          const CustomItemDialogMore(
            assetName: 'assets/icons/share.svg',
            title: CustomTextWigdet(
              title: 'Bagikan',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
