import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/views/my_video_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/views/my_video_potrait_view.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';

import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../controllers/my_video_controller.dart';

class MyVideoView extends GetView<MyVideoController> {
  const MyVideoView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();

    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        useActions: true,
        titleAppBar: 'Video Saya',
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifikasi',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {
                    Get.toNamed('/notification');
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                )
              ],
        body: orientation.isLandscape.value
            ? const MyVideoLandscapeView()
            : const MyVideoPotraitView(),
      ),
    );
  }
}
