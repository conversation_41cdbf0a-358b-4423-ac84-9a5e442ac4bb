import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_item_grid_widget.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_item_widget.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_dropdown.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/buttons/custom_switch_button.dart';
import '../../../../../widgets/components/custom_container.dart';
import '../../../../../widgets/custom_text_field_widget.dart';
import '../controllers/my_video_controller.dart';

class MyVideoPotraitView extends GetView<MyVideoController> {
  const MyVideoPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomContainer(
          bgColor: secondBlueColor.withOpacity(0.15),
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomFilledButtonWidget(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  radius: 12,
                  bgColor: secondBlueColor.withOpacity(0.1),
                  withIcon: true,
                  assetName: 'assets/icons/arrow_left.svg',
                  title: 'kembali',
                  fontSize: 22,
                  fontColor: whiteColor,
                  padding: EdgeInsets.only(left: 12.w, right: 24.w),
                  heightButton: 65,
                  widthIcon: 40,
                  heightIcon: 40,
                ),
                CustomFilledButtonWidget(
                  onPressed: () {
                    Get.toNamed('/upload-video');
                  },
                  radius: 12,
                  bgColor: blueColor,
                  withIcon: true,
                  assetName: 'assets/icons/icon_plus.svg',
                  title: 'Unggah Video',
                  fontSize: 22,
                  fontColor: whiteColor,
                  padding: EdgeInsets.only(left: 12.w, right: 24.w),
                  heightButton: 65,
                  widthIcon: 40,
                  heightIcon: 40,
                )
              ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 24.h),
          child: Row(
            children: [
              Obx(
                () {
                  return CustomDropdown(
                    textSelected: CustomTextWigdet(
                      title: controller.sortSelected.value.isEmpty
                          ? 'Upload date / newest'
                          : controller.sortSelected.value,
                      textColor: secondWhiteColor,
                      fontSize: 22,
                      fontWeight: FontWeight.w500,
                    ),
                    height: 65,
                    onSelected: (val) {
                      final item = val['subtitle']!.isEmpty
                          ? val['title']
                          : '${val['title']} / ${val['subtitle']}';
                      final item1 = val['subtitle']!.isEmpty
                          ? val['title']
                          : val['subtitle'];

                      controller.selectSort(item!, item1!);
                    },
                    itemsDropdown: const [
                      {"title": "Upload date", "subtitle": "newest"},
                      {"title": "Upload date", "subtitle": "oldest"},
                      {"title": "Recent activity", "subtitle": ""},
                      {"title": "A-Z", "subtitle": ""},
                      {"title": "Z-A", "subtitle": ""},
                    ],
                  );
                },
              ),
              16.horizontalSpace,
              Flexible(
                child: SizedBox(
                  height: 65.h,
                  child: CustomTextFieldWidget(
                    radius: 8,
                    controller: controller.searchController,
                    hintText: 'Cari video saya...',
                    colorField: secondBlueColor.withOpacity(0.1),
                    contentPadding: EdgeInsetsDirectional.all(10.r),
                    colorText: whiteColor,
                    colorTextHint: secondWhiteColor,
                  ),
                ),
              ),
              16.horizontalSpace,
              CustomFilledButtonWidget(
                onPressed: () {},
                withIcon: true,
                onlyIcon: true,
                bgColor: secondBlueColor.withOpacity(0.10),
                assetName: 'assets/icons/mic.svg',
                radius: 8,
                heightButton: 65,
                widthIcon: 48,
                heightIcon: 48,
              ),
              16.horizontalSpace,
              CustomContainer(
                radius: 8,
                height: 65,
                bgColor: secondBlueColor.withOpacity(0.1),
                widget: CustomSwitchButton(
                  children: [
                    Obx(
                      () => CustomFilledButtonWidget(
                        onPressed: () {
                          controller.selectViewMode('list');
                        },
                        withIcon: true,
                        onlyIcon: true,
                        bgColor: Colors.transparent,
                        assetName: 'assets/icons/list_view.svg',
                        radius: 8,
                        widthIcon: 48,
                        heightIcon: 48,
                        isSelected: controller.isGridView.value == false,
                        bgSelectedColor: secondWhiteColor.withValues(alpha: .5),
                      ),
                    ),
                    4.verticalSpace,
                    Obx(
                      () => CustomFilledButtonWidget(
                        onPressed: () {
                          controller.selectViewMode('grid');
                        },
                        withIcon: true,
                        onlyIcon: true,
                        bgColor: Colors.transparent,
                        assetName: 'assets/icons/grid_view.svg',
                        radius: 8,
                        widthIcon: 48,
                        heightIcon: 48,
                        isSelected: controller.isGridView.value,
                        bgSelectedColor: secondWhiteColor.withValues(alpha: .5),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(
                left: 40.w, right: 40.w, top: 24.h, bottom: 40.h),
            child: Obx(
              () {
                final loading = controller.isLoading.value;
                final datas = controller.listMyVod;
                final isGridView = controller.isGridView.value;

                if (loading) {
                  return const CustomLoadingWidget();
                }

                if (!loading && datas.isEmpty) {
                  return const Center(
                    child: CustomDataEmpty(
                        title: 'Tidak ada data',
                        assetName: 'assets/icons/no_data_video.svg'),
                  );
                }

                if (isGridView) {
                  return SizedBox(
                    height: Get.height * .35,
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GridView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: datas.length,
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3,
                                    childAspectRatio: 12 / 13,
                                    mainAxisSpacing: 24,
                                    crossAxisSpacing: 24),
                            itemBuilder: (ctx, idx) {
                              final item = datas[idx];
                              return MyVodItemGridWidget(
                                  item: item,
                                  index: idx,
                                  controller: controller);
                            },
                          ),
                          controller.isLoadingMoreData.value
                              ? const Center(
                                  child: CustomLoadingWidget(),
                                )
                              : controller.isLastPage.value
                                  ? const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16),
                                      child: CustomTextWigdet(
                                        title: 'Semua konten sudah dimuat.',
                                        fontSize: 24,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    )
                                  : CustomFilledButtonWidget(
                                      onPressed: () {
                                        controller.page++;
                                        controller.getMyVod(
                                          search: controller.searchVod.value,
                                          sortBy: controller
                                              .sortValueForApi.value
                                              .toLowerCase(),
                                        );
                                      },
                                      title: 'Tampilkan Lebih Banyak',
                                      bgColor: blueColor,
                                      fontColor: whiteColor,
                                      heightButton: 68,
                                      radius: 8,
                                    ),
                        ],
                      ),
                    ),
                  );
                }
                return ListView.builder(
                  itemCount: datas.length + 1,
                  itemBuilder: (ctx, idx) {
                    if (idx == datas.length) {
                      return Obx(
                        () {
                          if (controller.isLoadingMoreData.value) {
                            return const Center(
                              child: CustomLoadingWidget(),
                            );
                          }
                          return controller.isLastPage.value
                              ? const Center(
                                  child: CustomTextWigdet(
                                    title: 'Semua konten sudah dimuat.',
                                    fontSize: 24,
                                    fontWeight: FontWeight.w500,
                                  ),
                                )
                              : CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.page.value++;
                                    controller.getMyVod(
                                        search: controller.searchVod.value,
                                        sortBy:
                                            controller.sortValueForApi.value);
                                  },
                                  heightButton: 68,
                                  bgColor: blueColor,
                                  title: 'Tampilkan Lebih Banyak',
                                  fontColor: whiteColor,
                                  radius: 8,
                                );
                        },
                      );
                    }

                    final item = datas[idx];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: MyVodItemWidget(
                          item: item, index: idx, controller: controller),
                    );
                  },
                );
              },
            ),
          ),
        )
      ],
    );
  }
}
