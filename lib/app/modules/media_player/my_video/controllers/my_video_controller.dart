import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:ntp/ntp.dart';

class MyVideoController extends GetxController {
  TextEditingController searchController = TextEditingController();
  RxList<VodModel> listMyVod = <VodModel>[].obs;
  RxMap<int, bool> openDetailStatus = <int, bool>{}.obs;

  RxInt page = 1.obs;
  RxString searchVod = ''.obs;
  RxString sortSelected = ''.obs;
  RxString sortValueForApi = ''.obs;
  RxBool isLoading = false.obs;
  RxBool isLoadingMoreData = false.obs;
  RxBool isLoadingUpdate = false.obs;
  RxBool isLoadingDelete = false.obs;
  RxBool isLastPage = false.obs;
  RxBool isGridView = false.obs;

  final VodService vodService = VodService();
  final timeNow = DateTime.now().obs;
  bool _hasTyped = false;

  @override
  void onInit() {
    initTimeNow();

    getMyVod();

    searchController.addListener(() {
      searchVod.value = searchController.text;
    });

    debounce(
      searchVod,
      (query) {
        if (!_hasTyped) {
          _hasTyped = true;
          return;
        }

        if (query.isNotEmpty) {
          listMyVod.clear();
          getMyVod(search: query);
        } else {
          getMyVod();
        }
      },
      time: const Duration(seconds: 1),
    );
    super.onInit();
  }

  // ! Init TimeNow
  void initTimeNow() async {
    timeNow.value = await NTP.now();
  }

  // * Select Sort
  void selectSort(String selected, String selectForApi) {
    sortValueForApi.value = selectForApi;
    String trim = selected.trim().split(' ').first.replaceAll('-', '');

    page.value = 1;
    isLastPage.value = false;
    sortSelected.value = selected;
    sortValueForApi.value = trim;

    getMyVod(
        search: searchVod.value, sortBy: sortValueForApi.value.toLowerCase());
  }

  // * Select View Mode
  void selectViewMode(String? mode) {
    if (mode == 'list') {
      isGridView.value = false;
      return;
    }
    isGridView.value = true;
  }

  // * Open Detail Reject
  void openContainerDetail(int idx) {
    final current = openDetailStatus[idx] ?? false;

    if (current) {
      openDetailStatus[idx] = false;
    } else {
      openDetailStatus.updateAll((key, value) => false);

      openDetailStatus[idx] = !(openDetailStatus[idx] ?? false);
    }
  }

  // * Get My VoD
  Future<void> getMyVod(
      {String? search = '', String? sortBy = 'newest'}) async {
    final currentPage = page.value;

    if (currentPage == 1) {
      listMyVod.clear();
      isLoading.value = true;
    } else {
      isLoadingMoreData.value = true;
    }

    final result = await vodService.getMyVod(
        search: search, sortBy: sortBy, page: currentPage);
    List<VodModel> newData = result.resultValue ?? [];

    if (currentPage == 1) {
      listMyVod.value = newData;
    } else {
      if (newData.isNotEmpty) {
        listMyVod.addAll(newData);
      } else {
        isLastPage.value = true;
        page.value = currentPage - 1;
      }
    }

    isLoading.value = false;
    isLoadingMoreData.value = false;
  }

  // * Update Privacy and Comment
  Future<void> updatePrivacyAndComment(
      String idVod, String privacy, bool isCommentEnabled) async {
    isLoadingUpdate.value = true;
    String message = '';
    String status = '';

    try {
      final result = await vodService.updatePrivacyAndComment(
        idVod: idVod,
        privacy: privacy,
        isCommentEnabled: isCommentEnabled,
      );

      if (result.isSuccess) {
        LogService.log.i('Update successful');
        message = result.succesMessage!;
        status = 'Success';
      } else {
        message = result.errorMessage!;
        status = 'Failed';
        LogService.log
            .w('Update failed: ${result.errorMessage ?? "No message"}');
      }
    } catch (_) {
      LogService.log.e('Exception during update');
    } finally {
      isLoadingUpdate.value = false;
      Get.back();

      Future.delayed(const Duration(milliseconds: 100), () async {
        await getMyVod();
      });
      SnackbarUtil.showOnce(title: status, message: message);
    }
  }

  // * Delete My Video By ID
  Future<void> deleteMyVideoByID(String idVod) async {
    isLoadingDelete.value = true;

    final result = await vodService.deleteMyVideoByID(idVod: idVod);

    if (result.isSuccess) {
      Get.back();

      Future.delayed(const Duration(milliseconds: 100), () async {
        await getMyVod();
      });
      SnackbarUtil.showOnce(
          title: 'Sukses', message: result.succesMessage ?? '');
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
    isLoadingDelete.value = false;
  }
}
