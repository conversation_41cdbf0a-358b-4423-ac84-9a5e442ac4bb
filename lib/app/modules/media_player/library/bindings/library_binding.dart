import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';

import '../controllers/library_controller.dart';

class LibraryBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => LibraryController());
    // Get.lazyPut(() => PlaylistController());

    if (!Get.isRegistered<MyVideoController>()) {
      Get.put(MyVideoController());
    }
  }
}
