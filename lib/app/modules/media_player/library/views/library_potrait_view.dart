import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/bookmarks/views/bookmarks_view.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/clip_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playback_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_header.dart';
import 'package:mides_skadik/widgets/components/custom_section.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/library/custom_playlist_grid.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';

class LibraryPotraitView extends GetView {
  const LibraryPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    final clipController = Get.put(ClipController());
    final playbackController = Get.put(PlaybackController());
    final userInfoController = Get.put(DashboardController());

    return Column(
      children: [
        const CustomDivider(),
        Obx(
          () {
            final data = userInfoController.userProfile.value;
            return CustomHeader(
              widthUserInfo: 80,
              heightUserInfo: 80,
              userName: data?.name,
              userImageUrl: data?.imageProfile,
              onMyVideoPress: () {
                Get.toNamed('/my-video');
              },
              onUploadPress: () {
                Get.toNamed('/upload-video');
              },
            );
          },
        ),
        const CustomDivider(),
        Expanded(
          child: RefreshIndicator(
            color: baseBlueColor,
            onRefresh: () async {
              clipController.loadClips();
              playbackController.loadPlaybacks();
            },
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 24.h, horizontal: 40.w),
                    child: CustomContainer(
                      bgColor: greyColor.withOpacity(0.1),
                      radius: 12,
                      widget: ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 0.12, sigmaY: 0.12),
                          child: Column(
                            children: [
                              CustomContainer(
                                bgColor: secondBlueColor.withOpacity(0.2),
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(12.r),
                                  topLeft: Radius.circular(12.r),
                                ),
                                widget: CustomSection(
                                  sectionTitle: 'Riwayat',
                                  sectionTitleButton: 'Lihat Semua',
                                  onPressed: () {
                                    Get.toNamed('/history');
                                  },
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 24.h),
                                child: SizedBox(
                                  height: 500.h,
                                  child: Obx(
                                    () {
                                      if (playbackController
                                          .playbacks.isEmpty) {
                                        return const Center(
                                            child: CustomDataEmpty(
                                                title:
                                                    'Tidak ada data riwayat.',
                                                assetName:
                                                    'assets/icons/no_data_video.svg'));
                                      }

                                      if (playbackController.isLoading.value) {
                                        return const Center(
                                          child: CustomLoadingWidget(),
                                        );
                                      }

                                      return ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        itemCount:
                                            playbackController.playbacks.length,
                                        itemBuilder: (it, idx) {
                                          var row =
                                              playbackController.playbacks[idx];
                                          return Row(
                                            children: [
                                              24.horizontalSpace,
                                              GestureDetector(
                                                onTap: () {
                                                  Get.to(VideoPlayerView(),
                                                      binding:
                                                          VideoPlayerBinding(),
                                                      arguments: {
                                                        "videoUrl":
                                                            row.vod?.videoUrl,
                                                        "videoId": row.vod?.id,
                                                        "totalLike":
                                                            row.vod?.totalLike,
                                                        "totalDislike": row
                                                            .vod?.totalDislike,
                                                        "totalRating": row
                                                            .vod?.totalRating,
                                                        "totalView":
                                                            row.vod?.totalView,
                                                        "videoSourceUrl": row
                                                            .vod
                                                            ?.videoSourceUrl,
                                                        "videoTitle":
                                                            row.vod?.title,
                                                        "videoTags":
                                                            row.vod?.tagNames,
                                                        "lastWatchTime":
                                                            stringToDuration(row
                                                                .lastWatchedTime!),
                                                      });
                                                },
                                                child: CustomVideoFeed(
                                                  thumbnailUrl:
                                                      row.vod?.thumbnailUrl ??
                                                          '',
                                                  titleVideo: row.vod?.title,
                                                  userName: row.vod?.uploader
                                                          ?.username ??
                                                      "-",
                                                  duration: formatDuration(
                                                      row.vod?.duration),
                                                  tags: (row.vod?.tag ?? [])
                                                      .map((e) => e.name ?? "")
                                                      .toList(),
                                                  photoUserUrl: row.vod
                                                      ?.uploader?.imageProfile,
                                                  totalView: row.vod?.totalView,
                                                  uploadDate: timeRange(
                                                      playbackController
                                                          .timeNow.value,
                                                      row.vod?.uploadDate ??
                                                          playbackController
                                                              .timeNow.value),
                                                  uploaderName: row
                                                          .vod
                                                          ?.uploader
                                                          ?.pasisName ??
                                                      "-",
                                                  lastDuration: formatDuration(
                                                      row.vod?.duration),
                                                  lastWatch: (convertDurationToSeconds(row
                                                              .lastWatchedTime!) /
                                                          convertDurationToSeconds(
                                                              row.vod?.duration ??
                                                                  '00:00:00'))
                                                      .clamp(0.0, 1.0),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  40.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 40.w),
                    child: CustomContainer(
                      bgColor: greyColor.withOpacity(0.1),
                      radius: 12,
                      widget: ClipRRect(
                        borderRadius: BorderRadius.circular(12.r),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 0.12, sigmaY: 0.12),
                          child: Column(
                            children: [
                              CustomContainer(
                                bgColor: secondBlueColor.withOpacity(0.2),
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(12.r),
                                  topLeft: Radius.circular(12.r),
                                ),
                                widget: CustomSection(
                                  sectionTitle: 'Klip',
                                  sectionTitleButton: 'Lihat Semua',
                                  onPressed: () {
                                    Get.to(() => const BookmarksView());
                                  },
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(vertical: 24.h),
                                child: SizedBox(
                                  height: 500.h,
                                  child: Obx(
                                    () {
                                      if (clipController.clips.isEmpty) {
                                        return const Center(
                                            child: CustomDataEmpty(
                                                title: 'Tidak ada data klip.',
                                                assetName:
                                                    'assets/icons/no_data_video.svg'));
                                      }

                                      if (clipController.isLoading.value) {
                                        return const Center(
                                          child: CustomLoadingWidget(),
                                        );
                                      }

                                      return ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        itemCount: clipController.clips.length,
                                        itemBuilder: (it, idx) {
                                          var row = clipController.clips[idx];
                                          return Row(
                                            children: [
                                              24.horizontalSpace,
                                              CustomVideoFeed(
                                                titleVideo: row.name,
                                                thumbnailUrl:
                                                    row.sourceVod?.thumbnailUrl,
                                                userName: row.sourceVod
                                                    ?.uploader?.username,
                                                duration: formatDuration(
                                                    row.sourceVod?.duration),
                                                tags: (row.sourceVod?.tag ?? [])
                                                    .map((e) => e.name ?? "")
                                                    .toList(),
                                                photoUserUrl: row.sourceVod
                                                    ?.uploader?.imageProfile,
                                                totalView:
                                                    row.sourceVod?.totalView,
                                                uploadDate:
                                                    row.sourceVod?.uploadDate !=
                                                            null
                                                        ? timeRange(
                                                            DateTime.now(),
                                                            DateTime.parse(row
                                                                .sourceVod!
                                                                .uploadDate!))
                                                        : "-",
                                                uploaderName: row.sourceVod
                                                        ?.uploader?.pasisName ??
                                                    "-",
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  32.verticalSpace,
                  CustomSection(
                    sectionTitle: 'Playlists',
                    sectionTitleButton: 'View All',
                    onPressed: () {
                      Get.toNamed('/playlists');
                    },
                  ),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 24.h, horizontal: 40.w),
                    child: const CustomPlaylistGrid(
                      isScrollable: false,
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
