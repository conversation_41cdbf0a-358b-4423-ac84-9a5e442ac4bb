import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/library/views/library_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/library/views/library_potrait_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';

class LibraryView extends StatefulWidget {
  const LibraryView({super.key});

  @override
  State<LibraryView> createState() => _LibraryViewState();
}

class _LibraryViewState extends State<LibraryView> {
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        titleAppBar: 'Library',
        fontWeight: FontWeight.w200,
        withIcon: true,
        customIconPath: "assets/icons/icon_library.svg",
        isHamburgerIcon: true,
        fontSizeAppBar: 32,
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifications',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                // Padding(
                //   padding: EdgeInsets.only(right: 20.w),
                //   child: CustomFilledButtonWidget(
                //     onPressed: () {},
                //     withIcon: true,
                //     assetName: 'assets/icons/settings.svg',
                //     title: 'Setting',
                //     fontSize: 22,
                //     heightIcon: 32,
                //     heightButton: 64,
                //     bgColor: secondBlueColor.withOpacity(0.25),
                //     fontColor: whiteColor,
                //     radius: 10,
                //     padding: EdgeInsets.symmetric(horizontal: 20.w),
                //   ),
                // ),
              ]
            : [
                IconButton(
                  onPressed: () {
                    Get.toNamed('/notification');
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                // IconButton(
                //   onPressed: () {},
                //   icon: SvgPicture.asset(
                //     'assets/icons/settings.svg',
                //     width: 48.w,
                //     height: 48.h,
                //   ),
                // ),
              ],
        body: orientation.isLandscape.value
            ? LibraryLandscapeView(landscape: orientation.isLandscape.value)
            : const LibraryPotraitView(),
      ),
    );
  }
}
