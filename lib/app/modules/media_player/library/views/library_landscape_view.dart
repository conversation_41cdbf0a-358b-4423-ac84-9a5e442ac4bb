import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';
import 'package:mides_skadik/app/modules/media_player/bookmarks/views/bookmarks_view.dart';
import 'package:mides_skadik/app/modules/media_player/history/views/history_view.dart';
import 'package:mides_skadik/app/modules/media_player/library/bindings/library_binding.dart';

import 'package:mides_skadik/app/modules/media_player/my_video/views/my_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/clip_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playback_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlists/views/playlists_view.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/upload_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_header.dart';
import 'package:mides_skadik/widgets/components/custom_section.dart';
import 'package:mides_skadik/widgets/media_player/library/custom_playlist_grid.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';

class LibraryLandscapeView extends GetView {
  final bool landscape;
  const LibraryLandscapeView({super.key, required this.landscape});

  @override
  Widget build(BuildContext context) {
    final clipController = Get.find<ClipController>();
    final playbackController = Get.find<PlaybackController>();
    final loginController = Get.find<LoginController>();
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 32.w),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: CustomContainer(
              widget: Column(
                children: [
                  CustomSection(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 24),
                    sectionTitle: 'Playlists',
                    sectionTitleButton: 'View All',
                    onPressed: () {
                      Get.to(() => const PlaylistsView());
                    },
                  ),
                  const Expanded(
                    child: CustomPlaylistGrid(),
                  ),
                ],
              ),
            ),
          ),
          24.horizontalSpace,
          Expanded(
            flex: 2,
            child: Column(
              children: [
                const CustomDivider(),
                CustomHeader(
                  isLandscape: landscape,
                  titleSize: 32,
                  subTitleSize: 24,
                  heightUserInfo: 100,
                  widthUserInfo: 90,
                  maxWidth: 300,
                  userName: loginController.userProfile.value?.name,
                  userImageUrl: loginController.userProfile.value?.imageProfile,
                  onMyVideoPress: () {
                    Get.to(() => const MyVideoView(),
                        binding: LibraryBinding());
                  },
                  onUploadPress: () {
                    Get.to(() => const UploadVideoView(),
                        binding: UploadVideoBinding());
                  },
                ),
                const CustomDivider(),
                16.verticalSpace,
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        CustomContainer(
                          bgColor: greyColor.withOpacity(0.1),
                          radius: 12,
                          widget: ClipRRect(
                            borderRadius: BorderRadius.circular(12.r),
                            child: BackdropFilter(
                              filter:
                                  ImageFilter.blur(sigmaX: 0.12, sigmaY: 0.12),
                              child: Column(
                                children: [
                                  CustomContainer(
                                    width: double.infinity,
                                    height: 80,
                                    bgColor: secondBlueColor.withOpacity(0.2),
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(12.r),
                                      topLeft: Radius.circular(12.r),
                                    ),
                                    widget: CustomSection(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 2.h, horizontal: 20.w),
                                      sectionTitle: 'Riwayat',
                                      sectionTitleButton: 'Lihat Semua',
                                      onPressed: () {
                                        Get.to(() => const HistoryView());
                                      },
                                    ),
                                  ),
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 24.h),
                                    child: SizedBox(
                                      height: 500.h,
                                      child: playbackController
                                              .playbacks.isEmpty
                                          ? Center(
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.history_outlined,
                                                    size: 64.sp,
                                                    color: Colors.grey[400],
                                                  ),
                                                  16.verticalSpace,
                                                  Text(
                                                    'Riwayat masih kosong',
                                                    style: TextStyle(
                                                      color: Colors.grey[400],
                                                      fontSize: 24.sp,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                    ),
                                                  ),
                                                  8.verticalSpace,
                                                  Text(
                                                    'Video yang sudah ditonton akan muncul di sini',
                                                    style: TextStyle(
                                                      color: Colors.grey[600],
                                                      fontSize: 18.sp,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            )
                                          : ListView.builder(
                                              scrollDirection: Axis.horizontal,
                                              itemCount: playbackController
                                                  .playbacks.length,
                                              itemBuilder: (it, idx) {
                                                var row = playbackController
                                                    .playbacks[idx];
                                                return Row(
                                                  children: [
                                                    24.horizontalSpace,
                                                    CustomVideoFeed(
                                                      thumbnailUrl: row.vod
                                                              ?.thumbnailUrl ??
                                                          '',
                                                      titleVideo:
                                                          row.vod?.title,
                                                      userName: row
                                                              .vod
                                                              ?.uploader
                                                              ?.username ??
                                                          "-",
                                                      duration:
                                                          row.vod?.duration,
                                                      tags: (row.vod?.tag ?? [])
                                                          .map((e) =>
                                                              e.name ?? "")
                                                          .toList(),
                                                      photoUserUrl: row
                                                          .vod
                                                          ?.uploader
                                                          ?.imageProfile,
                                                      totalView:
                                                          row.vod?.totalView,
                                                      uploadDate: timeRange(
                                                          playbackController
                                                              .timeNow.value,
                                                          row.vod?.uploadDate ??
                                                              playbackController
                                                                  .timeNow
                                                                  .value),
                                                      uploaderName: row
                                                              .vod
                                                              ?.uploader
                                                              ?.pasisName ??
                                                          "-",
                                                      lastDuration:
                                                          formatDuration(row
                                                              .lastWatchedTime),
                                                      lastWatch: (convertDurationToSeconds(
                                                                  row.lastWatchedTime ??
                                                                      '00:00:00') /
                                                              convertDurationToSeconds(row
                                                                      .vod
                                                                      ?.duration ??
                                                                  '00:00:00'))
                                                          .clamp(0.0, 1.0),
                                                    ),
                                                  ],
                                                );
                                              },
                                            ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        32.verticalSpace,
                        CustomContainer(
                          bgColor: greyColor.withOpacity(0.1),
                          radius: 12,
                          widget: ClipRRect(
                            borderRadius: BorderRadius.circular(12.r),
                            child: BackdropFilter(
                              filter:
                                  ImageFilter.blur(sigmaX: 0.12, sigmaY: 0.12),
                              child: Column(
                                children: [
                                  CustomContainer(
                                    width: double.infinity,
                                    height: 80,
                                    bgColor: secondBlueColor.withOpacity(0.2),
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(12.r),
                                      topLeft: Radius.circular(12.r),
                                    ),
                                    widget: CustomSection(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 2.h, horizontal: 20.w),
                                      sectionTitle: 'Klip',
                                      sectionTitleButton: 'Lihat Semua',
                                      onPressed: () {
                                        Get.to(() => const BookmarksView());
                                      },
                                    ),
                                  ),
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(vertical: 24.h),
                                    child: SizedBox(
                                      height: 500.h,
                                      child: Obx(
                                        () => clipController.clips.isEmpty
                                            ? Center(
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .bookmark_border_rounded,
                                                      size: 64.sp,
                                                      color: Colors.grey[400],
                                                    ),
                                                    16.verticalSpace,
                                                    Text(
                                                      'Belum ada klip yang ditandai',
                                                      style: TextStyle(
                                                        color: Colors.grey[400],
                                                        fontSize: 24.sp,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                    8.verticalSpace,
                                                    Text(
                                                      'Tandai klip favorit Anda untuk menontonnya nanti',
                                                      style: TextStyle(
                                                        color: Colors.grey[600],
                                                        fontSize: 18.sp,
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : ListView.builder(
                                                scrollDirection:
                                                    Axis.horizontal,
                                                itemCount:
                                                    clipController.clips.length,
                                                itemBuilder: (it, idx) {
                                                  var row =
                                                      clipController.clips[idx];
                                                  return Row(
                                                    children: [
                                                      24.horizontalSpace,
                                                      CustomVideoFeed(
                                                        titleVideo: row.name,
                                                        thumbnailUrl: row
                                                            .sourceVod
                                                            ?.thumbnailUrl,
                                                        userName: row
                                                            .sourceVod
                                                            ?.uploader
                                                            ?.username,
                                                        duration: row.sourceVod
                                                            ?.duration,
                                                        tags: (row.sourceVod
                                                                    ?.tag ??
                                                                [])
                                                            .map((e) =>
                                                                e.name ?? "")
                                                            .toList(),
                                                        photoUserUrl: row
                                                            .sourceVod
                                                            ?.uploader
                                                            ?.imageProfile,
                                                        totalView: row.sourceVod
                                                            ?.totalView,
                                                        uploadDate: row
                                                                    .sourceVod
                                                                    ?.uploadDate !=
                                                                null
                                                            ? timeRange(
                                                                DateTime.now(),
                                                                DateTime.parse(row
                                                                    .sourceVod!
                                                                    .uploadDate!))
                                                            : "-",
                                                        uploaderName: row
                                                                .sourceVod
                                                                ?.uploader
                                                                ?.pasisName ??
                                                            "-",
                                                      ),
                                                    ],
                                                  );
                                                },
                                              ),
                                      ),
                                    ),
                                  ),
                                  100.verticalSpace,
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
