import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_potrait_view.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import '../controllers/upload_video_controller.dart';

class UploadVideoView extends GetView<UploadVideoController> {
  final String? idVod;
  final String? title;
  final String? titleVideo;
  final String? descriptionVideo;
  final String? thumbnailUrl;
  final String? privasi;
  final bool? isCommentEnable;
  final bool? isEdit;
  final List<String>? categories;
  const UploadVideoView({
    super.key,
    this.idVod,
    this.title,
    this.titleVideo,
    this.descriptionVideo,
    this.thumbnailUrl,
    this.privasi,
    this.isCommentEnable,
    this.categories,
    this.isEdit,
  });
  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;

    return WillPopScope(
      onWillPop: () async => false,
      child: CustomScaffold(
        useAppBar: true,
        withIcon: true,
        iconWithBackground: true,
        titleAppBar: title ?? 'New Video',
        heightAppBar: 150.h,
        body: orientation == Orientation.landscape
            ? UploadVideoLandscapeView(
                idVod: idVod ?? '',
                title: titleVideo,
                descriptionVideo: descriptionVideo,
                thumbnailUrl: thumbnailUrl,
                privasi: privasi,
                isCommentEnable: isCommentEnable,
                isEdit: isEdit,
                categories: categories,
              )
            : UploadVideoPotraitView(
                idVod: idVod ?? '',
                title: titleVideo,
                descriptionVideo: descriptionVideo,
                thumbnailUrl: thumbnailUrl,
                privasi: privasi,
                isCommentEnable: isCommentEnable,
                isEdit: isEdit,
                categories: categories,
              ),
      ),
    );
  }
}
