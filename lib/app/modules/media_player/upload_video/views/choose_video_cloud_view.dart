import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/choose_video_cloud_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_filter.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ChooseVideoCloudView extends GetView<ChooseVideoCloudController> {
  const ChooseVideoCloudView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      withIcon: true,
      titleAppBar: 'Cloud',
      useAppBar: true,
      iconWithBackground: true,
      heightAppBar: 150.h,
      body: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Container(
          padding: const EdgeInsets.all(16),
          width: Get.width,
          child: DataTableTheme(
            data: DataTableThemeData(
              dividerThickness: 0,
              columnSpacing: 24,
              dataRowColor: MaterialStateProperty.all(Colors.transparent),
              headingRowColor: MaterialStateProperty.all(Colors.transparent),
            ),
            child: Obx(
              () => DataTable(
                showCheckboxColumn: false,
                columns: [
                  DataColumn(
                      label: Center(
                    child: CustomTextWigdet(
                      title: 'Title',
                      textColor: whiteColor,
                      fontWeight: FontWeight.bold,
                      textAlign: TextAlign.center,
                    ),
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'Duration',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'Date Created',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'File Size',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'File Format',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'Resolution',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                  DataColumn(
                      label: CustomTextWigdet(
                    title: 'Frame Rate',
                    textColor: whiteColor,
                    fontWeight: FontWeight.bold,
                    textAlign: TextAlign.center,
                  )),
                ],
                rows: List.generate(controller.videoData.length, (index) {
                  final video = controller.videoData[index];
                  final isSelected = controller.selectedIndex.value == index;

                  return DataRow(
                    selected: isSelected,
                    onSelectChanged: (_) {
                      controller.selectedIndex.value = index;
                    },
                    color: MaterialStateProperty.resolveWith<Color?>(
                      (Set<MaterialState> states) {
                        if (isSelected) {
                          return Colors.blue.withOpacity(0.2); // <-- your selected row color
                        }
                        return Colors.transparent;
                      },
                    ),
                    cells: [
                      DataCell(CustomTextWigdet(title: video['title']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['duration']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['date']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['size']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['format']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['resolution']!, textColor: whiteColor)),
                      DataCell(CustomTextWigdet(title: video['frameRate']!, textColor: whiteColor)),
                    ],
                  );
                }),
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: CustomContainer(
        padding: EdgeInsets.fromLTRB(16, 16, 16, Get.bottomBarHeight),
        widget: Obx(() {
          return Opacity(
            opacity: controller.selectedIndex >= 0 ? 1.0 : 0.2,
            child: CustomFilledButtonWidget(
              title: "Upload",
              bgColor: blueColor,
              fontColor: whiteColor,
              radius: 8,
              onPressed: controller.selectedIndex.value >= 0 ? controller.uploadVideo : null,
              heightButton: 130.h,
            ),
          );
        }),
      ),
    );
  }
}
