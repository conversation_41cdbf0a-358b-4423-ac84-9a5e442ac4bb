import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_category.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_privacy.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_thumbnail.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_video_preview.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_button_text.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/buttons/custom_switch_button.dart';
import '../../../../../widgets/components/custom_container.dart';
import '../../../../../widgets/custom_text_field_widget.dart';
import '../../../../../widgets/custom_text_wigdet.dart';

class UploadVideoPotraitView extends StatefulWidget {
  final String idVod;
  final String? title;
  final String? descriptionVideo;
  final String? thumbnailUrl;
  final List<String>? categories;
  final String? privasi;
  final bool? isCommentEnable;
  final bool? isEdit;

  const UploadVideoPotraitView(
      {super.key,
      required this.idVod,
      this.title,
      this.descriptionVideo,
      this.thumbnailUrl,
      this.categories,
      this.privasi,
      this.isCommentEnable,
      this.isEdit});

  @override
  State<UploadVideoPotraitView> createState() => _UploadVideoPotraitViewState();
}

class _UploadVideoPotraitViewState extends State<UploadVideoPotraitView> {
  final UploadVideoController controller = Get.put(UploadVideoController());

  @override
  void initState() {
    super.initState();

    if (widget.isEdit == true) {
      controller.initEditData(
        title: widget.title ?? '',
        description: widget.descriptionVideo ?? '',
        thumbnailUrl: widget.thumbnailUrl ?? '',
        isCommentEnabled: widget.isCommentEnable ?? false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 32.h),
            child: Column(
              children: [
                //
                CustomUploadVideoPreviewWidget(),
                32.verticalSpace,
                Row(
                  children: [
                    Expanded(
                      child: CustomFilledButtonWidget(
                        onPressed: controller.chooseLocalFile,
                        title: 'Select From Local',
                        radius: 12,
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                        withIcon: true,
                        iconPosition: IconPosition.right,
                        assetName: 'assets/icons/upload.svg',
                        heightButton: 96,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: blueColor,
                        fontColor: whiteColor,
                      ),
                    ),
                    24.horizontalSpace,
                    Expanded(
                      child: CustomFilledButtonWidget(
                        onPressed: controller.chooseCloudFile,
                        title: 'Select From Cloud',
                        radius: 12,
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                        withIcon: true,
                        iconPosition: IconPosition.right,
                        assetName: 'assets/icons/cloud_upload.svg',
                        heightButton: 96,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: whiteColor,
                        fontColor: baseBlueColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          CustomContainer(
            width: double.infinity,
            // height: 1250,
            bgColor: secondBlueColor.withOpacity(0.04),
            widget: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    CustomTextFieldWidget(
                      colorText: whiteColor,
                      hintText: 'Judul video',
                      colorTextHint: secondWhiteColor.withOpacity(0.5),
                      colorField: secondBlueColor.withOpacity(0.1),
                      radius: 10,
                      controller: controller.titleVideo,
                    ),
                    32.verticalSpace,
                    CustomContainer(
                      bgColor: secondBlueColor.withOpacity(0.1),
                      radius: 12,
                      widget: Obx(
                        () => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextFieldWidget(
                              colorText: whiteColor,
                              hintText: 'Deskripsi video...',
                              colorTextHint: secondWhiteColor.withOpacity(0.5),
                              colorField: secondBlueColor.withOpacity(0),
                              radius: 10,
                              maxLength: controller.maxLength,
                              maxLines: 10,
                              controller: controller.descriptionVideo,
                              onChanged: (value) {
                                controller.onChanged(value);
                              },
                            ),
                            Padding(
                              padding:
                                  EdgeInsets.only(left: 32.w, bottom: 32.h),
                              child: CustomTextWigdet(
                                title:
                                    '${controller.inputText.value.length}/${controller.maxLength}',
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor.withOpacity(0.5),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    32.verticalSpace,
                    Row(
                      children: [
                        Flexible(
                          child: CustomContainer(
                            height: context.isPortrait ? 550 : 560,
                            radius: 12,
                            bgColor: secondBlueColor.withOpacity(0.15),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 32.w, vertical: 32.h),
                              child: Column(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  CustomUploadThumbnailWidget(
                                    thumbnailUrl: widget.thumbnailUrl,
                                  ),
                                  CustomFilledButtonWidget(
                                    onPressed: controller.chooseThumbnail,
                                    title: 'Pilih File',
                                    radius: 12,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w400,
                                    withIcon: true,
                                    assetName: 'assets/icons/upload.svg',
                                    heightButton: 64,
                                    widthIcon: 40,
                                    heightIcon: 40,
                                    bgColor: blueColor,
                                    fontColor: whiteColor,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        32.horizontalSpace,
                        Flexible(
                          child: CustomContainer(
                            radius: 12,
                            height: context.isPortrait ? 550 : 560,
                            bgColor: secondBlueColor.withOpacity(0.15),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 32.w, vertical: 32.h),
                              child: Column(
                                children: [
                                  CustomUploadCategoryWidget(
                                    selectedTags: widget.categories,
                                  ),
                                  32.verticalSpace,
                                  CustomUploadPrivacyWidget(
                                    privasi: widget.privasi,
                                  ),
                                  16.verticalSpace,
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const CustomTextWigdet(
                                        title: 'Komentar',
                                        fontSize: 24,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      CustomContainer(
                                        radius: 8,
                                        bgColor:
                                            secondBlueColor.withOpacity(0.1),
                                        height: 65,
                                        widget: Obx(
                                          () => CustomSwitchButton(
                                            children: [
                                              CustomButtonText(
                                                title: 'Aktif',
                                                fontSize: 18,
                                                isSelected:
                                                    controller.comment.value ==
                                                        "Aktif",
                                                onTap: () {
                                                  controller.comment.value =
                                                      'Aktif';
                                                },
                                              ),
                                              4.horizontalSpace,
                                              CustomButtonText(
                                                title: 'Tidak Aktif',
                                                fontSize: 18,
                                                isSelected:
                                                    controller.comment.value ==
                                                        "Tidak Aktif",
                                                onTap: () {
                                                  controller.comment.value =
                                                      "Tidak Aktif";
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    32.verticalSpace,
                    Obx(
                      () => CustomFilledButtonWidget(
                        onPressed: () {
                          widget.isEdit ?? false
                              ? controller.editVideo(widget.idVod)
                              : controller.save();
                        },
                        isLoading: controller.isSaving.value,
                        title: widget.isEdit ?? false ? 'Save' : 'Unggah',
                        radius: 12,
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                        heightButton: 96,
                        widthButton: double.infinity,
                        bgColor:
                            controller.isSaving.value ? greyColor : blueColor,
                        fontColor: whiteColor,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
