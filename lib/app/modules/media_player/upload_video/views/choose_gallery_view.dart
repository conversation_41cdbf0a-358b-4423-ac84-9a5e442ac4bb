import 'dart:io';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:mides_skadik/app/data/utils/video_util.dart';
import 'package:mides_skadik/app/modules/choose_screen/controllers/choose_screen_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/choose_gallery_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/widgets/components/custom_shimmer.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ChooseGalleryView extends GetView<ChooseGalleryController> {
  const ChooseGalleryView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      titleAppBar: "Gallery",
      withIcon: true,
      iconWithBackground: true,
      heightAppBar: 150.h,
      body: Obx(
        () => GridView(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 6,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          children: [
            CustomContainer(
              bgColor: greyColor,
              padding: EdgeInsets.symmetric(horizontal: 0, vertical: 0),
              radius: 0,
              widget: Stack(
                children: [
                  Center(
                    child: SvgPicture.asset(
                      "assets/icons/camera.svg",
                      fit: BoxFit.cover,
                      width: 80,
                      height: 80,
                    ),
                  ),
                ],
              ),
            ),
            ...controller.files.map((e) {
              return GestureDetector(
                onTap: () {
                  Get.back(result: e.path);
                },
                child: CustomContainer(
                  bgColor: greyColor,
                  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                  radius: 0,
                  widget: Stack(
                    children: [
                      if (e.path.endsWith(".mp4"))
                        FutureBuilder(
                            future: VideoThumbnail.thumbnailData(
                              video: e.path,
                              imageFormat: ImageFormat.WEBP,
                              maxWidth: 512,
                              quality: 75,
                            ),
                            builder: (context, snapshot) {
                              if (snapshot.data == null) {
                                return const CustomShimmer(
                                  width: double.infinity,
                                  height: double.infinity,
                                );
                              }
                              return Image.memory(
                                snapshot.data!,
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: double.infinity,
                              );
                            })
                      else
                        Image.file(
                          File(e.path),
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                        ),
                      FutureBuilder(
                          future: VideoUtil.getVideoDuration(File(e.path)),
                          builder: (context, snapshot) {
                            if (snapshot.data == null) return const CustomContainer();
                            return Positioned(
                              bottom: 8,
                              right: 8,
                              child: CustomContainer(
                                bgColor: blackColor.withOpacity(0.4),
                                radius: 4,
                                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                widget: CustomTextWigdet(
                                  title: snapshot.data ?? "",
                                  fontSize: 14,
                                ),
                              ),
                            );
                          }),
                    ],
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
