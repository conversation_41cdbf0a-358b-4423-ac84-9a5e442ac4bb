import 'dart:io';
import 'dart:typed_data';

import 'package:file_manager/file_manager.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

class ChooseGalleryController extends GetxController {
  final FileManagerController fileManager = FileManagerController();

  RxList<FileSystemEntity> files = <FileSystemEntity>[].obs;

  RxList<String> selectedExtension = <String>[].obs;

  @override
  void onInit() {
    super.onInit();

    final args = Get.arguments;
    if (args != null && args['ext'] != null) {
      selectedExtension.value = args['ext'];
    }
  }

  @override
  void onReady() {
    _fetchAllMp4();
    super.onReady();
  }

  Future<void> _fetchAllMp4() async {
    if (Platform.isAndroid) {
      await Permission.storage.request();
      await Permission.videos.request(); // For Android 13+
    }

    if (await Permission.storage.isGranted || await Permission.videos.isGranted) {
      var dirs = await FileManager.getStorageList();

      files.clear();

      for (var dir in dirs) {
        LogService.log.i("Dir: ${dir.path}");
        scanForMp4(dir);
      }
    } else {
      LogService.log.e("Permission not granted!");
      // Optionally show dialog to guide user
    }
  }

  void scanForMp4(Directory dir) async {
    try {
      List<FileSystemEntity> entities = dir.listSync(); // not recursive here
      List<FileSystemEntity> foundFiles = [];

      for (FileSystemEntity entity in entities) {
        if (entity is Directory) {
          scanForMp4(entity);
        } else if (entity is File) {
          String lowerPath = entity.path.toLowerCase();
          for (var ext in selectedExtension) {
            if (lowerPath.endsWith(ext.toLowerCase())) {
              foundFiles.add(entity);
              LogService.log.i("File Found ($ext): ${entity.path}");
            }
          }
        }
      }

      files.addAll(foundFiles);
    } catch (e) {
      LogService.log.e("Error scanning directory: $e");
    }
  }
}
