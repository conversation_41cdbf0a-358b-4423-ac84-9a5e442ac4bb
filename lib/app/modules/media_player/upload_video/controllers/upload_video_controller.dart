import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:mides_skadik/app/data/models/response/media_player/category/media_player_category_model.dart';
import 'package:mides_skadik/app/data/services/media_player/category/media_player_category_service.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/utils/file_picker.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/choose_video_cloud_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/choose_video_cloud_view.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';

class UploadVideoController extends GetxController {
  final MyVideoController myVideoController = Get.find<MyVideoController>();
  final TextEditingController titleVideo = TextEditingController();
  final TextEditingController descriptionVideo = TextEditingController();

  final Rx<VideoPlayerController?> videoPlayerController =
      Rx<VideoPlayerController?>(null);

  final int maxLength = 300;

  var inputText = ''.obs;

  RxBool isPlaying = false.obs;
  RxString privacy = "public".obs;
  RxString comment = "Aktif".obs;

  RxList<MediaPlayerCategory> categories = <MediaPlayerCategory>[].obs;

  RxList<String> selectedTags = <String>[].obs;
  RxString selectedVideoPath = "".obs;
  RxString selectedThumbnailPath = "".obs;

  bool isEdit = false;
  RxBool hasInitialize = false.obs;
  RxBool isLoadingCategory = false.obs;

  RxBool isDragging = false.obs;
  RxBool wasPlayingBeforeDrag = false.obs;

  Rx<Duration> currentPosition = Duration.zero.obs;
  RxInt defaultProgress = 0.obs;

  RxBool isSaving = false.obs;

  VodService vodService = VodService();

  @override
  void onReady() {
    loadCategory();

    isPlaying.value = false;

    super.onReady();
  }

  @override
  void dispose() {
    videoPlayerController.value?.removeListener(updateCurrentPosition);
    videoPlayerController.value?.dispose();
    titleVideo.dispose();

    super.dispose();
  }

  @override
  void onClose() {
    super.onClose();
    if (selectedTags.isNotEmpty) {
      selectedTags.clear();
    }
  }

  void initEditData({
    required String title,
    required String description,
    required String thumbnailUrl,
    required bool isCommentEnabled,
  }) {
    if (isEdit) return;

    titleVideo.text = title;
    descriptionVideo.text = description;
    inputText.value = description;
    comment.value = isCommentEnabled ? 'Aktif' : 'Tidak Aktif';
    isEdit = true;
  }

  void updateCurrentPosition() {
    final controller = videoPlayerController.value;
    if (controller != null && controller.value.isInitialized) {
      currentPosition.value = controller.value.position;
      defaultProgress.value = (controller.value.position.inSeconds /
              controller.value.duration.inSeconds *
              100)
          .toInt();
    }
  }

  void loadCategory() async {
    isLoadingCategory.value = true;

    var res = await MediaPlayerCategoryService().getList();
    if (res.isSuccess) {
      categories.value = res.resultValue ?? [];
    } else {
      categories.value = [];
    }

    isLoadingCategory.value = false;
  }

  @override
  void onChanged(String text) {
    inputText.value = text;
  }

  void chooseLocalFile() async {
    String? path = await FilePickerUtil.pickVideo();

    if (path != null) {
      LogService.log.i("Path: $path");
      selectedVideoPath.value = path;
      isEdit = false;

      // Dispose old controller if exists
      videoPlayerController.value?.dispose();

      // Create and initialize new controller
      final controller = VideoPlayerController.file(File(path));
      await controller.initialize();
      controller.setLooping(true);
      // controller.play();

      controller.addListener(updateCurrentPosition);

      videoPlayerController.value = controller;

      getThumbnail(path);
    }
  }

  void chooseThumbnail() async {
    String? path = await FilePickerUtil.pickImage();

    if (path != null) {
      selectedThumbnailPath.value = path;
    }
  }

  void playPause() async {
    if (videoPlayerController.value != null) {
      if (isPlaying.value) {
        videoPlayerController.value!.pause();
      } else {
        videoPlayerController.value!.play();
      }
    }

    isPlaying.value = videoPlayerController.value?.value.isPlaying == true;
  }

  void chooseCloudFile() async {
    var res = await Get.to(const ChooseVideoCloudView(),
        binding: ChooseVideoCloudBinding());

    if (res != null) {}
  }

  void getThumbnail(String videoPath) async {
    XFile thumbnailFile = await VideoThumbnail.thumbnailFile(
      video: videoPath,
      thumbnailPath: (await getTemporaryDirectory()).path,
      imageFormat: ImageFormat.JPEG,
      maxHeight:
          512, // specify the height of the thumbnail, let the width auto-scaled to keep the source aspect ratio
      quality: 80,
    );

    LogService.log.i("Path: ${thumbnailFile.path}");

    selectedThumbnailPath.value = thumbnailFile.path;
  }

  void setSeekbarProgress(int value) {
    currentPosition.value = Duration(
        seconds: (value /
                    100 *
                    videoPlayerController.value!.value.duration.inSeconds ??
                0)
            .toInt());
  }

  void save() async {
    try {
      isSaving.value = true;

      bool res = await vodService.createVod(
        videoFile: File(selectedVideoPath.value),
        thumbnailFile: File(selectedThumbnailPath.value),
        title: titleVideo.text,
        desc: descriptionVideo.text,
        tags: selectedTags,
        visibility: privacy.value,
        isCommentEnabled: comment.value == "Aktif" ? true : false,
      );

      if (res) {
        Get.back();
        SnackbarUtil.showOnce(
            title: "Berhasil", message: "Berhasil menyimpan video");
      } else {
        SnackbarUtil.showOnce(title: "Gagal", message: "Gagal menyimpan video");
      }
    } catch (e) {
      SnackbarUtil.showOnce(title: "Gagal", message: "Gagal menyimpan video");
    } finally {
      isSaving.value = false;
    }
  }

  void editVideo(String id) async {
    try {
      isSaving.value = true;

      final videoFile = selectedVideoPath.value.isEmpty
          ? null
          : File(selectedVideoPath.value);
      final thumbnailFile = selectedThumbnailPath.value.isEmpty
          ? null
          : File(selectedThumbnailPath.value);

      bool result = await vodService.updateVideo(
        videoFile: videoFile,
        thumbnailFile: thumbnailFile,
        id: id,
        title: titleVideo.text,
        desc: descriptionVideo.text,
        tags: selectedTags,
        visibility: privacy.value,
        isCommentEnabled: comment.value == "Aktif" ? true : false,
      );

      if (result) {
        SnackbarUtil.showOnce(
            title: "Sukses", message: "Berhasil mengedit video");
        myVideoController.getMyVod();
      } else {
        SnackbarUtil.showOnce(title: "Gagal", message: "Gagal mengedit video");
      }
    } catch (e) {
      SnackbarUtil.showOnce(title: "Gagal", message: "Gagal mengedit video $e");
    } finally {
      isSaving.value = false;
    }
  }

  void selectTags(String tag) {
    if (selectedTags.contains(tag)) {
      selectedTags.remove(tag);
      return;
    }

    if (selectedTags.length >= 5) {
      SnackbarUtil.showOnce(title: "Info", message: "Kategori maksimal 5");
      return;
    }

    selectedTags.add(tag);
  }
}
