import 'package:get/get.dart';

class ChooseVideoCloudController extends GetxController {
  final RxInt selectedIndex = (-1).obs;

  final List<Map<String, String>> videoData = [
    {
      'title': 'Kekuatan Angkatan Udara Indonesia: Se<PERSON>ah dan <PERSON>',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Pesawat Tempur TNI-AU: Menjaga Kedaulatan Udara',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': '<PERSON><PERSON><PERSON> TNI-AU dan Angkatan Bersenjata Negara Sahabat',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Inovasi Teknologi di Angkatan Udara Indonesia',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Profil Pesawat F-16 Fighting Falcon di TNI-AU',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Misi Kemanusiaan TNI-AU: Bantuan Bencana Alam',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Pameran Alutsista TNI-AU: Menampilkan Kekuatan Pertahanan',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Cerita Pilot TNI-AU: Pengalaman Terbang di Langit Indonesia',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Strategi Pertahanan Udara Indonesia di Era Modern',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
    {
      'title': 'Sejarah Perang Udara Indonesia: Dari Masa ke Masa',
      'duration': '9:59',
      'date': '2025-05-05',
      'size': '999 MB',
      'format': 'MP4',
      'resolution': '4K',
      'frameRate': '60',
    },
  ];

  void uploadVideo() {}
}
