import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';

import '../controllers/upload_video_controller.dart';

class UploadVideoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<UploadVideoController>(
      () => UploadVideoController(),
    );

    if (!Get.isRegistered<MyVideoController>()) {
      Get.put(MyVideoController());
    }
  }
}
