import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/clip_collection/clip_collection_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_collection_model.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/clip_service.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/playlist_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/controllers/video_player_controller.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:ntp/ntp.dart';
import 'package:video_trimmer/video_trimmer.dart';

class ClippingController extends GetxController {
  var isPlaying = false.obs;
  var isTrimming = false.obs;
  var progressVisibility = false.obs;

  var startValue = 0.0.obs;
  var endValue = 0.0.obs;
  var videoFilePath = ''.obs;

  var isLoading = false.obs;
  var isLoadingCollection = false.obs;
  var currentPage = 1.obs;
  var todalData = 0.obs;
  var isLoadingMoreData = false.obs;

  // Untuk mengelola video trimming
  final _trimmer = Trimmer();

  final listCollection = <ClipCollectionModel>[].obs;
  final listPlaylistCollection = <PlaylistCollectionModel>[].obs;
  final collectionIds = <String>[].obs;
  final playlistIds = <String>[].obs;
  final nameController = TextEditingController();
  final descripsionController = TextEditingController();
  final visibilityTap = 'public'.obs;
  final timeNow = DateTime.now().obs;

  @override
  void onInit() async {
    super.onInit();
    timeNow.value = await NTP.now();
    fetchDataClip();
    fetchDataPlaylist();
    print(playlistIds);
  }

  void fetchMoreDataClip() {
    if (todalData.value == listCollection.length) {
      return;
    }
    currentPage.value++;
    getListClipCollection();
  }

  // TODO: Set Playlist ID to List
  void setSelectedPlaylistId(String id, bool? isSelected) {
    isSelected == true ? playlistIds.add(id) : playlistIds.remove(id);
  }

  /// =========================================
  ///  Fetch Data Collection
  void fetchDataClip() async {
    listCollection.clear();
    isLoadingCollection.value = true;
    collectionIds.clear();
    var response = await ClipService()
        .getClipCollection(page: currentPage.value, limit: 10);

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty =
            listCollection.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          listCollection.add(item);
        }
      }
      isLoadingCollection.value = false;
    }
  }

  /// =========================================
  /// Get List Collection
  void getListClipCollection() async {
    try {
      isLoadingMoreData.value = true;
      var response = await ClipService()
          .getClipCollection(page: currentPage.value, limit: 10);

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty =
              listCollection.where((element) => element.id == item.id).isEmpty;
          if (empty) {
            listCollection.add(item);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  void fetchMoreDataPlaylist() {
    if (todalData.value == listPlaylistCollection.length) {
      return;
    }
    currentPage.value++;
    getListPlaylist();
  }

  /// =========================================
  ///  Fetch Data Collection
  void fetchDataPlaylist() async {
    listPlaylistCollection.clear();
    isLoadingCollection.value = true;
    playlistIds.clear();
    var response = await PlaylistService()
        .getPlaylistClipCollection(page: currentPage.value, limit: 10);

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = listPlaylistCollection
            .where((element) => element.id == item.id)
            .isEmpty;
        if (empty) {
          listPlaylistCollection.add(item);
        }
      }
      isLoadingCollection.value = false;
    }
  }

  /// =========================================
  /// Get List Collection
  void getListPlaylist() async {
    try {
      isLoadingMoreData.value = true;
      var response = await PlaylistService()
          .getPlaylistClipCollection(page: currentPage.value, limit: 10);

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty = listPlaylistCollection
              .where((element) => element.id == item.id)
              .isEmpty;
          if (empty) {
            listPlaylistCollection.add(item);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  // Fungsi untuk memuat video
  void loadVideo(File videoFile) {
    print(
        "ClippingController: Attempting to load video from path: ${videoFile.path}");
    if (!videoFile.existsSync()) {
      print(
          "ClippingController: ERROR - File does not exist at path: ${videoFile.path}");
      // Mungkin tampilkan error atau tangani di sini juga
      return; // Jangan lanjutkan jika file tidak ada
    }
    _trimmer.loadVideo(videoFile: videoFile);
    videoFilePath.value = videoFile.path;
  }

  // Fungsi untuk memulai trimming
  Future<String?> saveTrimmedVideo() async {
    progressVisibility.value = true;
    String? outputPath;

    await _trimmer.saveTrimmedVideo(
      startValue: startValue.value,
      endValue: endValue.value,
      onSave: (String? outputPath) {
        outputPath = outputPath;
        progressVisibility.value = false;
      },
    );

    return outputPath;
  }

  // Fungsi untuk mengontrol pemutaran video
  Future<void> playPauseVideo() async {
    bool playbackState = await _trimmer.videoPlaybackControl(
      startValue: startValue.value,
      endValue: endValue.value,
    );
    isPlaying.value = playbackState;
  }

  Future<String> simpanTrim(
      {required File videoFile,
      required String title,
      required String desc,
      required String vodId}) async {
    isLoading.value = true;
    var data = await VodService().createClip(
        videoFile: videoFile, title: title, desc: desc, vodId: vodId);
    if (data.isNotEmpty) {
      isLoading.value = false;
      return data;
    } else {
      isLoading.value = false;
      return "";
    }
  }

  void updateClipCollection({required String id}) async {
    isLoading.value = true;
    var data = await ClipService()
        .updateCollection(id: id, collectionIds: collectionIds);
    if (data.isSuccess) {
      isLoading.value = false;
      fetchDataClip();
      if (Get.isRegistered<VideoController>()) {
        Get.delete<VideoController>();
      }
      if (Get.isRegistered<DashboardController>()) {
        Get.find<DashboardController>().refreshPage();
      }
      Get.to(() => const BottomNavBar(choosenScreen: 'Media Player'));
    } else {
      isLoading.value = false;
    }
  }

  Future<void> createClipCollection() async {
    isLoading.value = true;
    var data = await ClipService().createCollection(
        name: nameController.text, description: descripsionController.text);
    if (data.isSuccess) {
      isLoading.value = false;
      fetchDataClip();
    } else {
      isLoading.value = false;
    }
  }

  void updatePlaylistCollection({
    required String id,
    bool needBackNavigate = false,
  }) async {
    isLoading.value = true;
    var data = await PlaylistService()
        .updatePlaylistCollection(id: id, playlistIds: playlistIds);
    if (data.isSuccess) {
      isLoading.value = false;
      fetchDataPlaylist();
      if (Get.isRegistered<VideoController>()) {
        Get.delete<VideoController>();
      }
      if (Get.isRegistered<DashboardController>()) {
        Get.find<DashboardController>().refreshPage();
      }

      if (needBackNavigate == true) {
        Get.back<bool>(result: true);
        SnackbarUtil.showOnce(
            title: 'Sukses',
            message: 'Video/Klip Berhasil Disimpan ke Playlist');
      } else {
        Get.to(() => const BottomNavBar(choosenScreen: 'Media Player'));
      }
    } else {
      isLoading.value = false;
      SnackbarUtil.showOnce(
          title: 'Gagal', message: 'Video/Klip Gagal Disimpan ke Playlist');
      LogService.log.w(data.errorMessage);
    }
  }

  Future<void> createPlaylistClipCollection() async {
    isLoading.value = true;
    var data = await PlaylistService().createPlaylistCollection(
        name: nameController.text,
        description: descripsionController.text,
        visibility: visibilityTap.value);
    if (data.isSuccess) {
      isLoading.value = false;
      fetchDataPlaylist();
    } else {
      isLoading.value = false;
    }
  }
}
