import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/controllers/clipping_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_collection_card.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class PlaylistCollectionView extends GetView<ClippingController> {
  final String? vodId;
  final bool? needBackNavigate;
  const PlaylistCollectionView({
    super.key,
    this.vodId,
    this.needBackNavigate,
  });
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      withIcon: true,
      heightAppBar: 110,
      titleAppBar: 'Playlist Collection',
      customIconPath: "assets/icons/back.svg",
      actions: [
        Padding(
          padding: EdgeInsets.only(right: 40.w),
          child: Row(children: [
            GestureDetector(
              onTap: () {
                showDialog(
                    context: context,
                    builder: (_) {
                      return AlertDialog(
                        backgroundColor: darkBlueColor,
                        title: const Text('Create New Playlist',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.w500)),
                        content: SizedBox(
                          width: 400,
                          height: 300,
                          child: Column(
                            children: [
                              TextField(
                                controller: controller.nameController,
                                decoration: InputDecoration(
                                  hintText: 'Judul Playlist',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: secondBlueColor.withOpacity(0.2),
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                ),
                                style: const TextStyle(color: Colors.white),
                              ),
                              16.verticalSpace,
                              TextField(
                                controller: controller.descripsionController,
                                decoration: InputDecoration(
                                  hintText: 'Deskripsi Playlist',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: secondBlueColor.withOpacity(0.2),
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                ),
                                style: const TextStyle(color: Colors.white),
                                maxLines: 5, // for multiline input
                              ),
                              16.verticalSpace,
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Visibility',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  8.horizontalSpace,
                                  Obx(
                                    () => DropdownButton<String>(
                                      value: controller.visibilityTap.value,
                                      icon: const Icon(Icons.arrow_drop_down),
                                      iconSize: 24,
                                      elevation: 16,
                                      style:
                                          const TextStyle(color: Colors.black),
                                      underline: Container(
                                        height: 2,
                                        color: Colors.transparent,
                                      ),
                                      dropdownColor: darkBlueColor,
                                      onChanged: (String? newValue) {
                                        controller.visibilityTap.value =
                                            newValue!;
                                      },
                                      items: <String>['public', 'private']
                                          .map<DropdownMenuItem<String>>(
                                              (String value) {
                                        return DropdownMenuItem<String>(
                                          value: value,
                                          child: Text(value,
                                              style: const TextStyle(
                                                  color: Colors.white)),
                                        );
                                      }).toList(),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  secondBlueColor.withOpacity(0.10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            onPressed: () {
                              Get.back();
                            },
                            child: const Text('Batal',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )),
                          ),
                          TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor: blueColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            onPressed: () async {
                              await controller.createPlaylistClipCollection();
                              controller.fetchDataPlaylist();
                              Get.back();
                            },
                            child: Obx(
                              () => Text(
                                controller.isLoading.value
                                    ? 'Loading ...'
                                    : 'Simpan',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    });
              },
              child: CustomContainer(
                height: 64,
                width: 240,
                bgColor: secondBlueColor.withOpacity(0.10),
                borderRadius: BorderRadius.circular(10.r),
                widget: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/plus.svg',
                      width: 30.w,
                      height: 30.h,
                    ),
                    8.horizontalSpace,
                    CustomTextWigdet(
                      title: "New Playlist",
                      textColor: secondWhiteColor,
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                    ),
                  ],
                ),
              ),
            ),
            16.horizontalSpace,
            GestureDetector(
              onTap: () {
                if (vodId != null) {
                  controller.updatePlaylistCollection(
                    id: vodId ?? '',
                    needBackNavigate: needBackNavigate ?? false,
                  );
                } else {
                  // Handle the null case appropriately, e.g., show a message or use a default value
                  LogService.log.w('vodId is null, cannot update collection.');
                }
              },
              child: Obx(
                () => CustomContainer(
                  height: 64,
                  width: 240,
                  bgColor: blueColor,
                  borderRadius: BorderRadius.circular(10.r),
                  alignment: Alignment.center,
                  widget: CustomTextWigdet(
                    title: controller.isLoading.value
                        ? 'Loading ...'
                        : "Save to selected",
                    textColor: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ]),
        ),
      ],
      body: RefreshIndicator(
        onRefresh: () async {
          try {
            controller.fetchDataPlaylist();
          } catch (e) {
            LogService.log.e('Error refreshing data: $e');
          }
        },
        child: Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 32.w),
            child: Obx(
              () {
                final isPortrait =
                    MediaQuery.of(context).orientation == Orientation.portrait;
                return GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: isPortrait ? 3 : 5,
                    childAspectRatio: isPortrait ? 14 / 18 : 14 / 20,
                    crossAxisSpacing: 24.w,
                    mainAxisSpacing: 24.h,
                  ),
                  itemCount: controller.listPlaylistCollection.length,
                  itemBuilder: (it, idx) {
                    final item = controller.listPlaylistCollection[idx];
                    return CustomCollectionCard(
                      judulVideo: item.name ?? '-',
                      selected:
                          controller.listPlaylistCollection[idx].isSelected,
                      onChanged: (value) {
                        controller.listPlaylistCollection[idx].isSelected =
                            value ?? false;
                        controller.setSelectedPlaylistId(
                            item.id ?? '', value == true);
                        controller.listPlaylistCollection.refresh();
                        LogService.log.i(
                          'Checkbox value changed: $value ${controller.playlistIds}',
                        );
                      },
                      changeThumbnail: true,
                      visibility:
                          controller.listPlaylistCollection[idx].visibility,
                      createdDate: timeRange(
                        controller.timeNow.value,
                        controller.listPlaylistCollection[idx].createdAt ??
                            controller.timeNow.value,
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
