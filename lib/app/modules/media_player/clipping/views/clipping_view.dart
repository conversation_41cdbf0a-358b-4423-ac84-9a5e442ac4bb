import 'dart:io';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/bindings/clipping_binding.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/clipping_collection_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:video_trimmer/video_trimmer.dart';

import '../controllers/clipping_controller.dart';

class ClippingView extends StatefulWidget {
  final File? file;
  final String? vodId;
  const ClippingView({super.key, this.file, this.vodId});

  @override
  State<ClippingView> createState() => _ClippingViewState();
}

class _ClippingViewState extends State<ClippingView> {
  final Trimmer _trimmer = Trimmer();

  double _startValue = 0.0;
  double _endValue = 0.0;

  bool _isPlaying = false;
  // bool _progressVisibility = false;

  ClippingController controller = Get.put(ClippingController());

  TextEditingController titleController = TextEditingController();
  TextEditingController descController = TextEditingController();

  Future<String?> _saveVideo({required String type}) async {
    setState(() {
      controller.isLoading.value = true;
    });

    String? value;

    await _trimmer.saveTrimmedVideo(
        startValue: _startValue,
        endValue: _endValue,
        storageDir: StorageDir.externalStorageDirectory,
        onSave: (String? outputPath) {
          setState(() {
            value = outputPath;
          });
        });
    return value;
  }

  void _loadVideo() async {
    if (widget.file != null) {
      // Tambahkan pengecekan existsSync di sini juga
      if (await widget.file!.exists()) {
        // Gunakan await
        LogService.log.i(
            "ClippingView: Loading video from existing file: ${widget.file!.path}");
        _trimmer.loadVideo(videoFile: widget.file!);
      } else {
        LogService.log.e(
            'ClippingView: Video file provided does NOT exist at path: ${widget.file!.path}');
        // Mungkin tampilkan pesan error atau navigasi kembali
        Get.snackbar("Error", "Video file for trimming not found.",
            backgroundColor: Colors.red);
        if (Navigator.canPop(context)) Navigator.pop(context);
      }
    } else {
      LogService.log
          .e('ClippingView: No video file provided (widget.file is null)');
      if (Navigator.canPop(context)) Navigator.pop(context);
    }
  }

  @override
  void initState() {
    super.initState();

    _loadVideo();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      body: Builder(
        builder: (context) => Container(
          padding: EdgeInsets.only(bottom: 30.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                AspectRatio(
                    aspectRatio: 16 / 9, child: VideoViewer(trimmer: _trimmer)),
                TrimViewer(
                  trimmer: _trimmer,
                  viewerHeight: 50.0,
                  viewerWidth: Get.width,
                  // maxVideoLength: const Duration(seconds: 10),
                  onChangeStart: (value) => _startValue = value,
                  onChangeEnd: (value) => _endValue = value,
                  onChangePlaybackState: (value) =>
                      setState(() => _isPlaying = value),
                ),
                TextButton(
                  child: _isPlaying
                      ? const Icon(
                          Icons.pause,
                          size: 80.0,
                          color: Colors.white,
                        )
                      : const Icon(
                          Icons.play_arrow,
                          size: 80.0,
                          color: Colors.white,
                        ),
                  onPressed: () async {
                    bool playbackState = await _trimmer.videoPlaybackControl(
                      startValue: _startValue,
                      endValue: _endValue,
                    );
                    setState(() {
                      _isPlaying = playbackState;
                    });
                  },
                ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      TextField(
                        controller: titleController,
                        decoration: InputDecoration(
                          hintText: 'Judul clip',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: secondBlueColor.withOpacity(0.2),
                          hintStyle: TextStyle(color: Colors.grey[400]),
                        ),
                        style: TextStyle(color: Colors.white),
                      ),
                      const SizedBox(height: 16.0),
                      TextField(
                        controller: descController,
                        decoration: InputDecoration(
                          hintText: 'Catatan clip',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          filled: true,
                          fillColor: secondBlueColor.withOpacity(0.2),
                          hintStyle: TextStyle(color: Colors.grey[400]),
                        ),
                        style: const TextStyle(color: Colors.white),
                        maxLines: 5, // for multiline input
                      ),
                      const SizedBox(height: 24.0),
                      Obx(
                        () => Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 16.0),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                                onPressed: controller.isLoading.value
                                    ? null
                                    : () async {
                                        _saveVideo(type: 'collection')
                                            .then((outputPath) async {
                                          var res = await controller.simpanTrim(
                                              videoFile: File(outputPath ?? ''),
                                              title: titleController.text,
                                              desc: descController.text,
                                              vodId: widget.vodId ?? '');
                                          if (res.isNotEmpty) {
                                            if (outputPath != null &&
                                                outputPath.isNotEmpty) {
                                              final trimmedFile =
                                                  File(outputPath);
                                              if (await trimmedFile.exists()) {
                                                await trimmedFile.delete();
                                                LogService.log.i(
                                                    'ClippingView: Trimmed file deleted from $outputPath');
                                              }
                                              final oriFile =
                                                  File(widget.file!.path);
                                              if (await oriFile.exists()) {
                                                await oriFile.delete();
                                                LogService.log.i(
                                                    'ClippingView: Original file deleted from ${widget.file!.path}');
                                              }
                                            }
                                            Get.back();
                                            Get.back();
                                          }
                                        });
                                      },
                                child: Text(
                                  controller.isLoading.value
                                      ? 'Loading ...'
                                      : 'Save',
                                  style: const TextStyle(
                                      color: Colors.white, fontSize: 16),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            Expanded(
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey[300],
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 16.0),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                ),
                                onPressed: controller.isLoading.value
                                    ? null
                                    : () async {
                                        _saveVideo(type: 'collection')
                                            .then((outputPath) async {
                                          var res = await controller.simpanTrim(
                                              videoFile: File(outputPath ?? ''),
                                              title: titleController.text,
                                              desc: descController.text,
                                              vodId: widget.vodId ?? '');
                                          if (res.isNotEmpty) {
                                            if (outputPath != null &&
                                                outputPath.isNotEmpty) {
                                              final trimmedFile =
                                                  File(outputPath);
                                              if (await trimmedFile.exists()) {
                                                await trimmedFile.delete();
                                                LogService.log.i(
                                                    'ClippingView: Trimmed file deleted from $outputPath');
                                              }
                                              final oriFile =
                                                  File(widget.file!.path);
                                              if (await oriFile.exists()) {
                                                await oriFile.delete();
                                                LogService.log.i(
                                                    'ClippingView: Original file deleted from ${widget.file!.path}');
                                              }
                                            }
                                            Get.to(
                                              () => ClippingCollectionView(
                                                vodId: res,
                                              ),
                                              binding: ClippingBinding(),
                                            );
                                          }
                                        });
                                      },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const SizedBox(
                                        width: 20,
                                      ),
                                      Text(
                                        controller.isLoading.value
                                            ? 'Loading ...'
                                            : 'Save to Collection',
                                        style: TextStyle(
                                            color: controller.isLoading.value
                                                ? Colors.white
                                                : Colors.black87,
                                            fontSize: 16),
                                      ),
                                      const Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.black54,
                                        size: 16,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
