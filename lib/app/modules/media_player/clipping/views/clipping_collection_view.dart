import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/controllers/clipping_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_collection_card.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ClippingCollectionView extends GetView<ClippingController> {
  final String? vodId;
  const ClippingCollectionView({super.key, this.vodId});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      withIcon: true,
      heightAppBar: 110,
      titleAppBar: 'Collection',
      customIconPath: "assets/icons/back.svg",
      actions: [
        Padding(
          padding: EdgeInsets.only(right: 40.w),
          child: Row(children: [
            GestureDetector(
              onTap: () {
                showDialog(
                    context: context,
                    builder: (_) {
                      return AlertDialog(
                        backgroundColor: darkBlueColor,
                        title: const Text('Create New Collection',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.w500)),
                        content: SizedBox(
                          width: 400,
                          height: 250,
                          child: Column(
                            children: [
                              TextField(
                                controller: controller.nameController,
                                decoration: InputDecoration(
                                  hintText: 'Judul Collection',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: secondBlueColor.withOpacity(0.2),
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                ),
                                style: TextStyle(color: Colors.white),
                              ),
                              16.verticalSpace,
                              TextField(
                                controller: controller.descripsionController,
                                decoration: InputDecoration(
                                  hintText: 'Deskripsi Collection',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8.0),
                                  ),
                                  filled: true,
                                  fillColor: secondBlueColor.withOpacity(0.2),
                                  hintStyle: TextStyle(color: Colors.grey[400]),
                                ),
                                style: const TextStyle(color: Colors.white),
                                maxLines: 5, // for multiline input
                              ),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  secondBlueColor.withOpacity(0.10),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            onPressed: () {
                              Get.back();
                            },
                            child: const Text('Batal',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )),
                          ),
                          TextButton(
                            style: TextButton.styleFrom(
                              backgroundColor: blueColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                            ),
                            onPressed: () async {
                              await controller.createClipCollection();
                              controller.fetchDataClip();
                              Get.back();
                            },
                            child: Obx(
                              () => Text(
                                controller.isLoading.value
                                    ? 'Loading ...'
                                    : 'Simpan',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    });
              },
              child: CustomContainer(
                height: 64,
                width: 240,
                bgColor: secondBlueColor.withOpacity(0.10),
                borderRadius: BorderRadius.circular(10.r),
                widget: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/plus.svg',
                      width: 30.w,
                      height: 30.h,
                    ),
                    8.horizontalSpace,
                    CustomTextWigdet(
                      title: "New Collection",
                      textColor: secondWhiteColor,
                      fontSize: 24,
                      fontWeight: FontWeight.w500,
                    ),
                  ],
                ),
              ),
            ),
            16.horizontalSpace,
            GestureDetector(
              onTap: () {
                if (vodId != null) {
                  controller.updateClipCollection(id: vodId ?? '');
                } else {
                  // Handle the null case appropriately, e.g., show a message or use a default value
                  LogService.log.w('vodId is null, cannot update collection.');
                }
              },
              child: Obx(
                () => CustomContainer(
                  height: 64,
                  width: 240,
                  bgColor: blueColor,
                  borderRadius: BorderRadius.circular(10.r),
                  alignment: Alignment.center,
                  widget: CustomTextWigdet(
                    title: controller.isLoading.value
                        ? 'Loading ...'
                        : "Save to selected",
                    textColor: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ]),
        ),
      ],
      body: RefreshIndicator(
        onRefresh: () async {
          try {
            controller.fetchDataClip();
          } catch (e) {
            LogService.log.e('Error refreshing data: $e');
          }
        },
        child: Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 32.w),
            child: Obx(
              () => GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 15 / 18,
                ),
                itemCount: controller.listCollection.length,
                itemBuilder: (it, idx) {
                  final item = controller.listCollection[idx];
                  return CustomCollectionCard(
                    judulVideo: item.name ?? '-',
                    selected: controller.listCollection[idx].isSelected,
                    onChanged: (value) {
                      LogService.log.i(
                        'Checkbox value changed: $value',
                      );
                      controller.collectionIds.add(item.id ?? '');
                      controller.listCollection[idx].isSelected =
                          value ?? false;
                      controller.listCollection.refresh();
                    },
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
