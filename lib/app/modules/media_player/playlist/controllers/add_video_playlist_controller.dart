import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/playlist_service.dart';

class AddVideoPlaylistController extends GetxController {
  RxList<String> selected = <String>[].obs;
  RxString playlistId = ''.obs;

  void toggleSelection(String id) {
    if (selected.contains(id)) {
      selected.remove(id);
    } else {
      selected.add(id);
    }
  }

  bool isSelected(String id) {
    return selected.contains(id);
  }

  void save() {
    for (var element in selected) {
      PlaylistService().assignVod(
        vodId: element,
        playlistIds: [playlistId.value],
      );
    }

    Get.back<bool>(result: true);
  }
}
