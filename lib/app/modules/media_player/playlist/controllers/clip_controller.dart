import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/clip_model.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/clip_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class ClipController extends GetxController {
  final ClipService _clipService = ClipService();
  final clips = <ClipModel>[].obs;
  final isLoading = true.obs;
  final errorMessage = ''.obs;

  @override
  void onReady() {
    super.onReady();
    loadClips();
  }

  Future<void> loadClips() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _clipService.getClips(
        page: 1,
        limit: 10,
      );

      if (result.isSuccess) {
        clips.assignAll(result.resultValue ?? []);
      } else {
        errorMessage.value = result.errorMessage ?? 'Failed to load clips';
      }
    } catch (e) {
      LogService.log.e('Failed to load clips: $e');
      errorMessage.value = 'Failed to load clips: $e';
    } finally {
      isLoading.value = false;
    }
  }
}
