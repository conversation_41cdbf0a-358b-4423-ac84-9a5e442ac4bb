import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_model.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/playlist_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/bindings/clipping_binding.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/playlist_collection_view.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/views/add_video_playlist_view.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/upload_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_view.dart';
import 'package:mides_skadik/widgets/media_player/playlist/custom_playlist_form.dart';
import 'package:flutter/material.dart';

class PlaylistController extends GetxController {
  final PlaylistService _playlistService = PlaylistService();
  final playlists = <PlaylistModel>[].obs;
  final isLoading = true.obs;
  final errorMessage = ''.obs;

  final Rx<PlaylistModel?> selectedPlaylist = Rx<PlaylistModel?>(null);

  @override
  void onReady() {
    super.onReady();
    loadPlaylists();
  }

  Future<void> loadPlaylists() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _playlistService.getPlaylists(
        page: 1,
        limit: 10,
      );

      if (result.isSuccess) {
        playlists.assignAll(result.resultValue ?? []);

        selectedPlaylist.value = playlists.isNotEmpty ? playlists.first : null;
      } else {
        errorMessage.value = result.errorMessage ?? 'Failed to load playlists';
      }
    } catch (e) {
      errorMessage.value = 'Failed to load playlists: $e';
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  void setSelectedPlaylist(PlaylistModel playlist) {
    selectedPlaylist.value = playlist;
    LogService.log.i('Selected playlist: ${playlist.name}');
  }

  void newPlaylist() async {
    PlaylistModel? result = await showDialog(
      context: Get.context!,
      builder: (context) {
        return const Dialog(
          child: CustomPlaylistFormWidget(),
        );
      },
    );

    if (result != null) {
      await _playlistService.createPlaylist(
        name: result.name ?? '',
        description: result.description ?? '',
        visibility: result.visibility ?? 'public',
      );

      loadPlaylists();
    }
  }

  void editPlaylist(PlaylistModel playlist) async {
    PlaylistModel? result = await showDialog(
      context: Get.context!,
      builder: (context) {
        return Dialog(
          child: CustomPlaylistFormWidget(playlist: playlist),
        );
      },
    );

    if (result != null) {
      playlists.removeWhere((p) => p.id == playlist.id);
      playlists.add(result);
      selectedPlaylist.value = result;

      await _playlistService.updatePlaylist(
        id: playlist.id ?? '',
        name: result.name ?? '',
        description: result.description ?? '',
        visibility: result.visibility ?? 'public',
      );

      loadPlaylists();
    }
  }

  void addVideoToPlaylist(PlaylistModel playlist) async {
    if (!Get.isRegistered<MyVideoController>()) {
      Get.put(MyVideoController());
    }

    await Get.to(
      AddVideoPlaylistView(
        playlistId: playlist.id ?? '',
      ),
    );

    loadPlaylists();
  }

  void addOrRemoveFromPlaylist(Vods vod) async {
    await Get.to(
      PlaylistCollectionView(
        vodId: vod.id,
        needBackNavigate: true,
      ),
      binding: ClippingBinding(),
    );

    loadPlaylists();
  }

  void deleteVideoFromPlaylist(String playlistId, String vodId) async {
    await _playlistService.removeVod(
      playlistId: playlistId,
      vodIds: [vodId],
    );

    loadPlaylists();
  }
}
