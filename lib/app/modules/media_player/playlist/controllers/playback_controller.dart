import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/services/media_player/playlist/vod_playback_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:ntp/ntp.dart';

class PlaybackController extends GetxController {
  final VodPlaybackService _playbackService = VodPlaybackService();
  final playbacks = <PlaybackModel>[].obs;
  final isLoading = true.obs;
  final errorMessage = ''.obs;

  var timeNow = DateTime.now().obs;

  @override
  void onReady() async {
    loadPlaybacks();
    timeNow.value = await NTP.now();
    super.onReady();
  }

  Future<void> loadPlaybacks() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _playbackService.getAllVodPlayback(
        page: 1,
        limit: 10,
      );

      if (result.isSuccess) {
        playbacks.assignAll(result.resultValue ?? []);
      } else {
        errorMessage.value = result.errorMessage ?? 'Failed to load playback';
      }
    } catch (e) {
      LogService.log.e('Failed to load playback: $e');
      errorMessage.value = 'Failed to load playback: $e';
    } finally {
      isLoading.value = false;
    }
  }
}
