import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/bottom_sheet.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_dropdown.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/custom_user_info.dart';

class PlaylistPotraitView extends GetView {
  const PlaylistPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    final playListcontroller = Get.find<PlaylistController>();

    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SizedBox(
                  width: 72.w,
                  height: 72.h,
                  child: SvgPicture.asset('assets/icons/back.svg'),
                ),
              ),
              16.horizontalSpace,
              const CustomTextWigdet(
                title: 'Playlist',
                fontSize: 32,
                fontWeight: FontWeight.w700,
              )
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
          child: CustomContainer(
            widget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomContainer(
                  bgColor: secondBlueColor.withOpacity(0.2),
                  radius: 12,
                  widget: Column(
                    children: [
                      CustomContainer(
                        height: 360,
                        widget: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(12.r),
                                topRight: Radius.circular(12.r),
                              ),
                              child: Image.asset(
                                'assets/images/airplane.png',
                                width: double.infinity,
                                height: 360.h,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: Padding(
                                padding:
                                    EdgeInsets.only(right: 40.w, bottom: 40.h),
                                child: CustomFilledButtonWidget(
                                  onPressed: () {},
                                  withIcon: true,
                                  onlyIcon: true,
                                  assetName: 'assets/icons/pencil.svg',
                                  bgColor: blackColor.withOpacity(0.4),
                                  widthButton: 65,
                                  heightButton: 65,
                                  widthIcon: 48,
                                  heightIcon: 48,
                                  radius: 12,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(16.r),
                        child: const Row(
                          children: [
                            CustomUserInfo(
                              width: 80,
                              height: 80,
                            ),
                            Spacer(),
                            CustomTextWigdet(
                              title:
                                  'Public  •  9 videos  •  99 views  •  Updated today',
                              fontSize: 18,
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                24.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {},
                      withIcon: true,
                      title: 'Putar semua',
                      assetName: 'assets/icons/play.svg',
                      widthButton: 500,
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: blueColor,
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        playListcontroller.editPlaylist(
                            playListcontroller.selectedPlaylist.value!);
                      },
                      withIcon: true,
                      title: 'Edit Deskripsi',
                      assetName: 'assets/icons/pencil.svg',
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: secondBlueColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        playListcontroller.addVideoToPlaylist(
                            playListcontroller.selectedPlaylist.value!);
                      },
                      withIcon: true,
                      title: 'Tambah Video',
                      assetName: 'assets/icons/add_to_playlist.svg',
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: secondBlueColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {},
                      withIcon: true,
                      title: 'Download',
                      assetName: 'assets/icons/download_playlist.svg',
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: secondBlueColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {},
                      withIcon: true,
                      title: 'Bagikan',
                      assetName: 'assets/icons/share.svg',
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: secondBlueColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                    )
                  ],
                ),
                24.verticalSpace,
                const CustomDropdown(width: 500, height: 65, itemsDropdown: [
                  {"title": "Manual", "subtitle": ""},
                  {"title": "Date added", "subtitle": "newest"},
                  {"title": "Date added", "subtitle": "oldest"},
                  {"title": "Most popular", "subtitle": ""},
                  {"title": "Date uploaded", "subtitle": "newest"},
                  {"title": "Date uploaded", "subtitle": "oldest"},
                ]),
                24.verticalSpace,
                Obx(() {
                  final videos =
                      playListcontroller.selectedPlaylist.value?.vods ?? [];
                  return CustomContainer(
                    height: 1225,
                    widget: videos.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.playlist_play_rounded,
                                  size: 64.sp,
                                  color: Colors.grey[400],
                                ),
                                16.verticalSpace,
                                Text(
                                  'Playlist Kosong',
                                  style: TextStyle(
                                    color: Colors.grey[400],
                                    fontSize: 20.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                8.verticalSpace,
                                Text(
                                  'Tidak ada video dalam playlist ini',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 16.sp,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: videos.length,
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              var row = videos[index];
                              return Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: CustomVideoItemLarge(
                                  showMenuIcon: true,
                                  thumbnailWidth: 480,
                                  thumbnailHeight: 280,
                                  title: row.title ?? '',
                                  description: row.desc ?? '',
                                  thumbnailUrl: row.thumbnailUrl ?? '',
                                  tags: (row.tag ?? [])
                                      .map((e) => e.name ?? '')
                                      .toList(),
                                  uploadDate: timeRange(
                                    DateTime.now(),
                                    DateTime.parse(row.uploadDate ??
                                        DateTime.now().toIso8601String()),
                                  ),
                                  uploaderName: row.uploader?.pasisName ?? "-",
                                  totalViews: row.totalView ?? 0,
                                  duration: row.duration ?? '',
                                  onPressMore: () {
                                    BottomSheetUtil.showBottomSheet(
                                      items: [
                                        BottomSheetItem(
                                          icon:
                                              "assets/icons/add_to_playlist.svg",
                                          title: 'Add or Remove Playlist',
                                          onTap: () {
                                            Get.back();
                                            playListcontroller
                                                .addOrRemoveFromPlaylist(row);
                                          },
                                        ),
                                        BottomSheetItem(
                                          icon: "assets/icons/share.svg",
                                          title: 'Share',
                                          onTap: () {
                                            Get.back();
                                          },
                                        ),
                                        BottomSheetItem(
                                          icon: "assets/icons/close.svg",
                                          iconColor: redColor,
                                          title: 'Remove From Playlist',
                                          textColor: redColor,
                                          onTap: () {
                                            Get.back();

                                            playListcontroller
                                                .deleteVideoFromPlaylist(
                                                    row.id ?? '',
                                                    playListcontroller
                                                            .selectedPlaylist
                                                            .value!
                                                            .id ??
                                                        '');
                                          },
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                  );
                }),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
