import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/utils/bottom_sheet.dart';
import 'package:mides_skadik/app/data/utils/date_format.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_dropdown.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/media_player/playlist/custom_playlist_view.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_playlist.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/custom_user_info.dart';

class PlaylistLandscapeView extends GetView<PlaylistController> {
  final bool landscape;
  const PlaylistLandscapeView({super.key, required this.landscape});
  @override
  Widget build(BuildContext context) {
    final playListcontroller = Get.find<PlaylistController>();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Side

        SizedBox(
          width: 800.w,
          child: Column(
            children: [
              CustomContainer(
                bgColor: secondBlueColor.withOpacity(0.15),
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                height: 170.h,
                widget: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {
                        Get.back();
                      },
                      withIcon: true,
                      title: 'Back',
                      assetName: 'assets/icons/arrow_left.svg',
                      heightButton: 65,
                      widthIcon: 40,
                      heightIcon: 40,
                      bgColor: greyColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        controller.newPlaylist();
                      },
                      withIcon: true,
                      title: 'New playlist',
                      assetName: 'assets/icons/plus.svg',
                      heightButton: 65,
                      widthIcon: 26,
                      heightIcon: 26,
                      bgColor: greyColor.withOpacity(0.3),
                      fontColor: whiteColor,
                      fontSize: 22,
                      radius: 12,
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                    ),
                  ],
                ),
              ),
              24.verticalSpace,
              const Expanded(
                child: CustomPlaylistView(),
              ),
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: 24.w),
            child: CustomContainer(
              widget: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomContainer(
                    bgColor: secondBlueColor.withOpacity(0.15),
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    height: 170.h,
                    widget: Row(
                      children: [
                        Expanded(
                          child: Obx(
                            () => CustomTextWigdet(
                              title: playListcontroller
                                      .selectedPlaylist.value?.name ??
                                  '',
                              textColor: secondWhiteColor,
                              fontSize: 32,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  24.verticalSpace,
                  Obx(() {
                    var selected = playListcontroller.selectedPlaylist.value;

                    var firstVideo = selected?.vods?.isNotEmpty == true
                        ? selected?.vods?.first
                        : null;

                    var uploadDate = firstVideo?.uploadDate != null
                        ? DateFormatUtil.formatDateTimeFromString(
                            firstVideo!.uploadDate!)
                        : '-';

                    String visibility = (selected?.visibility ?? 'Public')
                            .toLowerCase()
                            .capitalize ??
                        '';

                    int views = (selected?.vods ?? [])
                        .map((e) => e.totalView)
                        .fold(0, (a, b) => a + (b ?? 0));
                    String totalView =
                        NumberFormat("###,###", "id").format(views);

                    return CustomContainer(
                      bgColor: secondBlueColor.withOpacity(0.2),
                      radius: 12,
                      widget: Column(
                        children: [
                          CustomContainer(
                            height: 360,
                            widget: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(12.r),
                                    topRight: Radius.circular(12.r),
                                  ),
                                  child: Image.network(
                                    selected?.vods?.isNotEmpty == true
                                        ? selected!.vods!.first.thumbnailUrl ??
                                            ''
                                        : '',
                                    width: double.infinity,
                                    height: 360.h,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return CustomContainer(
                                        width: Get.width,
                                        height: Get.height,
                                        bgColor: greyColor,
                                        widget: const Icon(Icons.error),
                                      );
                                    },
                                  ),
                                ),
                                Align(
                                  alignment: Alignment.bottomRight,
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        right: 40.w, bottom: 40.h),
                                    child: CustomFilledButtonWidget(
                                      onPressed: () {
                                        controller.editPlaylist(selected!);
                                      },
                                      withIcon: true,
                                      onlyIcon: true,
                                      assetName: 'assets/icons/pencil.svg',
                                      bgColor: blackColor.withOpacity(0.4),
                                      widthButton: 65,
                                      heightButton: 65,
                                      widthIcon: 48,
                                      heightIcon: 48,
                                      radius: 12,
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.all(16.r),
                            child: Row(
                              children: [
                                CustomUserInfo(
                                  width: 80,
                                  height: 80,
                                  title: selected?.createdBy ?? "",
                                ),
                                const Spacer(),
                                CustomTextWigdet(
                                  title:
                                      '$visibility  •  ${selected!.vods?.length ?? 0} videos  •  $totalView views  •  Updated today',
                                  fontSize: 18,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                  24.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomFilledButtonWidget(
                        onPressed: () {},
                        withIcon: true,
                        title: 'Putar semua',
                        assetName: 'assets/icons/play.svg',
                        widthButton: 700,
                        heightButton: 65,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: blueColor,
                        fontColor: whiteColor,
                        fontSize: 22,
                        radius: 12,
                      ),
                      CustomFilledButtonWidget(
                        onPressed: () {
                          controller.editPlaylist(
                              playListcontroller.selectedPlaylist.value!);
                        },
                        withIcon: true,
                        title: 'Edit Details',
                        assetName: 'assets/icons/pencil.svg',
                        heightButton: 65,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: secondBlueColor.withOpacity(0.3),
                        fontColor: whiteColor,
                        fontSize: 22,
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                      ),
                      CustomFilledButtonWidget(
                        onPressed: () {
                          controller.addVideoToPlaylist(
                              playListcontroller.selectedPlaylist.value!);
                        },
                        withIcon: true,
                        title: 'Tambah Video',
                        assetName: 'assets/icons/add_to_playlist.svg',
                        heightButton: 65,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: secondBlueColor.withOpacity(0.3),
                        fontColor: whiteColor,
                        fontSize: 22,
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                      ),
                      CustomFilledButtonWidget(
                        onPressed: () {},
                        withIcon: true,
                        title: 'Bagikan',
                        assetName: 'assets/icons/share.svg',
                        heightButton: 65,
                        widthIcon: 40,
                        heightIcon: 40,
                        bgColor: secondBlueColor.withOpacity(0.3),
                        fontColor: whiteColor,
                        fontSize: 22,
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                      )
                    ],
                  ),
                  24.verticalSpace,
                  Row(
                    children: [
                      const CustomDropdown(
                        width: 500,
                        height: 65,
                        itemsDropdown: [
                          {"title": "Manual", "subtitle": ""},
                          {"title": "Date added", "subtitle": "newest"},
                          {"title": "Date added", "subtitle": "oldest"},
                          {"title": "Most popular", "subtitle": ""},
                          {"title": "Date uploaded", "subtitle": "newest"},
                          {"title": "Date uploaded", "subtitle": "oldest"},
                        ],
                      ),
                      16.horizontalSpace,
                      Flexible(
                        child: SizedBox(
                          height: 65.h,
                          child: CustomTextFieldWidget(
                            colorField: secondBlueColor.withOpacity(0.15),
                            colorText: whiteColor,
                            colorTextHint: greyColor,
                            fontSize: 24,
                            radius: 10,
                            contentPadding: EdgeInsetsDirectional.symmetric(
                                horizontal: 10.w, vertical: 10.h),
                            hintText: 'Cari Playlist',
                          ),
                        ),
                      ),
                      16.horizontalSpace,
                      CustomFilledButtonWidget(
                        onPressed: () {},
                        withIcon: true,
                        onlyIcon: true,
                        assetName: 'assets/icons/mic.svg',
                        widthButton: 65,
                        heightButton: 65,
                        widthIcon: 50,
                        heightIcon: 50,
                        radius: 10,
                        bgColor: secondBlueColor.withOpacity(0.15),
                      )
                    ],
                  ),
                  24.verticalSpace,
                  Obx(() {
                    final videos =
                        playListcontroller.selectedPlaylist.value?.vods ?? [];
                    return CustomContainer(
                      height: 450,
                      widget: videos.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.playlist_play_rounded,
                                    size: 64.sp,
                                    color: Colors.grey[400],
                                  ),
                                  16.verticalSpace,
                                  Text(
                                    'Playlist Kosong',
                                    style: TextStyle(
                                      color: Colors.grey[400],
                                      fontSize: 20.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  8.verticalSpace,
                                  Text(
                                    'Tidak ada video dalam playlist ini',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 16.sp,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              itemCount: videos.length,
                              physics: const AlwaysScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                var row = videos[index];
                                return Padding(
                                  padding: EdgeInsets.only(bottom: 16.h),
                                  child: CustomVideoItemLarge(
                                    showMenuIcon: true,
                                    thumbnailWidth: 480,
                                    thumbnailHeight: 280,
                                    title: row.title ?? '',
                                    description: row.desc ?? '',
                                    thumbnailUrl: row.thumbnailUrl ?? '',
                                    tags: (row.tag ?? [])
                                        .map((e) => e.name ?? '')
                                        .toList(),
                                    uploadDate: timeRange(
                                        DateTime.now(),
                                        DateTime.parse(row.uploadDate ??
                                            DateTime.now().toIso8601String())),
                                    uploaderName:
                                        row.uploader?.pasisName ?? "-",
                                    totalViews: row.totalView ?? 0,
                                    duration: row.duration ?? '',
                                    onPressMore: () {
                                      BottomSheetUtil.showBottomSheet(
                                        items: [
                                          BottomSheetItem(
                                            icon:
                                                "assets/icons/add_to_playlist.svg",
                                            title:
                                                'Add or Remove From Playlist',
                                            onTap: () {
                                              Get.back();
                                              playListcontroller
                                                  .addOrRemoveFromPlaylist(row);
                                            },
                                          ),
                                          BottomSheetItem(
                                            icon: "assets/icons/share.svg",
                                            title: 'Share',
                                            onTap: () {
                                              Get.back();
                                            },
                                          ),
                                          BottomSheetItem(
                                            icon: "assets/icons/close.svg",
                                            iconColor: redColor,
                                            title: 'Remove From Playlist',
                                            textColor: redColor,
                                            onTap: () {
                                              Get.back();

                                              playListcontroller
                                                  .deleteVideoFromPlaylist(
                                                      row.id ?? '',
                                                      playListcontroller
                                                              .selectedPlaylist
                                                              .value!
                                                              .id ??
                                                          '');
                                            },
                                          ),
                                        ],
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                    );
                  }),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
