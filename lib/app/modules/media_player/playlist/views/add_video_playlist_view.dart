import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/add_video_playlist_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_item_checklist_widget.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_item_grid_widget.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_item_widget.dart';

class AddVideoPlaylistView extends StatefulWidget {
  final String playlistId;
  const AddVideoPlaylistView({super.key, required this.playlistId});

  @override
  State<AddVideoPlaylistView> createState() => _AddVideoPlaylistViewState();
}

class _AddVideoPlaylistViewState extends State<AddVideoPlaylistView> {
  @override
  Widget build(BuildContext context) {
    final addVideoController = Get.put(AddVideoPlaylistController());
    final controller = Get.find<MyVideoController>();
    final datas = controller.listMyVod;
    addVideoController.playlistId.value = widget.playlistId;
    return CustomScaffold(
      useAppBar: true,
      titleAppBar: 'Add Video to Playlist',
      onBackPressed: () {
        Get.back();
      },
      withIcon: true,
      actions: [
        CustomFilledButtonWidget(
          onPressed: addVideoController.save,
          title: 'Save',
          heightButton: 68,
          bgColor: blueColor,
          fontColor: whiteColor,
          radius: 12.r,
          padding: EdgeInsets.symmetric(horizontal: 24.w),
        ),
        16.horizontalSpace,
      ],
      body: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
        itemCount: datas.length + 1,
        itemBuilder: (ctx, idx) {
          if (idx == datas.length) {
            return Obx(
              () => controller.isLastPage.value
                  ? const Center(
                      child: CustomTextWigdet(
                        title: 'Semua konten sudah dimuat.',
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  : CustomFilledButtonWidget(
                      onPressed: () {
                        controller.page.value++;
                        controller.getMyVod(
                            search: controller.searchVod.value,
                            sortBy: controller.sortValueForApi.value);
                      },
                      heightButton: 68,
                      bgColor: blueColor,
                      title: 'Tampilkan Lebih Banyak',
                      fontColor: whiteColor,
                    ),
            );
          }

          final item = datas[idx];
          return Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            child: MyVodItemChecklistWidget(
                item: item, index: idx, controller: addVideoController),
          );
        },
      ),
    );
  }
}
