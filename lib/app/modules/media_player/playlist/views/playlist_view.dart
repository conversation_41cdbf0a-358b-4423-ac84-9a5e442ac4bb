import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/views/playlist_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/views/playlist_potrait_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_model.dart';

class PlaylistView extends StatefulWidget {
  final PlaylistModel? playlist;
  const PlaylistView({super.key, this.playlist});

  @override
  State<PlaylistView> createState() => _PlaylistViewState();
}

class _PlaylistViewState extends State<PlaylistView> {
  final controller = Get.find<PlaylistController>();

  @override
  void initState() {
    super.initState();
    if (widget.playlist != null) {
      controller.setSelectedPlaylist(widget.playlist!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        richTitleAppBar: [
          WidgetSpan(
            child: CustomTextWigdet(
              title: "Library",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: whiteColor,
            ),
          ),
          WidgetSpan(
            child: CustomTextWigdet(
              title: " / Playlist",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: secondWhiteColor.withOpacity(0.50),
            ),
          ),
        ],
        fontWeight: FontWeight.w200,
        withIcon: true,
        fontSizeAppBar: 32,
        customIconPath: "assets/icons/icon_library.svg",
        isHamburgerIcon: true,
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifications',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/settings.svg',
                    title: 'Setting',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/settings.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
              ],
        body: orientation.isLandscape.value ? PlaylistLandscapeView(landscape: orientation.isLandscape.value) : const PlaylistPotraitView(),
      ),
    );
  }
}
