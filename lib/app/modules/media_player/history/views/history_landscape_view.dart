import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/history/controllers/history_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_search_bar.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class HistoryLandscapeView extends GetView<HistoryController> {
  final bool landscape;
  const HistoryLandscapeView({super.key, required this.landscape});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomSearchBar(
          controller: controller.searchHistory,
        ),
        Padding(
            padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 32.w),
            child: Obx(
              () {
                return SizedBox(
                  height: 1160.h,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      spacing: 14,
                      children: controller.groupedHistory.entries.map((entry) {
                        return SizedBox(
                          width: 1240.w,
                          child: CustomContainer(
                            widget: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextWigdet(
                                  title: entry.key,
                                  fontSize: 32,
                                  fontWeight: FontWeight.w700,
                                  textColor: whiteColor,
                                ),
                                16.verticalSpace,
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: entry.value.length,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      final data = entry.value[index];
                                      return Padding(
                                        padding: EdgeInsets.only(bottom: 16.h),
                                        child: CustomVideoItemLarge(
                                          thumbnailWidth: 480,
                                          thumbnailHeight: 280,
                                          thumbnailUrl: data.vod?.thumbnailUrl,
                                          duration: formatDuration(
                                              data.vod?.duration),
                                          title: data.vod?.title,
                                          totalViews: data.vod?.totalView,
                                          uploadDate: timeRange(
                                            controller.timeNow.value,
                                            data.vod?.uploadDate ??
                                                controller.timeNow.value,
                                          ),
                                          tags: data.vod?.tagNames,
                                          description: data.vod?.desc,
                                          imageUrl:
                                              data.vod?.uploader?.imageProfile,
                                          userName:
                                              data.vod?.uploader?.username,
                                          uploaderName:
                                              data.vod?.uploader?.pasisName,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                );
              },
            )),
      ],
    );
  }
}
