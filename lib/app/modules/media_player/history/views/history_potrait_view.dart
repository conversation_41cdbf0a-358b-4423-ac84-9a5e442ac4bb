import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/history/controllers/history_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_search_bar.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/history/history_item_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class HistoryPotraitView extends GetView<HistoryController> {
  const HistoryPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomSearchBar(
          controller: controller.searchHistory,
          isFocus: true,
          onBack: () {
            Get.back();
          },
        ),
        Obx(
          () {
            if (controller.isLoading.value) {
              return const Expanded(
                child: Center(
                  child: CustomLoadingWidget(),
                ),
              );
            }

            if (controller.history.isEmpty) {
              return const Expanded(
                child: Center(
                  child: CustomDataEmpty(
                    title: 'Tidak ada data History.',
                    assetName: 'assets/icons/no_data_video.svg',
                  ),
                ),
              );
            }

            return Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: controller.groupedHistory.entries.map((entry) {
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: entry.key,
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                            textColor: whiteColor,
                          ),
                          16.verticalSpace,
                          ListView.builder(
                            itemCount: entry.value.length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              final data = entry.value[index];
                              return Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: GestureDetector(
                                  onTap: () {
                                    Get.to(VideoPlayerView(),
                                        binding: VideoPlayerBinding(),
                                        arguments: {
                                          "videoUrl": data.vod?.videoUrl,
                                          "videoId": data.vod?.id,
                                          "totalLike": data.vod?.totalLike,
                                          "totalDislike":
                                              data.vod?.totalDislike,
                                          "totalRating": data.vod?.totalRating,
                                          "totalView": data.vod?.totalView,
                                          "videoSourceUrl":
                                              data.vod?.videoSourceUrl,
                                          "videoTitle": data.vod?.title,
                                          "videoTags": data.vod?.tagNames,
                                          "lastWatchTime": stringToDuration(
                                              data.lastWatchedTime!),
                                        });
                                  },
                                  child: HistoryItemWidget(
                                      item: data,
                                      index: index,
                                      controller: controller),
                                ),
                              );
                            },
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ),
            );
          },
        )
      ],
    );
  }
}
