import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/services/media_player/history/history_services.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:ntp/ntp.dart';

class HistoryController extends GetxController {
  TextEditingController searchHistory = TextEditingController();
  List<PlaybackModel> history = <PlaybackModel>[].obs;

  RxString searchValue = ''.obs;
  var groupedHistory = <String, List<PlaybackModel>>{}.obs;
  var timeNow = DateTime.now().obs;

  // * Services
  HistoryServices historyServices = HistoryServices();

  // * Loading
  RxBool isLoading = false.obs;

  Future<void> getHistory(String query) async {
    isLoading.value = true;

    final result = await historyServices.getHistory(search: query);

    if (result.isSuccess) {
      history = result.resultValue ?? [];
    } else {
      history = [];
    }

    groupedVideo();

    isLoading.value = false;
  }

  void groupedVideo() {
    Map<String, List<PlaybackModel>> tempGroup = {};

    var today = timeNow.value;
    var yesterday = timeNow.value.subtract(const Duration(days: 1));

    for (var data in history) {
      final date = data.createdAt!;
      String key;

      if (isSameDay(date, today)) {
        key = 'Hari Ini';
      } else if (isSameDay(date, yesterday)) {
        key = 'Kemarin';
      } else if (date.isAfter(today.subtract(const Duration(days: 7)))) {
        key = getWeekName(date.weekday);
      } else {
        key = formattedDateOnly(date, format: 'dd MMM yy', isCapitalize: true);
      }

      tempGroup.putIfAbsent(key, () => []).add(data);
    }

    groupedHistory.value = Map.fromEntries(tempGroup.entries.toList()
      ..sort((a, b) =>
          b.value.first.createdAt!.compareTo(a.value.first.createdAt!)));
  }

  @override
  void onInit() async {
    super.onInit();
    getHistory('');
    timeNow.value = await NTP.now();

    searchHistory.addListener(() {
      searchValue.value = searchHistory.text;
    });

    debounce(searchValue, (query) {
      if (query.isNotEmpty) {
        getHistory(query);
      } else {
        getHistory(query);
      }
    }, time: const Duration(seconds: 1));
  }
}
