import 'package:get/get.dart';

class NotificationController extends GetxController {
  var isSemuaSelected = true.obs;
  var isVideoSelected = false.obs;
  var isKomentarSelected = false.obs;
  var isMenyebutkanSelected = false.obs;

  void filterSelected(String filter) {
    final filterMap = {
      'Semua': isSemuaSelected,
      'Video': isVideoSelected,
      'Komentar': isKomentarSelected,
      'Menyebutkan': isMenyebutkanSelected
    };

    filterMap.forEach((key, value) => value.value = false);

    if (filterMap.containsKey(filter)) {
      filterMap[filter]?.value = true;
    }
  }
}
