import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_filter.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_notification_item.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';

import '../controllers/notification_controller.dart';

class NotificationView extends GetView<NotificationController> {
  const NotificationView({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      withIcon: true,
      titleAppBar: 'Notifications',
      customIconPath: "assets/icons/back.svg",
      fontWeight: FontWeight.w300,
      fontSizeAppBar: 36,
      body: Column(
        children: [
          CustomContainer(
            bgColor: Colors.transparent,
            widget: Padding(
                padding: EdgeInsets.all(24.r),
                child: Obx(
                  () => Row(
                    children: [
                      CustomButtonFilter(
                        onPressed: () {
                          controller.filterSelected('Semua');
                        },
                        title: 'Semua',
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        heightButton: 56,
                        isSelected: controller.isSemuaSelected.value,
                      ),
                      16.horizontalSpace,
                      CustomButtonFilter(
                        onPressed: () {
                          controller.filterSelected('Video');
                        },
                        title: 'Video',
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        heightButton: 56,
                        isSelected: controller.isVideoSelected.value,
                      ),
                      16.horizontalSpace,
                      CustomButtonFilter(
                        onPressed: () {
                          controller.filterSelected('Komentar');
                        },
                        title: 'Komentar',
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        heightButton: 56,
                        isSelected: controller.isKomentarSelected.value,
                      ),
                      16.horizontalSpace,
                      CustomButtonFilter(
                        onPressed: () {
                          controller.filterSelected('Menyebutkan');
                        },
                        title: 'Menyebutkan',
                        radius: 12,
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        heightButton: 56,
                        isSelected: controller.isMenyebutkanSelected.value,
                      ),
                    ],
                  ),
                )),
          ),
          const CustomDivider(),
          const Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  CustomNotificationItem(
                    type: 'Penting',
                    length: 1,
                    isApprovalVideo: true,
                    title: 'Judul Video',
                    statusApproved: 'REJECTED',
                    time: '9 Jam yang lalu',
                    description:
                        'ALASAN: Konten tidak sesuai dengan pedoman kami, seperti relevansi terhadap materi pendidikan kadet, kualitas video yang tidak memenuhi standar, atau pelanggaran terhadap kebijakan hak cipta dan etika. Silakan perbaiki konten sesuai pedoman dan unggah kembali.',
                  ),
                  CustomNotificationItem(
                    type: 'Hari Ini',
                    length: 6,
                  ),
                  CustomNotificationItem(
                    type: 'Minggu Ini',
                    length: 10,
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
