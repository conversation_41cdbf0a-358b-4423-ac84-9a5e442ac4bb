import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';

import '../controllers/new_content_controller.dart';

class NewContentView extends GetView<NewContentController> {
  const NewContentView({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      withIcon: true,
      titleAppBar: 'New Content',
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.h),
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5, mainAxisSpacing: 4.h, crossAxisSpacing: 4.w),
          itemCount: 20,
          itemBuilder: (it, idx) {
            if (idx == 0) {
              return GestureDetector(
                onTap: () {},
                child: CustomContainer(
                  widget: Stack(
                    alignment: Alignment.center,
                    children: [
                      Image.asset('assets/images/use_camera.png'),
                      SvgPicture.asset(
                        'assets/icons/camera.svg',
                        width: 94.w,
                        height: 84.h,
                      )
                    ],
                  ),
                ),
              );
            } else {
              return CustomContainer(
                widget: Stack(
                  children: [
                    Image.asset(
                      'assets/images/airplane.png',
                      fit: BoxFit.cover,
                      height: double.infinity,
                    ),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: CustomCountDuration(
                        duration: '99:99',
                        padding: EdgeInsetsDirectional.symmetric(
                            horizontal: 12.w, vertical: 12.h),
                      ),
                    ),
                  ],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}
