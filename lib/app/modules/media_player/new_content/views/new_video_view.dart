import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/new_content/controllers/new_content_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_text.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_switch_button.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_list.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class NewVideoView extends GetView<NewContentController> {
  const NewVideoView({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(NewContentController());
    return CustomScaffold(
      useAppBar: true,
      withIcon: true,
      titleAppBar: 'New Video',
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 32.h),
              child: Column(
                children: [
                  CustomContainer(
                    width: double.infinity,
                    height: 620,
                    radius: 12,
                    bgColor: secondBlueColor.withOpacity(0.15),
                    widget: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/icons/cloud_upload.svg',
                          width: 72.w,
                          height: 72.h,
                          fit: BoxFit.cover,
                        ),
                        8.verticalSpace,
                        const CustomTextWigdet(
                          title: 'Unggah Video',
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                        ),
                        8.verticalSpace,
                        const CustomTextWigdet(
                          title: 'Format video: ... (500MB',
                          fontSize: 24,
                          fontWeight: FontWeight.w300,
                        ),
                      ],
                    ),
                  ),
                  32.verticalSpace,
                  CustomFilledButtonWidget(
                    onPressed: () {},
                    title: 'Pilih File',
                    radius: 12,
                    fontSize: 24,
                    fontWeight: FontWeight.w400,
                    withIcon: true,
                    assetName: 'assets/icons/upload.svg',
                    heightButton: 96,
                    widthIcon: 40,
                    heightIcon: 40,
                    bgColor: blueColor,
                    fontColor: whiteColor,
                  ),
                ],
              ),
            ),
            CustomContainer(
              width: double.infinity,
              // height: 1250,
              bgColor: secondBlueColor.withOpacity(0.04),
              widget: Padding(
                padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 40.h),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      CustomTextFieldWidget(
                        colorText: whiteColor,
                        hintText: 'Judul video',
                        colorTextHint: secondWhiteColor.withOpacity(0.5),
                        colorField: secondBlueColor.withOpacity(0.1),
                        radius: 10,
                      ),
                      32.verticalSpace,
                      CustomContainer(
                        bgColor: secondBlueColor.withOpacity(0.1),
                        radius: 12,
                        widget: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextFieldWidget(
                              colorText: whiteColor,
                              hintText: 'Deskripsi video...',
                              colorTextHint: secondWhiteColor.withOpacity(0.5),
                              colorField: secondBlueColor.withOpacity(0),
                              radius: 10,
                              maxLength: controller.maxLength,
                              maxLines: 10,
                              controller: controller.descriptionVideo,
                              onChanged: (value) {
                                controller.onChanged(value);
                              },
                            ),
                            Obx(() => Padding(
                                  padding:
                                      EdgeInsets.only(left: 32.w, bottom: 32.h),
                                  child: CustomTextWigdet(
                                    title:
                                        '${controller.inputText.value.length}/${controller.maxLength}',
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    textColor:
                                        secondWhiteColor.withOpacity(0.5),
                                  ),
                                ))
                          ],
                        ),
                      ),
                      32.verticalSpace,
                      Row(
                        children: [
                          Flexible(
                            child: CustomContainer(
                              height: context.isPortrait ? 550 : 560,
                              radius: 12,
                              bgColor: secondBlueColor.withOpacity(0.15),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 32.w, vertical: 32.h),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomContainer(
                                      width: double.infinity,
                                      height: 335,
                                      bgColor:
                                          secondBlueColor.withOpacity(0.15),
                                      radius: 10,
                                      widget: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            'assets/icons/cloud_upload.svg',
                                            width: 72.w,
                                            height: 72.h,
                                            fit: BoxFit.cover,
                                          ),
                                          8.verticalSpace,
                                          const CustomTextWigdet(
                                            title: 'Unggah gambar mini khusus',
                                            fontSize: 24,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          8.verticalSpace,
                                          const CustomTextWigdet(
                                            title: 'Format gambar: ... (2MB)',
                                            fontSize: 24,
                                            fontWeight: FontWeight.w300,
                                          ),
                                        ],
                                      ),
                                    ),
                                    CustomFilledButtonWidget(
                                      onPressed: () {},
                                      title: 'Pilih File',
                                      radius: 12,
                                      fontSize: 24,
                                      fontWeight: FontWeight.w400,
                                      withIcon: true,
                                      assetName: 'assets/icons/upload.svg',
                                      heightButton: 64,
                                      widthIcon: 40,
                                      heightIcon: 40,
                                      bgColor: blueColor,
                                      fontColor: whiteColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          32.horizontalSpace,
                          Flexible(
                            child: CustomContainer(
                              radius: 12,
                              height: context.isPortrait ? 550 : 560,
                              bgColor: secondBlueColor.withOpacity(0.15),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 32.w, vertical: 32.h),
                                child: Column(
                                  children: [
                                    CustomContainerList(
                                      title: 'Kategori',
                                      withButton: false,
                                      height: context.isPortrait ? 300 : 310,
                                      fontSize: 24,
                                      widget: SizedBox(
                                        height: 200.h,
                                        child: SingleChildScrollView(
                                          scrollDirection: Axis.vertical,
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 3.5.w,
                                                vertical: 10.h),
                                            child: Wrap(
                                                alignment: WrapAlignment.start,
                                                spacing: 10.w,
                                                runSpacing: 15.h,
                                                children: [
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Pelatihan',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Operasi & Misi',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Teknologi',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Operasi & Misi',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Sejarah',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Berita',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Kisah Kehidupan',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Karir',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Komunitas',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Kesehatan',
                                                  ),
                                                  CustomFilledButtonWidget(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    radius: 8,
                                                    fontSize: 18,
                                                    onPressed: () {},
                                                    title: 'Ulasan',
                                                  ),
                                                ]),
                                          ),
                                        ),
                                      ),
                                    ),
                                    32.verticalSpace,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const CustomTextWigdet(
                                          title: 'Privasi',
                                          fontSize: 24,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        CustomContainer(
                                          bgColor:
                                              secondBlueColor.withOpacity(0.1),
                                          radius: 8,
                                          height: 65,
                                          widget: Obx(
                                            () => CustomSwitchButton(
                                              children: [
                                                CustomButtonText(
                                                  title: 'Publik',
                                                  fontSize: 18,
                                                  isSelected:
                                                      controller.isPublic.value,
                                                  onTap: () {
                                                    controller.privateSelected(
                                                        'Publik');
                                                  },
                                                ),
                                                4.horizontalSpace,
                                                CustomButtonText(
                                                  title: 'Pribadi',
                                                  fontSize: 18,
                                                  isSelected: controller
                                                      .isPrivate.value,
                                                  onTap: () {
                                                    controller.privateSelected(
                                                        'Pribadi');
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    16.verticalSpace,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const CustomTextWigdet(
                                          title: 'Komentar',
                                          fontSize: 24,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        CustomContainer(
                                          radius: 8,
                                          bgColor:
                                              secondBlueColor.withOpacity(0.1),
                                          height: 65,
                                          widget: Obx(
                                            () => CustomSwitchButton(
                                              children: [
                                                CustomButtonText(
                                                  title: 'Aktif',
                                                  fontSize: 18,
                                                  isSelected:
                                                      controller.isActive.value,
                                                  onTap: () {
                                                    controller
                                                        .onOffSelected('Aktif');
                                                  },
                                                ),
                                                4.horizontalSpace,
                                                CustomButtonText(
                                                  title: 'Tidak Aktif',
                                                  fontSize: 18,
                                                  isSelected: controller
                                                      .isNonActive.value,
                                                  onTap: () {
                                                    controller.onOffSelected(
                                                        'Tidak Aktif');
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      32.verticalSpace,
                      CustomFilledButtonWidget(
                        onPressed: () {},
                        title: 'Unggah',
                        radius: 12,
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                        heightButton: 96,
                        widthButton: double.infinity,
                        bgColor: blueColor,
                        fontColor: whiteColor,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
