import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NewContentController extends GetxController {
  final TextEditingController descriptionVideo = TextEditingController();
  final int maxLength = 300;

  var inputText = ''.obs;

  // selected privation
  var isPublic = true.obs;
  var isPrivate = false.obs;

  // selected on / off
  var isActive = true.obs;
  var isNonActive = false.obs;

  void onChanged(String text) {
    inputText.value = text;
  }

  void privateSelected(String privation) {
    final privationMap = {
      'Publik': isPublic,
      'Pribadi': isPrivate,
    };

    privationMap.forEach((key, value) => value.value = false);

    if (privation.contains(privation)) {
      privationMap[privation]?.value = true;
    }
  }

  void onOffSelected(String privation) {
    final privationMap = {
      'Aktif': isActive,
      'Tidak Aktif': isNonActive,
    };

    privationMap.forEach((key, value) => value.value = false);

    if (privation.contains(privation)) {
      privationMap[privation]?.value = true;
    }
  }
}
