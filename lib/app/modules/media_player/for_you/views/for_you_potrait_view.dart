import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';

import '../../../../../widgets/components/buttons/custom_button_filter.dart';
import '../../../../../widgets/components/custom_divider.dart';
import '../../../../../widgets/media_player/videos/custom_video_feed.dart';

class ForYouPotraitView extends GetView {
  const ForYouPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
          child: const Row(
            children: [
              CustomButtonFilter(title: 'Semua'),
              CustomButtonFilter(title: 'Video'),
              CustomButtonFilter(title: 'Saluran'),
              CustomButtonFilter(title: 'Playlist'),
              CustomDivider(
                isVertical: true,
                width: 2,
                height: 35,
              ),
              CustomButtonFilter(title: 'Playlist'),
            ],
          ),
        ),
        const CustomDivider(),
        12.verticalSpace,
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  childAspectRatio: 17 / 18,
                  crossAxisSpacing: 24.w,
                  mainAxisSpacing: 24.h),
              itemCount: 50,
              itemBuilder: (it, idx) {
                return const CustomVideoFeed(
                  width: double.infinity,
                );
              },
            ),
          ),
        )
      ],
    );
  }
}
