import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/for_you/views/for_you_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/for_you/views/for_you_potrait_view.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';

import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/custom_scaffold.dart';
import '../controllers/for_you_controller.dart';

class ForYouView extends GetView<ForYouController> {
  const ForYouView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        titleAppBar: 'Untuk Kamu',
        fontWeight: FontWeight.w200,
        withIcon: true,
        customIconPath: 'assets/icons/back.svg',
        fontSizeAppBar: 32,
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/search.svg',
                    title: 'Cari',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifikasi',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {
                    // layoutNav.currentTab.value = 1;
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/search.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Get.toNamed('/notification');
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
              ],
        body: orientation.isLandscape.value
            ? const ForYouLandscapeView()
            : const ForYouPotraitView(),
      ),
    );
  }
}
