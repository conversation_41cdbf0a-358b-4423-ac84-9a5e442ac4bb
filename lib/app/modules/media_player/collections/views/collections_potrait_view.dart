import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_dropdown.dart';
import '../../../../../widgets/components/custom_container.dart';
import '../../../../../widgets/media_player/videos/custom_video_feed.dart';
import '../../../../../widgets/custom_text_wigdet.dart';

class CollectionsPotraitView extends GetView {
  const CollectionsPotraitView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomContainer(
          width: double.infinity,
          height: 100,
          bgColor: secondBlueColor.withOpacity(0.04),
          widget: Padding(
            padding: EdgeInsets.only(right: 32.w),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    Get.back();
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/back.svg',
                    width: 100.w,
                    height: 100.h,
                  ),
                ),
                CustomTextWigdet(
                  title: "Judul koleksi",
                  textColor: secondWhiteColor,
                  fontSize: 32,
                  fontWeight: FontWeight.w700,
                ),
                const Spacer(),
                CustomContainer(
                  height: 64,
                  width: 240,
                  bgColor: secondBlueColor.withOpacity(0.10),
                  borderRadius: BorderRadius.circular(10.r),
                  widget: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/edit.svg',
                        width: 40.w,
                        height: 40.h,
                      ),
                      8.horizontalSpace,
                      CustomTextWigdet(
                        title: "Edit Details",
                        textColor: secondWhiteColor,
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        10.verticalSpace,
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomDropdown(itemsDropdown: [
                    {"title": "Manual", "subtitle": ""},
                    {"title": "Date added", "subtitle": "newest"},
                    {"title": "Date added", "subtitle": "oldest"},
                    {"title": "Most popular", "subtitle": ""},
                    {"title": "Date uploaded", "subtitle": "newest"},
                    {"title": "Date uploaded", "subtitle": "oldest"},
                  ]),
                  24.verticalSpace,
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 24.w,
                        mainAxisSpacing: 24.h,
                        childAspectRatio: 17 / 18),
                    itemCount: 24,
                    itemBuilder: (context, index) {
                      return const CustomVideoFeed(
                        width: 480,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
