import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/collections/views/collections_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/collections/views/collections_potrait_view.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/custom_scaffold.dart';
import '../../../../../widgets/custom_text_wigdet.dart';
import '../controllers/collections_controller.dart';

class CollectionsView extends GetView<CollectionsController> {
  const CollectionsView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        isHamburgerIcon: true,
        withIcon: true,
        customIconPath: "assets/icons/hamburger.svg",
        richTitleAppBar: [
          const WidgetSpan(
              child: CustomTextWigdet(
            title: 'Library ',
            fontSize: 32,
          )),
          WidgetSpan(
              child: CustomTextWigdet(
            title: '/ Collections ',
            fontSize: 32,
            textColor: greyColor,
          )),
        ],
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifikasi',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.toNamed('/notification');
                    },
                    withIcon: true,
                    assetName: 'assets/icons/settings.svg',
                    title: 'Pengaturan',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {
                    Get.toNamed('/notification');
                  },
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                16.horizontalSpace,
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/settings.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
              ],
        body: orientation.isLandscape.value
            ? const CollectionsLandscapeView()
            : const CollectionsPotraitView(),
      ),
    );
  }
}
