import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_collection_card.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../../../../../widgets/components/buttons/custom_dropdown.dart';

class CollectionsLandscapeView extends GetView {
  const CollectionsLandscapeView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            IconButton(
              onPressed: () {
                Get.back();
              },
              icon: SvgPicture.asset(
                'assets/icons/back.svg',
                width: 80.w,
                height: 80.h,
              ),
            ),
            CustomTextWigdet(
              title: "Judul koleksi",
              textColor: secondWhiteColor,
              fontSize: 32,
              fontWeight: FontWeight.w700,
            ),
            CustomContainer(
              height: 64,
              width: 240,
              bgColor: secondBlueColor.withOpacity(0.10),
              borderRadius: BorderRadius.circular(10.r),
              widget: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/icons/edit.svg',
                    width: 40.w,
                    height: 40.h,
                  ),
                  8.horizontalSpace,
                  CustomTextWigdet(
                    title: "Edit Details",
                    textColor: secondWhiteColor,
                    fontSize: 24,
                    fontWeight: FontWeight.w500,
                  ),
                ],
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(left: 24.w, right: 24.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: SizedBox(
                  height: Get.height * 0.84,
                  child: GridView.builder(
                    itemCount: 20,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 14 / 18,
                        mainAxisSpacing: 12.h,
                        crossAxisSpacing: 24.w),
                    itemBuilder: (item, idx) {
                      return CustomCollectionCard(
                        height: 450,
                      );
                    },
                  ),
                ),
              ),
              24.horizontalSpace,
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const CustomDropdown(itemsDropdown: [
                          {"title": "Manual", "subtitle": ""},
                          {"title": "Date added", "subtitle": "newest"},
                          {"title": "Date added", "subtitle": "oldest"},
                          {"title": "Most popular", "subtitle": ""},
                          {"title": "Date uploaded", "subtitle": "newest"},
                          {"title": "Date uploaded", "subtitle": "oldest"},
                        ]),
                        Flexible(
                          child: SizedBox(
                            height: 65.h,
                            width: 950.w,
                            child: CustomTextFieldWidget(
                              colorField: secondBlueColor.withOpacity(0.15),
                              radius: 12,
                              hintText: 'Cari Koleksi',
                              colorTextHint: whiteColor,
                              colorText: whiteColor,
                              contentPadding: EdgeInsetsDirectional.symmetric(
                                  vertical: 10.h, horizontal: 10.w),
                            ),
                          ),
                        ),
                        CustomFilledButtonWidget(
                          onPressed: () {},
                          withIcon: true,
                          onlyIcon: true,
                          assetName: 'assets/icons/mic.svg',
                          widthButton: 64,
                          heightButton: 64,
                          widthIcon: 48,
                          heightIcon: 48,
                          radius: 12,
                          bgColor: secondBlueColor.withOpacity(0.15),
                        ),
                      ],
                    ),
                    32.verticalSpace,
                    SizedBox(
                      height: Get.height * 0.773,
                      child: GridView.builder(
                        shrinkWrap: true,
                        // physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 24.w,
                            mainAxisSpacing: 24.h,
                            childAspectRatio: 17 / 18),
                        itemCount: 24,
                        itemBuilder: (context, index) {
                          return const CustomVideoFeed(
                            width: 480,
                          );
                        },
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}
