import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_dropdown.dart';
import '../../../../../widgets/components/custom_container.dart';
import '../../../../../widgets/components/custom_playlists.dart';
import '../../../../../widgets/components/custom_search_bar.dart';
import '../../../../../widgets/custom_text_wigdet.dart';

class PlaylistsLandscapeView extends GetView {
  const PlaylistsLandscapeView({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const CustomSearchBar(),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 40.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const CustomDropdown(
                itemsDropdown: [
                  {"title": "Manual", "subtitle": ""},
                  {"title": "Date added", "subtitle": "newest"},
                  {"title": "Date added", "subtitle": "oldest"},
                  {"title": "Most popular", "subtitle": ""},
                  {"title": "Date uploaded", "subtitle": "newest"},
                  {"title": "Date uploaded", "subtitle": "oldest"},
                ],
              ),
              CustomContainer(
                width: 250,
                height: 64,
                bgColor: secondBlueColor.withOpacity(0.10),
                borderRadius: BorderRadius.circular(5),
                widget: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        "assets/icons/plus.svg",
                        width: 24.w,
                        height: 24.h,
                      ),
                      16.horizontalSpace,
                      CustomTextWigdet(
                        title: "Daftar Putar Baru",
                        fontSize: 22,
                        fontWeight: FontWeight.w500,
                        textColor: secondWhiteColor,
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        16.verticalSpace,
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 40.w),
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5,
                childAspectRatio: 8.5 / 8,
                crossAxisSpacing: 24.w,
                mainAxisSpacing: 24.h,
              ),
              itemCount: 24,
              itemBuilder: (it, idx) {
                return CustomPlaylists(
                  onTap: () {
                    Get.toNamed('/playlist');
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
