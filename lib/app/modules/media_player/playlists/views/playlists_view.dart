import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/playlists/views/playlists_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/playlists/views/playlists_potrait_view.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/components/custom_scaffold.dart';
import '../../../../../widgets/custom_text_wigdet.dart';
import '../controllers/playlists_controller.dart';

class PlaylistsView extends GetView<PlaylistsController> {
  const PlaylistsView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        richTitleAppBar: [
          WidgetSpan(
            child: CustomTextWigdet(
              title: "Library",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: whiteColor,
            ),
          ),
          WidgetSpan(
            child: CustomTextWigdet(
              title: " / Playlists",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: secondWhiteColor.withOpacity(0.50),
            ),
          ),
        ],
        fontWeight: FontWeight.w200,
        withIcon: true,
        fontSizeAppBar: 32,
        customIconPath: "assets/icons/hamburger.svg",
        isHamburgerIcon: true,
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifications',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/settings.svg',
                    title: 'Setting',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/settings.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
              ],
        body: orientation.isLandscape.value
            ? const PlaylistsLandscapeView()
            : const PlaylistsPotraitView(),
      ),
    );
  }
}
