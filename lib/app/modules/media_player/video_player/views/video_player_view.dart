import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_potrait_view.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import '../controllers/video_player_controller.dart';

class VideoPlayerView extends GetView<VideoController> {
  VideoPlayerView({super.key}) {
    // Pastikan OrientationController terdaftar
    if (!Get.isRegistered<OrientationController>()) {
      Get.put(OrientationController());
    }
  }

  // Gunakan getter untuk mendapatkan controller yang sudah terdaftar
  OrientationController get orientation => Get.find<OrientationController>();

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => CustomScaffold(
        body: orientation.isLandscape.value
            ? VideoPlayerLandscapeView(landscape: orientation.isLandscape.value)
            : VideoPlayerPotraitView(),
      ),
    );
  }
}
