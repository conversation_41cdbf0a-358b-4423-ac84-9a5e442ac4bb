import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/history/controllers/history_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playback_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/controllers/video_player_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_player.dart';
import 'package:skeletonizer/skeletonizer.dart';

class VideoPlayerLandscapeView extends GetView<VideoController> {
  final bool landscape;
  VideoPlayerLandscapeView({super.key, required this.landscape});

  // Gunakan find untuk mendapatkan controller yang sudah diinisialisasi oleh binding
  VideoController get videoPlayerController => Get.find<VideoController>();
  final dashboardController = Get.find<DashboardController>();

  // Helper method untuk safe argument access
  T? _getArgument<T>(String key, [T? defaultValue]) {
    try {
      return Get.arguments?[key] ?? defaultValue;
    } catch (e) {
      LogService.log.e('Error accessing argument $key: $e');
      return defaultValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Pastikan video dihentikan dan controller dilepas sebelum kembali
        if (Get.isRegistered<VideoController>()) {
          controller.stopVideo();
          // controller.socket.disconnect();
          Get.delete<VideoController>();
        }
        if (Get.isRegistered<DashboardController>()) {
          Get.find<DashboardController>().refreshPage();
        }
        if (Get.isRegistered<PlaybackController>()) {
          Get.find<PlaybackController>().loadPlaybacks();
        }
        if (Get.isRegistered<HistoryController>()) {
          Get.find<HistoryController>().getHistory('');
        }
        // Get.to(() => const BottomNavBar(choosenScreen: 'Media Player'));
        Get.back();
        return true;
      },
      child: RefreshIndicator(
        onRefresh: () async {
          try {
            videoPlayerController.initVideo();
          } catch (e) {
            LogService.log.e('Error refreshing video: $e');
          }
        },
        child: SizedBox(
          height: Get.height,
          child: ListView(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: CustomVideoPlayer(
                      title:
                          _getArgument<String>('videoTitle', 'Video Title') ??
                              'Video Title',
                      uploaderName: 'Uploader Name',
                      uploaderHandle: '@uploadercallsign',
                      thumbnail: 'assets/images/airplane.png',
                      views: _getArgument<int>('totalView', 0) ?? 0,
                      comments: 5,
                      tags:
                          _getArgument<List>('videoTags')?.cast<String>() ?? [],
                      videoPlayerController: videoPlayerController,
                      paddingColumn: EdgeInsets.symmetric(horizontal: 10.w),
                    ),
                  ),
                  SizedBox(
                    width: 800.w,
                    height: Get.height - 100.h,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Expanded(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 40.w),
                          child: Obx(
                            () {
                              final datas = dashboardController.listRecommVod;
                              return Skeletonizer(
                                enabled: dashboardController
                                    .isLoadingRecommVod.value,
                                child: GridView.builder(
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 1,
                                    childAspectRatio: 16 / 10,
                                    crossAxisSpacing: 24.w,
                                    mainAxisSpacing: 24.h,
                                  ),
                                  itemCount:
                                      datas.isNotEmpty ? datas.length : 6,
                                  itemBuilder: (context, index) {
                                    final item =
                                        datas.isNotEmpty ? datas[index] : null;
                                    return GestureDetector(
                                      onTap: () async {
                                        if (item != null) {
                                          LogService.log.i(
                                              'Video ID: ${item.id}, Video Title: ${item.title}');

                                          // Stop current video before navigation
                                          if (Get.isRegistered<
                                              VideoController>()) {
                                            final currentController =
                                                Get.find<VideoController>();
                                            currentController.stopVideo();
                                            await Future.delayed(const Duration(
                                                milliseconds: 100));
                                            currentController.onClose();
                                            Get.delete<VideoController>();
                                          }

                                          // Navigate with valid arguments
                                          Get.to(
                                            () => VideoPlayerView(),
                                            binding: VideoPlayerBinding(),
                                            arguments: {
                                              "videoUrl": item.videoUrl,
                                              "videoId": item.id,
                                              "totalLike": item.totalLike,
                                              "totalDislike": item.totalDislike,
                                              "totalRating": item.totalRating,
                                              "totalView": item.totalView,
                                              "videoSourceUrl":
                                                  item.videoSourceUrl,
                                              "videoTitle": item.title,
                                              "videoTags":
                                                  item.tagNames.cast<String>(),
                                            },
                                            preventDuplicates: false,
                                          );
                                        } else {
                                          LogService.log.e(
                                              'Cannot navigate: item is null');
                                        }
                                      },
                                      child: CustomVideoFeed(
                                        width: double.infinity,
                                        thumbnailUrl: item?.thumbnailUrl ?? '',
                                        duration:
                                            formatDuration(item?.duration),
                                        titleVideo: item?.title,
                                        totalView: item?.totalView,
                                        uploadDate: timeRange(
                                          dashboardController.timeNow.value,
                                          item?.uploadDate ??
                                              dashboardController.timeNow.value,
                                        ),
                                        tags: item?.tagNames.cast<String>(),
                                        uploaderName: item?.uploader?.pasisName,
                                        userName: item?.uploader?.username,
                                      ),
                                    );
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  // SizedBox(
                  //   height: Get.height / 2.4,
                  //   child: Padding(
                  //     padding: EdgeInsets.symmetric(horizontal: 40.w),
                  //     child: Obx(
                  //       () {
                  //         final datas = dashboardController.listRecommVod;
                  //         return Skeletonizer(
                  //           enabled:
                  //               dashboardController.isLoadingRecommVod.value,
                  //           child: GridView.builder(
                  //             shrinkWrap: true,
                  //             gridDelegate:
                  //                 SliverGridDelegateWithFixedCrossAxisCount(
                  //                     crossAxisCount: 3,
                  //                     childAspectRatio: 16 / 18,
                  //                     crossAxisSpacing: 24.w,
                  //                     mainAxisSpacing: 24.h),
                  //             itemCount: datas.isNotEmpty ? datas.length : 6,
                  //             itemBuilder: (it, idx) {
                  //               final item =
                  //                   datas.isNotEmpty ? datas[idx] : null;
                  //               return GestureDetector(
                  //                 onTap: () async {
                  //                   // Hanya navigasi jika item tidak null
                  //                   if (item != null) {
                  //                     LogService.log.i(
                  //                         'Video ID: ${item.id}, Video Title: ${item.title}');
                  //                     // Pastikan video dihentikan dan controller dilepas sebelum navigasi
                  //                     if (Get.isRegistered<VideoController>()) {
                  //                       final currentController =
                  //                           Get.find<VideoController>();
                  //                       currentController.stopVideo();
                  //                       await Future.delayed(
                  //                           const Duration(milliseconds: 100));
                  //                       currentController.onClose();
                  //                       Get.delete<VideoController>();
                  //                     }

                  //                     // Navigasi dengan argumen yang valid
                  //                     Get.to(
                  //                       () => VideoPlayerView(),
                  //                       binding: VideoPlayerBinding(),
                  //                       arguments: {
                  //                         "videoUrl": item.videoUrl,
                  //                         "videoId": item.id,
                  //                         "totalLike": item.totalLike,
                  //                         "totalDislike": item.totalDislike,
                  //                         "totalRating": item.totalRating,
                  //                         "totalView": item.totalView,
                  //                         "videoSourceUrl": item.videoSourceUrl,
                  //                         "videoTitle": item.title,
                  //                         "videoTags":
                  //                             item.tagNames.cast<String>(),
                  //                       },
                  //                       preventDuplicates: false,
                  //                     );
                  //                   } else {
                  //                     LogService.log
                  //                         .e('Cannot navigate: item is null');
                  //                   }
                  //                 },
                  // child: CustomVideoFeed(
                  //   width: double.infinity,
                  //   thumbnailUrl: item?.thumbnailUrl ?? '',
                  //   duration: formatDuration(item?.duration),
                  //   titleVideo: item?.title,
                  //   totalView: item?.totalView,
                  //   uploadDate: timeRange(
                  //       dashboardController.timeNow.value,
                  //       item?.uploadDate ??
                  //           dashboardController.timeNow.value),
                  //   tags: item?.tagNames.cast<String>(),
                  //   uploaderName: item?.uploader?.pasisName,
                  //   userName: item?.uploader?.username,
                  // ),
                  //               );
                  //             },
                  //           ),
                  //         );
                  //       },
                  //     ),
                  //   ),
                  // ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
