import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_collection_card.dart';
import 'package:mides_skadik/widgets/components/custom_search_bar.dart';
import 'package:mides_skadik/widgets/components/custom_section.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class BookmarksLandscapeView extends GetView {
  final bool landscape;
  const BookmarksLandscapeView({super.key, required this.landscape});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const CustomSearchBar(),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextWigdet(
                        title: "Recently Saved",
                        fontSize: 32,
                        fontWeight: FontWeight.w600,
                        textColor: whiteColor,
                      ),
                      32.verticalSpace,
                      SizedBox(
                        height: context.isPortrait ? 500.h : 550.h,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: 20,
                          itemBuilder: (it, idx) {
                            return Padding(
                              padding: EdgeInsets.only(right: 24.h),
                              child: const CustomVideoFeed(),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                16.verticalSpace,
                CustomSection(
                  height: 96,
                  width: double.infinity,
                  sectionTitle: 'Collection',
                  sectionTitleButton: 'New Collection',
                  bgColor: secondBlueColor.withOpacity(0.04),
                  onPressed: () {},
                  padding: EdgeInsets.zero,
                  paddingChild: EdgeInsets.symmetric(horizontal: 40.w),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      childAspectRatio: 18 / 23,
                      crossAxisSpacing: 8,
                    ),
                    itemCount: 20,
                    itemBuilder: (context, idx) {
                      return GestureDetector(
                        onTap: () {
                          Get.toNamed('/collections');
                        },
                        child: CustomCollectionCard(
                          height: 555,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
