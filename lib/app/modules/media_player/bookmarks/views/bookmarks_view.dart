import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/bookmarks/views/bookmarks_landscape_view.dart';
import 'package:mides_skadik/app/modules/media_player/bookmarks/views/bookmarks_potrait_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/bookmarks_controller.dart';

class BookmarksView extends GetView<BookmarksController> {
  const BookmarksView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = Get.find<OrientationController>();
    return Obx(
      () => CustomScaffold(
        useAppBar: true,
        richTitleAppBar: [
          WidgetSpan(
            child: CustomTextWigdet(
              title: "Library",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: whiteColor,
            ),
          ),
          WidgetSpan(
            child: CustomTextWigdet(
              title: " / Bookmarks",
              fontSize: 32,
              fontWeight: FontWeight.w300,
              textColor: secondWhiteColor.withOpacity(0.50),
            ),
          ),
        ],
        fontWeight: FontWeight.w200,
        withIcon: true,
        fontSizeAppBar: 32,
        customIconPath: "assets/icons/hamburger.svg",
        isHamburgerIcon: true,
        actions: orientation.isLandscape.value
            ? [
                Padding(
                  padding: EdgeInsets.only(right: 16.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/notification.svg',
                    title: 'Notifications',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: CustomFilledButtonWidget(
                    onPressed: () {},
                    withIcon: true,
                    assetName: 'assets/icons/settings.svg',
                    title: 'Setting',
                    fontSize: 22,
                    heightIcon: 32,
                    heightButton: 64,
                    bgColor: secondBlueColor.withOpacity(0.25),
                    fontColor: whiteColor,
                    radius: 10,
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                  ),
                ),
              ]
            : [
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/notification.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
                IconButton(
                  onPressed: () {},
                  icon: SvgPicture.asset(
                    'assets/icons/settings.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
                ),
              ],
        body: orientation.isLandscape.value
            ? BookmarksLandscapeView(landscape: orientation.isLandscape.value)
            : const BookmarksPotraitView(),
      ),
    );
  }
}

//     return CustomScaffold(
//       withIcon: true,
//       isHamburgerIcon: true,
//       useAppBar: true,
//       titleAppBar: 'Library / Bookmarks',
//       fontSizeAppBar: 32,
//       fontWeight: FontWeight.w300,
//       actions: [
//         IconButton(
//           onPressed: () {
//             Get.back();
//           },
//           icon: SvgPicture.asset(
//             'assets/icons/notification.svg',
//             width: 48.w,
//             height: 48.h,
//           ),
//         ),
//         16.horizontalSpace,
//         IconButton(
//           onPressed: () {},
//           icon: SvgPicture.asset(
//             'assets/icons/settings.svg',
//             width: 48.w,
//             height: 48.h,
//           ),
//         ),
//       ],
//       body: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           CustomContainer(
//             width: double.infinity,
//             height: 100,
//             bgColor: secondBlueColor.withOpacity(0.04),
//             widget: Row(
//               children: [
//                 IconButton(
//                   onPressed: () {
//                     Get.back();
//                   },
//                   icon: SvgPicture.asset(
//                     'assets/icons/back.svg',
//                     width: 100.w,
//                     height: 100.h,
//                   ),
//                 ),
//                 CustomTextWigdet(
//                   title: "Penanda",
//                   textColor: secondWhiteColor,
//                   fontSize: 32,
//                   fontWeight: FontWeight.w700,
//                 ),
//               ],
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.h),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 CustomTextWigdet(
//                   title: "Baru saja disimpan",
//                   textColor: secondWhiteColor,
//                   fontSize: 32,
//                   fontWeight: FontWeight.w700,
//                 ),
//                 32.verticalSpace,
//                 SizedBox(
//                   height: context.isPortrait ? 500.h : 550.h,
//                   child: ListView.builder(
//                     scrollDirection: Axis.horizontal,
//                     itemCount: 20,
//                     itemBuilder: (it, idx) {
//                       return Row(
//                         children: [
//                           Padding(
//                             padding: EdgeInsets.only(right: 24.h),
//                             child: const CustomVideoFeed(),
//                           ),
//                         ],
//                       );
//                     },
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           10.verticalSpace,
//           CustomContainer(
//             width: double.infinity,
//             height: 100,
//             bgColor: secondBlueColor.withOpacity(0.04),
//             widget: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 40.w),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   CustomTextWigdet(
//                     title: "Koleksi",
//                     textColor: secondWhiteColor,
//                     fontSize: 32,
//                     fontWeight: FontWeight.w700,
//                   ),
//                   CustomContainer(
//                     height: 64,
//                     width: 240,
//                     bgColor: secondBlueColor.withOpacity(0.10),
//                     borderRadius: BorderRadius.circular(12.r),
//                     widget: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         SvgPicture.asset(
//                           'assets/icons/icon_plus.svg',
//                           width: 40.w,
//                           height: 40.h,
//                         ),
//                         8.horizontalSpace,
//                         CustomTextWigdet(
//                           title: "Lihat Semua",
//                           textColor: secondWhiteColor,
//                           fontSize: 24,
//                           fontWeight: FontWeight.w500,
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           Expanded(
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 32.w),
//               child: GridView.builder(
//                 physics: const NeverScrollableScrollPhysics(),
//                 gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//                   crossAxisCount: 3,
//                   childAspectRatio: 11 / 18,
//                 ),
//                 itemCount: 6,
//                 itemBuilder: (it, idx) {
//                   return GestureDetector(
//                     onTap: () {
//                       Get.to(() => const CollectionView());
//                     },
//                     child: CustomCollectionCard(
//                       height: 550,
//                     ),
//                   );
//                 },
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
