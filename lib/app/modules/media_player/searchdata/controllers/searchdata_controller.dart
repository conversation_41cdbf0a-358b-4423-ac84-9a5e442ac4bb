import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/browse/search_logs_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/category/media_player_category_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/services/media_player/browse/vod_browse_service.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/services/media_player/category/media_player_category_service.dart';
import 'package:mides_skadik/app/data/services/media_player/dashboard/vod_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:ntp/ntp.dart';

class SearchdataController extends GetxController {
  TextEditingController searchController = TextEditingController();

  List<SearchLogsModel> searchLogs = <SearchLogsModel>[].obs;
  List<SearchLogsModel> trending = <SearchLogsModel>[].obs;
  List<VodModel> searchVod = <VodModel>[].obs;
  List<VodModel> trendingVod = <VodModel>[].obs;
  List<String> selectedTag = <String>[].obs;
  List<MediaPlayerCategory> categories = <MediaPlayerCategory>[].obs;

  VodBrowseService browseService = VodBrowseService();
  VodService vodService = VodService();
  MediaPlayerCategoryService categoryService = MediaPlayerCategoryService();
  FocusNode focusNode = FocusNode();
  ScrollController scrollController = ScrollController();

  RxString textSearch = ''.obs;
  RxString tagID = ''.obs;
  RxString selectedSort = ''.obs;
  RxString sortValueForApi = ''.obs;
  RxBool isFocus = false.obs;
  RxInt page = 1.obs;

  var timeNow = DateTime.now().obs;

  // * Loading
  RxBool isLoading = false.obs;
  RxBool isLoadingTrending = false.obs;
  RxBool isLoadingCategory = false.obs;

  @override
  void onInit() async {
    super.onInit();
    timeNow.value = await NTP.now();
    await getHistoryLogs();
    await getTrendingLogs();
    await getListCategory();
    await getTrendingVideo();

    searchController.addListener(() {
      textSearch.value = searchController.text;
    });

    focusNode.addListener(() {
      isFocus.value = focusNode.hasFocus;
    });

    debounce<String>(
      textSearch,
      (query) {
        searchVod.clear();
        if (query.isNotEmpty) {
          searchVideo(
              query: textSearch.value,
              tagId: tagID.value,
              sortBy: sortValueForApi.value.toLowerCase());
        } else {
          getHistoryLogs();
          getTrendingLogs();
        }
      },
      time: const Duration(seconds: 1),
    );

    scrollController.addListener(_loadMoreData);
  }

  // TODO: Get History Logs
  Future<void> getHistoryLogs() async {
    isLoading.value = true;

    final result = await browseService.getSearchLogs();

    if (result.isSuccess) {
      searchLogs = result.resultValue ?? [];
    } else {
      searchLogs = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Trending
  Future<void> getTrendingLogs() async {
    isLoading.value = true;

    final result = await browseService.getSearchLogs(filter: 'trending');

    if (result.isSuccess) {
      trending = result.resultValue ?? [];
    } else {
      trending = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Trending Video
  Future<void> getTrendingVideo({String? sortBy}) async {
    isLoadingTrending.value = true;

    final result = await vodService.getPopularVod(limit: 12, sortBy: sortBy);

    if (result.isSuccess) {
      trendingVod = result.resultValue ?? [];
    } else {
      trendingVod = [];
    }

    isLoadingTrending.value = false;
  }

  // TODO: Search Video
  Future<void> searchVideo(
      {String? query = '', String? tagId = '', String? sortBy = ''}) async {
    isLoading.value = true;

    final result = await browseService.searchVod(
        search: query, tagId: tagId, sortBy: sortBy);

    if (result.isSuccess) {
      searchVod = result.resultValue ?? [];
    } else {
      searchVod = [];
    }

    isLoading.value = false;
  }

  // TODO: Get Category
  Future<void> getListCategory() async {
    isLoadingCategory.value = true;

    final result = await categoryService.getList();
    if (result.isSuccess) {
      categories = result.resultValue ?? [];
    } else {
      categories = [];
    }

    isLoadingCategory.value = false;
  }

  // TODO: Select Tags and Save to variable
  Future<void> selectTag(String tagId) async {
    selectedTag.contains(tagId)
        ? selectedTag.remove(tagId)
        : selectedTag.add(tagId);

    tagID.value = selectedTag.join(',');

    if (textSearch.value.isEmpty) return;

    await searchVideo(
        query: textSearch.value,
        tagId: tagID.value,
        sortBy: sortValueForApi.value.toLowerCase());
  }

  // TODO: Select sort from dropdown
  // * Select Sort
  void selectSort(String selected, String selectForApi) {
    sortValueForApi.value = selectForApi;

    // page.value = 1;
    // isLastPage.value = false;
    selectedSort.value = selected;

    if (textSearch.value.isNotEmpty) {
      searchVideo(
          query: textSearch.value,
          tagId: tagID.value,
          sortBy: sortValueForApi.value.toLowerCase());
      return;
    }

    getTrendingVideo(sortBy: sortValueForApi.value.toLowerCase());
  }

  // TODO: Clear Selected Tags
  Future<void> clearSelectedTags() async {
    selectedTag.clear();
    if (textSearch.value.isEmpty) return;
    await searchVideo(
        query: textSearch.value,
        tagId: tagID.value,
        sortBy: sortValueForApi.value.toLowerCase());
  }

  // TODO: Clear Selected Tags
  void clearSelectedSort() {
    selectedSort.value = '';
  }

  // TODO: Load More Data
  void _loadMoreData() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent) {
      LogService.log.i('Load More Data');
    }
  }

  // TODO: Sent Analitic
  Future<void> sentAnaliticVodById(
      {String? vodId = '', String? tagId = ''}) async {
    await vodService.getVodById(vodId: vodId, tagId: tagId);
  }

  // TODO: Clear Text Searc
  void clearSearchText() {
    searchController.clear();
  }
}
