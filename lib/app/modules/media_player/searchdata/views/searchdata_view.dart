import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/browse/search_logs_model.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/controllers/clipping_controller.dart';
import 'package:mides_skadik/app/modules/media_player/searchdata/views/history_trending_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_dropdown.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/components/custom_search_bar.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';
import 'package:mides_skadik/widgets/media_player/search_data/search_data_grid.dart';
import 'package:mides_skadik/widgets/media_player/search_data/search_data_list.dart';
import '../controllers/searchdata_controller.dart';

class SearchdataView extends GetView<SearchdataController> {
  const SearchdataView({super.key});
  @override
  Widget build(BuildContext context) {
    Get.put(SearchdataController());
    Get.put(ClippingController());
    final orientation = Get.find<OrientationController>();
    return CustomScaffold(
      body: SafeArea(
        child: Column(
          children: [
            Obx(
              () => CustomSearchBar(
                controller: controller.searchController,
                isFocus: controller.isFocus.value,
                focusNode: controller.focusNode,
                textSearch: controller.textSearch.value,
                onBack: () {
                  controller.clearSearchText();
                  controller.focusNode.unfocus();
                  controller.isFocus.value = false;
                },
              ),
            ),
            Obx(
              () {
                if (!controller.isFocus.value &&
                    controller.textSearch.value.isEmpty) {
                  return const SizedBox.shrink();
                }

                final isSelected = controller.selectedTag.isEmpty;
                return Padding(
                  padding: EdgeInsets.all(24.r),
                  child: Row(
                    spacing: 18,
                    children: [
                      CustomFilledButtonWidget(
                        onPressed: isSelected
                            ? () {}
                            : () {
                                controller.clearSelectedTags();
                              },
                        title: 'All',
                        widthButton: 73,
                        heightButton: 56,
                        radius: 8,
                        fontColor: whiteColor,
                        bgColor: baseBlueColor,
                        bgSelectedColor: whiteColor,
                        isSelected: isSelected,
                      ),
                      const CustomDivider(
                        isVertical: false,
                        height: 35,
                        width: 2,
                      ),
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Obx(
                            () {
                              if (controller.isLoadingCategory.value) {
                                return const CustomLoadingWidget();
                              }
                              return Wrap(
                                direction: Axis.horizontal,
                                runAlignment: WrapAlignment.center,
                                spacing: 10,
                                children: [
                                  ...controller.categories.map(
                                    (e) {
                                      final isSelected =
                                          controller.selectedTag.contains(e.id);
                                      return CustomFilledButtonWidget(
                                        onPressed: () {
                                          controller.selectTag(e.id!);
                                        },
                                        title: e.name ?? '',
                                        radius: 8,
                                        heightButton: 56,
                                        padding: const EdgeInsets.only(
                                            left: 10, right: 10),
                                        fontColor: whiteColor,
                                        bgColor: baseBlueColor,
                                        bgSelectedColor: whiteColor,
                                        isSelected: isSelected,
                                      );
                                    },
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      )
                    ],
                  ),
                );
              },
            ),
            const CustomDivider(),
            24.verticalSpace,
            Obx(
              () {
                final dataHistoryLogs = controller.searchLogs.take(5).toList();
                final dataTrendingLogs = controller.trending.take(5).toList();
                final dataSearch = controller.searchVod;
                final dataTrending = controller.trendingVod;
                final sortSelected = controller.selectedSort.value;

                if (controller.isLoading.value && controller.isFocus.value) {
                  return SizedBox(
                      height: orientation.isLandscape.value
                          ? Get.height * .7
                          : Get.height * 0.8,
                      child: const CustomLoadingWidget());
                }

                if (dataSearch.isEmpty &&
                    !controller.isLoading.value &&
                    controller.textSearch.isNotEmpty) {
                  return SizedBox(
                    height: orientation.isLandscape.value
                        ? Get.height * .7
                        : Get.height * 0.8,
                    child: const Center(
                      child: CustomDataEmpty(
                          title: 'Tidak ada data sesuai keyword',
                          assetName: 'assets/icons/no_data_video.svg'),
                    ),
                  );
                }

                if (controller.textSearch.isEmpty) {
                  if (controller.isFocus.value) {
                    return SizedBox(
                      height: orientation.isLandscape.value
                          ? Get.height * 0.8
                          : 1500.h,
                      child: HistoryTrendingView(
                        isLandscape: orientation.isLandscape.value,
                        dataHistory: dataHistoryLogs,
                        dataTrending: dataTrendingLogs,
                        onTapHistory: (SearchLogsModel item) {
                          controller.searchController.text = item.searchValue;
                        },
                        onTapTrending: (SearchLogsModel item) {
                          controller.searchController.text = item.searchValue;
                        },
                      ),
                    );
                  } else {
                    controller.clearSelectedSort();
                    return Padding(
                      padding: EdgeInsets.all(24.r),
                      child: Column(
                        spacing: 24,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const CustomTextWigdet(
                            title: 'Kategori Favorit',
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                          ),
                          const CustomTextWigdet(
                            title: 'Trending',
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                          ),
                          CustomDropdown(
                            onSelected: (val) {
                              final item = val['subtitle']!.isEmpty
                                  ? val['subtitle']
                                  : '${val['title']} / ${val['subtitle']}';
                              final item1 = val['subtitle'];

                              controller.selectSort(item!, item1!);
                            },
                            textSelected: CustomTextWigdet(
                              title: sortSelected.isEmpty
                                  ? 'Trending'
                                  : sortSelected,
                              textColor: secondWhiteColor,
                              fontSize: 22,
                              fontWeight: FontWeight.w500,
                            ),
                            itemsDropdown: const [
                              {"title": "Trending", "subtitle": ""},
                              {"title": "View count", "subtitle": "highest"},
                              {"title": "View count", "subtitle": "lowest"},
                              {"title": "Date uploaded", "subtitle": "newest"},
                              {"title": "Date uploaded", "subtitle": "oldest"},
                            ],
                          ),
                          SizedBox(
                            height: orientation.isLandscape.value
                                ? Get.height * .6
                                : Get.height * .7,
                            child: controller.isLoadingTrending.value
                                ? const CustomLoadingWidget()
                                : GridView.builder(
                                    controller: controller.scrollController,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount:
                                          orientation.isLandscape.value ? 4 : 3,
                                      childAspectRatio:
                                          orientation.isLandscape.value
                                              ? 15 / 16
                                              : 19 / 24,
                                      mainAxisSpacing: 10,
                                      crossAxisSpacing: 10,
                                    ),
                                    itemBuilder: (ctx, idx) {
                                      final item = dataTrending[idx];
                                      return SearchDataGrid(
                                          item: item,
                                          index: idx,
                                          controller: controller);
                                    },
                                    itemCount: dataTrending.length,
                                  ),
                          ),
                        ],
                      ),
                    );
                  }
                } else {
                  return dataSearch.isEmpty && controller.textSearch.isEmpty
                      ? const CustomLoadingWidget()
                      : Padding(
                          padding: EdgeInsets.only(
                              left: 24.w, right: 24.w, bottom: 16.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomDropdown(
                                onSelected: (val) {
                                  final item = val['subtitle']!.isEmpty
                                      ? val['subtitle']
                                      : '${val['title']} / ${val['subtitle']}';
                                  final item1 = val['subtitle'];

                                  controller.selectSort(item!, item1!);
                                },
                                textSelected: CustomTextWigdet(
                                  title: sortSelected.isEmpty
                                      ? 'Date uploaded / newest'
                                      : sortSelected,
                                  textColor: secondWhiteColor,
                                  fontSize: 22,
                                  fontWeight: FontWeight.w500,
                                ),
                                itemsDropdown: const [
                                  {
                                    "title": "View count",
                                    "subtitle": "highest"
                                  },
                                  {"title": "View count", "subtitle": "lowest"},
                                  {"title": "Rating", "subtitle": "highest"},
                                  {"title": "Rating", "subtitle": "lowest"},
                                  {
                                    "title": "Date uploaded",
                                    "subtitle": "newest"
                                  },
                                  {
                                    "title": "Date uploaded",
                                    "subtitle": "oldest"
                                  },
                                ],
                              ),
                              16.verticalSpace,
                              SizedBox(
                                height: orientation.isLandscape.value
                                    ? Get.height * 0.7
                                    : Get.height * 0.8,
                                child: orientation.isLandscape.value
                                    ? GridView.builder(
                                        itemCount: dataSearch.length,
                                        controller: controller.scrollController,
                                        gridDelegate:
                                            const SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: 4,
                                                childAspectRatio: 14 / 15,
                                                mainAxisSpacing: 24,
                                                crossAxisSpacing: 24),
                                        itemBuilder: (ctx, idx) {
                                          final item = dataSearch[idx];
                                          return SearchDataGrid(
                                              item: item,
                                              index: idx,
                                              controller: controller);
                                        },
                                      )
                                    : ListView.builder(
                                        itemCount: dataSearch.length,
                                        controller: controller.scrollController,
                                        itemBuilder: (ctx, idx) {
                                          final item = dataSearch[idx];
                                          return Padding(
                                              padding:
                                                  EdgeInsets.only(bottom: 16.h),
                                              child: SearchDataList(
                                                  item: item,
                                                  index: idx,
                                                  controller: controller));
                                        },
                                      ),
                              ),
                            ],
                          ),
                        );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
