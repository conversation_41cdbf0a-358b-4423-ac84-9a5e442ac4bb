import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/browse/search_logs_model.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_section.dart';
import 'package:mides_skadik/widgets/media_player/custom_history_search.dart';

class HistoryTrendingView extends GetView {
  final bool? isLandscape;
  final List<SearchLogsModel>? dataHistory;
  final List<SearchLogsModel>? dataTrending;
  final void Function(SearchLogsModel data)? onTapHistory;
  final void Function(SearchLogsModel data)? onTapTrending;
  const HistoryTrendingView({
    super.key,
    this.dataHistory,
    this.isLandscape,
    this.dataTrending,
    this.onTapHistory,
    this.onTapTrending,
  });
  @override
  Widget build(BuildContext context) {
    // final orientation = Get.find<OrientationController>();
    return SingleChildScrollView(
      child: Column(
        children: [
          CustomSection(
            sectionTitle: 'History',
            bgColor: baseBlueColor.withValues(alpha: .5),
            padding: EdgeInsets.zero,
            paddingChild:
                EdgeInsets.symmetric(horizontal: 40.w, vertical: 26.h),
          ),
          SizedBox(
            width: double.maxFinite,
            height: 560.h,
            child: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: dataHistory?.length,
              itemBuilder: (ctx, idx) {
                return CustomHistorySearch(
                  item: dataHistory?[idx].searchValue,
                  onTap: () => onTapHistory!(dataHistory![idx]),
                );
              },
            ),
          ),
          CustomSection(
            sectionTitle: 'Trending',
            bgColor: baseBlueColor.withValues(alpha: .5),
            padding: EdgeInsets.zero,
            paddingChild:
                EdgeInsets.symmetric(horizontal: 40.w, vertical: 26.h),
          ),
          SizedBox(
            width: double.maxFinite,
            height: 560.h,
            child: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: dataTrending?.length,
              itemBuilder: (ctx, idx) {
                return CustomHistorySearch(
                  item: dataTrending?[idx].searchValue,
                  onTap: () => onTapTrending!(dataTrending![idx]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
