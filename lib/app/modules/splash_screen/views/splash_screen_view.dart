import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';

import '../controllers/splash_screen_controller.dart';

// ignore: must_be_immutable
class SplashScreenView extends GetView<SplashScreenController> {
  SplashScreenView({super.key});

  @override
  SplashScreenController controller = Get.put(SplashScreenController());

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      body: Center(
        child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 4.w,
              sigmaY: 4.h,
            ),
            child: SizedBox(
              width: Get.width / 2.5,
              height: Get.height / 2.5,
              child: Image.asset(
                'assets/images/logo.png',
                height: 50.h,
              ),
            )),
      ),
    );
  }
}
