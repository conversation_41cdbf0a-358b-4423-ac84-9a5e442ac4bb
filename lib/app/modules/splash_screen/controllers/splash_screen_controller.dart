import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';

class SplashScreenController extends GetxController {
  @override
  void onInit() {
    super.onInit();
    initial();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void initial() async {
    var token = await LocalStorage().get('token');
    debugPrint('Token: $token');
    Future.delayed(const Duration(seconds: 2)).then((val) {
      if (token != null && token.toString() != "") {
        Get.offAllNamed('/choose-screen');
      } else {
        Get.offAllNamed('/login');
      }
    });
  }
}
