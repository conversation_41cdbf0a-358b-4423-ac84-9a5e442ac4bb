import 'dart:async';
import 'dart:math';

import 'package:get/get.dart';
import 'package:mediasfu_mediasoup_client/mediasfu_mediasoup_client.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/signaling/web_socket.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:random_string/random_string.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class ShareScreenController extends GetxController {
  final mediasoupDevice = Rxn<Device>();
  final webSocket = Rxn<WebSocket>();
  final produce = false.obs;
  final sendTransport = Rxn<Transport>();
  final recvTransport = Rxn<Transport>();
  final consume = true.obs;
  final displayName = RxString(nouns[Random.secure().nextInt(2500)]);
  final id = ''.obs;
  final closed = false.obs;
  final roomId = ''.obs;
  final peerId = ''.obs;
  final url = 'wss://$baseWSUrl'.obs;

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    LogService.log.i('🔄 ShareScreenController onClose() called');
    close();
    super.onClose();
  }

  void join() async {
    // Check if already closed
    if (closed.value) {
      LogService.log.w('⚠️ Cannot join: ShareScreenController is closed');
      return;
    }

    LogService.log.i('🔄 Starting ShareScreenController join...');

    if (peerId.value.isEmpty) {
      peerId.value = randomAlpha(8); // Generate ID unik untuk peer ini
      LogService.log.i('Generated Peer ID: ${peerId.value}');
    }
    if (id.value.isEmpty) {
      var user = await LocalStorage().get('user');
      id.value = user['id']; // Generate ID unik untuk room ini by user id
      LogService.log.i('Generated Room ID: ${id.value}');
    }
    webSocket.value = WebSocket(
      peerId: peerId.value,
      roomId: id.value,
      url: url.value,
    );

    LogService.log.i('Join room: https://$baseWSUrl/?roomId=${id.value}');

    webSocket.value!.onOpen = joinRoom;
    webSocket.value!.onFail = () {
      print('WebSocket connection failed');
    };
    webSocket.value!.onDisconnected = () {
      if (sendTransport != null) {
        sendTransport.value!.close();
        sendTransport.value = null;
      }
      if (recvTransport != null) {
        recvTransport.value!.close();
        recvTransport.value = null;
      }
    };
    webSocket.value!.onClose = () {
      if (closed.value) return;

      close();
    };

    webSocket.value!.onRequest = (request, accept, reject) async {
      switch (request['method']) {
        case 'newConsumer':
          {
            if (!consume.value) {
              reject(403, 'I do not want to consume');
              break;
            }
            try {
              recvTransport.value!.consume(
                id: request['data']['id'],
                producerId: request['data']['producerId'],
                kind: RTCRtpMediaTypeExtension.fromString(
                    request['data']['kind']),
                rtpParameters:
                    RtpParameters.fromMap(request['data']['rtpParameters']),
                appData: Map<String, dynamic>.from(request['data']['appData']),
                peerId: request['data']['peerId'],
                accept: accept,
              );
            } catch (error) {
              print('newConsumer request failed: $error');
              throw (error);
            }
            break;
          }
        default:
          break;
      }
    };

    webSocket.value!.onNotification = (notification) async {
      switch (notification['method']) {
        //TODO: todo;
        case 'producerScore':
          {
            break;
          }
        case 'consumerClosed':
          {
            // String consumerId = notification['data']['consumerId'];
            // peersBloc.add(PeerRemoveConsumer(consumerId: consumerId));

            break;
          }
        case 'consumerPaused':
          {
            // String consumerId = notification['data']['consumerId'];
            // peersBloc.add(PeerPausedConsumer(consumerId: consumerId));
            break;
          }

        case 'consumerResumed':
          {
            // String consumerId = notification['data']['consumerId'];
            // peersBloc.add(PeerResumedConsumer(consumerId: consumerId));
            break;
          }

        case 'newPeer':
          {
            // final Map<String, dynamic> newPeer =
            //     Map<String, dynamic>.from(notification['data']);
            // peersBloc.add(PeerAdd(newPeer: newPeer));
            break;
          }

        case 'peerClosed':
          {
            String peerIds = notification['data']['peerId'];
            peerId.value = peerIds;
            // peersBloc.add(PeerRemove(peerId: peerId));
            break;
          }

        default:
          break;
      }
    };
  }

  Future<void> joinRoom() async {
    try {
      // Check if already closed before proceeding
      if (closed.value) {
        LogService.log
            .w('⚠️ Cannot join room: ShareScreenController is closed');
        return;
      }

      LogService.log.i('🔄 Joining room...');
      mediasoupDevice.value = Device();

      dynamic routerRtpCapabilities =
          await webSocket.value!.socket.request('getRouterRtpCapabilities', {});

      print(routerRtpCapabilities);

      final rtpCapabilities = RtpCapabilities.fromMap(routerRtpCapabilities);
      rtpCapabilities.headerExtensions
          .removeWhere((he) => he.uri == 'urn:3gpp:video-orientation');
      await mediasoupDevice.value!.load(routerRtpCapabilities: rtpCapabilities);

      if (mediasoupDevice.value!
                  .canProduce(RTCRtpMediaType.RTCRtpMediaTypeAudio) ==
              true ||
          mediasoupDevice.value!
                  .canProduce(RTCRtpMediaType.RTCRtpMediaTypeVideo) ==
              true) {
        produce.value = true;
      }

      if (produce.value) {
        Map transportInfo =
            await webSocket.value!.socket.request('createWebRtcTransport', {
          'forceTcp': false,
          'producing': true,
          'consuming': false,
          'sctpCapabilities': mediasoupDevice.value!.sctpCapabilities.toMap(),
        });

        sendTransport.value = mediasoupDevice.value!.createSendTransportFromMap(
            transportInfo,
            producerCallback: producerCallback);

        sendTransport.value!.on('connect', (Map data) {
          webSocket.value!.socket
              .request('connectWebRtcTransport', {
                'transportId': sendTransport.value!.id,
                'dtlsParameters': data['dtlsParameters'].toMap(),
              })
              .then(data['callback'])
              .catchError(data['errback']);
        });

        sendTransport.value!.on('produce', (Map data) async {
          try {
            Map response = await webSocket.value!.socket.request(
              'produce',
              {
                'transportId': sendTransport.value!.id,
                'kind': data['kind'],
                'rtpParameters': data['rtpParameters'].toMap(),
                if (data['appData'] != null)
                  'appData': Map<String, dynamic>.from(data['appData'])
              },
            );

            data['callback'](response['id']);
          } catch (error) {
            data['errback'](error);
          }
        });

        sendTransport.value!.on('producedata', (data) async {
          try {
            Map response =
                await webSocket.value!.socket.request('produceData', {
              'transportId': sendTransport.value!.id,
              'sctpStreamParameters': data['sctpStreamParameters'].toMap(),
              'label': data['label'],
              'protocol': data['protocol'],
              'appData': data['appData'],
            });

            data['callback'](response['id']);
          } catch (error) {
            data['errback'](error);
          }
        });
      }

      if (consume.value) {
        Map transportInfo = await webSocket.value!.socket.request(
          'createWebRtcTransport',
          {
            'forceTcp': false,
            'producing': false,
            'consuming': true,
            'sctpCapabilities': mediasoupDevice.value!.sctpCapabilities.toMap(),
          },
        );

        recvTransport.value = mediasoupDevice.value!.createRecvTransportFromMap(
          transportInfo,
          consumerCallback: (Consumer consumer) {
            consumer.on('trackended', () {});
            consumer.on('transportclose', () {});
          },
        );

        recvTransport.value!.on(
          'connect',
          (data) {
            webSocket.value!.socket
                .request(
                  'connectWebRtcTransport',
                  {
                    'transportId': recvTransport.value!.id,
                    'dtlsParameters': data['dtlsParameters'].toMap(),
                  },
                )
                .then(data['callback'])
                .catchError(data['errback']);
          },
        );
      }

      Map response = await webSocket.value!.socket.request('join', {
        'displayName': displayName.value,
        'device': {
          'name': "Flutter",
          'flag': 'flutter',
          'version': '0.8.0',
        },
        'rtpCapabilities': mediasoupDevice.value!.rtpCapabilities.toMap(),
        'sctpCapabilities': mediasoupDevice.value!.sctpCapabilities.toMap(),
      });

      response['peers'].forEach((value) {
        LogService.log.i(
          'PeerAdd: ${value['peerId']}',
        );
      });

      if (produce.value) {
        enableShareScreen();

        sendTransport.value!.on('connectionstatechange', (connectionState) {
          if (connectionState == 'connected') {}
        });
      }
    } catch (error) {
      LogService.log.e('❌ Error joining room: $error');
      close();
    }
  }

  void producerCallback(Producer producer) {
    producer.on('trackended', () {});
  }

  void consumerCallback(Consumer consumer, [dynamic accept]) {
    ScalabilityMode.parse(
        consumer.rtpParameters.encodings.first.scalabilityMode);

    accept({});
  }

  void enableShareScreen() async {
    if (mediasoupDevice.value!
            .canProduce(RTCRtpMediaType.RTCRtpMediaTypeVideo) ==
        false) {
      return;
    }
    MediaStream? videoStream;
    MediaStreamTrack? track;
    try {
      RtpCodecCapability? codec;
      try {
        codec = mediasoupDevice.value!.rtpCapabilities.codecs.firstWhere(
          (RtpCodecCapability c) => c.mimeType.toLowerCase() == 'video/h264',
        );
      } catch (e) {
        codec = mediasoupDevice.value!.rtpCapabilities.codecs.firstWhere(
          (RtpCodecCapability c) => c.mimeType.toLowerCase() == 'video/vp8',
          orElse: () => throw 'no suitable video codec found',
        );
      }

      videoStream = await createScreenShareStream();
      track = videoStream.getVideoTracks().first;

      sendTransport.value!.produce(
        track: track,
        codecOptions: ProducerCodecOptions(
          videoGoogleStartBitrate: 1000,
        ),
        encodings: kIsWeb
            ? [
                RtpEncodingParameters(
                    scalabilityMode: 'S3T3_KEY', scaleResolutionDownBy: 1.0),
              ]
            : [],
        stream: videoStream,
        appData: {
          'source': 'screen',
        },
        source: 'screen',
        codec: codec,
      );
    } catch (error) {
      LogService.log.e('Video production error: $error');
      Get.back();
      if (videoStream != null) {
        await videoStream.dispose();
      }
    }
  }

  void close() {
    if (closed.value) {
      LogService.log.i('🔄 ShareScreenController already closed');
      return;
    }

    LogService.log.i('🔄 Closing ShareScreenController...');
    closed.value = true;

    try {
      // Close WebSocket connection
      if (webSocket.value != null) {
        LogService.log.i('🔄 Closing WebSocket...');
        webSocket.value?.close();
        webSocket.value = null;
      }

      // Close send transport
      if (sendTransport.value != null) {
        LogService.log.i('🔄 Closing send transport...');
        sendTransport.value?.close();
        sendTransport.value = null;
      }

      // Close receive transport
      if (recvTransport.value != null) {
        LogService.log.i('🔄 Closing receive transport...');
        recvTransport.value?.close();
        recvTransport.value = null;
      }

      // Stop screen capture
      try {
        const platform = MethodChannel('com.example.example/screen_share');
        platform.invokeMethod('stopScreenCapture');
        LogService.log.i('🔄 Screen capture stopped');
      } catch (e) {
        LogService.log.w('⚠️ Error stopping screen capture: $e');
      }

      LogService.log.i('✅ ShareScreenController closed successfully');
    } catch (e) {
      LogService.log.e('❌ Error during ShareScreenController close: $e');
    }
  }

  Future<MediaStream> createScreenShareStream() async {
    const platform = MethodChannel('com.example.example/screen_share');

    Map<String, dynamic> mediaConstraints = {
      'audio': false,
      'video': {
        'mandatory': {
          'minWidth': '1280',
          'minHeight': '720',
          'minFrameRate': '30',
        },
        'optional': [],
      },
    };

    try {
      LogService.log.i('Meminta izin screen capture dan memulai service...');
      final bool permissionAndServiceStarted =
          await platform.invokeMethod('startScreenCapture');

      if (!permissionAndServiceStarted) {
        close();
        Get.back();
        throw Exception(
            'Izin screen capture ditolak atau service gagal dimulai.');
      }

      LogService.log
          .i('Izin diberikan & service berjalan, memanggil getDisplayMedia...');
      MediaStream stream = await mediaDevices.getDisplayMedia(mediaConstraints);
      LogService.log.i('getDisplayMedia berhasil.');

      if (stream.getVideoTracks().isNotEmpty) {
        stream.getVideoTracks().first.onEnded = () {
          LogService.log
              .i('Screen sharing dihentikan oleh user (event onEnded).');
          platform.invokeMethod('stopScreenCapture');
        };
      } else {
        LogService.log.i(
            'Peringatan: getDisplayMedia mengembalikan stream tanpa video track.');
        platform.invokeMethod('stopScreenCapture');
        throw Exception('Gagal mendapatkan video track dari screen share.');
      }

      return stream;
    } catch (e) {
      LogService.log.i('Error saat createScreenShareStream: $e');
      try {
        LogService.log.i('Mencoba menghentikan service karena error...');
        await platform.invokeMethod('stopScreenCapture');
      } catch (stopError) {
        LogService.log.i(
            'Error saat menghentikan service setelah error awal: $stopError');
      }
      throw e;
    }
  }

  Future<String> getDesktopMediaSourceId() async {
    Completer<String> completer = Completer<String>();
    try {
      await mediaDevices
          .getDisplayMedia({'video': true}).then((MediaStream stream) {
        stream.getVideoTracks().forEach((MediaStreamTrack track) {
          completer.complete(track.id);
        });
      });
    } catch (error) {
      completer.completeError(error);
    }
    return completer.future;
  }
}
