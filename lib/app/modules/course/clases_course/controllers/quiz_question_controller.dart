import 'dart:async';

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_question_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_review_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/submit_question_model.dart';
import 'package:mides_skadik/app/data/services/course/quiz_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/soket_service.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/share_screen_controller.dart';

class QuizQuestionController extends GetxController {
  var isLoading = false.obs;
  var quizQuestion = const QuizQuestionModel().obs;
  var quizQuestionId = ''.obs;
  var idSelectedQuestion = ''.obs;
  var remainingTime = 0.obs;
  var idRandomQuiz = ''.obs;
  var isLoadingAnswer = false.obs;
  var idStartQuiz = ''.obs;
  var numberQuestion = 1.obs;
  var answer = ''.obs;
  var randomQuestionIds = <String>[].obs;
  var isLoadingSubmitQuestion = false.obs;
  var charactersCount = 0.obs;

  Rx<SubmitQuestionModel> submitQuestion = const SubmitQuestionModel().obs;
  RxList<QuizReviewModel> listQuizReview = <QuizReviewModel>[].obs;
  final essayAnswerController = TextEditingController();
  SocketService socketService = SocketService();

  @override
  void onInit() {
    super.onInit();
    startTimer();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void getQuizQuestionId({String? id, int? index}) async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    idRandomQuiz.value = id ?? '';
    numberQuestion.value = index ?? 1;
    quizQuestion.value = const QuizQuestionModel();
    var response =
        await QuizService().getQuizQuestionId(id: id, userId: user['id']);
    if (response.isSuccess) {
      quizQuestion.value = response.resultValue!;
      idSelectedQuestion.value = quizQuestion.value.answer ?? '';
      essayAnswerController.text = quizQuestion.value.answer ?? '';
      isLoading.value = false;
    }
  }

  void selectQuestion({required id, required answer}) async {
    isLoadingAnswer.value = true;
    idSelectedQuestion.value = id;
    var user = await LocalStorage().get('user');
    submitQuestion.value = const SubmitQuestionModel();

    var response = await QuizService().postSubmitQuestion(
      userId: user['id'],
      quizQuestionId: idRandomQuiz.value,
      userTakeQuizId: idStartQuiz.value,
      answer:
          essayAnswerController.text.isEmpty ? id : essayAnswerController.text,
    );
    if (response.isSuccess) {
      submitQuestion.value = response.resultValue!;
      getQuizQuestionId(id: submitQuestion.value.quizQuestionId);
      answer.value = id;
      isLoadingAnswer.value = false;
    } else {
      debugPrint('Error: ${response.errorMessage}');
      isLoadingAnswer.value = false;
    }
    isLoadingAnswer.value = false;
  }

  // Fungsi untuk mulai countdown timer
  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime.value > 0) {
        remainingTime.value--;
      } else {
        timer.cancel(); // Timer berhenti jika sudah mencapai 0
        finishQuiz();
      }
    });
  }

  void nextQuestion() {
    int currentIndex = randomQuestionIds.indexOf(idRandomQuiz.value);
    if (currentIndex < randomQuestionIds.length - 1) {
      getQuizQuestionId(
          id: randomQuestionIds[currentIndex + 1],
          index: (currentIndex + 1) + 1);
    } else {
      debugPrint('No more questions available');
    }
  }

  void previousQuestion() {
    int currentIndex = randomQuestionIds.indexOf(idRandomQuiz.value);
    if (currentIndex > 0) {
      getQuizQuestionId(
          id: randomQuestionIds[currentIndex - 1],
          index: (currentIndex - 1) + 1);
    } else {
      debugPrint('No previous question available');
    }
  }

  void finishQuiz() async {
    isLoadingSubmitQuestion.value = true;
    var user = await LocalStorage().get('user');
    var response = await QuizService().postFinishQuiz(
      userId: user['id'],
      quizId: idRandomQuiz.value,
      userTakeQuizId: idStartQuiz.value,
      spendTime: remainingTime.value.toString(),
    );
    if (response.isSuccess) {
      isLoadingSubmitQuestion.value = false;
      Get.put(ShareScreenController()).close();
      Get.back();
    } else {
      LogService.log.e('Error finishing quiz: ${response.errorMessage}');
      isLoadingSubmitQuestion.value = false;
    }
  }

  void getQuizReview() async {
    var response = await QuizService().getQuizReview(
      userTakeQuizId: idStartQuiz.value,
    );
    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = listQuizReview
            .where((element) => element.questionId == item.questionId)
            .isEmpty;
        if (empty) {
          listQuizReview.add(item);
        }
      }
    } else {
      debugPrint('Error fetching quiz review: ${response.errorMessage}');
    }
  }
}
