import 'package:audioplayers/audioplayers.dart';
import 'package:chewie/chewie.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/mapel_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/hanjar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/open_hanjar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/submapel_model.dart';
import 'package:mides_skadik/app/data/services/course/hanjar/hanjar_services.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class HanjarController extends GetxController {
  var isLoadingAllHanjar = false.obs;
  var isloadingMoreHanjar = false.obs;
  var todalData = 0.obs;
  var isLoadingDetail = false.obs;
  var isLoadingSubmapel = false.obs;

  RxBool responseResult = false.obs;

  // Audio
  final player = AudioPlayer();
  var isPlaying = false.obs;
  var duration = Duration.zero.obs;
  var position = Duration.zero.obs;

  final AudioPlayer players = AudioPlayer();
  // var isPlaying = false.obs;
  var isLoading = false.obs;

  RxList<HanjarModel> allHanjar = <HanjarModel>[].obs;
  RxList<SubmapelModel> subMapel = <SubmapelModel>[].obs;
  RxList<MapelModel> mapel = <MapelModel>[].obs;
  var detailHanjar = const OpenHanjarModel().obs;

  RxList<SubmapelModel> filteredAssignment = <SubmapelModel>[].obs;

  // Video
  // late VideoPlayerController videoController;
  var isInitialized = false.obs;
  var isPlayingVideo = false.obs;
  late ChewieController chewieController;

  late TextEditingController searchController;

  RxString searchText = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchAllHanjar();
    initController();
    fetchMapel();
    players.onPlayerStateChanged.listen(
      (state) {
        isPlaying.value = state == PlayerState.playing;
      },
    );

    searchController = TextEditingController();

    searchController.addListener(() {
      searchAssignment();
    });

    players.onDurationChanged.listen(
      (newDuration) {
        duration.value = newDuration;
      },
    );

    players.onPositionChanged.listen(
      (newDuration) {
        position.value = newDuration;
      },
    );

    // videoController.addListener(() {
    //   isPlaying.value = videoController.value.isPlaying;
    // });
  }

  late final WebViewController webViewCtrl;

  void initController() {
    webViewCtrl = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Optional: bisa digunakan untuk progress bar
          },
          onPageStarted: (String url) {
            isLoading.value = true;
          },
          onPageFinished: (String url) {
            isLoading.value = false;
          },
          onHttpError: (HttpResponseError error) {
            // handle error
          },
          onWebResourceError: (WebResourceError error) {
            // handle error
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      );
  }

  void load(String url) {
    webViewCtrl.loadRequest(Uri.parse(url));
  }

  //Audio
  // Future<void> playAudio(String url) async {
  //   try {
  //     isLoading.value = true;
  //     await _player.setUrl(url);
  //     await _player.play();
  //     isPlaying.value = true;
  //   } catch (e) {
  //     print("Error playing audio: $e");
  //   } finally {
  //     isLoading.value = false;
  //   }
  // }

  void seek(Duration position) {
    player.seek(position);
  }

  void searchAssignment() {
    final query = searchController.text.toLowerCase();
    if (query.isEmpty) {
      filteredAssignment.assignAll(subMapel);
    } else {
      filteredAssignment.assignAll(
        subMapel
            .where((item) => item.mapel!.name!.toLowerCase().contains(query)),
      );
    }
  }

  Future<void> pauseAudio() async {
    await player.pause();
    isPlaying.value = false;
  }

  Future<void> togglePlayPause(String url) async {
    if (isPlaying.value) {
      await pauseAudio();
    } else {
      // await playAudio(url);
    }
  }

  void getOpenHanjar({required String id}) async {
    isLoadingDetail.value = true;

    detailHanjar.value = const OpenHanjarModel();
    var response = await HanjarServices().getOpenHanjar(id: id);
    print("load");
    if (response.isSuccess) {
      detailHanjar.value = response.resultValue!;

      print("success");

      isLoadingDetail.value = false;
    }
  }

  void fetchAllHanjar() async {
    // allHanjar.clear();
    isLoadingAllHanjar.value = true;
    var response = await HanjarServices().getAllHanjar();
    // LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty =
            allHanjar.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          allHanjar.add(item);
        }
      }
      isLoadingAllHanjar.value = false;
    }
  }

  void fetchMapel() async {
    // allHanjar.clear();
    isLoadingSubmapel.value = true;
    var response = await HanjarServices().getMapel();
    // LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = mapel.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          mapel.add(item);
        }
      }
      isLoadingSubmapel.value = false;
    }
  }

  void fetchSubMapel({required String id}) async {
    // allHanjar.clear();
    isLoadingSubmapel.value = true;
    var response = await HanjarServices().getSubMapel(id: id);
    // LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = subMapel.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          subMapel.add(item);
          filteredAssignment.add(item);
        }
      }
      isLoadingSubmapel.value = false;
    }
  }

  void getAllhanjar() async {
    try {
      isloadingMoreHanjar.value = true;
      var response = await HanjarServices().getAllHanjar();

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty =
              allHanjar.where((element) => element.id == item.id).isEmpty;
          if (empty) {
            allHanjar.add(item);
          }
        }
        isloadingMoreHanjar.value = false;
      }
    } catch (e) {
      isLoadingAllHanjar.value = false;
    }
  }

  void postCompleted({required String id}) async {
    isLoadingDetail.value = true;

    var response = await HanjarServices().postMark(id: id);
    print("load ${response.isSuccess}");

    if (response.isSuccess) {
      responseResult.value = response.isSuccess;

      print("success");

      isLoadingDetail.value = false;
    } else {
      responseResult.value = response.isSuccess;
      print("Failed ${response.isSuccess}");
    }
  }

  @override
  void dispose() {
    player.dispose();
    super.dispose();
  }
}
