import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_by_id_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_progress_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_question_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/start_quiz_model.dart';
import 'package:mides_skadik/app/data/models/response/course/assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/detail_assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/history_assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/submisson_assignment_model.dart';
import 'package:mides_skadik/app/data/services/course/assignment_services.dart';
import 'package:mides_skadik/app/data/services/course/quiz_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/quiz_question_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/share_screen_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/open_quiz_course_view.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/quiz_running_course_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:flutter/material.dart';

enum FilterType { latest, last7Days, last21Days }

class ClasesCourseController extends GetxController {
  var isOpen = false.obs;
  var isOpenSubmitQuiz = false.obs;
  var selectedTabClasesIndex = 0.obs;
  var selectedMapetIndex = 0.obs;
  var selectSbs = 0.obs;
  var selectedMapel = <bool>[false, false, false].obs;
  var listButton = 1.obs;
  var isQuizFinished = false.obs;
  var quillController = QuillController.basic();
  var inputTeks = ''.obs;
  var isLoading = false.obs;
  var isLoadingAttempt = false.obs;
  var currentPage = 1.obs;
  var todalData = 0.obs;
  var isLoadingMoreData = false.obs;
  var idNavigation = ''.obs;

  RxList<QuizNavigate> idNavigationList = <QuizNavigate>[].obs;
  RxList<QuizModel> listQuiz = <QuizModel>[].obs;
  Rx<QuizByIdModel> quizById = const QuizByIdModel().obs;
  Rx<QuizQuestionModel> quizQuestion = const QuizQuestionModel().obs;
  Rx<StartQuizModel> startQuiz = const StartQuizModel().obs;
  Rx<QuizProgressModel> quizProgress = const QuizProgressModel().obs;

  //! Assingment variable
  Rx<PlatformFile?> selectedFile = Rx<PlatformFile?>(null);
  RxString author = 'Muhammad Dani Mu’ti'.obs;
  RxString saveAs = ''.obs;
  RxString errorMessage = ''.obs;
  var selectedFileBase64 = ''.obs;
  var isUpload = false.obs;
  var isSave = false.obs;
  var isLoadingDetail = false.obs;

  RxList<AssignmentModel> listAssigment = <AssignmentModel>[].obs;
  RxList<HistoryAssignmentModel> listHistoryAsignment =
      <HistoryAssignmentModel>[].obs;
  var detailAssignmentModel = DetailAssignmentModel().obs;
  var assignmentAnswer = SubmissonAssignmentModel().obs;
  RxBool responseRsult = false.obs;
  File? selectedFiles;
  RxString? chooseFile;

  RxList<AssignmentModel> filteredAssignment = <AssignmentModel>[].obs;
  RxList<HistoryAssignmentModel> filteredHistoryAssignment =
      <HistoryAssignmentModel>[].obs;

  RxList<AssignmentModel> filteredList = <AssignmentModel>[].obs;

  RxString searchText = ''.obs;

  var selectedFilter = FilterType.latest.obs;
  var filteredItems = <AssignmentModel>[].obs;

  late TextEditingController searchController;

  @override
  void onInit() {
    super.onInit();
    fetchData();
    fetchAllAssignment();
    fetchHistoryAssignment();

    searchController = TextEditingController();

    searchController.addListener(() {
      searchAssignment();
    });

    filteredAssignment.assignAll(listAssigment);
    // Jalankan filter setiap kali searchText berubah
    // ever(searchText, (_) => searchAssignment()); // Default tampilkan semua
  }

  RxInt selectedTabAssignmentIndex = 0.obs;
  RxString selectedFilterTime = ''.obs;
  RxString selectedFilterDeadline = ''.obs;

  RxString userName = ''.obs;
  RxList<AssignmentModel> listAssignment = <AssignmentModel>[].obs;

  /// =========================
  ///  Filter Assignment
  void filterAssignment() {
    if (selectedTabAssignmentIndex.value == 0) {
      fetchAssignment(
        time: selectedFilterTime.value,
        deadline: selectedFilterDeadline.value,
      );
    } else if (selectedTabAssignmentIndex.value == 1) {
      fetchAssignment(
        status: 'All Task List',
        time: selectedFilterTime.value,
        deadline: selectedFilterDeadline.value,
      );
    } else if (selectedTabAssignmentIndex.value == 2) {
      fetchAssignment(
        status: 'Task Histories',
        time: selectedFilterTime.value,
        deadline: selectedFilterDeadline.value,
      );
    }
  }

  /// =========================
  ///  Fetch Assignment List
  void fetchAssignment({
    String status = '',
    String time = '',
    String deadline = '',
  }) async {
    listAssignment.clear();
    isLoading.value = true;

    try {
      var user = await LocalStorage().get('user');
      userName.value = user['name'];

      var response = await AssignmentService().getAssignmentList();

      LogService.log.i('Cek data assignment $response');

      if (response.isSuccess) {
        for (var item in response.resultValue!) {
          bool isNotDuplicate =
              listAssignment.where((e) => e.id == item.id).isEmpty;
          if (isNotDuplicate) {
            listAssignment.add(item);
          }
        }
      }
    } catch (e) {
      // LogService.log.e('Error fetchAssignment: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// =========================================
  /// REFRESH DATA
  void refreshPage() {
    listQuiz.clear();
    currentPage.value = 1;
    fetchData();
  }

  /// Assignment

  ///Filter
  void setFilter(FilterType filter) {
    selectedFilter.value = filter;
    applyFilter();
  }

  void applyFilter() {
    final now = DateTime.now();
    List<AssignmentModel> result = [];

    switch (selectedFilter.value) {
      case FilterType.latest:
        result = filteredAssignment.toList();
        result.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
        filteredItems.value = result.take(5).toList();
        break;

      case FilterType.last7Days:
        filteredItems.value = filteredAssignment
            .where((e) => e.createdAt!.isAfter(now.subtract(Duration(days: 7))))
            .toList();
        break;

      case FilterType.last21Days:
        filteredItems.value = filteredAssignment
            .where(
                (e) => e.createdAt!.isAfter(now.subtract(Duration(days: 21))))
            .toList();
        break;
    }
  }

  void fetchMoreDataAssignment() {
    if (todalData.value == listAssigment.length) {
      return;
    }
    currentPage.value++;
    getListQuiz();
  }

  void fetchAllAssignment() async {
    listAssigment.clear();
    isLoading.value = true;
    var response = await AssignmentService().getAssignmentList();
    LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty =
            listAssigment.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          listAssigment.add(item);

          filteredAssignment.add(item);
        }
      }
      isLoading.value = false;
    }
  }

  void getAllAsignment() async {
    try {
      isLoadingMoreData.value = true;
      var response = await AssignmentService().getAssignmentList();

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty =
              listAssigment.where((element) => element.id == item.id).isEmpty;
          if (empty) {
            listAssigment.add(item);
            filteredAssignment.assignAll(listAssigment);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  void fetchHistoryAssignment() async {
    listAssigment.clear();
    isLoading.value = true;
    var response = await AssignmentService().getHistoryAssignment();
    LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = listHistoryAsignment
            .where((element) => element.id == item.id)
            .isEmpty;
        if (empty) {
          listHistoryAsignment.add(item);
          filteredHistoryAssignment.add(item);
        }
      }
      isLoading.value = false;
    }
  }

  void getHistoryAssignment() async {
    try {
      isLoadingMoreData.value = true;
      var response = await AssignmentService().getHistoryAssignment();

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty = listHistoryAsignment
              .where((element) => element.id == item.id)
              .isEmpty;
          if (empty) {
            listHistoryAsignment.add(item);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  void getAssignmentDetail({required String id}) async {
    isLoadingDetail.value = true;

    detailAssignmentModel.value = const DetailAssignmentModel();
    var response = await AssignmentService().getAssignementDetail(id: id);
    print("load");
    if (response.isSuccess) {
      detailAssignmentModel.value = response.resultValue!;

      print("success");

      isLoadingDetail.value = false;
    }
  }

  /// Assingment Search
  ///
  void searchAssignment() {
    final query = searchController.text.toLowerCase();
    if (query.isEmpty) {
      filteredAssignment.assignAll(listAssigment);
      filteredHistoryAssignment.assignAll(listHistoryAsignment);
    } else {
      filteredAssignment.assignAll(
        listAssigment
            .where((item) => item.title!.toLowerCase().contains(query)),
      );
      filteredHistoryAssignment.assignAll(
        listHistoryAsignment
            .where((item) => item.title!.toLowerCase().contains(query)),
      );
    }
  }

  ///

  void getSubmissionAssignment({required String id}) async {
    isLoadingDetail.value = true;

    assignmentAnswer.value = const SubmissonAssignmentModel();
    var response = await AssignmentService().getAssignemntAnswer(id: id);
    print("load ${response.isSuccess}");

    if (response.isSuccess) {
      assignmentAnswer.value = response.resultValue!;
      responseRsult.value = response.isSuccess;

      print("success");

      isLoadingDetail.value = false;
    } else {
      responseRsult.value = response.isSuccess;
      print("Failed ${response.isSuccess}");
    }
  }

  ///

  void fetchMoreData() {
    if (todalData.value == listQuiz.length) {
      return;
    }
    currentPage.value++;
    getListQuiz();
  }

  /// =========================================
  ///  Fetch Data Quiz List
  void fetchData() async {
    listQuiz.clear();
    isLoading.value = true;
    var response = await QuizService()
        .getQuizList(page: currentPage.value, search: '', type: 'Quiz');
    LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = listQuiz.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          listQuiz.add(item);
        }
      }
      isLoading.value = false;
    }
  }

  /// =========================================
  /// Get List Quiz
  void getListQuiz() async {
    try {
      isLoadingMoreData.value = true;
      var response = await QuizService()
          .getQuizList(page: currentPage.value, type: 'Quiz');

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty =
              listQuiz.where((element) => element.id == item.id).isEmpty;
          if (empty) {
            listQuiz.add(item);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  /// =========================================
  /// Attempt Quiz
  void attemptQuiz({String? id}) async {
    isLoading.value = true;
    quizById.value = const QuizByIdModel();
    idNavigationList.clear();
    var response = await QuizService().getQuizListById(id: id);
    if (response.isSuccess) {
      quizById.value = response.resultValue!;
      idNavigation.value = id!;
      idNavigationList.addAll(quizById.value.quizNavigate!);
      for (var item in quizById.value.quizNavigate!) {
        bool empty =
            idNavigationList.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          idNavigationList.add(item);
        }
      }
      getQuizProgress();
      Get.to(() => const OpenQuizCourseView());
      isLoading.value = false;
    }
  }

  /// =========================================
  /// Selected Navigation
  void selectedNavigation({String? id}) async {
    isLoading.value = true;
    quizById.value = const QuizByIdModel();
    idNavigation.value = id!;
    attemptQuiz(id: id);
    getQuizProgress();
  }

  /// =========================================
  /// Start Quiz
  void startQuizId({required String quizId}) async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');

    var response = await QuizService().postStartQuiz(
      userId: user['id'].toString(),
      quizId: quizId,
    );
    if (response.isSuccess) {
      startQuiz.value = response.resultValue!;
      isLoading.value = false;
      // getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
      if (Get.isRegistered<QuizQuestionController>()) {
        Get.find<QuizQuestionController>()
            .getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
        Get.find<QuizQuestionController>().remainingTime.value =
            startQuiz.value.duration ?? 0;
        Get.find<QuizQuestionController>().idStartQuiz.value =
            startQuiz.value.id ?? '';
        Get.find<QuizQuestionController>().randomQuestionIds.value =
            startQuiz.value.randomQuestionIds ?? [];
      } else {
        Get.put(QuizQuestionController())
            .getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
        Get.put(QuizQuestionController()).randomQuestionIds.value =
            startQuiz.value.randomQuestionIds ?? [];
        Get.put(QuizQuestionController()).remainingTime.value =
            startQuiz.value.duration ?? 0;
        Get.put(QuizQuestionController()).idStartQuiz.value =
            startQuiz.value.id ?? '';
        Get.put(QuizQuestionController()).numberQuestion.value = 1;
      }
      Get.put(ShareScreenController()).join();
      Get.to(() => QuizRunningCourseView());
    } else {
      isLoading.value = false;
      Get.snackbar("Error", response.errorMessage.toString());
    }
  }

  /// =========================================
  /// Next
  void nextQuiz() async {
    int currentIndex = idNavigationList
        .indexWhere((element) => element.id == idNavigation.value);
    if (currentIndex != -1 && currentIndex < idNavigationList.length - 1) {
      String? nextId = idNavigationList[currentIndex + 1].id;
      idNavigation.value = nextId!;
      attemptQuiz(id: nextId);
      getQuizProgress();
    }
  }

  /// =========================================
  /// Previous Quiz
  void previousQuiz() {
    int currentIndex = idNavigationList
        .indexWhere((element) => element.id == idNavigation.value);
    if (currentIndex != -1 && currentIndex > 0) {
      String? previousId = idNavigationList[currentIndex - 1].id;
      idNavigation.value = previousId!;
      attemptQuiz(id: previousId);
      getQuizProgress();
    }
  }

  void getQuizProgress() async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    var response = await QuizService().getQuizProgress(
      userId: user['id'],
      quizId: idNavigation.value,
    );

    if (response.isSuccess) {
      quizProgress.value = response.resultValue!;
      isLoading.value = false;
    } else {
      isLoading.value = false;
      Get.snackbar("Error", response.errorMessage.toString());
    }
  }

  isUploads(bool upload) {
    isUpload.value = upload;
  }

  isSaves(bool upload) {
    isSave.value = upload;
  }

  changeSelectedButton(int selected) {
    listButton.value = selected;
  }

  setInputText() {
    inputTeks.value = quillController.document.toPlainText();
  }

  Future<void> pickFile() async {
    FilePickerResult? result =
        await FilePicker.platform.pickFiles(withData: true);

    if (result != null) {
      final file = result.files.first;

      // Mengecek ukuran file
      if (file.size > 20 * 1024 * 1024) {
        // 20MB
        errorMessage.value =
            "Your file exceeds the limit. Only files up to 20MB can be uploaded.";
      } else {
        // Menyimpan file yang dipilih ke variabel yang sesuai
        selectedFile.value =
            file; // Pastikan ini adalah Rx<PlatformFile?> sesuai deklarasi

        // Reset errorMessage
        errorMessage.value = '';

        // Debug: print file yang dipilih
        print('File Name: ${file.name}');
        print('File Size: ${file.size}');
        print('File Path: ${file.path}');

        // Jika ingin mengakses bytes, kamu bisa langsung menggunakan file.bytes
        final bytes = file.bytes;
        if (bytes != null) {
          // Jika ingin encode ke base64, uncomment kode berikut
          // final base64String = base64Encode(bytes);
          // print('Base64 String: $base64String');
        }
      }
    } else {
      // Jika user membatalkan memilih file
      errorMessage.value = "No file selected.";
    }
  }

  void removeFile() {
    selectedFile.value = null;
    selectedFileBase64.value = "";
    errorMessage.value = '';
  }

  Future<void> removeAssignment(
      {required String id, required String file}) async {
    isLoading.value = true;
    var response =
        await AssignmentService().deleteSubmission(id: id, file: file);
    if (response.isSuccess) {
      Get.snackbar("Deleted", "Your file was deleted.",
          backgroundColor: greenColor, colorText: whiteColor);

      getSubmissionAssignment(id: id);
      isLoading.value = false;
    } else {
      isLoading.value = false;
      Get.snackbar("Deleted", response.errorMessage!,
          backgroundColor: redColor);
    }
  }

  Future<void> uploadFile({required String id}) async {
    listAssigment.clear();
    isLoading.value = true;
    var response = await AssignmentService().uploadAssignment(
      id: id,
      file: selectedFile.value!,
    );

    if (response.isSuccess) {
      Get.snackbar("Uploaded", "Your file was uploaded.",
          backgroundColor: greenColor, colorText: whiteColor);

      listAssigment.clear();
      filteredAssignment.clear();
      fetchAllAssignment();
      getSubmissionAssignment(id: id);
      isLoading.value = false;
    } else {
      isLoading.value = false;
      Get.snackbar("Uploaded", response.errorMessage!,
          backgroundColor: redColor);
    }
  }

  openFilter() {
    isOpen.value = !isOpen.value;
    LogService.log.i('open filter');
  }

  clearText() {
    isOpen.value = !isOpen.value;
    LogService.log.i('open filter');
  }

  openSubmitQuiz() {
    isOpenSubmitQuiz.value = !isOpenSubmitQuiz.value;
    LogService.log.i('open submit quiz');
  }

  selectTab(int index) {
    selectedTabClasesIndex.value = index;
  }

  selectedMapet(int index) {
    selectedMapetIndex.value = index;
  }

  selectedSbs(int index) {
    selectSbs.value = index;
  }

  finishQUiz() {
    isQuizFinished.value = !isQuizFinished.value;
  }

  void toggleMapel(int index) {
    selectedMapel[index] = !selectedMapel[index];
  }

  @override
  void onClose() {
    quillController.dispose();
    super.onClose();
  }
}
