import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_content_task.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_header_task.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_information_task.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_list_navigation_task.dart';
import 'package:mides_skadik/widgets/course/class/custom_table_course.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:skeletonizer/skeletonizer.dart';

class OpenQuizCourseView extends GetView<ClasesCourseController> {
  final String? imageUrl;
  const OpenQuizCourseView({super.key, this.imageUrl = ''});
  @override
  Widget build(BuildContext context) {
    final formatter = DateFormat('EEEE, MMMM, dd, yyyy, HH:mm');
    return CustomScaffold(
      useAppBar: true,
      isCourse: true,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 55.h),
        child: Column(
          children: [
            Row(
              children: [
                CustomFilledButtonWidget(
                  onPressed: () {
                    Get.back();
                  },
                  isOutlined: true,
                  withIcon: true,
                  borderColor: greyColor,
                  assetName: 'assets/icons/left.svg',
                  title: 'Back',
                  fontColor: whiteColor,
                  fontSize: 16,
                  widthButton: 115,
                  heightIcon: 20,
                  fontWeight: FontWeight.w500,
                  radius: 4,
                ),
                const Spacer(),
                CustomFilledButtonWidget(
                  onPressed: () {},
                  isOutlined: true,
                  borderColor: greyColor,
                  title: 'Mark Completed',
                  fontColor: whiteColor,
                  fontSize: 16,
                  widthButton: 165,
                  fontWeight: FontWeight.w500,
                  radius: 4,
                )
              ],
            ),
            32.verticalSpace,
            Obx(
              () => CustomHeaderTask(
                imageUrl: imageUrl ?? controller.quizById.value.quiz?.file,
                titleHeader:
                    controller.quizById.value.quiz?.submapel?.name ?? '-',
                userName: 'Yoshua Kaesang',
              ),
            ),
            32.verticalSpace,
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomListNavigationTask(
                  title: 'List Quiz',
                  subTitle: 'Total Quiz',
                  total: 10,
                  listWidget: Obx(
                    () => Skeletonizer(
                      enabled: controller.quizById.value.quizNavigate == null,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ...?controller.quizById.value.quizNavigate
                              ?.asMap()
                              .entries
                              .map((entry) {
                            // final index = entry.key;
                            final item = entry.value;
                            return GestureDetector(
                              onTap: () {
                                controller.selectedNavigation(id: item.id);
                              },
                              child: CustomContainer(
                                bgColor:
                                    controller.idNavigation.value == item.id
                                        ? baseBlueColor.withOpacity(0.3)
                                        : Colors.transparent,
                                width: double.infinity,
                                radius: 8,
                                widget: Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 12.h, horizontal: 20.w),
                                  child: CustomTextWigdet(
                                    title: item.title ?? '',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    textColor:
                                        controller.idNavigation.value == item.id
                                            ? whiteColor
                                            : secondWhiteColor.withOpacity(0.5),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),
                ),
                24.horizontalSpace,
                Expanded(
                  child: Obx(
                    () => Skeletonizer(
                      enabled: controller.quizById.value.quiz == null,
                      child: Column(
                        children: [
                          CustomContainer(
                            bgColor: baseBlueColor.withOpacity(0.3),
                            width: double.infinity,
                            radius: 10,
                            widget: Padding(
                              padding: EdgeInsets.all(24.r),
                              child: Column(
                                children: [
                                  CustomContentTask(
                                    contentTitle:
                                        controller.quizById.value.quiz?.title ??
                                            '-',
                                    statusProgress: controller
                                            .quizById.value.quiz?.status
                                            ?.toUpperCase() ??
                                        'NOT STARTED',
                                    widgets: [
                                      CustomContainer(
                                        width: double.infinity,
                                        bgColor: baseBlueColor.withOpacity(0.2),
                                        radius: 8,
                                        widget: Padding(
                                          padding: EdgeInsets.all(20.r),
                                          child: Column(
                                            children: [
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/calendar.svg',
                                                  name: 'Due',
                                                  value: getFormattedDate(
                                                      controller
                                                              .quizById
                                                              .value
                                                              .quiz
                                                              ?.startDate ??
                                                          DateTime.now())),
                                              12.verticalSpace,
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/timer.svg',
                                                  name: 'Duration',
                                                  value:
                                                      '${controller.quizById.value.quiz?.duration?.toString()} menit'),
                                              12.verticalSpace,
                                              const CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/spellcheck.svg',
                                                  name: 'Allowed Attempts',
                                                  value: '2'),
                                              12.verticalSpace,
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/question_answer.svg',
                                                  name: 'Questions Total',
                                                  value:
                                                      '${controller.quizById.value.quiz?.count?.quizQuestions}'),
                                            ],
                                          ),
                                        ),
                                      ),
                                      44.verticalSpace,
                                      // Conditions for data summary is empty or not
                                      Obx(
                                        () => controller.quizProgress.value
                                                    .status ==
                                                'Completed'
                                            ? CustomContainer(
                                                bgColor: baseBlueColor
                                                    .withOpacity(0.3),
                                                width: double.infinity,
                                                radius: 8,
                                                widget: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              horizontal: 20.w,
                                                              vertical: 12.h),
                                                      child: CustomTextWigdet(
                                                        title: 'Quiz Summary',
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        textColor:
                                                            secondWhiteColor,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 500.w,
                                                      child: CustomTableCourse(
                                                        dataColumnTable: [
                                                          DataColumn2(
                                                              label:
                                                                  CustomTextWigdet(
                                                                title:
                                                                    'ATTEMPT',
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                textColor:
                                                                    secondWhiteColor
                                                                        .withOpacity(
                                                                            0.5),
                                                              ),
                                                              size:
                                                                  ColumnSize.S,
                                                              fixedWidth:
                                                                  110.w),
                                                          DataColumn2(
                                                              label:
                                                                  CustomTextWigdet(
                                                                title:
                                                                    'TIME SUBMITTED',
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                textColor:
                                                                    secondWhiteColor
                                                                        .withOpacity(
                                                                            0.5),
                                                              ),
                                                              fixedWidth:
                                                                  330.w),
                                                          DataColumn2(
                                                            label:
                                                                CustomTextWigdet(
                                                              title:
                                                                  'INCORRECT',
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              textColor:
                                                                  secondWhiteColor
                                                                      .withOpacity(
                                                                          0.5),
                                                            ),
                                                          ),
                                                          DataColumn2(
                                                              label:
                                                                  CustomTextWigdet(
                                                                title: 'SCORE',
                                                                fontSize: 14,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                textColor:
                                                                    secondWhiteColor
                                                                        .withOpacity(
                                                                            0.5),
                                                              ),
                                                              fixedWidth:
                                                                  140.w),
                                                          DataColumn2(
                                                            label:
                                                                CustomTextWigdet(
                                                              title: 'REVIEW',
                                                              fontSize: 14,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500,
                                                              textColor:
                                                                  secondWhiteColor
                                                                      .withOpacity(
                                                                          0.5),
                                                            ),
                                                          ),
                                                        ],
                                                        dataRowTable: [
                                                          DataRow(
                                                            cells: [
                                                              const DataCell(
                                                                Align(
                                                                  alignment:
                                                                      Alignment
                                                                          .center,
                                                                  child:
                                                                      CustomTextWigdet(
                                                                    title: '1',
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                  ),
                                                                ),
                                                              ),
                                                              DataCell(
                                                                CustomTextWigdet(
                                                                  title: formatter.format(controller
                                                                          .quizProgress
                                                                          .value
                                                                          .startDate ??
                                                                      DateTime
                                                                          .now()),
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                              ),
                                                              DataCell(
                                                                CustomTextWigdet(
                                                                  title: controller
                                                                      .quizProgress
                                                                      .value
                                                                      .totalIncorrect
                                                                      .toString(),
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                              ),
                                                              DataCell(
                                                                CustomTextWigdet(
                                                                  title: controller
                                                                      .quizProgress
                                                                      .value
                                                                      .totalScore
                                                                      .toString(),
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                ),
                                                              ),
                                                              DataCell(
                                                                GestureDetector(
                                                                  onTap: () {
                                                                    Get.toNamed(
                                                                        '/quiz-review-course',
                                                                        arguments: {
                                                                          "title":
                                                                              controller.quizById.value.quiz?.title ?? '-',
                                                                          "userId": controller
                                                                              .quizProgress
                                                                              .value
                                                                              .userId,
                                                                          "quizId": controller
                                                                              .quizProgress
                                                                              .value
                                                                              .quizId
                                                                        });
                                                                  },
                                                                  child:
                                                                      const CustomTextWigdet(
                                                                    title:
                                                                        'Review',
                                                                    textDecoration:
                                                                        TextDecoration
                                                                            .underline,
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w500,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          )
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )
                                            : const SizedBox.shrink(),
                                      ),
                                      44.verticalSpace,
                                      Obx(
                                        () => CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.startQuizId(
                                              quizId: controller.quizById.value
                                                      .quiz?.id ??
                                                  '',
                                            );
                                          },
                                          title: controller.quizProgress.value
                                                      .status ==
                                                  'Completed'
                                              ? 'Reattempt'
                                              : 'Attempt Now',
                                          fontColor: whiteColor,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          bgColor: blueColor,
                                          radius: 4,
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          60.verticalSpace,
                          Obx(
                            () => Row(
                              children: [
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.previousQuiz();
                                  },
                                  isOutlined: controller.listButton.value == 1
                                      ? false
                                      : true,
                                  borderColor: greyColor,
                                  bgColor: controller.listButton.value == 1
                                      ? greyColor
                                      : Colors.transparent,
                                  title: 'Previous',
                                  fontSize: 16,
                                  fontColor: whiteColor,
                                  widthButton: 105,
                                  heightButton: 45,
                                  radius: 4,
                                ),
                                const Spacer(),
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.nextQuiz();
                                  },
                                  bgColor: controller.listButton.value == 3
                                      ? greyColor
                                      : blueColor,
                                  title: 'Next',
                                  fontSize: 16,
                                  fontColor: whiteColor,
                                  widthButton: 105,
                                  heightButton: 45,
                                  radius: 4,
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
