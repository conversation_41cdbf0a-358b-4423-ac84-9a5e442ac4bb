// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../../dashboard_course/views/pdf_viewer_page.dart';
import '../controllers/clases_course_controller.dart';

class DetailAssignmentView extends GetView<ClasesCourseController> {
  final String? imageUrl;
  final int? index;
  DetailAssignmentView({super.key, this.imageUrl = '', this.index = 0});

  ClasesCourseController controller = Get.put(ClasesCourseController());

  @override
  Widget build(BuildContext context) {
    final String id = Get.arguments['id'];

    print(id);

    controller.getAssignmentDetail(id: id);
    controller.getSubmissionAssignment(id: id);

    var index = ''.obs;

    //! File Card
    Widget fileCard() {
      return Container(
        width: 669,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10.5),
        decoration: BoxDecoration(
          border: Border.all(
            color: greyColor,
          ),
          // color: whiteColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: whiteColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: SvgPicture.asset(
                'assets/icons/pdf.svg',
                width: 10,
                height: 10,
                color: secondWhiteColor,
              ),
            ),
            12.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: controller.detailAssignmentModel.value.title ?? '',
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  textColor: secondWhiteColor,
                ),
                Row(
                  children: [
                    CustomTextWigdet(
                      title: "Makalah.pdf",
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      textColor: greyColor,
                    ),
                    6.horizontalSpace,
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: greyColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: "5 MB",
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      textColor: greyColor,
                    ),
                  ],
                ),
              ],
            ),
            const Spacer(),
            InkWell(
              onTap: () {
                if (controller.detailAssignmentModel.value.file != null) {
                  FileDownloadUtil.downloadFile(
                    controller.detailAssignmentModel.value.file ?? "",
                    fileName:
                        controller.detailAssignmentModel.value.title ?? '',
                  );
                } else {
                  Get.snackbar("Error", "Data File tidak tersedia",
                      backgroundColor: redColor, colorText: whiteColor);
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: greyColor,
                  ),
                  borderRadius: BorderRadius.circular(
                    10,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.download_outlined,
                      color: whiteColor,
                      size: 15,
                    ),
                    4.horizontalSpace,
                    CustomTextWigdet(
                      title: "Download",
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      textColor: whiteColor,
                    )
                  ],
                ),
              ),
            ),
            8.horizontalSpace,
            InkWell(
              onTap: () {
                print(
                    "buka link ${controller.detailAssignmentModel.value.file}");
                if (controller.detailAssignmentModel.value.file != null) {
                  Get.dialog(
                    PDFViwerPage(
                      path: controller.detailAssignmentModel.value.file ?? "",
                      title: "${controller.detailAssignmentModel.value.title}",
                      subtitle:
                          "${controller.detailAssignmentModel.value.subMapel!.name}",
                    ),
                  );
                } else {
                  Get.snackbar("Error", "Data File tidak tersedia",
                      backgroundColor: redColor, colorText: whiteColor);
                }
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: greyColor,
                  ),
                  borderRadius: BorderRadius.circular(
                    10,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.visibility_outlined,
                      color: whiteColor,
                      size: 15,
                    ),
                    4.horizontalSpace,
                    CustomTextWigdet(
                      title: "Read",
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      textColor: whiteColor,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return CustomScaffold(
        useAppBar: true,
        isCourse: true,
        body: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 55.h),
            child: controller.isLoadingDetail.value
                ? Center(
                    child: CircularProgressIndicator(
                      color: blueColor,
                    ),
                  )
                : SingleChildScrollView(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CustomFilledButtonWidget(
                              onPressed: () {
                                Get.back();
                                controller.selectedFile.value = null;
                              },
                              isOutlined: true,
                              withIcon: true,
                              borderColor: greyColor,
                              assetName: 'assets/icons/left.svg',
                              title: 'Back',
                              fontColor: whiteColor,
                              fontSize: 16,
                              widthButton: 115,
                              heightIcon: 20,
                              fontWeight: FontWeight.w500,
                              radius: 4,
                            ),
                            const Spacer(),
                            CustomFilledButtonWidget(
                              onPressed: () {},
                              isOutlined: true,
                              borderColor: greyColor,
                              title: 'Mark Completed',
                              fontColor: whiteColor,
                              fontSize: 16,
                              widthButton: 190,
                              fontWeight: FontWeight.w500,
                              radius: 4,
                            )
                          ],
                        ),
                        32.verticalSpace,
                        SizedBox(
                          height: 290.h,
                          width: double.infinity,
                          child: Stack(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(10.r),
                                child: Stack(
                                  children: [
                                    SizedBox(
                                      height: 290.h,
                                      width: double.infinity,
                                      child: (controller.detailAssignmentModel
                                                  .value.thumbnail ==
                                              null)
                                          ? Image.asset(
                                              'assets/images/image 115.png',
                                              fit: BoxFit.cover,
                                            )
                                          : CachedNetworkImage(
                                              imageUrl:
                                                  "${controller.detailAssignmentModel.value.thumbnail ?? ""}",
                                              fit: BoxFit.cover,
                                              placeholder: (context, url) =>
                                                  const CircularProgressIndicator(),
                                              errorWidget:
                                                  (context, url, error) =>
                                                      const Icon(Icons.error),
                                            ),
                                    ),
                                    Container(
                                      height: 290.h,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                            colors: [
                                              baseBlueColor,
                                              baseBlueColor.withOpacity(0.25)
                                            ],
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            stops: const [
                                              0.16,
                                              1,
                                            ]),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Align(
                                alignment: Alignment.bottomLeft,
                                child: Padding(
                                  padding:
                                      EdgeInsets.only(left: 32.w, bottom: 32.h),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomTextWigdet(
                                        title: controller
                                            .detailAssignmentModel.value.title
                                            .toString(),
                                        fontSize: 24,
                                        fontWeight: FontWeight.w700,
                                      ),
                                      15.verticalSpace,
                                      Row(
                                        children: [
                                          const CircleAvatar(
                                            radius: 10,
                                          ),
                                          4.horizontalSpace,
                                          CustomTextWigdet(
                                            title:
                                                "${controller.detailAssignmentModel.value.createdBy?.name}",
                                            fontSize: 16,
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        32.verticalSpace,
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomContainer(
                              bgColor: secondBlueColor.withOpacity(0.1),
                              width: 260,
                              radius: 10,
                              widget: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.w, vertical: 20.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        const CustomTextWigdet(
                                          title: 'List Assignment',
                                          fontSize: 18,
                                        ),
                                        const Spacer(),
                                        SvgPicture.asset(
                                          'assets/icons/list.svg',
                                          height: 20.h,
                                        )
                                      ],
                                    ),
                                    4.verticalSpace,
                                    CustomTextWigdet(
                                      title: 'Assignment',
                                      fontSize: 14,
                                      textColor: baseBlueColor,
                                    ),
                                    20.verticalSpace,
                                    SizedBox(
                                      height: Get.height / 2,
                                      child: ListView.builder(
                                        itemCount: index == 0
                                            ? controller.listAssigment.length
                                            : controller
                                                .listHistoryAsignment.length,
                                        itemBuilder: (item, idx) {
                                          return Obx(
                                            () => Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 8),
                                              child: CustomFilledButtonWidget(
                                                widthButton: 230,
                                                heightButton: 45,
                                                onPressed: () {
                                                  index.value = controller
                                                      .listAssigment[idx].id!;

                                                  print(controller
                                                      .listAssigment[idx].id);
                                                  controller
                                                      .getAssignmentDetail(
                                                          id: controller
                                                              .listAssigment[
                                                                  idx]
                                                              .id
                                                              .toString());
                                                  controller
                                                      .getSubmissionAssignment(
                                                          id: controller
                                                              .listAssigment[
                                                                  idx]
                                                              .id
                                                              .toString());
                                                  controller
                                                      .changeSelectedButton(
                                                          idx + 1);
                                                  controller.selectedFile
                                                      .value = null;
                                                },
                                                bgColor:
                                                    whiteColor.withOpacity(0.1),
                                                bgSelectedColor:
                                                    whiteColor.withOpacity(0.5),
                                                isSelected: controller
                                                        .listButton.value ==
                                                    idx + 1,
                                                radius: 8,
                                                title: controller
                                                    .listAssigment[idx].title,
                                                fontSize: 14,
                                                fontColor: whiteColor,
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                            24.horizontalSpace,
                            Obx(
                              () => Expanded(
                                child: Column(
                                  children: [
                                    CustomContainer(
                                      bgColor: baseBlueColor.withOpacity(0.3),
                                      width: double.infinity,
                                      radius: 10,
                                      widget: Padding(
                                        padding: EdgeInsets.all(24.r),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                CustomContainer(
                                                  width: 60,
                                                  height: 60,
                                                  radius: 10,
                                                  bgColor: baseBlueColor
                                                      .withOpacity(0.5),
                                                  widget: Center(
                                                    child: SvgPicture.asset(
                                                      'assets/icons/question_answer.svg',
                                                      height: 32.h,
                                                    ),
                                                  ),
                                                ),
                                                20.horizontalSpace,
                                                CustomTextWigdet(
                                                  title: controller
                                                      .detailAssignmentModel
                                                      .value
                                                      .title
                                                      .toString(),
                                                  fontSize: 24,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                const Spacer(),
                                                CustomContainer(
                                                  bgColor: greyColor,
                                                  height: 30,
                                                  widget: Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 12.w,
                                                            vertical: 4.h),
                                                    child: Row(
                                                      children: [
                                                        Container(
                                                          width: 5.w,
                                                          height: 5.h,
                                                          decoration:
                                                              BoxDecoration(
                                                                  shape: BoxShape
                                                                      .circle,
                                                                  color:
                                                                      whiteColor),
                                                        ),
                                                        4.horizontalSpace,
                                                        CustomTextWigdet(
                                                          title: controller
                                                              .detailAssignmentModel
                                                              .value
                                                              .status
                                                              .toString(),
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w400,
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                            32.verticalSpaceFromWidth,
                                            CustomContainer(
                                              width: double.infinity,
                                              bgColor: baseBlueColor
                                                  .withOpacity(0.2),
                                              radius: 8,
                                              widget: Padding(
                                                padding: EdgeInsets.all(20.r),
                                                child: Column(
                                                  children: [
                                                    _content(
                                                        assetName:
                                                            'assets/icons/calendar.svg',
                                                        name: 'Due',
                                                        value: controller
                                                            .detailAssignmentModel
                                                            .value
                                                            .startDate
                                                            .toString()),
                                                    12.verticalSpace,
                                                    _content(
                                                        assetName:
                                                            'assets/icons/timer.svg',
                                                        name: 'Duration',
                                                        value: controller
                                                            .detailAssignmentModel
                                                            .value
                                                            .timeRemaining
                                                            .toString()),
                                                    12.verticalSpace,
                                                  ],
                                                ),
                                              ),
                                            ),
                                            30.verticalSpace,
                                            CustomTextWigdet(
                                              title: "INSTRUCTION",
                                              fontSize: 18,
                                              textColor: greyColor,
                                            ),
                                            10.verticalSpace,
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 5,
                                                vertical: 5,
                                              ),
                                              width: Get.width,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color: greyColor,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                color:
                                                    secondBlueColor.withOpacity(
                                                  0.1,
                                                ),
                                              ),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10),
                                                    child: CustomTextWigdet(
                                                      title: controller
                                                          .detailAssignmentModel
                                                          .value
                                                          .description
                                                          .toString(),
                                                      fontSize: 16,
                                                      textColor: whiteColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            20.verticalSpace,
                                            fileCard(),
                                            20.verticalSpace,
                                            Divider(
                                              color: greyColor,
                                            ),
                                            10.verticalSpace,
                                            CustomTextWigdet(
                                              title: "SUBMISSION FILE",
                                              fontSize: 18,
                                              textColor: greyColor,
                                              fontWeight: FontWeight.w500,
                                            ),
                                            10.verticalSpace,
                                            Obx(
                                              () {
                                                return controller.responseRsult
                                                            .value ==
                                                        true
                                                    ? Container(
                                                        decoration:
                                                            BoxDecoration(
                                                          color: const Color(
                                                              0xFF2B2F36),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                        padding:
                                                            const EdgeInsets
                                                                .all(12),
                                                        child: Row(
                                                          children: [
                                                            const Icon(
                                                                Icons
                                                                    .picture_as_pdf,
                                                                color: Colors
                                                                    .white,
                                                                size: 30),
                                                            const SizedBox(
                                                                width: 10),
                                                            Expanded(
                                                              child: Column(
                                                                crossAxisAlignment:
                                                                    CrossAxisAlignment
                                                                        .start,
                                                                children: [
                                                                  CustomTextWigdet(
                                                                      title:
                                                                          "${controller.assignmentAnswer.value.assignment?.title}",
                                                                      fontSize:
                                                                          16,
                                                                      textColor:
                                                                          whiteColor),
                                                                  4.verticalSpace,
                                                                  CustomTextWigdet(
                                                                      title:
                                                                          "PDF • MB",
                                                                      fontSize:
                                                                          12,
                                                                      textColor:
                                                                          whiteColor),
                                                                ],
                                                              ),
                                                            ),
                                                            Text(
                                                              "${controller.assignmentAnswer.value.createdAt}",
                                                              style: TextStyle(
                                                                color:
                                                                    secondGreyColor,
                                                              ),
                                                            )
                                                          ],
                                                        ),
                                                      )
                                                    : InkWell(
                                                        onTap: () {
                                                          //! Dialog upload file
                                                          Get.dialog(
                                                            Dialog(
                                                              backgroundColor:
                                                                  const Color(
                                                                      0xFF1C1F2A),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                              child: Container(
                                                                width: 720,
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(
                                                                        20),
                                                                child: Column(
                                                                  crossAxisAlignment:
                                                                      CrossAxisAlignment
                                                                          .start,
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    // Header
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .spaceBetween,
                                                                      children: [
                                                                        const Text(
                                                                          'File picker',
                                                                          style: TextStyle(
                                                                              color: Colors.white,
                                                                              fontSize: 16,
                                                                              fontWeight: FontWeight.bold),
                                                                        ),
                                                                        IconButton(
                                                                          icon: const Icon(
                                                                              Icons.close,
                                                                              color: Colors.white70),
                                                                          onPressed: () =>
                                                                              Get.back(),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    20.verticalSpace,

                                                                    // Attachment button
                                                                    const Align(
                                                                      alignment:
                                                                          Alignment
                                                                              .centerLeft,
                                                                      child: Text(
                                                                          'Attachment',
                                                                          style:
                                                                              TextStyle(color: Colors.white70)),
                                                                    ),
                                                                    const SizedBox(
                                                                        height:
                                                                            8),
                                                                    Obx(
                                                                      () {
                                                                        if (controller.selectedFile.value ==
                                                                            null) {
                                                                          return ElevatedButton(
                                                                            style:
                                                                                ElevatedButton.styleFrom(
                                                                              backgroundColor: const Color(0xFF2A2F3D),
                                                                              foregroundColor: Colors.white70,
                                                                              shape: RoundedRectangleBorder(
                                                                                borderRadius: BorderRadius.circular(6),
                                                                              ),
                                                                            ),
                                                                            onPressed:
                                                                                () {
                                                                              controller.pickFile();
                                                                            },
                                                                            child:
                                                                                const Text('Choose file'),
                                                                          );
                                                                        }

                                                                        final file = controller
                                                                            .selectedFile
                                                                            .value!;
                                                                        return Container(
                                                                          decoration:
                                                                              BoxDecoration(
                                                                            color:
                                                                                const Color(0xFF2B2F36),
                                                                            borderRadius:
                                                                                BorderRadius.circular(8),
                                                                          ),
                                                                          padding: const EdgeInsets
                                                                              .all(
                                                                              12),
                                                                          child:
                                                                              Row(
                                                                            children: [
                                                                              const Icon(Icons.picture_as_pdf, color: Colors.white, size: 30),
                                                                              const SizedBox(width: 10),
                                                                              Expanded(
                                                                                child: Column(
                                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                                  children: [
                                                                                    CustomTextWigdet(
                                                                                      title: file.name,
                                                                                      textColor: whiteColor,
                                                                                      fontSize: 14,
                                                                                    ),
                                                                                    CustomTextWigdet(
                                                                                      title: "PDF • ${(file.size / 1024 / 1024).toStringAsFixed(1)}MB",
                                                                                      fontSize: 14,
                                                                                      textColor: greyColor,
                                                                                    )
                                                                                  ],
                                                                                ),
                                                                              ),
                                                                              Container(
                                                                                decoration: BoxDecoration(
                                                                                    borderRadius: BorderRadius.circular(5),
                                                                                    border: Border.all(
                                                                                      color: whiteColor,
                                                                                    )),
                                                                                child: TextButton.icon(
                                                                                  icon: Icon(
                                                                                    Icons.edit,
                                                                                    size: 16,
                                                                                    color: whiteColor,
                                                                                  ),
                                                                                  label: Text(
                                                                                    "Change",
                                                                                    style: TextStyle(
                                                                                      color: whiteColor,
                                                                                    ),
                                                                                  ),
                                                                                  onPressed: controller.pickFile,
                                                                                ),
                                                                              ),
                                                                              const SizedBox(width: 8),
                                                                              Container(
                                                                                decoration: BoxDecoration(
                                                                                    borderRadius: BorderRadius.circular(5),
                                                                                    border: Border.all(
                                                                                      color: redColor,
                                                                                    )),
                                                                                child: TextButton.icon(
                                                                                  icon: const Icon(Icons.delete, color: Colors.red, size: 16),
                                                                                  label: const Text("Delete", style: TextStyle(color: Colors.red)),
                                                                                  onPressed: controller.removeFile,
                                                                                ),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        );
                                                                      },
                                                                    ),

                                                                    const SizedBox(
                                                                        height:
                                                                            20),

                                                                    // Save as
                                                                    const Align(
                                                                      alignment:
                                                                          Alignment
                                                                              .centerLeft,
                                                                      child: Text(
                                                                          'Save as',
                                                                          style:
                                                                              TextStyle(color: Colors.white70)),
                                                                    ),
                                                                    8.verticalSpace,
                                                                    TextField(
                                                                      enabled:
                                                                          false,
                                                                      decoration:
                                                                          InputDecoration(
                                                                        filled:
                                                                            true,
                                                                        fillColor:
                                                                            const Color(0xFF2A2F3D),
                                                                        hintText:
                                                                            'save as',
                                                                        hintStyle:
                                                                            const TextStyle(color: Colors.white38),
                                                                        border:
                                                                            OutlineInputBorder(
                                                                          borderRadius:
                                                                              BorderRadius.circular(6),
                                                                          borderSide:
                                                                              BorderSide.none,
                                                                        ),
                                                                      ),
                                                                      style: const TextStyle(
                                                                          color:
                                                                              Colors.white),
                                                                    ),
                                                                    8.verticalSpace,

                                                                    // Author
                                                                    const Align(
                                                                      alignment:
                                                                          Alignment
                                                                              .centerLeft,
                                                                      child: Text(
                                                                          'Author',
                                                                          style:
                                                                              TextStyle(color: Colors.white70)),
                                                                    ),
                                                                    8.verticalSpace,
                                                                    TextField(
                                                                      enabled:
                                                                          false,
                                                                      controller:
                                                                          TextEditingController(
                                                                              text: 'Muhammad Dani Mu\'ti'),
                                                                      decoration:
                                                                          InputDecoration(
                                                                        filled:
                                                                            true,
                                                                        fillColor:
                                                                            const Color(0xFF2A2F3D),
                                                                        border:
                                                                            OutlineInputBorder(
                                                                          borderRadius:
                                                                              BorderRadius.circular(6),
                                                                          borderSide:
                                                                              BorderSide.none,
                                                                        ),
                                                                      ),
                                                                      style: const TextStyle(
                                                                          color:
                                                                              Colors.white),
                                                                    ),
                                                                    30.verticalSpace,
                                                                    // Upload Button
                                                                    Row(
                                                                      mainAxisAlignment:
                                                                          MainAxisAlignment
                                                                              .end,
                                                                      children: [
                                                                        CustomFilledButtonWidget(
                                                                          onPressed:
                                                                              () {
                                                                            controller.isUploads(true);
                                                                            Get.back();
                                                                          },
                                                                          widthButton:
                                                                              Get.width * 0.25,
                                                                          isSelected:
                                                                              false,
                                                                          heightButton:
                                                                              60,
                                                                          bgColor:
                                                                              blueColor,
                                                                          fontColor:
                                                                              secondWhiteColor,
                                                                          title:
                                                                              "Upload this file",
                                                                        ),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                        child: Obx(
                                                          () {
                                                            if (controller
                                                                        .isUpload
                                                                        .value ==
                                                                    false ||
                                                                controller
                                                                        .selectedFile
                                                                        .value ==
                                                                    null) {
                                                              return Container(
                                                                width:
                                                                    Get.width,
                                                                height: 274,
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        12,
                                                                    horizontal:
                                                                        10.5),
                                                                decoration:
                                                                    BoxDecoration(
                                                                  border: Border
                                                                      .all(
                                                                    color:
                                                                        blueColor,
                                                                  ),
                                                                  color: blueColor
                                                                      .withOpacity(
                                                                          0.15),
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10),
                                                                ),
                                                                child: Column(
                                                                  mainAxisAlignment:
                                                                      MainAxisAlignment
                                                                          .center,
                                                                  children: [
                                                                    SvgPicture
                                                                        .asset(
                                                                      'assets/icons/file_copy.svg',
                                                                      width: 19,
                                                                      height:
                                                                          22,
                                                                      color: whiteColor
                                                                          .withOpacity(
                                                                              0.6),
                                                                    ),
                                                                    20.verticalSpace,
                                                                    CustomTextWigdet(
                                                                        title:
                                                                            'You can drag and drop files here to add them',
                                                                        fontSize:
                                                                            24,
                                                                        fontWeight:
                                                                            FontWeight
                                                                                .w500,
                                                                        textColor:
                                                                            whiteColor),
                                                                    CustomTextWigdet(
                                                                        title:
                                                                            'Maximum file size: 20MB, maximum number of files: 20',
                                                                        fontSize:
                                                                            20,
                                                                        textColor:
                                                                            greyColor),
                                                                  ],
                                                                ),
                                                              );
                                                            }
                                                            return Container(
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: const Color(
                                                                    0xFF2B2F36),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            8),
                                                              ),
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(12),
                                                              child: Row(
                                                                children: [
                                                                  const Icon(
                                                                      Icons
                                                                          .picture_as_pdf,
                                                                      color: Colors
                                                                          .white,
                                                                      size: 30),
                                                                  const SizedBox(
                                                                      width:
                                                                          10),
                                                                  Expanded(
                                                                    child:
                                                                        Column(
                                                                      crossAxisAlignment:
                                                                          CrossAxisAlignment
                                                                              .start,
                                                                      children: [
                                                                        Text(
                                                                            controller
                                                                                .selectedFile.value!.name,
                                                                            style:
                                                                                const TextStyle(color: Colors.white)),
                                                                        Text(
                                                                            "PDF • ${(controller.selectedFile.value!.size / 1024 / 1024).toStringAsFixed(1)}MB",
                                                                            style:
                                                                                const TextStyle(color: Colors.white60, fontSize: 12)),
                                                                      ],
                                                                    ),
                                                                  ),
                                                                  Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                            borderRadius:
                                                                                BorderRadius.circular(5),
                                                                            border: Border.all(
                                                                              color: whiteColor,
                                                                            )),
                                                                    child:
                                                                        TextButton
                                                                            .icon(
                                                                      icon:
                                                                          Icon(
                                                                        Icons
                                                                            .edit,
                                                                        size:
                                                                            16,
                                                                        color:
                                                                            whiteColor,
                                                                      ),
                                                                      label:
                                                                          Text(
                                                                        "Change",
                                                                        style:
                                                                            TextStyle(
                                                                          color:
                                                                              whiteColor,
                                                                        ),
                                                                      ),
                                                                      onPressed:
                                                                          controller
                                                                              .pickFile,
                                                                    ),
                                                                  ),
                                                                  const SizedBox(
                                                                      width: 8),
                                                                  Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                            borderRadius:
                                                                                BorderRadius.circular(5),
                                                                            border: Border.all(
                                                                              color: redColor,
                                                                            )),
                                                                    child:
                                                                        TextButton
                                                                            .icon(
                                                                      icon: const Icon(
                                                                          Icons
                                                                              .delete,
                                                                          color: Colors
                                                                              .red,
                                                                          size:
                                                                              16),
                                                                      label: const Text(
                                                                          "Delete",
                                                                          style:
                                                                              TextStyle(color: Colors.red)),
                                                                      onPressed:
                                                                          controller
                                                                              .removeFile,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            );
                                                          },
                                                        ),
                                                      );
                                              },
                                            ),
                                            30.verticalSpace,
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Obx(
                                                  () {
                                                    if (controller
                                                        .isSave.value) {
                                                      return Row(
                                                        children: [
                                                          Container(
                                                            decoration:
                                                                BoxDecoration(
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(
                                                                                5),
                                                                    border:
                                                                        Border
                                                                            .all(
                                                                      color:
                                                                          whiteColor,
                                                                    )),
                                                            child:
                                                                TextButton.icon(
                                                              icon: Icon(
                                                                Icons.edit,
                                                                size: 16,
                                                                color:
                                                                    whiteColor,
                                                              ),
                                                              label: Text(
                                                                "Change",
                                                                style:
                                                                    TextStyle(
                                                                  color:
                                                                      whiteColor,
                                                                ),
                                                              ),
                                                              onPressed:
                                                                  controller
                                                                      .pickFile,
                                                            ),
                                                          ),
                                                          10.horizontalSpace,
                                                          Container(
                                                            decoration:
                                                                BoxDecoration(
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(
                                                                                5),
                                                                    border:
                                                                        Border
                                                                            .all(
                                                                      color:
                                                                          redColor,
                                                                    )),
                                                            child:
                                                                TextButton.icon(
                                                              icon: const Icon(
                                                                  Icons.delete,
                                                                  color: Colors
                                                                      .red,
                                                                  size: 16),
                                                              label: const Text(
                                                                  "Delete",
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .red)),
                                                              onPressed:
                                                                  controller
                                                                      .removeFile,
                                                            ),
                                                          )
                                                        ],
                                                      );
                                                    }

                                                    return controller
                                                                .responseRsult
                                                                .value ==
                                                            true
                                                        ? Row(
                                                            children: [
                                                              Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                        borderRadius:
                                                                            BorderRadius.circular(
                                                                                5),
                                                                        border:
                                                                            Border.all(
                                                                          color:
                                                                              whiteColor,
                                                                        )),
                                                                child:
                                                                    TextButton
                                                                        .icon(
                                                                  icon: Icon(
                                                                    Icons.edit,
                                                                    size: 16,
                                                                    color:
                                                                        whiteColor,
                                                                  ),
                                                                  label: Text(
                                                                    "Edit Submission",
                                                                    style:
                                                                        TextStyle(
                                                                      color:
                                                                          whiteColor,
                                                                    ),
                                                                  ),
                                                                  onPressed:
                                                                      controller
                                                                          .pickFile,
                                                                ),
                                                              ),
                                                              10.horizontalSpace,
                                                              Container(
                                                                decoration:
                                                                    BoxDecoration(
                                                                        borderRadius:
                                                                            BorderRadius.circular(
                                                                                5),
                                                                        border:
                                                                            Border.all(
                                                                          color:
                                                                              redColor,
                                                                        )),
                                                                child:
                                                                    TextButton
                                                                        .icon(
                                                                  icon: const Icon(
                                                                      Icons
                                                                          .delete,
                                                                      color: Colors
                                                                          .red,
                                                                      size: 16),
                                                                  label: const Text(
                                                                      "Remove Submission",
                                                                      style: TextStyle(
                                                                          color:
                                                                              Colors.red)),
                                                                  onPressed:
                                                                      () async {
                                                                    await controller.removeAssignment(
                                                                        id: id,
                                                                        file: controller
                                                                            .assignmentAnswer
                                                                            .value
                                                                            .file!);
                                                                  },
                                                                ),
                                                              )
                                                            ],
                                                          )
                                                        : Row(
                                                            children: [
                                                              InkWell(
                                                                onTap: () {},
                                                                child:
                                                                    Container(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          5,
                                                                      horizontal:
                                                                          30),
                                                                  height: 35,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    border:
                                                                        Border
                                                                            .all(
                                                                      color:
                                                                          greyColor,
                                                                    ),
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(5),
                                                                  ),
                                                                  child: Center(
                                                                    child:
                                                                        CustomTextWigdet(
                                                                      title:
                                                                          "Cancel",
                                                                      fontSize:
                                                                          16,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w500,
                                                                      textColor:
                                                                          whiteColor,
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                              8.horizontalSpace,
                                                              InkWell(
                                                                onTap:
                                                                    () async {
                                                                  await controller.uploadFile(
                                                                      id: controller
                                                                          .detailAssignmentModel
                                                                          .value
                                                                          .id
                                                                          .toString());

                                                                  // controller.isSaves(true);
                                                                },
                                                                child:
                                                                    Container(
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          10,
                                                                      horizontal:
                                                                          10),
                                                                  height: 35,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: controller
                                                                            .isSave
                                                                            .value
                                                                        ? greyColor
                                                                        : blueColor,
                                                                    border:
                                                                        Border
                                                                            .all(
                                                                      color:
                                                                          greyColor,
                                                                    ),
                                                                    borderRadius:
                                                                        BorderRadius
                                                                            .circular(5),
                                                                  ),
                                                                  child: Obx(
                                                                    () =>
                                                                        Center(
                                                                      child: controller
                                                                              .isLoading
                                                                              .value
                                                                          ? CircularProgressIndicator(
                                                                              color: whiteColor,
                                                                            )
                                                                          : CustomTextWigdet(
                                                                              title: "Save Changes",
                                                                              fontSize: 18,
                                                                              fontWeight: FontWeight.w500,
                                                                              textColor: whiteColor,
                                                                            ),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          );
                                                  },
                                                )
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    60.verticalSpace,
                                    Obx(
                                      () => Row(
                                        children: [
                                          CustomFilledButtonWidget(
                                            onPressed:
                                                controller.listButton.value == 1
                                                    ? null
                                                    : () {
                                                        controller
                                                            .changeSelectedButton(
                                                                controller
                                                                        .listButton
                                                                        .value -
                                                                    1);
                                                      },
                                            isOutlined:
                                                controller.listButton.value == 1
                                                    ? false
                                                    : true,
                                            borderColor: greyColor,
                                            bgColor:
                                                controller.listButton.value == 1
                                                    ? greyColor
                                                    : Colors.transparent,
                                            title: 'Previous',
                                            fontSize: 16,
                                            fontColor: whiteColor,
                                            widthButton: 105,
                                            heightButton: 45,
                                            radius: 4,
                                          ),
                                          const Spacer(),
                                          CustomFilledButtonWidget(
                                            onPressed:
                                                controller.listButton.value == 3
                                                    ? null
                                                    : () {
                                                        controller.listAssigment
                                                            .map(
                                                              (e) => controller
                                                                  .getAssignmentDetail(
                                                                      id: e.id
                                                                          .toString()),
                                                            )
                                                            .toList();

                                                        // controller
                                                        //     .getSubmissionAssignment(
                                                        //         id: index.value
                                                        //             .toString());
                                                        controller
                                                            .changeSelectedButton(
                                                                controller
                                                                        .listButton
                                                                        .value +
                                                                    1);
                                                      },
                                            bgColor:
                                                controller.listButton.value == 3
                                                    ? greyColor
                                                    : blueColor,
                                            title: 'Next',
                                            fontSize: 16,
                                            fontColor: whiteColor,
                                            widthButton: 105,
                                            heightButton: 45,
                                            radius: 4,
                                          ),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
          ),
        ));
  }
}

Widget _content({
  required String assetName,
  required String name,
  required String value,
}) {
  return Row(
    children: [
      SvgPicture.asset(
        assetName,
        height: 16,
        color: whiteColor.withOpacity(0.4),
      ),
      8.horizontalSpace,
      Expanded(
        flex: 1,
        child: CustomTextWigdet(
          title: name,
          fontSize: 18,
          fontWeight: FontWeight.w500,
          textColor: whiteColor.withOpacity(0.4),
        ),
      ),
      Expanded(
        flex: 3,
        child: CustomTextWigdet(
          title: (value.isNotEmpty && value != "0")
              ? DateFormat('EEEE, d MMMM yyyy – HH:mm', 'id_ID')
                  .format(DateTime.tryParse(value) ?? DateTime.now())
              : "-",
          fontSize: 18,
          fontWeight: FontWeight.w500,
          textColor: whiteColor,
        ),
      ),
    ],
  );
}
