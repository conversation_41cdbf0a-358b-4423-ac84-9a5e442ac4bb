// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/hanjar_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_video_player.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class OpenHanjarView extends StatefulWidget {
  final String? imageUrl;
  OpenHanjarView({super.key, this.imageUrl = ''});

  @override
  State<OpenHanjarView> createState() => _OpenHanjarViewState();
}

class _OpenHanjarViewState extends State<OpenHanjarView> {
  HanjarController controller = Get.put(HanjarController());
  final String id = Get.arguments['id'];

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getOpenHanjar(id: id);
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //! File Card
    Widget fileCard() {
      return Container(
        width: 669,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10.5),
        decoration: BoxDecoration(
          border: Border.all(
            color: greyColor,
          ),
          color: whiteColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: whiteColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: SvgPicture.asset(
                'assets/icons/pdf.svg',
                width: 10,
                height: 10,
                color: secondWhiteColor,
              ),
            ),
            12.horizontalSpace,
          ],
        ),
      );
    }

    return CustomScaffold(
        useAppBar: true,
        isCourse: true,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 55.h),
          child: Column(
            children: [
              Row(
                children: [
                  CustomFilledButtonWidget(
                    onPressed: () {
                      Get.back();
                    },
                    isOutlined: true,
                    withIcon: true,
                    borderColor: greyColor,
                    assetName: 'assets/icons/left.svg',
                    title: 'Back',
                    fontColor: whiteColor,
                    fontSize: 16,
                    widthButton: 115,
                    heightIcon: 20,
                    fontWeight: FontWeight.w500,
                    radius: 4,
                  ),
                  const Spacer(),
                  Obx(
                    () {
                      return controller.detailHanjar.value.isCompleted == true
                          ? CustomContainer(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 5),
                              borderRadius: BorderRadius.circular(5),
                              bgColor: greenColor.withOpacity(0.2),
                              widget: Row(
                                children: [
                                  Icon(
                                    Icons.check,
                                    color: greenColor,
                                  ),
                                  Text(
                                    "Completed",
                                    style: TextStyle(
                                        fontSize: 14, color: greenColor),
                                  ),
                                ],
                              ),
                            )
                          : CustomFilledButtonWidget(
                              onPressed: () {},
                              isOutlined: true,
                              borderColor: greyColor,
                              title: 'Mark Completed',
                              fontColor: whiteColor,
                              fontSize: 16,
                              widthButton: 165,
                              fontWeight: FontWeight.w500,
                              radius: 4,
                            );
                    },
                  )
                ],
              ),
              32.verticalSpace,
              32.verticalSpace,
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomContainer(
                    bgColor: secondBlueColor.withOpacity(0.1),
                    width: 260,
                    height: Get.height,
                    radius: 10,
                    widget: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 20.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const CustomTextWigdet(
                                title: 'List Hanjar',
                                fontSize: 18,
                              ),
                              const Spacer(),
                              SvgPicture.asset(
                                'assets/icons/list.svg',
                                height: 20.h,
                              )
                            ],
                          ),
                          4.verticalSpace,
                          CustomTextWigdet(
                            title: 'Hanjar',
                            fontSize: 14,
                            textColor: baseBlueColor,
                          ),
                          20.verticalSpace,
                          SizedBox(
                            height: Get.height / 1.7,
                            child: ListView.builder(
                              itemCount: 20,
                              itemBuilder: (item, idx) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: CustomFilledButtonWidget(
                                    widthButton: 230,
                                    heightButton: 45,
                                    onPressed: () {
                                      // controller.changeSelectedButton(
                                      //     idx + 1);
                                    },
                                    bgColor: whiteColor.withOpacity(0.1),
                                    bgSelectedColor:
                                        whiteColor.withOpacity(0.5),
                                    isSelected: true,
                                    radius: 8,
                                    title: 'Hanjar',
                                    fontSize: 16,
                                    fontColor: whiteColor,
                                  ),
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  24.horizontalSpace,
                  Obx(() {
                    if (controller.isLoadingDetail.value) {
                      return Expanded(
                          child: Center(
                        child: CircularProgressIndicator(),
                      ));
                    } else if (controller.detailHanjar.value.type == "Hanjar") {
                      var data = controller.detailHanjar.value;
                      return Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: const Color(0x1A90ADD9),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    controller.detailHanjar.value.title
                                        .toString(),
                                    style: TextStyle(
                                        color: whiteColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16),
                                  ),
                                  20.verticalSpace,
                                  SizedBox(
                                      width: Get.width,
                                      height: Get.height / 1.3,
                                      // child: PDFViwerPage(
                                      // path:
                                      //     "http://93.127.185.148:5000/api/v1/m/hanjar/file/meteorologi_basic.pdf",
                                      // title: "title",
                                      // subtitle: "subtitle"),
                                      // child: OpenReadHanjar()),
                                      child:
                                          SfPdfViewer.network('${data.file}'))
                                ],
                              ),
                            ),
                            60.verticalSpace,
                          ],
                        ),
                      );
                    } else if (controller.detailHanjar.value.type == "Video") {
                      var data = controller.detailHanjar.value;
                      return Expanded(
                        child: Column(
                          children: [
                            HanjarVideoPlayer(
                              videoUrl: data.file.toString(),
                            ),
                            60.verticalSpace,
                          ],
                        ),
                      );
                    } else if (controller.detailHanjar.value.type == "Slide") {
                      var data = controller.detailHanjar.value;
                      return Expanded(
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: const Color(0x1A90ADD9),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    controller.detailHanjar.value.title
                                        .toString(),
                                    style: TextStyle(
                                        color: whiteColor,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16),
                                  ),
                                  20.verticalSpace,

                                  SizedBox(
                                    width: Get.width,
                                    height: Get.height / 1.5,
                                    child: InAppWebView(
                                      initialUrlRequest: URLRequest(
                                          url: WebUri(
                                              'https://docs.google.com/gview?embedded=true&url=${Uri.encodeFull(data.file.toString())}')),
                                      onWebViewCreated: (InAppWebViewController
                                          webController) {
                                        // controller.webViewController = webController;
                                      },
                                      onLoadStop: (controller, url) {
                                        print("Page finished loading: $url");
                                      },
                                    ),
                                  ),

                                  // initialFile: "assets/dummy/index.html",
                                ],
                              ),
                            ),
                            60.verticalSpace,
                            60.verticalSpace,
                          ],
                        ),
                      );
                    }
                    return Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: CircularProgressIndicator(
                              color: whiteColor,
                            ),
                          ),
                          60.verticalSpace,
                        ],
                      ),
                    );
                  }),
                ],
              )
            ],
          ),
        ));
  }
}

Widget _content({
  required String assetName,
  required String name,
  required String value,
}) {
  return Row(
    children: [
      SvgPicture.asset(
        assetName,
        height: 16.h,
        color: whiteColor.withOpacity(0.4),
      ),
      4.horizontalSpace,
      Expanded(
        flex: 1,
        child: CustomTextWigdet(
          title: name,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: whiteColor.withOpacity(0.4),
        ),
      ),
      Expanded(
        flex: 3,
        child: CustomTextWigdet(
          title: value,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: whiteColor,
        ),
      ),
    ],
  );
}
