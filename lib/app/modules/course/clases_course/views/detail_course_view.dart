// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'package:audioplayers/audioplayers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/data_dummy.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/hanjar_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/open_hanjar_view.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/pdf_viewer_page.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_list_item.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_list_item_audio.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_list_item_slide.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_list_item_video.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../controllers/clases_course_controller.dart';

class DetailCourseView extends GetView<ClasesCourseController> {
  final String? imageUrl;
  DetailCourseView({super.key, this.imageUrl = ''});

  ClasesCourseController controller = Get.put(ClasesCourseController());
  HanjarController hanjarController = Get.put(HanjarController());

  @override
  Widget build(BuildContext context) {
    //! Header assignment
    Widget _headerAssignment() {
      return Row(
        children: [
          Container(
            width: 52,
            height: 52,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: secondBlueColor.withOpacity(0.1),
            ),
            child: SvgPicture.asset(
              'assets/icons/question_answer.svg',
              width: 10,
              height: 10,
              color: secondWhiteColor,
            ),
          ),
          32.horizontalSpace,
          Expanded(
            child: Text(
              "Assignment 1",
              style: TextStyle(
                  fontSize: 24, color: whiteColor, fontWeight: FontWeight.w600),
            ),
          ),
          Container(
            width: Get.width * 0.14,
            padding: const EdgeInsets.all(5),
            height: 38,
            decoration: BoxDecoration(
              color: whiteColor.withOpacity(0.15),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Center(
              child: Text(
                "NOT STARTED",
                style: TextStyle(
                  color: whiteColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          )
        ],
      );
    }

//! Author
    Widget _author() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text("STRATIFIKASI DOKTRIN",
              style: TextStyle(
                color: whiteColor,
                fontSize: 24,
                fontWeight: FontWeight.w700,
              )),
          14.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const CircleAvatar(
                radius: 10,
                backgroundImage: AssetImage("assets/images/Person.jpg"),
              ),
              4.horizontalSpace,
              Text(
                "Yoshua Kaesang",
                style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w300,
                    color: whiteColor),
              )
            ],
          ),
        ],
      );
    }

//! asignment
    Widget _assignment(int index) {
      return Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 10,
        ),
        width: Get.width,
        height: 44,
        decoration: BoxDecoration(
          color: whiteColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          "Assignment ${index + 1}",
          style: TextStyle(
            color: whiteColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

//! assignment time
    Widget _assignmentTime() {
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 10,
          vertical: 10,
        ),
        width: Get.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: secondBlueColor.withOpacity(
            0.1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: Get.width * 0.2,
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        'assets/icons/calendar.svg',
                        width: 26,
                        height: 16,
                        color: greyColor,
                      ),
                      2.horizontalSpace,
                      Text(
                        "Due Date",
                        style: TextStyle(
                          color: greyColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Text(
                    "Tuesday, April 25, 2025, 15:30",
                    style: TextStyle(
                      color: whiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            20.verticalSpace,
            Row(
              children: [
                Container(
                  width: Get.width * 0.2,
                  child: Row(
                    children: [
                      SvgPicture.asset(
                        'assets/icons/timer.svg',
                        width: 16,
                        height: 16,
                        color: greyColor,
                      ),
                      2.horizontalSpace,
                      Text(
                        "Time Remaining",
                        style: TextStyle(
                          color: greyColor,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Text(
                    "3 hours, 22 mins",
                    style: TextStyle(
                      color: whiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    //! File Card
    Widget fileCard() {
      return Container(
        width: 669,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10.5),
        decoration: BoxDecoration(
          border: Border.all(
            color: greyColor,
          ),
          color: whiteColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: whiteColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(10),
              ),
              child: SvgPicture.asset(
                'assets/icons/pdf.svg',
                width: 10,
                height: 10,
                color: secondWhiteColor,
              ),
            ),
            12.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Tamplate pembuatan makalah",
                    style: TextStyle(
                      color: secondWhiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    )),
                Row(
                  children: [
                    Text("Makalah.pdf",
                        style: TextStyle(
                          color: greyColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        )),
                    6.horizontalSpace,
                    Container(
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: greyColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    6.horizontalSpace,
                    Text("5 MB",
                        style: TextStyle(
                          color: greyColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        )),
                  ],
                ),
              ],
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(
                  color: greyColor,
                ),
                borderRadius: BorderRadius.circular(
                  10,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.download_outlined,
                    color: whiteColor,
                  ),
                  4.horizontalSpace,
                  Text(
                    "Download",
                    style: TextStyle(
                      color: whiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                ],
              ),
            ),
            8.horizontalSpace,
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              decoration: BoxDecoration(
                border: Border.all(
                  color: greyColor,
                ),
                borderRadius: BorderRadius.circular(
                  10,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.visibility_outlined,
                    color: whiteColor,
                  ),
                  4.horizontalSpace,
                  Text(
                    "Read",
                    style: TextStyle(
                      color: whiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      );
    }

    //Audio

    // audioPlayerWidget() {
    //   hanjarController.load(
    //       'https://open.spotify.com/track/0fK7ie6XwGxQTIkpFoWkd1?si=Gi1JXQ75TsS5UxzX8aBw8g'); // ganti dengan URL kamu
    // }

    String formatDuration(Duration duration) {
      return DateFormat('mm:ss').format(DateTime(0).add(duration));
    }

    audio() {
      return Container(
        padding: const EdgeInsets.all(16),
        color: Colors.black87,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(children: [
              const Icon(Icons.music_note, color: Colors.white),
              const SizedBox(width: 10),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Topic A Record Materi 1",
                        style: TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold)),
                    Text("Yoshua Kaesang",
                        style: TextStyle(color: Colors.white60, fontSize: 12)),
                  ],
                ),
              ),
              ElevatedButton(
                onPressed: () {},
                child: const Text("Mark completed",
                    style: TextStyle(fontSize: 12)),
              )
            ]),
            const SizedBox(height: 16),
            Obx(() => Row(
                  children: [
                    Text(formatDuration(hanjarController.position.value),
                        style: const TextStyle(color: Colors.white)),
                    Expanded(
                      child: Slider(
                        activeColor: Colors.white,
                        inactiveColor: Colors.grey,
                        min: 0,
                        max: hanjarController.duration.value.inMilliseconds
                            .toDouble(),
                        value: hanjarController.position.value.inMilliseconds
                            .toDouble()
                            .clamp(
                                0.0,
                                hanjarController.duration.value.inMilliseconds
                                    .toDouble()),
                        onChanged: (value) => hanjarController
                            .seek(Duration(milliseconds: value.toInt())),
                      ),
                    ),
                    Text(formatDuration(hanjarController.duration.value),
                        style: const TextStyle(color: Colors.white)),
                  ],
                )),
            const SizedBox(height: 8),
            Obx(() => Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                        icon: const Icon(Icons.shuffle, color: Colors.white),
                        onPressed: () {}),
                    IconButton(
                        icon: const Icon(Icons.skip_previous,
                            color: Colors.white),
                        onPressed: () {}),
                    CircleAvatar(
                      backgroundColor: Colors.white,
                      child: IconButton(
                        icon: hanjarController.isLoading.value
                            ? const CircularProgressIndicator()
                            : Icon(hanjarController.isPlaying.value
                                ? Icons.pause
                                : Icons.play_arrow),
                        onPressed: () async {
                          if (hanjarController.isPlaying.value) {
                            hanjarController.pauseAudio();
                          } else {
                            String url =
                                'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3';

                            await hanjarController.players.play(UrlSource(url));
                          }
                        },
                      ),
                    ),
                    IconButton(
                        icon: const Icon(Icons.skip_next, color: Colors.white),
                        onPressed: () {}),
                    IconButton(
                        icon: const Icon(Icons.repeat, color: Colors.white),
                        onPressed: () {}),
                  ],
                ))
          ],
        ),
      );
    }

    final data = DataDummy().quizSummary;
    return Obx(
      () => CustomScaffold(
        bottomNavigationBar: controller.selectedMapetIndex.value == 3
            ? audio()
            : const SizedBox(),
        useAppBar: true,
        isCourse: true,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 55.h),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  CustomFilledButtonWidget(
                    onPressed: () {
                      Get.back();
                    },
                    isOutlined: true,
                    withIcon: true,
                    borderColor: greyColor,
                    assetName: 'assets/icons/left.svg',
                    title: 'Back',
                    fontColor: whiteColor,
                    fontSize: 16,
                    widthButton: 115,
                    heightIcon: 20,
                    fontWeight: FontWeight.w500,
                    radius: 4,
                  ),
                  // const Spacer(),
                  // CustomFilledButtonWidget(
                  //   onPressed: () {},
                  //   isOutlined: true,
                  //   borderColor: greyColor,
                  //   title: 'Mark Completed',
                  //   fontColor: whiteColor,
                  //   fontSize: 16,
                  //   widthButton: 165,
                  //   fontWeight: FontWeight.w500,
                  //   radius: 4,
                  // )
                ],
              ),
              32.verticalSpace,
              SizedBox(
                height: 290.h,
                width: double.infinity,
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10.r),
                      child: Stack(
                        children: [
                          SizedBox(
                            height: 290.h,
                            width: double.infinity,
                            child: (imageUrl == null || imageUrl!.isEmpty)
                                ? Image.asset(
                                    'assets/images/image 115.png',
                                    fit: BoxFit.cover,
                                  )
                                : CachedNetworkImage(
                                    imageUrl: imageUrl ?? '',
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) =>
                                        const CircularProgressIndicator(),
                                    errorWidget: (context, url, error) =>
                                        const Icon(Icons.error),
                                  ),
                          ),
                          Container(
                            height: 290.h,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                  colors: [
                                    baseBlueColor,
                                    baseBlueColor.withOpacity(0.25)
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  stops: const [
                                    0.16,
                                    1,
                                  ]),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 32.w, bottom: 32.h),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Obx(
                              () => CustomTextWigdet(
                                title:
                                    hanjarController.detailHanjar.value.title ??
                                        '',
                                fontSize: 24,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            15.verticalSpace,
                            Row(
                              children: [
                                const CircleAvatar(
                                  radius: 10,
                                ),
                                4.horizontalSpace,
                                Obx(
                                  () => CustomTextWigdet(
                                    title: hanjarController.detailHanjar.value
                                            .subMapel?.name ??
                                        '',
                                    fontSize: 16,
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              40.verticalSpace,
              Obx(
                () => Container(
                  child: Column(
                    children: [
                      Row(
                        children: [
                          InkWell(
                            onTap: () {
                              controller.selectedMapet(0);
                              controller.searchController.clear();
                            },
                            child: const CustomContainer(
                              widget: CustomTextWigdet(title: "All"),
                            ),
                          ),
                          const SizedBox(
                            width: 32,
                          ),
                          InkWell(
                            onTap: () {
                              controller.selectedMapet(1);
                              controller.searchController.clear();
                            },
                            child: const CustomContainer(
                              widget: CustomTextWigdet(title: "Hanjar"),
                            ),
                          ),
                          const SizedBox(
                            width: 32,
                          ),
                          InkWell(
                            onTap: () {
                              controller.selectedMapet(2);
                              controller.searchController.clear();
                            },
                            child: const CustomContainer(
                              widget: CustomTextWigdet(title: "Video"),
                            ),
                          ),
                          const SizedBox(
                            width: 32,
                          ),
                          InkWell(
                            onTap: () {
                              controller.selectedMapet(3);
                              controller.searchController.clear();
                            },
                            child: const CustomContainer(
                              widget: CustomTextWigdet(title: "Audio"),
                            ),
                          ),
                          const SizedBox(
                            width: 32,
                          ),
                          InkWell(
                            onTap: () {
                              controller.selectedMapet(4);
                              controller.searchController.clear();
                            },
                            child: const CustomContainer(
                              widget: CustomTextWigdet(title: "Slide"),
                            ),
                          )
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Container(
                        width: Get.width,
                        height: 1,
                        color: greyColor,
                        child: Row(
                          children: [
                            Container(
                              width: Get.width * 0.02,
                              height: 1,
                              color: controller.selectedMapetIndex.value == 0
                                  ? blueColor
                                  : greyColor.withOpacity(0),
                            ),
                            32.horizontalSpace,
                            Container(
                              width: Get.width * 0.075,
                              height: 1,
                              color: controller.selectedMapetIndex.value == 1
                                  ? blueColor
                                  : greyColor.withOpacity(0),
                            ),
                            30.horizontalSpace,
                            Container(
                              width: Get.width * 0.05,
                              height: 1,
                              color: controller.selectedMapetIndex.value == 2
                                  ? blueColor
                                  : greyColor.withOpacity(0),
                            ),
                            30.horizontalSpace,
                            Container(
                              width: Get.width * 0.075,
                              height: 1,
                              color: controller.selectedMapetIndex.value == 3
                                  ? blueColor
                                  : greyColor.withOpacity(0),
                            ),
                            20.horizontalSpace,
                            Container(
                              width: Get.width * 0.05,
                              height: 1,
                              color: controller.selectedMapetIndex.value == 4
                                  ? blueColor
                                  : greyColor.withOpacity(0),
                            ),
                          ],
                        ),
                      ),
                      20.verticalSpace,
                      Row(
                        children: [
                          CustomFilledButtonWidget(
                            onPressed: () {
                              controller.openFilter();
                            },
                            withIcon: true,
                            assetName: 'assets/icons/filter.svg',
                            widthIcon: 24,
                            heightIcon: 24,
                            title: 'Add Filter',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            fontColor: whiteColor,
                            bgColor: whiteColor.withOpacity(0.05),
                            widthButton: 160,
                            heightButton: 48,
                            radius: 8,
                          ),
                          8.horizontalSpace,
                          CustomTextFieldWidget(
                            radius: 8,
                            obscureText: true,
                            colorField: whiteColor.withOpacity(0.05),
                            colorText: whiteColor,
                            hintText: 'Search',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            colorTextHint: whiteColor.withOpacity(0.5),
                            assetNameIcon: 'assets/icons/search.svg',
                            iconWidth: 5,
                            iconHeight: 5,
                            widthField: 240,
                            heightField: 48,
                            contentPadding: EdgeInsetsDirectional.only(
                              top: 16.h,
                            ),
                          ),
                        ],
                      ),
                      40.verticalSpace,
                      Obx(
                        () {
                          if (controller.selectedMapetIndex.value == 0) {
                            return SizedBox(
                              height: Get.height / 1.7,
                              child: ListView(
                                children: [
                                  if (!hanjarController
                                          .isLoadingAllHanjar.value &&
                                      hanjarController.allHanjar.isEmpty)
                                    const Center(
                                      child: CustomTextWigdet(
                                        title: 'No Hanjar Found',
                                        fontSize: 32,
                                        fontWeight: FontWeight.w700,
                                        textColor: Colors.white,
                                      ),
                                    ),
                                  ...hanjarController.allHanjar.map((e) {
                                    return HanjarListItemWidget(
                                      onWatch: () {
                                        Get.to(
                                          OpenHanjarView(),
                                          arguments: {'id': '${e.id}'},
                                        );
                                      },
                                      onCompleted: () async {
                                        hanjarController.postCompleted(
                                            id: e.id.toString());

                                        hanjarController.getAllhanjar();
                                      },
                                      onPlay: () {},
                                      type: e.type ?? '',
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 10),
                                      title: e.title.toString(),
                                      subtitle: e.file!.split(".").last,
                                      onDownload: () {
                                        FileDownloadUtil.downloadFile(
                                          e.file.toString(),
                                          fileName: e.title,
                                        );
                                      },
                                      onRead: () {
                                        Get.dialog(
                                          PDFViwerPage(
                                            path: e.file.toString(),
                                            title: e.title,
                                            subtitle:
                                                e.subMapel!.name.toString(),
                                          ),
                                        );
                                      },
                                    );
                                  }),
                                  if (hanjarController.isLoadingAllHanjar.value)
                                    const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          } else if (controller.selectedMapetIndex.value == 1) {
                            return SizedBox(
                              height: Get.height / 1.7,
                              child: ListView(
                                children: [
                                  if (!hanjarController
                                          .isLoadingAllHanjar.value &&
                                      hanjarController.allHanjar.isEmpty)
                                    const Center(
                                      child: CustomTextWigdet(
                                        title: 'No Hanjar Found',
                                        fontSize: 32,
                                        fontWeight: FontWeight.w700,
                                        textColor: Colors.white,
                                      ),
                                    ),
                                  ...hanjarController.allHanjar
                                      .where(
                                    (e) => e.type == "Hanjar",
                                  )
                                      .map((e) {
                                    return HanjarListItemWidget(
                                      isVisible: true,
                                      type: e.type ?? '',
                                      isCompleted: e.isCompleted!,
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 10),
                                      title: e.title.toString(),
                                      subtitle: e.file!.split(".").last,
                                      onDownload: () {
                                        downloadVideo(
                                            e.file.toString(), e.id.toString());
                                      },
                                      onPlay: () {},
                                      onWatch: () {
                                        Get.to(
                                          OpenHanjarView(),
                                          arguments: {'id': '${e.id}'},
                                        );
                                      },
                                      onCompleted: () async {
                                        hanjarController.postCompleted(
                                            id: e.id.toString());

                                        hanjarController.getAllhanjar();
                                      },
                                      onRead: () {
                                        Get.to(
                                          OpenHanjarView(),
                                          arguments: {'id': '${e.id}'},
                                        );
                                      },
                                    );
                                  }),
                                  if (hanjarController.isLoadingAllHanjar.value)
                                    const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          } else if (controller.selectedMapetIndex.value == 2) {
                            return SizedBox(
                              height: Get.height / 1.7,
                              child: Obx(
                                () => ListView(
                                  children: [
                                    if (!hanjarController
                                            .isLoadingAllHanjar.value &&
                                        hanjarController.allHanjar.isEmpty)
                                      const Center(
                                        child: CustomTextWigdet(
                                          title: 'No Hanjar Found',
                                          fontSize: 32,
                                          fontWeight: FontWeight.w700,
                                          textColor: Colors.white,
                                        ),
                                      ),
                                    ...hanjarController.allHanjar
                                        .where(
                                      (e) => e.type == "Video",
                                    )
                                        .map((e) {
                                      return HanjarListItemVideo(
                                        isCompleted: e.isCompleted,
                                        margin: const EdgeInsets.symmetric(
                                            vertical: 10),
                                        title: e.title.toString(),
                                        subtitle: e.file!.split(".").last,
                                        onWatch: () {
                                          Get.to(
                                            OpenHanjarView(),
                                            arguments: {'id': '${e.id}'},
                                          );
                                        },
                                        onMark: () {
                                          hanjarController.postCompleted(
                                              id: e.id.toString());
                                          hanjarController.getAllhanjar();
                                        },
                                      );
                                    }),
                                    if (hanjarController
                                        .isLoadingAllHanjar.value)
                                      const Center(
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          } else if (controller.selectedMapetIndex.value == 3) {
                            return SizedBox(
                              height: Get.height / 2,
                              child: ListView(
                                children: [
                                  if (!hanjarController
                                          .isLoadingAllHanjar.value &&
                                      hanjarController.allHanjar.isEmpty)
                                    const Center(
                                      child: CustomTextWigdet(
                                        title: 'No Hanjar Found',
                                        fontSize: 32,
                                        fontWeight: FontWeight.w700,
                                        textColor: Colors.white,
                                      ),
                                    ),
                                  ...hanjarController.allHanjar
                                      .where(
                                    (e) => e.type == "Audio",
                                  )
                                      .map((e) {
                                    return HanjarListItemAudio(
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 10),
                                      title: e.title.toString(),
                                      subtitle: e.file!.split(".").last,
                                      onDownload: () async {
                                        await hanjarController.players
                                            .play(UrlSource(e.file.toString()));
                                      },
                                      onRead: () {
                                        Get.to(
                                          OpenHanjarView(),
                                          arguments: {'id': '${e.id}'},
                                        );
                                      },
                                    );
                                  }),
                                  if (hanjarController.isLoadingAllHanjar.value)
                                    const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          } else {
                            return SizedBox(
                              height: Get.height / 1.7,
                              child: ListView(
                                children: [
                                  if (!hanjarController
                                          .isLoadingAllHanjar.value &&
                                      hanjarController.allHanjar.isEmpty)
                                    const Center(
                                      child: CustomTextWigdet(
                                        title: 'No Hanjar Found',
                                        fontSize: 32,
                                        fontWeight: FontWeight.w700,
                                        textColor: Colors.white,
                                      ),
                                    ),
                                  ...hanjarController.allHanjar
                                      .where(
                                    (e) => e.type == "Slide",
                                  )
                                      .map((e) {
                                    return HanjarListItemSlide(
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 10),
                                      title: e.title.toString(),
                                      subtitle: e.file!.split(".").last,
                                      onDownload: () {},
                                      onRead: () {
                                        Get.to(
                                          OpenHanjarView(),
                                          arguments: {'id': '${e.id}'},
                                        );
                                      },
                                    );
                                  }),
                                  if (hanjarController.isLoadingAllHanjar.value)
                                    const Center(
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                      ),
                                    ),
                                ],
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _content({
  required String assetName,
  required String name,
  required String value,
}) {
  return Row(
    children: [
      SvgPicture.asset(
        assetName,
        height: 16.h,
        color: whiteColor.withOpacity(0.4),
      ),
      4.horizontalSpace,
      Expanded(
        flex: 1,
        child: CustomTextWigdet(
          title: name,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: whiteColor.withOpacity(0.4),
        ),
      ),
      Expanded(
        flex: 3,
        child: CustomTextWigdet(
          title: value,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          textColor: whiteColor,
        ),
      ),
    ],
  );
}
