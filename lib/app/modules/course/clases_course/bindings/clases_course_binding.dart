import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/hanjar_controller.dart';

import 'package:mides_skadik/app/modules/course/clases_course/controllers/quiz_question_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/annoucement_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/assignment_calendar_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/attendance_history_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/monthly_schedule_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/task_dashboard_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/today_announcement_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/today_schedule_controller.dart';
import 'package:mides_skadik/widgets/controllers/course_notification_controller.dart';

import '../controllers/clases_course_controller.dart';

class ClasesCourseBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<QuizQuestionController>(
      () => QuizQuestionController(),
    );
    Get.lazyPut<ClasesCourseController>(
      () => ClasesCourseController(),
    );

    Get.lazyPut(() => TodayAnnouncementController());
    Get.lazyPut(() => TodayScheduleController());
    Get.lazyPut(() => AttendanceHistoryController());
    Get.lazyPut(() => MonthlyScheduleController());
    Get.lazyPut(() => AssignmentCalendarController());

    Get.lazyPut(() => CourseNotificationController());
    Get.lazyPut(() => TaskDashboardController());
    Get.lazyPut(() => HanjarController());
  }
}
