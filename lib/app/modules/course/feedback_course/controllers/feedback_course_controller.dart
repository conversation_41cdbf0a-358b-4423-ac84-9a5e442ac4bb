import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/course/feedback/feedback_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class FeedbackCourseController extends GetxController {
  final feedbackController = TextEditingController();
  final selectedImages = <File>[].obs;
  final FeedbackService _service = FeedbackService();
  final feedbackList = <dynamic>[].obs;

  void pickImages() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.image,
    );

    if (result != null && result.paths.isNotEmpty) {
      final newImages = result.paths.map((path) => File(path!)).toList();
      if ((selectedImages.length + newImages.length) > 3) {
        SnackbarUtil.showOnce(
          title: "Peringa<PERSON>",
          message: "Maksimal 3 gambar diperbolehkan.",
        );
        return;
      }
      selectedImages.addAll(newImages);
    }
  }

  void removeImageAt(int index) {
    selectedImages.removeAt(index);
  }

  void submitFeedback() async {
    final feedbackText = feedbackController.text.trim();

    // Validasi content kosong
    if (feedbackText.isEmpty) {
      SnackbarUtil.showOnce(
        title: "Peringatan",
        message: "Masukkan isi feedback terlebih dahulu.",
      );
      return;
    }

    File? img1 = selectedImages.isNotEmpty ? selectedImages[0] : null;
    File? img2 = selectedImages.length > 1 ? selectedImages[1] : null;
    File? img3 = selectedImages.length > 2 ? selectedImages[2] : null;

    final result = await _service.postFeedback(
      content: feedbackText,
      image1: img1,
      image2: img2,
      image3: img3,
    );

    if (result.isSuccess) {
      feedbackController.clear();
      selectedImages.clear();
      getFeedbackList();
      SnackbarUtil.showOnce(
        title: "Berhasil",
        message: "Feedback berhasil dikirim.",
      );
    } else {
      SnackbarUtil.showOnce(
        title: "Gagal",
        message: result.errorMessage ?? "Gagal mengirim feedback.",
      );
    }
  }

  Future<void> getFeedbackList() async {
    final result = await _service.getFeedbackList();

    if (result.isSuccess && result.resultValue != null) {
      feedbackList.assignAll(result.resultValue!);
    } else {
      LogService.log.e("Error fetching feedback list: ${result.errorMessage}");
      SnackbarUtil.showOnce(
        title: "Gagal Memuat",
        message: result.errorMessage ?? "Gagal mengambil data feedback.",
      );
    }
  }

  @override
  void onClose() {
    feedbackController.dispose();
    super.onClose();
  }

  @override
  void onInit() {
    super.onInit();
    getFeedbackList();
  }
}
