import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/feedback_course_controller.dart';

class FeedbackCourseView extends GetView<FeedbackCourseController> {
  const FeedbackCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 60.h, horizontal: 360.w),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    110.verticalSpace,
                    Row(
                      children: [
                        CustomFilledButtonWidget(
                          onPressed: () {
                            Get.offAll(
                              () => const BottomNavBar(choosenScreen: 'course'),
                            );
                          },
                          withIcon: true,
                          assetName: 'assets/icons/back.svg',
                          title: 'Feedback',
                          fontWeight: FontWeight.w600,
                          fontColor: whiteColor,
                          fontSize: 32,
                          heightIcon: 32,
                          widthIcon: 32,
                          bgColor: Colors.transparent,
                        ),
                      ],
                    ),
                    40.verticalSpace,
                    CustomContainer(
                      width: 760,
                      radius: 8,
                      bgColor: baseBlueColor.withValues(alpha: 0.3),
                      widget: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 32.h, horizontal: 20.w),
                        child: Column(
                          children: [
                            SvgPicture.asset("assets/icons/feedback.svg"),
                            20.verticalSpace,
                            CustomTextWigdet(
                              title:
                                  'Beri masukan terkait sistem pendidikan di M-IDES',
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                              textColor: whiteColor,
                            ),
                            8.verticalSpace,
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 90.w),
                              child: CustomTextWigdet(
                                title:
                                    'Masukan anda sangat berarti agar kami dapat selalu memberikan pengalaman terbaik ketika kamu menggunakan sistem pendidikan ini',
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                                textColor:
                                    secondWhiteColor.withValues(alpha: 0.7),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            40.verticalSpace,
                            // INPUTAN DISINI
                            CustomContainer(
                              width: double.infinity,
                              height: 500,
                              padding: EdgeInsets.all(16.r),
                              bgColor: whiteColor.withOpacity(0.05),
                              border: Border.all(
                                color: secondWhiteColor.withOpacity(0.3),
                                width: 0.3,
                              ),
                              radius: 8,
                              widget: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextField(
                                    controller: controller.feedbackController,
                                    maxLines: 5,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w400,
                                    ),
                                    decoration: InputDecoration(
                                      hintText: 'Enter your feedback here...',
                                      hintStyle: TextStyle(
                                        fontSize: 18.sp,
                                        color:
                                            whiteColor.withValues(alpha: 0.7),
                                      ),
                                      border: InputBorder.none,
                                    ),
                                  ),
                                  5.verticalSpace,
                                  Obx(() {
                                    return Wrap(
                                      spacing: 8,
                                      children: controller.selectedImages
                                          .asMap()
                                          .entries
                                          .map((entry) {
                                        int index = entry.key;
                                        File image = entry.value;
                                        return Stack(
                                          children: [
                                            Image.file(image,
                                                width: 100,
                                                height: 100,
                                                fit: BoxFit.cover),
                                            Positioned(
                                              top: 0,
                                              right: 0,
                                              child: GestureDetector(
                                                onTap: () => controller
                                                    .selectedImages
                                                    .removeAt(index),
                                                child: Padding(
                                                  padding: EdgeInsets.all(5.r),
                                                  child: CustomContainer(
                                                    bgColor: greyColor
                                                        .withValues(alpha: 0.5),
                                                    radius: 4,
                                                    widget: Padding(
                                                      padding:
                                                          EdgeInsets.all(5.r),
                                                      child: Center(
                                                        child: Icon(
                                                          Icons.close,
                                                          size: 20.r,
                                                          color: whiteColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        );
                                      }).toList(),
                                    );
                                  }),
                                  const Spacer(),
                                  Align(
                                    alignment: Alignment.bottomRight,
                                    child: CustomFilledButtonWidget(
                                      onPressed: controller.pickImages,
                                      withIcon: true,
                                      assetName: "assets/icons/upload.svg",
                                      widthIcon: 20,
                                      heightIcon: 20,
                                      iconColor: whiteColor,
                                      title: "Upload Image",
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      fontColor: whiteColor,
                                      widthButton: 172,
                                      heightButton: 44,
                                      radius: 8,
                                      bgColor: Colors.transparent,
                                      borderColor: Colors.white24,
                                      isOutlined: true,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            20.verticalSpace,
                            CustomFilledButtonWidget(
                              onPressed: () {
                                controller.submitFeedback();
                              },
                              title: 'Send',
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              fontColor: whiteColor,
                              bgColor: blueColor,
                              widthButton: 720,
                              heightButton: 44,
                              radius: 8,
                            )
                          ],
                        ),
                      ),
                    ),
                    40.verticalSpace,
                    CustomTextWigdet(
                      title: 'Feeback History',
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      textColor: secondWhiteColor.withValues(
                        alpha: 0.7,
                      ),
                    ),
                    10.verticalSpace,
                    Obx(() {
                      final feedbacks = controller.feedbackList;

                      if (feedbacks.isEmpty) {
                        return CustomContainer(
                          width: double.infinity,
                          height: 870,
                          radius: 8,
                          bgColor: Colors.transparent,
                          border: Border.all(
                            color: secondWhiteColor.withOpacity(0.3),
                            width: 0.3,
                          ),
                          widget: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SvgPicture.asset('assets/icons/illus.svg'),
                              10.verticalSpace,
                              CustomTextWigdet(
                                title: 'No feedback yet on',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                textColor: whiteColor,
                              ),
                              CustomTextWigdet(
                                title:
                                    'Add feedback for our development, your feedback is valuable to us.',
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                textColor:
                                    secondWhiteColor.withValues(alpha: 0.7),
                              ),
                            ],
                          ),
                        );
                      }

                      return SizedBox(
                        width: double.infinity,
                        height: 800.h,
                        child: SingleChildScrollView(
                          child: Column(
                            children: feedbacks.map((item) {
                              return Padding(
                                padding: EdgeInsets.only(bottom: 16.h),
                                child: CustomContainer(
                                  width: double.infinity,
                                  radius: 8,
                                  widget: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // User Info
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CircleAvatar(
                                            radius: 30.r,
                                            backgroundImage: NetworkImage(
                                                item.user?.imageProfile ?? ''),
                                            backgroundColor:
                                                Colors.grey.shade800,
                                          ),
                                          10.horizontalSpace,
                                          Expanded(
                                            child: CustomContainer(
                                              radius: 8,
                                              padding: EdgeInsets.all(8.r),
                                              widget: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      CustomTextWigdet(
                                                        title:
                                                            item.user?.name ??
                                                                "Unknown",
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                        textColor: whiteColor,
                                                      ),
                                                      10.horizontalSpace,
                                                      CustomTextWigdet(
                                                        title: item.createdAt !=
                                                                null
                                                            ? '${item.createdAt!.day}/${item.createdAt!.month}/${item.createdAt!.year}'
                                                            : "Unknown Date",
                                                        fontSize: 12,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        textColor:
                                                            secondWhiteColor
                                                                .withOpacity(
                                                                    0.7),
                                                      ),
                                                    ],
                                                  ),

                                                  16.verticalSpace,
                                                  // Gambar
                                                  Wrap(
                                                    spacing: 10,
                                                    runSpacing: 10,
                                                    children: [
                                                      if (item
                                                          .image1.isNotEmpty)
                                                        CustomContainer(
                                                          width: 132,
                                                          height: 132,
                                                          radius: 8,
                                                          border: Border.all(
                                                            color:
                                                                secondWhiteColor
                                                                    .withOpacity(
                                                                        0.8),
                                                            width: 0.3,
                                                          ),
                                                          clipBehavior:
                                                              Clip.hardEdge,
                                                          widget: Image.network(
                                                            item.image1,
                                                            width: 132.w,
                                                            height: 132.h,
                                                            fit: BoxFit.cover,
                                                            errorBuilder:
                                                                (context, error,
                                                                    stackTrace) {
                                                              return Center(
                                                                child: Icon(
                                                                  Icons
                                                                      .broken_image,
                                                                  color:
                                                                      secondWhiteColor,
                                                                  size: 40.r,
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      if (item
                                                          .image2.isNotEmpty)
                                                        CustomContainer(
                                                          width: 132,
                                                          height: 132,
                                                          radius: 8,
                                                          clipBehavior:
                                                              Clip.hardEdge,
                                                          border: Border.all(
                                                            color:
                                                                secondWhiteColor
                                                                    .withOpacity(
                                                                        0.8),
                                                            width: 0.3,
                                                          ),
                                                          bgColor: Colors
                                                              .transparent,
                                                          widget: Image.network(
                                                            item.image2,
                                                            width: 132.w,
                                                            height: 132.h,
                                                            fit: BoxFit.cover,
                                                            errorBuilder:
                                                                (context, error,
                                                                    stackTrace) {
                                                              return Center(
                                                                child: Icon(
                                                                  Icons
                                                                      .broken_image,
                                                                  color:
                                                                      secondWhiteColor,
                                                                  size: 40.r,
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                      if (item
                                                          .image3.isNotEmpty)
                                                        CustomContainer(
                                                          width: 132,
                                                          height: 132,
                                                          radius: 8,
                                                          clipBehavior:
                                                              Clip.hardEdge,
                                                          border: Border.all(
                                                            color:
                                                                secondWhiteColor
                                                                    .withOpacity(
                                                                        0.8),
                                                            width: 0.3,
                                                          ),
                                                          bgColor: Colors
                                                              .transparent,
                                                          widget: Image.network(
                                                            item.image3,
                                                            width: 132.w,
                                                            height: 132.h,
                                                            fit: BoxFit.cover,
                                                            errorBuilder:
                                                                (context, error,
                                                                    stackTrace) {
                                                              return Center(
                                                                child: Icon(
                                                                  Icons
                                                                      .broken_image,
                                                                  color:
                                                                      secondWhiteColor,
                                                                  size: 40.r,
                                                                ),
                                                              );
                                                            },
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                  12.verticalSpace,
                                                  CustomTextWigdet(
                                                    title: item.content,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                    textColor: whiteColor,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
