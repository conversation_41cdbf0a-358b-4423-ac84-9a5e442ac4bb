import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:camera/camera.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/dashboard_course_controller.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:ntp/ntp.dart';

class AttendanceCourseController extends GetxController {
  var isCameraInitialized = false.obs;
  var currentPosition = Rxn<Position>();
  var currentTime = ''.obs;
  var isTakingPicture = false.obs;
  CameraController? cameraController;
  CameraController? get camera => cameraController;
  MapController? mapController;
  var isMapReady = false.obs;
  late MarkerIcon personIcon;
  final isClockOut = Get.arguments?['isClockOut'] ?? false;

  /// ================================
  /// TIME
  Future<void> fetchNtpTime() async {
    try {
      final now = await NTP.now();
      LogService.log.i('NTP Server Time: $now');
      currentTime.value = now.toIso8601String();
    } catch (e) {
      LogService.log.e('Gagal ambil waktu via NTP: $e');
    }
  }

  /// ================================
  /// PERMISSION
  Future<void> _initializePermissions() async {
    await Future.delayed(const Duration(milliseconds: 300));
    try {
      final statuses = await [
        Permission.camera,
        Permission.locationWhenInUse,
      ].request();

      if (!(statuses[Permission.camera]?.isGranted ?? false)) {
        LogService.log.w('Izin kamera ditolak');
      } else {
        await initializeCamera();
      }

      if (statuses[Permission.locationWhenInUse]?.isGranted ?? false) {
        await checkLocationService();
      } else {
        LogService.log.w('Izin lokasi ditolak');
      }
    } catch (e) {
      LogService.log.e('Gagal inisialisasi izin: $e');
    }
  }

  /// ================================
  /// CAMERA SECTION
  set camera(CameraController? value) {
    cameraController = value;
    isCameraInitialized.value = value != null;
  }

  Future<void> initializeCamera() async {
    final cameraPermission = await Permission.camera.status;
    if (!cameraPermission.isGranted) {
      LogService.log.w('Kamera tidak bisa diakses karena izin tidak diberikan');
      isCameraInitialized.value = false;
      return;
    }

    try {
      LogService.log.i('🎥 Memulai inisialisasi camera...');

      final cameras = await availableCameras();
      final frontCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      // Dispose camera lama jika ada
      if (cameraController != null) {
        await cameraController?.dispose();
      }

      cameraController = CameraController(
        frontCamera,
        ResolutionPreset.medium,
        enableAudio: false, // Disable audio untuk performa lebih baik
      );

      await cameraController?.initialize();

      // Pastikan camera benar-benar siap
      if (cameraController?.value.isInitialized == true) {
        isCameraInitialized.value = true;
        LogService.log.i('✅ Camera berhasil diinisialisasi');
      } else {
        throw Exception('Camera initialization failed');
      }
    } catch (e) {
      LogService.log.e('❌ Error initializing camera: $e');
      isCameraInitialized.value = false;
      // Retry sekali lagi setelah delay
      Future.delayed(const Duration(seconds: 2), () {
        if (!isCameraInitialized.value) {
          LogService.log.i('🔄 Mencoba inisialisasi camera ulang...');
          initializeCamera();
        }
      });
    }
  }

  Future<void> takePicture() async {
    if (isTakingPicture.value) return;

    if (cameraController == null || !cameraController!.value.isInitialized) {
      LogService.log.w('Kamera belum siap');
      return;
    }

    isTakingPicture.value = true;

    try {
      await _getCurrentLocation();
      final position = currentPosition.value;

      if (position == null) {
        LogService.log.w('📍 Lokasi tidak tersedia saat pengambilan foto');
        isTakingPicture.value = false;
        return;
      }

      final XFile image = await cameraController!.takePicture();

      // Tambahkan pengecekan eksistensi file
      final file = File(image.path);
      if (!file.existsSync()) {
        LogService.log
            .e('Gagal menyimpan foto: file tidak ditemukan di ${image.path}');
        isTakingPicture.value = false;
        return;
      }

      final String timestamp = DateTime.now().toIso8601String();

      final metadata = {
        'timestamp': timestamp,
        'latitude': position.latitude,
        'longitude': position.longitude,
        'camera': 'front',
      };

      final dashboardController = Get.isRegistered<DashboardCourseController>()
          ? Get.find<DashboardCourseController>()
          : Get.put(DashboardCourseController(), permanent: true);

      dashboardController.imagePath = image.path;
      dashboardController.metadata = metadata;
      dashboardController.showReview.value = true;

      LogService.log.i('Gambar berhasil diambil dan disimpan: ${image.path}');
      LogService.log.i('📝 Metadata: $metadata');

      final isClockOut = Get.arguments?['isClockOut'] ?? false;
      LogService.log.i('🚀 Navigasi ke BottomNavBar dengan argumen:');
      LogService.log.i('imagePath: ${image.path}');
      LogService.log.i('📍 metadata: $metadata');
      LogService.log.i('isClockOut: $isClockOut');
      Get.offAll(() => const BottomNavBar(choosenScreen: 'course'), arguments: {
        'imagePath': image.path,
        'metadata': metadata,
        'isClockOut': isClockOut,
      });
    } catch (e) {
      LogService.log.e('Gagal dalam pengambilan gambar: $e');
    } finally {
      isTakingPicture.value = false;
    }
  }

  void disposeCamera() {
    if (cameraController != null) {
      cameraController?.dispose();
      cameraController = null;
      isCameraInitialized.value = false;
    }
  }

  /// ================================
  /// GPS SECTION
  Future<void> checkLocationService() async {
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      LogService.log.w('Layanan lokasi tidak diaktifkan');
      return;
    }

    final permissionStatus = await Geolocator.checkPermission();
    if (permissionStatus == LocationPermission.denied) {
      final status = await Permission.locationWhenInUse.request();
      if (!status.isGranted) {
        LogService.log.w('Izin lokasi ditolak');
        return;
      }
    } else if (permissionStatus == LocationPermission.deniedForever) {
      LogService.log.w('Izin lokasi ditolak secara permanen');
      openAppSettings();
      return;
    }

    await _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    final hasPermission = await Geolocator.checkPermission();
    if (hasPermission == LocationPermission.denied ||
        hasPermission == LocationPermission.deniedForever) {
      LogService.log.w('Tidak ada izin lokasi aktif saat akses lokasi');
      return;
    }

    try {
      currentPosition.value = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      LogService.log.i('📍 Lokasi: ${currentPosition.value}');

      // Add markers to map after getting location (non-blocking)
      addMarkersToMap();
    } catch (e) {
      LogService.log.e('Gagal mendapatkan lokasi: $e');
    }
  }

  // Method untuk pause kamera dengan error handling
  Future<void> pauseCamera() async {
    try {
      if (cameraController != null && cameraController!.value.isInitialized) {
        LogService.log.i('⏸️ Pausing camera preview...');
        await cameraController?.pausePreview();
      }
    } catch (e) {
      LogService.log.e('Error pausing camera: $e');
    }
  }

  // Method untuk resume kamera dengan error handling
  Future<void> resumeCamera() async {
    try {
      if (cameraController != null && cameraController!.value.isInitialized) {
        LogService.log.i('▶️ Resuming camera preview...');
        await cameraController?.resumePreview();
      } else if (!isCameraInitialized.value) {
        LogService.log.i('🔄 Camera not initialized, reinitializing...');
        await initializeCamera();
      }
    } catch (e) {
      LogService.log.e('Error resuming camera: $e');
    }
  }

  /// ================================
  /// OSM MAPS CONTROL

  final GeoPoint fixedLocation =
      GeoPoint(latitude: -6.2153773, longitude: 106.7752294);
  final double allowedRadiusInMeters = 50000;

  bool isWithinRadius() {
    final userPosition = currentPosition.value;
    if (userPosition == null) return false;
    final distance = Geolocator.distanceBetween(
      userPosition.latitude,
      userPosition.longitude,
      fixedLocation.latitude,
      fixedLocation.longitude,
    );
    return distance <= allowedRadiusInMeters;
  }

  void onMapCreated(MapController controller) {
    mapController = controller;
    isMapReady.value = true;
    LogService.log.i('🗺️ Map controller initialized and ready');

    // Tambahkan marker setelah map ready
    if (currentPosition.value != null) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        addMarkersToMap();
      });
    }
  }

  void moveToCurrentLocation() async {
    if (currentPosition.value != null &&
        mapController != null &&
        isMapReady.value) {
      try {
        final pos = currentPosition.value!;
        final geoPoint =
            GeoPoint(latitude: pos.latitude, longitude: pos.longitude);
        await mapController!.moveTo(geoPoint);
        await mapController!.setZoom(zoomLevel: 17);
        LogService.log.i(
            '📍 Moved to current location: ${pos.latitude}, ${pos.longitude}');
      } catch (e) {
        LogService.log.e('Error moving to current location: $e');
      }
    } else {
      LogService.log
          .w('Cannot move to location: map not ready or position null');
    }
  }

  Future<void> loadCustomIcon() async {
    try {
      LogService.log.i("Loading circular person icon...");

      personIcon = const MarkerIcon(
        icon: Icon(
          Icons.person_pin_circle,
          color: Colors.blue,
          size: 56,
        ),
      );
      LogService.log.i("Circular person icon loaded successfully.");
    } catch (e) {
      LogService.log.e('Gagal load ikon person: $e');
    }
  }

  Future<void> addMarkersToMap() async {
    if (currentPosition.value != null &&
        mapController != null &&
        isMapReady.value) {
      final pos = currentPosition.value!;

      try {
        // Add current location marker
        final currentLocationMarker = GeoPoint(
          latitude: pos.latitude,
          longitude: pos.longitude,
        );

        // Jalankan operasi map secara non-blocking
        Future.microtask(() async {
          try {
            await mapController!.addMarker(
              currentLocationMarker,
              markerIcon: personIcon,
            );

            // Add fixed location marker
            await mapController!.addMarker(
              fixedLocation,
              markerIcon: const MarkerIcon(
                icon: Icon(
                  Icons.location_on,
                  color: Colors.red,
                  size: 56,
                ),
              ),
            );

            // Add circle around fixed location
            await mapController!.drawCircle(
              CircleOSM(
                key: "zoneArea",
                centerPoint: fixedLocation,
                radius: allowedRadiusInMeters,
                color: Colors.red.withOpacity(0.2),
                strokeWidth: 2,
              ),
            );

            LogService.log.i('✅ Map markers and circle added successfully');
          } catch (e) {
            LogService.log.e('❌ Error adding markers to map: $e');
          }
        });
      } catch (e) {
        LogService.log.e('❌ Error in addMarkersToMap: $e');
      }
    } else {
      LogService.log.w('⚠️ Cannot add markers: map not ready or position null');
    }
  }

  @override
  void onInit() {
    super.onInit();
    // Inisialisasi MapController tapi belum ready sampai onMapCreated dipanggil
    mapController = MapController(
      initPosition: fixedLocation,
    );
    loadCustomIcon();
    LogService.log.i('🎯 AttendanceCourseController initialized');
  }

  @override
  void onReady() {
    super.onReady();
    // Inisialisasi camera terlebih dahulu untuk menghindari freeze
    _initializePermissions().then((_) async {
      await initializeCamera();
      // Delay sebentar untuk memastikan camera sudah stabil
      await Future.delayed(const Duration(milliseconds: 500));
      // Baru inisialisasi location secara async
      _initializeLocationAsync();
    });
    fetchNtpTime();
  }

  // Method baru untuk inisialisasi location secara async
  Future<void> _initializeLocationAsync() async {
    try {
      await checkLocationService();
    } catch (e) {
      LogService.log.e('Error initializing location: $e');
    }
  }

  @override
  void onClose() {
    LogService.log.i('AttendanceCourseController disposed, kamera dimatikan');
    _disposeResources();
    super.onClose();
  }

  // Method untuk dispose semua resource dengan aman
  Future<void> _disposeResources() async {
    try {
      LogService.log.i('🧹 Disposing camera resources...');

      // Stop camera terlebih dahulu
      if (cameraController?.value.isInitialized == true) {
        await cameraController?.stopImageStream().catchError((e) {
          LogService.log.w('Error stopping image stream: $e');
        });
      }

      // Dispose camera controller
      await cameraController?.dispose().catchError((e) {
        LogService.log.w('Error disposing camera: $e');
      });

      cameraController = null;
      isCameraInitialized.value = false;

      LogService.log.i('✅ Camera resources disposed successfully');
    } catch (e) {
      LogService.log.e('❌ Error disposing resources: $e');
    }
  }
}
