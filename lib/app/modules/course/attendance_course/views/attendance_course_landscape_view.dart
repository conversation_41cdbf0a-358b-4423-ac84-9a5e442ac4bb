import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/attendance_course_controller.dart';

class AttendanceCourseLandscapeView
    extends GetView<AttendanceCourseController> {
  final bool isClockOut;

  const AttendanceCourseLandscapeView({
    super.key,
    required this.isClockOut,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AttendanceCourseController>();

    return Scaffold(
      body: Obx(() {
        if (!controller.isCameraInitialized.value) {
          return Center(
            child: CircularProgressIndicator(
              strokeCap: StrokeCap.round,
              color: baseBlueColor,
              strokeWidth: 8,
            ),
          );
        }

        return Stack(
          children: [
            // Background kamera
            Positioned.fill(
              child: CameraPreview(controller.camera!),
            ),

            // Overlay Bottom
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                child: CustomContainer(
                  width: 1200,
                  height: 450,
                  bgColor: Colors.black.withOpacity(0.5),
                  radius: 12,
                  widget: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Row(
                      children: [
                        // Kiri: MAP
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Obx(() {
                                final position =
                                    controller.currentPosition.value;
                                if (position == null) {
                                  return Center(
                                    child: CircularProgressIndicator(
                                      strokeCap: StrokeCap.round,
                                      color: baseBlueColor,
                                      strokeWidth: 8,
                                    ),
                                  );
                                }

                                return Stack(
                                  children: [
                                    controller.mapController != null
                                        ? OSMFlutter(
                                            controller:
                                                controller.mapController!,
                                            osmOption: OSMOption(
                                              userTrackingOption:
                                                  const UserTrackingOption(
                                                enableTracking: false,
                                                unFollowUser: false,
                                              ),
                                              zoomOption: const ZoomOption(
                                                initZoom: 17,
                                                minZoomLevel: 3,
                                                maxZoomLevel: 19,
                                                stepZoom: 1.0,
                                              ),
                                              userLocationMarker:
                                                  UserLocationMaker(
                                                personMarker: const MarkerIcon(
                                                  icon: Icon(
                                                    Icons
                                                        .location_history_rounded,
                                                    color: Colors.red,
                                                    size: 48,
                                                  ),
                                                ),
                                                directionArrowMarker:
                                                    const MarkerIcon(
                                                  icon: Icon(
                                                    Icons.double_arrow,
                                                    size: 48,
                                                  ),
                                                ),
                                              ),
                                              roadConfiguration:
                                                  const RoadOption(
                                                roadColor: Colors.yellowAccent,
                                              ),
                                            ),
                                            onMapIsReady: (isReady) {
                                              if (isReady &&
                                                  controller.mapController !=
                                                      null) {
                                                controller.onMapCreated(
                                                    controller.mapController!);
                                              }
                                            },
                                          )
                                        : Container(
                                            color: Colors.grey[300],
                                            child: const Center(
                                                child:
                                                    CircularProgressIndicator()),
                                          ),
                                    Positioned(
                                      top: 10,
                                      right: 10,
                                      child: FloatingActionButton(
                                        mini: true,
                                        backgroundColor: Colors.white,
                                        onPressed:
                                            controller.moveToCurrentLocation,
                                        child: Icon(Icons.my_location,
                                            color: blueColor),
                                      ),
                                    ),
                                  ],
                                );
                              }),
                            ),
                          ),
                        ),

                        // Kanan: Button
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.w, vertical: 16.h),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SvgPicture.asset("assets/icons/time.svg",
                                    width: 60.w, height: 60.h),
                                12.verticalSpace,
                                CustomTextWigdet(
                                  title: 'Today Presence',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  textColor: secondWhiteColor.withOpacity(0.6),
                                ),
                                4.verticalSpace,
                                CustomTextWigdet(
                                  title: '07.00 - 17.00',
                                  fontSize: 20,
                                  fontWeight: FontWeight.w600,
                                  textColor: whiteColor,
                                ),
                                16.verticalSpace,
                                CustomContainer(
                                  width: double.infinity,
                                  height: 50,
                                  bgColor: goldenYellow.withOpacity(0.2),
                                  radius: 8,
                                  widget: Center(
                                    child: CustomTextWigdet(
                                      title:
                                          "Lesson will start soon, take attendance!",
                                      fontSize: 16,
                                      fontWeight: FontWeight.w400,
                                      textColor: goldenYellow,
                                    ),
                                  ),
                                ),
                                24.verticalSpace,
                                CustomFilledButtonWidget(
                                  onPressed:
                                      controller.isCameraInitialized.value
                                          ? controller.takePicture
                                          : null,
                                  title: isClockOut ? 'Clock Out' : 'Clock In',
                                  fontColor: whiteColor,
                                  heightButton: 52,
                                  bgColor: blueColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  radius: 8,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
