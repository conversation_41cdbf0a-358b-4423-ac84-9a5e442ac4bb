// attendance_course_view.dart
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_osm_plugin/flutter_osm_plugin.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/attendance_course_controller.dart';
import 'attendance_course_landscape_view.dart';

class AttendanceCourseView extends GetView<AttendanceCourseController> {
  const AttendanceCourseView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AttendanceCourseController(), permanent: false);
    final isClockOut = Get.arguments?['isClockOut'] ?? false;
    final orientation = MediaQuery.of(context).orientation;

    // 👇 Redirect to landscape view if in landscape mode
    if (orientation == Orientation.landscape) {
      return AttendanceCourseLandscapeView(isClockOut: isClockOut);
    }

    // 👇 Default: POTRAIT MODE UI
    return Scaffold(
      body: Obx(() {
        if (!controller.isCameraInitialized.value) {
          return Center(
            child: CircularProgressIndicator(
              strokeCap: StrokeCap.round,
              color: baseBlueColor,
              strokeWidth: 8,
            ),
          );
        }

        return Stack(
          children: [
            Positioned.fill(
              child: CameraPreview(controller.camera!),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 150.h),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomContainer(
                      width: Get.width,
                      height: 40,
                      bgColor:
                          controller.isWithinRadius() ? greenColor : redColor,
                      radius: 4,
                      widget: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              controller.isWithinRadius()
                                  ? "assets/icons/check_course.svg"
                                  : "assets/icons/close_course.svg",
                              width: 20.w,
                              height: 20.h,
                            ),
                            8.horizontalSpace,
                            CustomTextWigdet(
                              title: controller.isWithinRadius()
                                  ? 'You are right at the class location'
                                  : 'You are not in the class location',
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                              textColor: whiteColor,
                            ),
                          ],
                        ),
                      ),
                    ),
                    16.verticalSpace,
                    CustomContainer(
                      width: 817,
                      height: 700,
                      bgColor: Colors.black.withOpacity(0.40),
                      radius: 8,
                      widget: ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 80, sigmaY: 80),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Padding(
                                padding:
                                    EdgeInsets.symmetric(horizontal: 318.w),
                                child: ClipOval(
                                  child: CustomContainer(
                                    width: 70,
                                    height: 70,
                                    bgColor: secondBlueColor.withOpacity(0.2),
                                    widget: Center(
                                      child: SvgPicture.asset(
                                        "assets/icons/time.svg",
                                        width: 40.w,
                                        height: 40.h,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              8.verticalSpace,
                              CustomTextWigdet(
                                title: 'Today Presence',
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                textColor: secondWhiteColor.withOpacity(0.6),
                              ),
                              8.verticalSpace,
                              CustomTextWigdet(
                                title: '07.00 - 17.00',
                                fontSize: 24,
                                fontWeight: FontWeight.w600,
                                textColor: whiteColor,
                              ),
                              8.verticalSpace,
                              CustomContainer(
                                width: 600,
                                height: 31,
                                bgColor: goldenYellow.withOpacity(0.2),
                                widget: Center(
                                  child: CustomTextWigdet(
                                    title:
                                        "The lesson will start in 5 minutes, take attendance immediatelly!",
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    textColor: goldenYellow,
                                  ),
                                ),
                              ),
                              8.verticalSpace,
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12.w),
                                child: CustomContainer(
                                  width: double.maxFinite,
                                  height: 377,
                                  bgColor: Colors.transparent,
                                  borderRadius: BorderRadius.circular(12),
                                  widget: ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: Obx(() {
                                      final position =
                                          controller.currentPosition.value;

                                      if (position == null) {
                                        return Center(
                                          child: CircularProgressIndicator(
                                            strokeCap: StrokeCap.round,
                                            color: baseBlueColor,
                                            strokeWidth: 8,
                                          ),
                                        );
                                      }

                                      return Stack(
                                        children: [
                                          controller.mapController != null
                                              ? OSMFlutter(
                                                  controller:
                                                      controller.mapController!,
                                                  osmOption: OSMOption(
                                                    userTrackingOption:
                                                        const UserTrackingOption(
                                                      enableTracking: false,
                                                      unFollowUser: false,
                                                    ),
                                                    zoomOption:
                                                        const ZoomOption(
                                                      initZoom: 17,
                                                      minZoomLevel: 3,
                                                      maxZoomLevel: 19,
                                                      stepZoom: 1.0,
                                                    ),
                                                    userLocationMarker:
                                                        UserLocationMaker(
                                                      personMarker:
                                                          const MarkerIcon(
                                                        icon: Icon(
                                                          Icons
                                                              .location_history_rounded,
                                                          color: Colors.red,
                                                          size: 48,
                                                        ),
                                                      ),
                                                      directionArrowMarker:
                                                          const MarkerIcon(
                                                        icon: Icon(
                                                          Icons.double_arrow,
                                                          size: 48,
                                                        ),
                                                      ),
                                                    ),
                                                    roadConfiguration:
                                                        const RoadOption(
                                                      roadColor:
                                                          Colors.yellowAccent,
                                                    ),
                                                  ),
                                                  onMapIsReady: (isReady) {
                                                    if (isReady &&
                                                        controller
                                                                .mapController !=
                                                            null) {
                                                      controller.onMapCreated(
                                                          controller
                                                              .mapController!);
                                                    }
                                                  },
                                                )
                                              : Container(
                                                  color: Colors.grey[300],
                                                  child: const Center(
                                                    child:
                                                        CircularProgressIndicator(),
                                                  ),
                                                ),
                                          Positioned(
                                            top: 10,
                                            right: 10,
                                            child: FloatingActionButton(
                                              mini: true,
                                              backgroundColor: Colors.white,
                                              onPressed: controller
                                                  .moveToCurrentLocation,
                                              child: Icon(Icons.my_location,
                                                  color: blueColor),
                                            ),
                                          ),
                                        ],
                                      );
                                    }),
                                  ),
                                ),
                              ),
                              16.verticalSpace,
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 24.w),
                                child: CustomFilledButtonWidget(
                                  onPressed:
                                      controller.isCameraInitialized.value
                                          ? controller.takePicture
                                          : null,
                                  title: isClockOut ? 'Clock Out' : 'Clock In',
                                  fontColor: whiteColor,
                                  heightButton: 52,
                                  bgColor: blueColor,
                                  fontSize: 24,
                                  fontWeight: FontWeight.w500,
                                  radius: 4,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
