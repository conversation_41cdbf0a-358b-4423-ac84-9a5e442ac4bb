import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../controllers/notifcation_course_controller.dart';

class NotifcationCourseView extends GetView<NotifcationCourseController> {
  const NotifcationCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('NotifcationCourseView'),
        centerTitle: true,
      ),
      body: const Center(
        child: Text(
          'NotifcationCourseView is working',
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}
