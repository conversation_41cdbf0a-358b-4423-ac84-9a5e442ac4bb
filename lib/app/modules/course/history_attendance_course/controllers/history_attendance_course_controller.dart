import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/attendance/attendances_model.dart';
import 'package:mides_skadik/app/data/services/course/attendance/attendance_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class HistoryAttendanceCourseController extends GetxController {
  RxList<AttendanceModel> historyAttendance = <AttendanceModel>[].obs;

  RxString startDate = ''.obs;
  RxString endDate = ''.obs;

  RxInt currentPage = 1.obs;
  RxInt totalPage = 0.obs;
  final AttendanceService attendaneService = AttendanceService();

  // Loading
  RxBool isLoading = false.obs;
  RxBool isLoadingMoreAttendance = false.obs;

  /// ================================
  /// DATE SECTION
  var dateNow = DateTime.now().obs;
  var dateWeek = DateTime.now().add(const Duration(days: 7)).obs;
  var isOpen = false.obs;

  openDialog() {
    isOpen.value = !isOpen.value;
  }

  changeDate(DateRangePickerSelectionChangedArgs args) {
    dateNow.value = args.value.startDate;
    dateWeek.value = args.value.endDate ?? args.value.startDate;
  }

  /// ================================
  /// PAGENATION SECTION
  void setPage(int page) {
    currentPage.value = page;

    getAttendanceHistoryViewAll();
  }

  // TODO: Get Attendance History
  Future<void> getAttendanceHistoryViewAll() async {
    startDate.value = formattedDateOnly(dateNow.value, format: 'yyyy-MM-dd');
    endDate.value = formattedDateOnly(dateWeek.value, format: 'yyyy-MM-dd');

    if (currentPage.value == 1) {
      isLoading.value = true;
    } else {
      isLoadingMoreAttendance.value = true;
    }

    final result = await attendaneService.getListPreviewHistory();

    if (result.isSuccess) {
      historyAttendance.value = result.resultValue ?? [];
      totalPage.value = result.resultTotalData ?? 0;
    } else {
      historyAttendance.value = [];
      totalPage.value = 0;
    }

    isLoading.value = false;
    isLoadingMoreAttendance.value = false;
  }

  @override
  void onClose() {
    historyAttendance.clear();
    super.onClose();
  }
}
