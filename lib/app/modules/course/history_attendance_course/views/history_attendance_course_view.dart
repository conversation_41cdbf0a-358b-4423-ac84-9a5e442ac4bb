import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../controllers/history_attendance_course_controller.dart';

class HistoryAttendanceCourseView
    extends GetView<HistoryAttendanceCourseController> {
  const HistoryAttendanceCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      body: Column(
        children: [
          CustomContainer(
            width: 500,
            widget: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SfDateRangePicker(
                  selectionMode: DateRangePickerSelectionMode.range,
                  selectionShape: DateRangePickerSelectionShape.rectangle,
                  navigationMode: DateRangePickerNavigationMode.none,
                  rangeSelectionColor: secondBlueColor.withOpacity(0.2),
                  startRangeSelectionColor: blueColor,
                  endRangeSelectionColor: blueColor,
                  backgroundColor: Colors.transparent,
                  rangeTextStyle: TextStyle(color: whiteColor),
                  initialSelectedRange: PickerDateRange(
                    DateTime.now(),
                    DateTime.now().add(
                      const Duration(days: 7),
                    ),
                  ),
                  onSelectionChanged:
                      Get.find<HistoryAttendanceCourseController>().changeDate,
                  yearCellStyle: DateRangePickerYearCellStyle(
                    textStyle: const TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w300),
                    todayTextStyle: const TextStyle(color: Colors.white),
                    todayCellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  headerStyle: const DateRangePickerHeaderStyle(
                      textAlign: TextAlign.center,
                      textStyle: TextStyle(color: Colors.white),
                      backgroundColor: Colors.transparent),
                  monthViewSettings: const DateRangePickerMonthViewSettings(
                      dayFormat: 'EEE', showTrailingAndLeadingDates: true),
                  monthCellStyle: DateRangePickerMonthCellStyle(
                    textStyle: const TextStyle(
                        color: Colors.white, fontWeight: FontWeight.w300),
                    todayTextStyle: const TextStyle(color: Colors.white),
                    todayCellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                16.verticalSpace,
                Row(
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {},
                      title: 'Cancel',
                      bgColor: darkRed,
                      radius: 4,
                    ),
                    16.horizontalSpace,
                    CustomFilledButtonWidget(
                      onPressed: () {},
                      bgColor: blueColor,
                      radius: 4,
                      title: 'Apply',
                    )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
