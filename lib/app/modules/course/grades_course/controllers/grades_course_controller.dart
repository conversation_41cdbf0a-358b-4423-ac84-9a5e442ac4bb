import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/accumulated_grades/accumulated_grades_model.dart';
import 'package:mides_skadik/app/data/services/course/grades/accumulated_grades/accumulated_grades_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class GradesCourseController extends GetxController {
  /// ================================
  /// VAR
  var isOpenDetailGrades = false.obs;
  final selectedMenuTabIndex = 0.obs;
  final isLoadingScore = false.obs;
  final Rx<AccumulatedScore?> accumulatedScore = Rx<AccumulatedScore?>(null);
  final AccumulatedScoreService scoreService = AccumulatedScoreService();

  /// ================================
  /// FUNCTION
  void openDetailGrades() {
    isOpenDetailGrades.toggle();
  }

  void changeTab(int index) {
    selectedMenuTabIndex.value = index;
  }

  /// ================================
  /// GET DATA DARI SERVICE
  Future<void> fetchAccumulatedScore() async {
    isLoadingScore.value = true;
    final result = await scoreService.getAccumulatedScore();
    isLoadingScore.value = false;

    if (result.isSuccess) {
      accumulatedScore.value = result.resultValue;
    } else {
      LogService.log.e('Error get score: ${result.errorMessage}');
    }
  }

  @override
  void onInit() {
    super.onInit();
    fetchAccumulatedScore();
  }
}
