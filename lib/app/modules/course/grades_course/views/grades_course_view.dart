import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/grades_course/views/grades_course_landscape.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/grades/academic/academic.dart';
import 'package:mides_skadik/widgets/course/grades/detail_grades.dart';
import 'package:mides_skadik/widgets/course/grades/personality/personality.dart';
import 'package:mides_skadik/widgets/course/grades/samapta/samapta.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/grades_course_controller.dart';

class GradesCourseView extends GetView<GradesCourseController> {
  const GradesCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    final gradesController = Get.put(GradesCourseController());

    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape) {
      return const GradesCourseViewLandscape();
    }
    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 60.w,
                bottom: 60.h,
                right: 60.w,
                top: 150.h,
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: 'Grades',
                      textColor: whiteColor,
                      fontWeight: FontWeight.w700,
                      fontSize: 32,
                    ),
                    40.verticalSpace,
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Profile Grades
                        CustomContainer(
                          bgColor: baseBlueColor.withValues(alpha: 0.3),
                          radius: 8,
                          width: 320,
                          widget: Obx(() {
                            final data =
                                gradesController.accumulatedScore.value;

                            if (data == null) {
                              return const Center(
                                child: CustomLoadingWidget(
                                  width: 30,
                                  height: 30,
                                  imageWidth: 30,
                                  imageHeight: 30,
                                  strokeWidth: 3,
                                ),
                              );
                            }

                            return Padding(
                              padding: EdgeInsets.all(10.r),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ClipOval(
                                    child: SizedBox(
                                      width: 80.w,
                                      height: 80.h,
                                      child: (data.imageProfile.isNotEmpty)
                                          ? Image.network(
                                              data.imageProfile,
                                              fit: BoxFit.cover,
                                              loadingBuilder: (context, child,
                                                  loadingProgress) {
                                                if (loadingProgress == null)
                                                  return child;
                                                return const CustomLoadingWidget(
                                                  width: 60,
                                                  height: 60,
                                                  imageWidth: 60,
                                                  imageHeight: 60,
                                                );
                                              },
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  color: greyColor,
                                                  child: const Icon(
                                                    Icons.person,
                                                    size: 40,
                                                    color: Colors.white,
                                                  ),
                                                );
                                              },
                                            )
                                          : Container(
                                              color: greyColor,
                                              child: const Icon(
                                                Icons.person,
                                                size: 40,
                                                color: Colors.white,
                                              ),
                                            ),
                                    ),
                                  ),
                                  10.verticalSpace,
                                  CustomTextWigdet(
                                    title: data.name,
                                    fontSize: 20,
                                    fontWeight: FontWeight.w700,
                                    textColor: whiteColor,
                                    textAlign: TextAlign.center,
                                  ),
                                  4.verticalSpace,
                                  CustomTextWigdet(
                                    title: '${data.nosis} • ${data.pangkat}',
                                    fontSize: 14,
                                    textColor:
                                        secondWhiteColor.withValues(alpha: 0.8),
                                    fontWeight: FontWeight.w500,
                                  ),
                                  20.verticalSpace,
                                  CustomContainer(
                                    width: 296,
                                    bgColor: baseBlueColor.withOpacity(0.9),
                                    radius: 8,
                                    widget: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 25.w, vertical: 16.h),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: ['NPA', 'NPK', 'NPJ', 'NPP']
                                            .map((label) {
                                          final value = data.scores[label];
                                          return Column(
                                            children: [
                                              CustomTextWigdet(
                                                title: label,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                textColor: whiteColor,
                                              ),
                                              CustomTextWigdet(
                                                title: value != null
                                                    ? value.toStringAsFixed(1)
                                                    : '-',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w600,
                                                textColor: whiteColor,
                                              ),
                                            ],
                                          );
                                        }).toList(),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            );
                          }),
                        ),
                        24.horizontalSpace,
                        Expanded(
                          child: Obx(
                            () => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: List.generate(3, (index) {
                                    final isSelected = gradesController
                                            .selectedMenuTabIndex.value ==
                                        index;
                                    final iconAsset = [
                                      'assets/icons/menu_book.svg',
                                      'assets/icons/mi_user.svg',
                                      'assets/icons/ph_barbell.svg',
                                    ][index];
                                    final title = [
                                      'Academic',
                                      'Personality',
                                      'Samapta'
                                    ][index];

                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 20.w),
                                      child: InkWell(
                                        onTap: () => gradesController
                                            .selectedMenuTabIndex.value = index,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(iconAsset,
                                                    width: 20.w, height: 20.h),
                                                5.horizontalSpace,
                                                CustomTextWigdet(
                                                  title: title,
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.w600,
                                                  textColor: whiteColor,
                                                ),
                                              ],
                                            ),
                                            8.verticalSpace,
                                            AnimatedContainer(
                                              duration: const Duration(
                                                  milliseconds: 300),
                                              height: 2,
                                              width: isSelected ? 110.w : 0,
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? blueColor
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                                ),
                                10.verticalSpace,
                                Divider(color: greyColor, height: 1),
                                24.verticalSpace,
                                Obx(() {
                                  switch (gradesController
                                      .selectedMenuTabIndex.value) {
                                    case 0:
                                      return const Academic();
                                    case 1:
                                      return const Personality();
                                    case 2:
                                      return const Samapta();
                                    default:
                                      return const SizedBox();
                                  }
                                })
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Obx(() {
              if (gradesController.isOpenDetailGrades.value) {
                return Positioned.fill(
                  child: Stack(
                    children: [
                      BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                        child: CustomContainer(
                          bgColor: blackColor.withOpacity(0.8),
                          widget: const Center(
                            child: DetailGrades(),
                          ),
                        ),
                      )
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
