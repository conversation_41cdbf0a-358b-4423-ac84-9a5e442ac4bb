import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/profile_model.dart';
import 'package:mides_skadik/app/data/services/course/profile/profile_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class ProfileCourseController extends GetxController {
  /// ================================
  /// VAR
  var selectedProfileTabIndex = 0.obs;
  var selectedMenuProfileIndex = 0.obs;
  var isOpenEditAccountProfile = false.obs;
  var isOpenEditPhoto = false.obs;
  var isLoadingProfile = false.obs;
  var profileData = Rxn<ProfileModel>();
  final selectedBiographyTabIndex = 0.obs;
  final ProfileService _profileService = ProfileService();

  /// ================================
  /// FUNCTION
  void selectedMenuProfile(int index) {
    selectedMenuProfileIndex.value = index;
  }

  void changeBiographyTab(int index) {
    selectedBiographyTabIndex.value = index;
  }

  void openEditProfile() {
    isOpenEditAccountProfile.toggle();
  }

  void openEditPhoto() {
    isOpenEditPhoto.toggle();
  }

  /// ================================
  /// GET PROFILE
  Future<void> getProfile() async {
    isLoadingProfile.value = true;
    final result = await _profileService.getProfile();
    isLoadingProfile.value = false;

    if (result.isSuccess) {
      profileData.value = result.resultValue;
    } else {
      SnackbarUtil.showOnce(title: 'Error', message: 'Something went wrong');
    }
  }

  @override
  void onInit() {
    super.onInit();
    getProfile();
  }
}
