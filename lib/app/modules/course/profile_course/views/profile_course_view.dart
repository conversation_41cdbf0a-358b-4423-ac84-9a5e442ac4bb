import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/modules/course/profile_course/views/profile_course_landscape.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/aboutme_profile.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/edit_account_profile/edit_account_profile.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/edit_photo_profile/edit_photo_profile.dart';
import 'package:mides_skadik/widgets/course/profile/change_password/change_password.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/personal_biography.dart';
import 'package:mides_skadik/widgets/course/profile/progress_profile.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/profile_course_controller.dart';

class ProfileCourseView extends GetView<ProfileCourseController> {
  const ProfileCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape) {
      return const ProfileCourseViewLandscape();
    }
    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 60.h, horizontal: 60.w),
                    child: Column(
                      children: [
                        110.verticalSpace,
                        Row(
                          children: [
                            CustomFilledButtonWidget(
                              onPressed: () {
                                Get.offAll(
                                  () => const BottomNavBar(
                                      choosenScreen: 'course'),
                                );
                              },
                              withIcon: true,
                              assetName: 'assets/icons/back.svg',
                              title: 'Profile',
                              fontWeight: FontWeight.w600,
                              fontColor: whiteColor,
                              fontSize: 32,
                              heightIcon: 32,
                              widthIcon: 32,
                              bgColor: Colors.transparent,
                            ),
                          ],
                        ),
                        32.verticalSpace,
                        Obx(() {
                          final profile = controller.profileData.value;
                          return Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              ClipOval(
                                child: SizedBox(
                                  width: 90.h,
                                  height: 90.h,
                                  child: Image.network(
                                    profile?.imageProfile ?? '',
                                    fit: BoxFit.cover,
                                    loadingBuilder:
                                        (context, child, loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return const CustomLoadingWidget(
                                        width: 60,
                                        height: 60,
                                        imageWidth: 60,
                                        imageHeight: 60,
                                      );
                                    },
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        color: greyColor,
                                        child: const Icon(
                                          Icons.person,
                                          size: 40,
                                          color: Colors.white,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                              10.horizontalSpace,
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextWigdet(
                                    title: profile?.name ?? "Nama Default",
                                    fontSize: 28,
                                    fontWeight: FontWeight.w600,
                                    textColor: whiteColor,
                                  ),
                                  CustomTextWigdet(
                                    title: profile?.nrp ?? "-",
                                    fontSize: 20,
                                    fontWeight: FontWeight.w400,
                                    textColor:
                                        secondWhiteColor.withOpacity(0.7),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              GestureDetector(
                                onTap: () {
                                  showModalBottomSheet(
                                      context: context,
                                      builder: (_) {
                                        return Container(
                                          width: Get.width * Get.width,
                                          height: 150.h,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 20.w, vertical: 20.h),
                                          decoration: BoxDecoration(
                                            color: whiteColor,
                                            borderRadius: BorderRadius.vertical(
                                              top: Radius.circular(20.h),
                                            ),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              20.verticalSpace,
                                              SizedBox(
                                                width: Get.width,
                                                height: 55.h,
                                                child: ElevatedButton(
                                                  onPressed: () {
                                                    StorageService()
                                                        .postSessionLogout();
                                                    Get.offAllNamed("/login");
                                                  },
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor: redColor,
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                    ),
                                                  ),
                                                  child: CustomTextWigdet(
                                                      title: 'Logout',
                                                      fontSize: 18,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      textColor: whiteColor),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      });
                                },
                                child: CustomContainer(
                                  width: 52,
                                  height: 52,
                                  shape: BoxShape.circle,
                                  bgColor: redColor.withOpacity(0.70),
                                  widget: Center(
                                    child: SvgPicture.asset(
                                      'assets/icons/clockin.svg',
                                      color: whiteColor,
                                    ),
                                  ),
                                ),
                              )
                            ],
                          );
                        }),
                        32.verticalSpace,
                        const ProgressProfile(),
                        32.verticalSpace,
                        Obx(
                          () => Visibility(
                            visible:
                                controller.selectedProfileTabIndex.value == 0,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: List.generate(3, (index) {
                                    final isSelected = controller
                                            .selectedMenuProfileIndex.value ==
                                        index;
                                    final iconAsset = [
                                      'assets/icons/mi_user.svg',
                                      'assets/icons/lock.svg',
                                      'assets/icons/description.svg',
                                    ][index];
                                    final title = [
                                      'About Me',
                                      'Change Password',
                                      'Personal Biography',
                                    ][index];

                                    return Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 20.w),
                                      child: InkWell(
                                        onTap: () => controller
                                            .selectedMenuProfile(index),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SvgPicture.asset(iconAsset,
                                                    width: 20.w, height: 20.h),
                                                5.horizontalSpace,
                                                CustomTextWigdet(
                                                  title: title,
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.w600,
                                                  textColor: whiteColor,
                                                ),
                                              ],
                                            ),
                                            8.verticalSpace,
                                            AnimatedContainer(
                                              duration: const Duration(
                                                  milliseconds: 300),
                                              height: 2,
                                              width: isSelected ? 120.w : 0,
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? blueColor
                                                    : Colors.transparent,
                                                borderRadius:
                                                    BorderRadius.circular(2),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }),
                                ),
                                10.verticalSpace,
                                Divider(color: greyColor, height: 1),
                              ],
                            ),
                          ),
                        ),
                        32.verticalSpace,
                        Obx(() {
                          if (controller.selectedProfileTabIndex.value == 0) {
                            switch (controller.selectedMenuProfileIndex.value) {
                              case 0:
                                return const SizedBox(
                                  child: AboutmeProfile(),
                                );
                              case 1:
                                return const SizedBox(
                                  child: ChangePassword(),
                                );
                              case 2:
                                return const SizedBox(
                                  child: PersonalBiography(),
                                );
                              default:
                                return const SizedBox();
                            }
                          } else {
                            return const SizedBox();
                          }
                        })
                      ],
                    ),
                  )
                ],
              ),
            ),
            // EDIT ACCOUNT PROFILE
            Obx(() {
              if (controller.isOpenEditAccountProfile.value) {
                return Positioned.fill(
                  child: Stack(
                    children: [
                      BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                        child: CustomContainer(
                          bgColor: blackColor.withOpacity(0.8),
                          widget: const Center(
                            child: EditAccountProfile(),
                          ),
                        ),
                      )
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            // EDIT PHOTO
            Obx(() {
              if (controller.isOpenEditPhoto.value) {
                return Positioned.fill(
                  child: Stack(
                    children: [
                      BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                        child: CustomContainer(
                          bgColor: blackColor.withOpacity(0.8),
                          widget: const Center(
                            child: EditPhotoProfile(),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),

            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
