import 'dart:async';
import 'dart:typed_data';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_dialog.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import 'package:pdf/widgets.dart' as pw;
import 'package:pdfx/pdfx.dart' as pdfx;

import 'package:flutter/services.dart';

class PDFViwerPage extends StatefulWidget {
  final String? path;
  final String? title;
  final String? subtitle;

  PDFViwerPage({
    Key? key,
    required this.path,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  _PDFViwerPageState createState() => _PDFViwerPageState();
}

class _PDFViwerPageState extends State<PDFViwerPage>
    with WidgetsBindingObserver {
  final PdfViewerController _pdfViewerController = PdfViewerController();

  int _pageNumber = 0;
  int _pageCount = 0;

  double zoomLevel = 0;

  bool isReady = false;
  String errorMessage = '';

  bool isLoading = true;

  String? path;
  Uint8List? pdfData;
  Uint8List? pdfDataRotate;

  List<Uint8List> pdfPages = [];

  int rotate = 0;

  bool isFullscreen = false;
  bool isOpenDrawer = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((e) {
      loadPdf();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isVertical = rotate == 0 || rotate == 180;

    return CustomHanjarDialog(
      title: widget.title,
      subtitle: widget.subtitle,
      child: Expanded(
        child: CustomContainer(
          padding: isFullscreen
              ? const EdgeInsets.all(0)
              : const EdgeInsets.fromLTRB(16, 8, 16, 24),
          widget: isLoading
              ? const CustomContainer(
                  width: double.infinity,
                  height: double.infinity,
                  alignment: Alignment.center,
                  widget: CustomLoadingWidget(),
                )
              : CustomContainer(
                  clipBehavior: Clip.antiAliasWithSaveLayer,
                  radius: isFullscreen ? 0 : 8,
                  widget: Column(
                    children: <Widget>[
                      CustomContainer(
                          bgColor: const Color(0xFF20303C),
                          padding: isFullscreen
                              ? EdgeInsets.all(0)
                              : const EdgeInsets.fromLTRB(8, 1, 8, 1),
                          clipBehavior: Clip.antiAliasWithSaveLayer,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.25),
                              spreadRadius: 0,
                              blurRadius: 4,
                              offset: const Offset(
                                  0, 4), // changes position of shadow
                            ),
                          ],
                          widget: Row(
                            children: [
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isOpenDrawer = !isOpenDrawer;
                                    });
                                  },
                                  child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 300),
                                    transitionBuilder: (child, animation) {
                                      return RotationTransition(
                                        turns: Tween(begin: 0.75, end: 1.0)
                                            .animate(animation),
                                        child: FadeTransition(
                                            opacity: animation, child: child),
                                      );
                                    },
                                    child: Icon(
                                      isOpenDrawer
                                          ? Icons.arrow_back
                                          : Icons.menu,
                                      key: ValueKey<bool>(
                                          isOpenDrawer), // important for AnimatedSwitcher to detect change
                                      color: whiteColor,
                                      size: 16,
                                    ),
                                  ),
                                ),
                              ),

                              16.horizontalSpace,
                              CustomTextWigdet(
                                title: widget.subtitle ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              16.horizontalSpace,
                              CustomContainer(
                                bgColor: const Color(0xFF191B1C),
                                padding: EdgeInsets.fromLTRB(8, 6, 8, 6),
                                widget: CustomTextWigdet(
                                  title: NumberFormat("###,###", "id")
                                      .format(_pageNumber),
                                  fontSize: 14,
                                ),
                              ),
                              8.horizontalSpace,
                              CustomTextWigdet(
                                title:
                                    " /    ${NumberFormat("###,###", "id").format(_pageCount)}",
                                fontSize: 14,
                              ),
                              16.horizontalSpace,
                              SizedBox(
                                height: 20,
                                child: VerticalDivider(
                                  thickness: 0.5,
                                  color: whiteColor,
                                ),
                              ),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  child: Icon(
                                    Icons.remove,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                  onTap: () async {
                                    if (pdfDataRotate == null) return;

                                    _pdfViewerController.zoomLevel -= 0.25;
                                    setState(() {});
                                  },
                                ),
                              ),
                              CustomContainer(
                                bgColor: const Color(0xFF191B1C),
                                padding: const EdgeInsets.fromLTRB(8, 6, 8, 6),
                                widget: CustomTextWigdet(
                                  title:
                                      "${NumberFormat("###,###", "id").format((zoomLevel * 100))}%",
                                  fontSize: 14,
                                ),
                              ),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  child: Icon(
                                    Icons.add,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                  onTap: () async {
                                    if (pdfDataRotate == null) return;
                                    _pdfViewerController.zoomLevel += 0.25;
                                    setState(() {});
                                  },
                                ),
                              ),
                              SizedBox(
                                height: 20,
                                child: VerticalDivider(
                                  thickness: 0.5,
                                  color: pdfDataRotate != null
                                      ? whiteColor
                                      : greyColor,
                                ),
                              ),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  child: Icon(
                                    Icons.present_to_all_outlined,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                  onTap: () async {
                                    if (pdfDataRotate == null) return;
                                    isFullscreen = !isFullscreen;

                                    if (isFullscreen) {
                                      SystemChrome.setEnabledSystemUIMode(
                                          SystemUiMode.immersiveSticky);
                                    } else {
                                      SystemChrome.setEnabledSystemUIMode(
                                          SystemUiMode.edgeToEdge);
                                    }

                                    setState(() {});
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  onTap: rotatePdf,
                                  child: Icon(
                                    Icons.rotate_90_degrees_ccw,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  child: Icon(
                                    Icons.download,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                  onTap: () async {
                                    if (pdfDataRotate == null) return;
                                    await FileDownloadUtil.downloadFile(
                                        widget.path ?? "");
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 40,
                                height: 50,
                                child: GestureDetector(
                                  child: Icon(
                                    Icons.print,
                                    color: pdfDataRotate != null
                                        ? whiteColor
                                        : greyColor,
                                    size: 16,
                                  ),
                                  onTap: () async {
                                    if (pdfDataRotate == null) return;
                                    await Printing.layoutPdf(
                                        onLayout: (_) async {
                                      return pdfData!;
                                    });
                                  },
                                ),
                              ),
                              // SizedBox(
                              //   width: 40,
                              //   height: 50,
                              //   child: GestureDetector(
                              //     child: Icon(
                              //       Icons.more_vert,
                              //       color: whiteColor,
                              //       size: 16,
                              //     ),
                              //     onTap: () async {
                              //       await FileDownloadUtil.downloadFile(widget.path ?? "");
                              //     },
                              //   ),
                              // ),
                            ],
                          )),
                      Expanded(
                        child: Stack(
                          children: [
                            SizedBox(
                              width: Get.width,
                              height: Get.height,
                              child: CustomContainer(
                                bgColor: greyColor,
                                widget: pdfDataRotate != null
                                    ? SfPdfViewer.memory(
                                        pdfDataRotate!,
                                        controller: _pdfViewerController,
                                        scrollDirection:
                                            PdfScrollDirection.vertical,
                                        onDocumentLoaded: (details) {
                                          _pageCount =
                                              _pdfViewerController.pageCount;
                                          _pageNumber =
                                              _pdfViewerController.pageNumber;
                                          zoomLevel =
                                              _pdfViewerController.zoomLevel;
                                          setState(() {});
                                        },
                                        onPageChanged: (details) {
                                          _pageNumber = details.newPageNumber;
                                          setState(() {});
                                        },
                                        onZoomLevelChanged: (details) {
                                          zoomLevel = details.newZoomLevel;
                                          setState(() {});
                                        },
                                        onTap: (details) {
                                          isOpenDrawer = false;
                                          setState(() {});
                                        },
                                      )
                                    : CustomContainer(
                                        margin: EdgeInsets.all(0),
                                        widget: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            const Icon(
                                              Icons.error_sharp,
                                              color: Colors.white,
                                            ),
                                            24.verticalSpace,
                                            const CustomTextWigdet.title(
                                                "Failed to load PDF"),
                                            const CustomTextWigdet.subtitle(
                                                "Please try again later"),
                                          ],
                                        ),
                                      ),
                              ),
                            ),
                            AnimatedPositioned(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.easeInOut,
                              top: 0,
                              bottom: 0,
                              left: isOpenDrawer
                                  ? 0
                                  : -Get.width *
                                      0.3, // hide by moving off screen
                              child: CustomContainer(
                                width: Get.width * 0.3,
                                bgColor: darkBlueColor.withOpacity(0.6),
                                widget: ListView(
                                  padding: EdgeInsets.fromLTRB(16, 16, 16, 16),
                                  children: [
                                    ...pdfPages.asMap().entries.map(
                                      (entry) {
                                        final index = entry.key;
                                        final pageBytes = entry.value;

                                        return Material(
                                          color: Colors.transparent,
                                          child: GestureDetector(
                                            onTap: () {
                                              _pdfViewerController
                                                  .jumpToPage(index + 1);
                                            },
                                            child: CustomContainer(
                                              bgColor: whiteColor,
                                              border: Border.all(
                                                color: _pageNumber == index + 1
                                                    ? blueColor
                                                    : Colors.transparent,
                                                width: 4,
                                              ),
                                              margin: const EdgeInsets.fromLTRB(
                                                  0, 0, 0, 8),
                                              widget: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  Image.memory(pageBytes),
                                                  4.verticalSpace,
                                                  CustomTextWigdet(
                                                    title: 'Page ${index + 1}',
                                                    textColor: blackColor,
                                                    fontSize: 16,
                                                  ),
                                                  16.verticalSpace,
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      },
                                    ).toList(),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      errorMessage.isEmpty
                          ? const SizedBox.shrink()
                          : CustomTextWigdet(
                              title: errorMessage.contains("FileNotFound")
                                  ? "File Not Found"
                                  : "Error Open PDF",
                              textAlign: TextAlign.center,
                              textColor: Colors.black,
                              fontSize: 28,
                            ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  void loadPdf() async {
    if (widget.path != null && widget.path!.contains("http")) {
      pdfData = await FileDownloadUtil.readBytes(widget.path!);

      pdfDataRotate = pdfData;

      initPdfViewer();
    }

    isLoading = false;

    setState(() {});
  }

  void rotatePdf() async {
    if (rotate == 360) rotate = 0;
    rotate = (rotate + 90);

    pdfDataRotate = await rotateExistingPdf(pdfData!, angleDegrees: rotate);

    setState(() {});
  }

  void initPdfViewer() async {
    try {
      final pdfDoc = await pdfx.PdfDocument.openData(pdfData!);
      pdfPages.clear();

      for (int i = 1; i <= pdfDoc.pagesCount; i++) {
        final page = await pdfDoc.getPage(i);
        final pageImage = await page.render(
          width: page.width,
          height: page.height,
          format: pdfx.PdfPageImageFormat.webp,
        );
        pdfPages.add(pageImage!.bytes);
        await page.close(); // release memory
      }

      await pdfDoc.close(); // release memory

      setState(() {});
    } catch (e) {
      pdfData = null;
      pdfDataRotate = null;
    }
  }

  Future<Uint8List> rotateExistingPdf(Uint8List originalPdf,
      {int angleDegrees = 90}) async {
    final pdfDoc = await pdfx.PdfDocument.openData(originalPdf);
    final pdf = pw.Document();

    final isRotated = angleDegrees % 180 != 0;

    for (int i = 1; i <= pdfDoc.pagesCount; i++) {
      final page = await pdfDoc.getPage(i);
      final pageImage =
          await page.render(width: page.width, height: page.height);

      final image = pw.MemoryImage(pageImage!.bytes);

      final originalWidth = page.width.toDouble();
      final originalHeight = page.height.toDouble();
      final rotatedWidth = isRotated ? originalHeight : originalWidth;
      final rotatedHeight = isRotated ? originalWidth : originalHeight;

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat(rotatedWidth, rotatedHeight),
          build: (context) {
            return pw.Center(
              child: pw.Transform.rotateBox(
                angle: angleDegrees * 3.1415927 / 180,
                child: pw.Image(image),
              ),
            );
          },
        ),
      );

      await page.close();
    }

    await pdfDoc.close();
    return pdf.save();
  }
}
