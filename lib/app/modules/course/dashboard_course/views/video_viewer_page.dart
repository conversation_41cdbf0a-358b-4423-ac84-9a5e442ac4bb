import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_dialog.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_video_player.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class VideoViewerPage extends StatefulWidget {
  final String? path;
  final String? title;
  final String? subtitle;

  VideoViewerPage({
    Key? key,
    required this.path,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  _VideoViewerPageState createState() => _VideoViewerPageState();
}

class _VideoViewerPageState extends State<VideoViewerPage>
    with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    return CustomHanjarDialog(
      title: widget.title,
      subtitle: widget.subtitle,
      child: Expanded(
        child: CustomContainer(
          bgColor: const Color(0xFF20303C),
          margin: const EdgeInsets.fromLTRB(16, 8, 16, 24),
          radius: 16,
          widget: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [HanjarVideoPlayer(videoUrl: widget.path ?? "")],
          ),
        ),
      ),
    );
  }
}
