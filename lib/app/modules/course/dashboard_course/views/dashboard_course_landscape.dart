import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/today_announcement_controller.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_alert.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/dashboard/academic_notification/adademic_notification_dashboard.dart.dart';
import 'package:mides_skadik/widgets/course/dashboard/assignment_calendar/assignment_calendar_dashboard.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/attendance_dashboard.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/review_attendance/review_attendance.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_dashboard.dart';
import 'package:mides_skadik/widgets/course/dashboard/learning/learning_dashboard.dart';
import 'package:mides_skadik/widgets/course/dashboard/task/tasklist_dashboard.dart';
import 'package:mides_skadik/widgets/course/dashboard/today_schedule/today_schedule_dashboard.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/annoucement_controller.dart';
import '../controllers/attendance_history_controller.dart';
import '../controllers/dashboard_course_controller.dart';
import '../controllers/today_schedule_controller.dart';

class DashboardCourseLandscape extends GetView<DashboardCourseController> {
  const DashboardCourseLandscape({
    super.key,
    this.onRequestChangePage,
  });

  final Function(int)? onRequestChangePage;

  @override
  Widget build(BuildContext context) {
    Get.isRegistered<DashboardCourseController>()
        ? Get.find<DashboardCourseController>()
        : Get.put(DashboardCourseController());

    Get.put(ClasesCourseController());
    Get.put(AnnouncementController());
    Get.put(TodayAnnouncementController());
    Get.put(AttendanceHistoryController());
    Get.put(TodayScheduleController());
    // final loginController = Get.find<LoginController>();
    final loginController = Get.find<LoginController>();
    final todayAnnouncementController = Get.find<TodayAnnouncementController>();
    Get.put(AnnouncementController());

    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 90),
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(0, 0, 0, 150),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            16.verticalSpace,
                            Obx(
                              () => CustomTextWigdet(
                                title: loginController.greeting().value,
                                fontSize: 18,
                                textColor: Colors.white.withOpacity(0.6),
                              ),
                            ),
                            Obx(() => CustomTextWigdet(
                                  title: loginController.user.value?.name ?? '',
                                  fontSize: 24,
                                  textColor: Colors.white,
                                  fontWeight: FontWeight.w600,
                                )),
                            16.verticalSpace,
                            Obx(
                              () => todayAnnouncementController.list.isNotEmpty
                                  ? CustomAlert(
                                      title: todayAnnouncementController
                                          .list.first.title,
                                      subtitle: todayAnnouncementController
                                          .list.first.description,
                                    )
                                  : const SizedBox(),
                            ),
                            const HanjarDashboard(),
                            TasklistDashboard(
                              onViewAll: () {
                                if (onRequestChangePage != null)
                                  onRequestChangePage!(1);
                              },
                            ),
                            const AcademicNotificationDashboard(),
                            LearningDashboard(
                              onViewAll: () {
                                if (onRequestChangePage != null) {
                                  onRequestChangePage!(1);
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                      16.horizontalSpace,
                      SizedBox(
                        width: 400.w,
                        child: Column(
                          children: [
                            const AttendanceDashboard(),
                            16.verticalSpace,
                            const TodayScheduleDashboard(),
                            16.verticalSpace,
                            const AssignmentCalendarDashboard(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Obx(() {
              final controller = Get.find<DashboardCourseController>();
              LogService.log.i('showReview: ${controller.showReview.value}');
              if (controller.showReview.value) {
                return Stack(
                  children: [
                    Positioned.fill(
                      child: Container(
                        color: Colors.black.withOpacity(0.6),
                      ),
                    ),
                    Center(
                      child: ReviewImageAttendance(
                        imagePath: controller.imagePath,
                        metadata: controller.metadata,
                        isClockOut: controller.isClockOut.value,
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
