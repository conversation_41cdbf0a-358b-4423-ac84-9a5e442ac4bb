import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/audio_player_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_dialog.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class SlideViewerPage extends StatefulWidget {
  final String? path;
  final String? title;
  final String? subtitle;

  SlideViewerPage({
    Key? key,
    required this.path,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  _SlideViewerPageState createState() => _SlideViewerPageState();
}

class _SlideViewerPageState extends State<SlideViewerPage>
    with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    return CustomHanjarDialog(
      title: widget.title,
      subtitle: widget.subtitle,
      child: InAppWebView(
        initialUrlRequest: URLRequest(
            url: WebUri(
                'https://docs.google.com/gview?embedded=true&url=${Uri.encodeFull(widget.path.toString())}')),
        onWebViewCreated: (InAppWebViewController webController) {
          // controller.webViewController = webController;
        },
        onLoadStop: (controller, url) {
          print("Page finished loading: $url");
        },
      ),
    );
  }
}
