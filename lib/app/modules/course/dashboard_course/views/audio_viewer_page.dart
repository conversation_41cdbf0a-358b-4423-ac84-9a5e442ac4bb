import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/audio_player_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_dialog.dart';

class AudioViewerPage extends StatefulWidget {
  final String? path;
  final String? title;
  final String? subtitle;

  AudioViewerPage({
    Key? key,
    required this.path,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  _AudioViewerPageState createState() => _AudioViewerPageState();
}

class _AudioViewerPageState extends State<AudioViewerPage>
    with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    return CustomHanjarDialog(
      title: widget.title,
      subtitle: widget.subtitle,
      width: Get.window.physicalSize.width * 0.7,
      height: 400,
      child: Material(
        color: darkBlueColor,
        child: AudioPlayerWidget(
          audioUrl: widget.path ?? "",
          title: widget.title ?? "",
        ),
      ),
    );
  }
}
