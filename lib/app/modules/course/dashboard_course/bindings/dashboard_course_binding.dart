import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/annoucement_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/today_announcement_controller.dart';
import '../controllers/dashboard_course_controller.dart';

class DashboardCourseBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<DashboardCourseController>(
      () => DashboardCourseController(),
    );
    Get.lazyPut<AnnouncementController>(
      () => AnnouncementController(),
    );
    Get.lazyPut<TodayAnnouncementController>(
      () => TodayAnnouncementController(),
    );
  }
}
