import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/announcement_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/assignment_model.dart';
import 'package:mides_skadik/app/data/services/course/announcement_service.dart';
import 'package:mides_skadik/app/data/services/course/assignment_service.dart';

class TaskDashboardController extends GetxController {
  RxList<AssignmentModel> list = <AssignmentModel>[].obs;

  RxBool isLoading = false.obs;

  @override
  void onReady() {
    loadData();
    super.onReady();
  }

  void loadData() async {
    isLoading.value = true;

    var res = await AssignmentService().getLastestAssignmentNearest();
    if (res.isSuccess) {
      list.value = res.resultValue ?? [];
    } else {
      list.value = [];
    }

    isLoading.value = false;
  }
}
