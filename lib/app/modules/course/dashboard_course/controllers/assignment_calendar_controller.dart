import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/announcement_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/assignment_calendar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/today_schedule_model.dart';
import 'package:mides_skadik/app/data/services/course/announcement_service.dart';
import 'package:mides_skadik/app/data/services/course/assignment_service.dart';

class AssignmentCalendarController extends GetxController {
  RxList<AssignmentCalendarModel> list = <AssignmentCalendarModel>[].obs;

  RxBool isLoading = false.obs;

  final Rx<int> _month = DateTime.now().month.obs;
  final Rx<int> _year = DateTime.now().year.obs;

  @override
  void onReady() {
    loadData();
    super.onReady();
  }

  void loadData({
    int? month,
    int? year,
  }) async {
    isLoading.value = true;

    var now = DateTime.now();
    _month.value = month ?? now.month;
    _year.value = year ?? now.year;

    var res = await AssignmentService().getByMonth(
      month: _month.value,
      year: _year.value,
    );
    if (res.isSuccess) {
      list.value = res.resultValue ?? [];
    } else {
      list.value = [];
    }

    isLoading.value = false;
  }
}
