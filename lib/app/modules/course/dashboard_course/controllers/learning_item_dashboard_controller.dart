import 'dart:developer';

import 'package:get/get.dart';
import 'package:mides_skadik/widgets/course/custom_tab_item.dart';

class LearningItemDashboardController extends GetxController {
  final RxList<TabItem> tabItems = <TabItem>[].obs;

  final TabItem _materiPendukung = TabItem(title: "SBS Materi Pendukung", key: "materi_pendukung");
  final TabItem _materiPokok = TabItem(title: "SBS Materi Pokok", key: "materi_pokok");
  final TabItem _materiPraktis = TabItem(title: "SBS Latihan Praktis", key: "latihan_praktis");

  final Rx<TabItem?> tabSelected = Rx<TabItem?>(null);

  Rx<bool> get tabIsMateriPendukung => (tabSelected.value?.key == 'materi_pendukung').obs;
  Rx<bool> get tabIsMateriPokok => (tabSelected.value?.key == 'materi_pokok').obs;
  Rx<bool> get tabIsLatihanPraktis => (tabSelected.value?.key == 'latihan_praktis').obs;

  void selectTab(TabItem item) {
    log(item.key, name: "Learning Item Dashboard");
    tabSelected.value = item;
  }

  @override
  void onReady() {
    tabItems.value = [
      _materiPendukung,
      _materiPokok,
      _materiPraktis,
    ];
    selectTab(_materiPendukung);
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
