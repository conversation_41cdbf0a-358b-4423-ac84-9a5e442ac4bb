import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/announcement_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/schedule_monthly_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/today_schedule_model.dart';
import 'package:mides_skadik/app/data/services/course/announcement_service.dart';
import 'package:mides_skadik/app/data/services/course/schedule_service.dart';

class TodayScheduleController extends GetxController {
  RxList<ScheduleMonthlyModel> list = <ScheduleMonthlyModel>[].obs;

  RxBool isLoading = false.obs;

  @override
  void onReady() {
    loadData();
    super.onReady();
  }

  void loadData() async {
    isLoading.value = true;

    var res = await ScheduleService().getTodaySchedule();
    if (res.isSuccess) {
      list.value = res.resultValue ?? [];
    } else {
      list.value = [];
    }

    isLoading.value = false;
  }
}
