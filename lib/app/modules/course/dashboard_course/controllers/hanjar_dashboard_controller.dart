import 'dart:developer';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/last_hanjar_model.dart';
import 'package:mides_skadik/app/data/services/course/hanjar_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/widgets/course/custom_tab_item.dart';

class HanjarDashboardController extends GetxController {
  final RxList<TabItem> tabItems = <TabItem>[].obs;

  final TabItem _todayHanjar =
      TabItem(title: "Today Hanjar", key: "today_hanjar");
  final TabItem _newHanjar = TabItem(title: "New Hanjar", key: "new_hanjar");

  final Rx<TabItem?> tabSelected = Rx<TabItem?>(null);

  RxList<LastHanjarModel> list = <LastHanjarModel>[].obs;
  final RxList<LastHanjarModel> _listNewHanjar = <LastHanjarModel>[].obs;
  final RxList<LastHanjarModel> _listTodayHanjar = <LastHanjarModel>[].obs;

  RxBool isLoading = false.obs;
  RxString thnPelajaranId = ''.obs;

  void selectTab(TabItem item) async {
    try {
      log(item.key, name: "Hanjar Dashboard");
      tabSelected.value = item;

      isLoading.value = true;

      if (item.key == 'today_hanjar') {
        await loadTodayHanjar();
      } else {
        await loadNewHanjar();
      }
    } catch (e) {
      log(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onReady() {
    tabItems.value = [_todayHanjar, _newHanjar];
    selectTab(_todayHanjar);
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<void> loadTodayHanjar() async {
    var thnPendidikanId = await LocalStorage().get('thn_pendidikan_id');
    var res = await HanjarService()
        .getTodayHanjar(tahunPendidikanId: thnPendidikanId);
    if (res.isSuccess) {
      _listTodayHanjar.value = res.resultValue ?? [];
    } else {
      _listTodayHanjar.value = [];
    }

    list.assignAll(_listTodayHanjar);
  }

  Future<void> loadNewHanjar() async {
    var res = await HanjarService().getLatestHanjar();
    if (res.isSuccess) {
      _listNewHanjar.value = res.resultValue ?? [];
    } else {
      _listNewHanjar.value = [];
    }

    list.assignAll(_listNewHanjar);
  }
}
