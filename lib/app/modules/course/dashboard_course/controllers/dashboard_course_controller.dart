import 'dart:io';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/attendance/attendances_model.dart';
import 'package:mides_skadik/app/data/services/course/attendance/attendance_service.dart';
import 'package:mides_skadik/app/data/services/course/attendance/history_attendance_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class DashboardCourseController extends GetxController {
  String imagePath = '';
  Map<String, dynamic> metadata = {};
  var isSubmitting = false.obs;
  var showReview = false.obs;
  var historyist = <AttendanceModel>[].obs;
  var isLoadingistory = false.obs;
  var isClockOut = false.obs;
  final storageService = StorageService();
  var isAttendanceCompleted = false.obs;

  /// ================================
  /// GET ARGUMENTS FROM ROUTE
  void _getArguments() {
    try {
      if (Get.arguments != null && Get.arguments is Map) {
        final args = Get.arguments as Map;
        imagePath = args['imagePath'] ?? '';
        metadata = args['metadata'] ?? {};
        isClockOut.value = args['isClockOut'] ?? false; // Ini penting

        LogService.log.i(' Arguments: $imagePath');
        LogService.log.i(' metadata dari arguments: $metadata');
        LogService.log.i(' isClockOut dari arguments: ${isClockOut.value}');
      }
    } catch (e) {
      LogService.log.e('Error getting arguments: $e');
      imagePath = '';
      metadata = {};
      isClockOut.value = false;
      showReview.value = false;
    }
  }

  /// ================================
  /// ATTENDANCE SUBMISSION CLOCKIN
  Future<void> submitAttendance() async {
    try {
      isSubmitting.value = true;

      final file = File(imagePath);
      if (!file.existsSync()) {
        LogService.log.e('File tidak ditemukan: $imagePath');
        SnackbarUtil.showOnce(
          title: 'Error',
          message: 'File gambar tidak ditemukan',
        );
        return;
      }

      LogService.log.i('Mengirim file presensi: $imagePath');

      final att = await AttendanceService().postAttendance(imagePath);

      if (att.isFailed) {
        final msg = att.errorMessage ?? '';
        LogService.log.w('Gagal submit presensi: $msg');

        if (msg.contains('already clocked in')) {
          showReview.value = false;
          imagePath = '';
          metadata = {};
          SnackbarUtil.showOnce(
            title: 'Info',
            message: 'Kamu sudah melakukan absen masuk hari ini',
          );
          Get.offAll(() => const BottomNavBar(choosenScreen: 'course'));
          return;
        }

        SnackbarUtil.showOnce(
          title: 'Gagal',
          message: msg.isNotEmpty ? msg : 'Presensi gagal dilakukan',
        );
        return;
      }

      final resultData = att.resultValue;
      final String attendanceId = resultData?.id ?? '';
      if (attendanceId.isNotEmpty) {
        await storageService.saveTodayAttendanceId(attendanceId);
        LogService.log.i('ID kehadiran berhasil disimpan: $attendanceId');
      }

      showReview.value = false;
      imagePath = '';
      metadata = {};

      SnackbarUtil.showOnce(
        title: 'Berhasil Clockin',
        message: 'Presensi berhasil',
      );
      Get.offAll(() => const BottomNavBar(choosenScreen: 'course'));
    } catch (e) {
      LogService.log.e('Error Colockin : $e');
      SnackbarUtil.showOnce(
        title: 'Error',
        message: 'Terjadi kesalahan saat submit',
      );
    } finally {
      isSubmitting.value = false;
    }
  }

  /// ================================
  /// ATTENDANCE SUBMISSION CLOCKOUT
  Future<void> submitClockOut() async {
    try {
      isSubmitting.value = true;

      final attendanceId = await storageService.getTodayAttendanceId();

      if (attendanceId == null || attendanceId.isEmpty) {
        SnackbarUtil.showOnce(
          title: 'Error',
          message: 'ID kehadiran tidak ditemukan untuk Clock Out',
        );
        return;
      }

      final file = File(imagePath);
      if (!file.existsSync()) {
        LogService.log.e('File tidak ditemukan: $imagePath');
        SnackbarUtil.showOnce(
          title: 'Error',
          message: 'File gambar tidak ditemukan',
        );
        return;
      }

      LogService.log.i('Mengirim file clock-out: $imagePath');

      final att =
          await AttendanceService().postClockOut(attendanceId, imagePath);

      if (att.isFailed) {
        final msg = att.errorMessage ?? '';
        LogService.log.w('Gagal clock-out: $msg');
        SnackbarUtil.showOnce(
          title: 'Gagal',
          message: msg.isNotEmpty ? msg : 'Clock Out gagal dilakukan',
        );
        return;
      }

      await storageService.removeTodayAttendanceId();
      LogService.log.i('🗑️ ID kehadiran berhasil dihapus setelah Clock Out');

      showReview.value = false;
      imagePath = '';
      metadata = {};

      SnackbarUtil.showOnce(
        title: 'Berhasil Clockout',
        message: 'Clock Out berhasil',
      );

      Get.offAll(() => const BottomNavBar(choosenScreen: 'course'));
    } catch (e) {
      LogService.log.e('Error Clock Out: $e');
      SnackbarUtil.showOnce(
        title: 'Error',
        message: 'Terjadi kesalahan saat Clock Out',
      );
    } finally {
      isSubmitting.value = false;
    }
  }

  /// ================================
  /// GET ATTENDANCE HISTORY
  Future<void> getAttendanceHistory() async {
    isLoadingistory.value = true;

    try {
      final result = await HistoryAttendance().getTodayAttendanceHistory();

      if (result.isFailed) {
        LogService.log
            .w('Gagal mendapatkan data presensi: ${result.errorMessage}');
        SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Gagal mengambil data',
        );
        return;
      }

      // Filter hanya untuk data hari ini
      final today = DateTime.now().toUtc();
      final list = (result.resultValue ?? []).where((item) {
        final itemDate = item.date!.toUtc();
        return itemDate.year == today.year &&
            itemDate.month == today.month &&
            itemDate.day == today.day;
      }).toList();

      historyist.assignAll(list);

      if (historyist.isNotEmpty) {
        final todayAttendance = historyist.first;
        final clockIn = todayAttendance.clockIn;
        final clockOut = todayAttendance.clockOut;
        LogService.log.i('clockIn: $clockIn, clockOut: $clockOut');
      } else {
        LogService.log.i('🗑️ Tidak ada kehadiran hari ini.');
        await storageService.removeTodayAttendanceId();
        LogService.log.i(
            '🧹 ID attendance dari local storage dihapus karena tidak ada data hari ini');
      }
    } catch (e) {
      LogService.log.e('Error loading history: $e');
    } finally {
      isLoadingistory.value = false;
    }
  }

  @override
  void onInit() {
    super.onInit();
    _getArguments();
    getAttendanceHistory();
  }

  @override
  void onReady() {
    super.onReady();
    getAttendanceHistory();
  }
}
