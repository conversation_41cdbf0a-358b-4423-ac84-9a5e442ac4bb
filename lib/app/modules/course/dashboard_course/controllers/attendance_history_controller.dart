import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/attendance/attendances_model.dart';
import 'package:mides_skadik/app/data/services/course/attendance/attendance_service.dart';

class AttendanceHistoryController extends GetxController {
  RxList<AttendanceModel> list = <AttendanceModel>[].obs;

  RxBool isLoading = false.obs;

  AttendanceModel? get todayAttendance {
    var today =
        list.where((e) => e.clockIn?.day == DateTime.now().day).firstOrNull;

    return today;
  }

  @override
  void onReady() {
    loadData();
    super.onReady();
  }

  void loadData() async {
    isLoading.value = true;

    var res = await AttendanceService().getListPreviewHistory();

    if (res.isSuccess) {
      list.value = res.resultValue ?? [];
    } else {
      list.value = [];
    }

    isLoading.value = false;
  }
}
