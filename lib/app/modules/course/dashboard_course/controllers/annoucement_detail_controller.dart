import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/announcement_model.dart';
import 'package:mides_skadik/app/data/services/course/announcement_service.dart';

class AnnouncementDetailController extends GetxController {
  Rx<AnnouncementModel?> detail = null.obs;

  RxBool isLoading = false.obs;

  @override
  void onReady() {
    super.onReady();
  }

  void loadData(String id) async {
    isLoading.value = true;

    var res = await AnnouncementService().getDetail(id);
    if (res.isSuccess) {
      detail.value = res.resultValue;
    } else {
      detail.value = null;
    }

    isLoading.value = false;
  }
}
