import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/assesment_course/controllers/assesment_course_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/quiz_question_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_filter.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/course/class/custom_submit_task_course.dart';

class RunningAssessmentCourseView extends GetView<AssesmentCourseController> {
  RunningAssessmentCourseView({super.key});
  final QuizQuestionController quizQuestionController =
      Get.put(QuizQuestionController());
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: CustomScaffold(
        useAppBar: true,
        isCourse: true,
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
          child: Stack(
            children: [
              Obx(
                () => Skeletonizer(
                  enabled: quizQuestionController.isLoading.value,
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        32.verticalSpace,
                        CustomTextWigdet(
                          title: controller.quizById.value.quiz?.title ?? '',
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                          textColor: whiteColor,
                        ),
                        32.verticalSpace,
                        CustomContainer(
                          bgColor: baseBlueColor.withOpacity(0.25),
                          width: double.infinity,
                          radius: 10,
                          widget: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 38.w, vertical: 40.h),
                            child: Column(
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Column(
                                      children: [
                                        CustomContainer(
                                          bgColor:
                                              baseBlueColor.withOpacity(0.2),
                                          width: 200,
                                          radius: 8,
                                          widget: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 14.w,
                                                vertical: 16.h),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                CustomTextWigdet(
                                                  title:
                                                      'Questions ${quizQuestionController.numberQuestion}',
                                                  fontSize: 20,
                                                  fontWeight: FontWeight.w600,
                                                  textColor: whiteColor,
                                                ),
                                                32.verticalSpace,
                                                CustomTextWigdet(
                                                  title: 'Flag Question',
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                  textColor: whiteColor
                                                      .withOpacity(0.3),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                        12.verticalSpace,
                                        CustomContainer(
                                          bgColor:
                                              baseBlueColor.withOpacity(0.2),
                                          width: 200,
                                          radius: 8,
                                          widget: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 14.w,
                                                vertical: 16.h),
                                            child: Obx(
                                              () => Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  CustomTextWigdet(
                                                    title: 'Remaining Time',
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                    textColor: whiteColor
                                                        .withOpacity(0.5),
                                                  ),
                                                  24.verticalSpace,
                                                  Center(
                                                    child: CustomTextWigdet(
                                                      title: formatRemainingTime(
                                                          quizQuestionController
                                                              .remainingTime
                                                              .value),
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      textColor: whiteColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    24.horizontalSpace,
                                    Expanded(
                                      child: CustomContainer(
                                        bgColor: baseBlueColor.withOpacity(0.2),
                                        radius: 8,
                                        padding: const EdgeInsets.all(16),
                                        widget: SingleChildScrollView(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              CustomTextWigdet(
                                                title: quizQuestionController
                                                            .quizQuestion
                                                            .value
                                                            .question !=
                                                        null
                                                    ? quizQuestionController
                                                        .quizQuestion
                                                        .value
                                                        .question!
                                                        .replaceAll(
                                                            RegExp(r'<[^>]*>'),
                                                            '')
                                                    : '',
                                                fontSize: 24,
                                                fontWeight: FontWeight.w600,
                                                textColor: whiteColor,
                                              ),
                                              8.verticalSpace,
                                              if (quizQuestionController
                                                      .quizQuestion
                                                      .value
                                                      .type !=
                                                  'Essay')
                                                CustomTextWigdet(
                                                  title: 'Choose one :',
                                                  fontSize: 14,
                                                  fontWeight: FontWeight.w400,
                                                  textColor: whiteColor
                                                      .withOpacity(0.5),
                                                ),
                                              if (quizQuestionController
                                                      .quizQuestion
                                                      .value
                                                      .type ==
                                                  'Essay')
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    CustomTextWigdet(
                                                      title: 'Answer :',
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      textColor: whiteColor
                                                          .withOpacity(0.5),
                                                    ),
                                                    8.verticalSpace,
                                                    cardEssay(),
                                                  ],
                                                ),
                                              16.verticalSpace,
                                              if (quizQuestionController
                                                      .quizQuestion
                                                      .value
                                                      .type ==
                                                  'Essay')
                                                CustomButtonFilter(
                                                  title: 'Simpan Draft',
                                                  bgColor: blueColor,
                                                  onPressed: () {
                                                    if (quizQuestionController
                                                        .essayAnswerController
                                                        .text
                                                        .isNotEmpty) {
                                                      quizQuestionController
                                                          .selectQuestion(
                                                        id: quizQuestionController
                                                            .quizQuestion
                                                            .value
                                                            .id,
                                                        answer: quizQuestionController
                                                            .essayAnswerController
                                                            .text,
                                                      );
                                                    } else {
                                                      Get.snackbar(
                                                        'Error',
                                                        'Please fill in the answer',
                                                        backgroundColor:
                                                            Colors.redAccent,
                                                      );
                                                    }
                                                  },
                                                ),
                                              8.verticalSpace,
                                              ...?quizQuestionController
                                                  .quizQuestion
                                                  .value
                                                  .quizAnswers
                                                  ?.map(
                                                (e) => cardChoice(
                                                  onTap: () {
                                                    quizQuestionController
                                                        .selectQuestion(
                                                            id: e.id,
                                                            answer: e.answer);
                                                  },
                                                  id: e.id,
                                                  answerKey:
                                                      String.fromCharCode(65 +
                                                          (quizQuestionController
                                                                  .quizQuestion
                                                                  .value
                                                                  .quizAnswers
                                                                  ?.indexOf(
                                                                      e) ??
                                                              0)),
                                                  answerText: e.answer,
                                                  isSelected: true,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                32.verticalSpace,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomFilledButtonWidget(
                                      onPressed: () {
                                        quizQuestionController
                                            .previousQuestion();
                                      },
                                      isOutlined:
                                          controller.listButton.value == 1
                                              ? false
                                              : true,
                                      borderColor: greyColor,
                                      bgColor: controller.listButton.value == 1
                                          ? greyColor
                                          : Colors.transparent,
                                      title: 'Previous',
                                      fontSize: 16,
                                      fontColor: whiteColor,
                                      widthButton: 250,
                                      heightButton: 45,
                                      radius: 4,
                                    ),
                                    CustomFilledButtonWidget(
                                      onPressed: () {
                                        quizQuestionController.nextQuestion();
                                      },
                                      bgColor: controller.listButton.value == 3
                                          ? greyColor
                                          : blueColor,
                                      title: 'Next Question',
                                      fontSize: 16,
                                      fontColor: whiteColor,
                                      widthButton: 250,
                                      heightButton: 45,
                                      radius: 4,
                                    ),
                                  ],
                                ),
                                36.verticalSpace,
                                CustomContainer(
                                  bgColor: baseBlueColor.withOpacity(0.2),
                                  width: double.infinity,
                                  radius: 10,
                                  widget: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 24.w, vertical: 24.h),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomTextWigdet(
                                          title: 'Quiz Navigation',
                                          fontSize: 20,
                                          fontWeight: FontWeight.w500,
                                          textColor: whiteColor,
                                        ),
                                        20.verticalSpace,
                                        Obx(
                                          () => SizedBox(
                                            child: Wrap(
                                              spacing: 4.w,
                                              runSpacing: 15.h,
                                              children: [
                                                ...controller.startQuiz.value
                                                    .randomQuestionIds!
                                                    .map(
                                                  (e) => GestureDetector(
                                                    onTap: () {
                                                      quizQuestionController
                                                          .getQuizQuestionId(
                                                              id: e,
                                                              index: (controller
                                                                      .startQuiz
                                                                      .value
                                                                      .randomQuestionIds!
                                                                      .indexOf(
                                                                          e) +
                                                                  1));
                                                    },
                                                    child: CustomContainer(
                                                      width: 55,
                                                      height: 60,
                                                      bgColor:
                                                          quizQuestionController
                                                                      .idRandomQuiz ==
                                                                  e
                                                              ? blueColor
                                                              : blueColor
                                                                  .withOpacity(
                                                                      0.20),
                                                      radius: 6,
                                                      widget: Center(
                                                        child: CustomTextWigdet(
                                                          title: ((controller
                                                                      .startQuiz
                                                                      .value
                                                                      .randomQuestionIds!
                                                                      .indexOf(
                                                                          e) +
                                                                  1)
                                                              .toString()),
                                                          fontSize: 18,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          textColor: whiteColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        20.verticalSpace,
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            quizQuestionController
                                                .getQuizReview();
                                            controller.openSubmitQuiz();
                                          },
                                          isOutlined: true,
                                          borderColor: greyColor,
                                          title: 'Finish Quiz',
                                          fontColor: whiteColor,
                                          fontSize: 16,
                                          widthButton: 165,
                                          fontWeight: FontWeight.w500,
                                          radius: 4,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              Obx(() {
                if (controller.isOpenSubmitQuiz.value) {
                  return Positioned.fill(
                    child: Stack(
                      children: [
                        // Blur + Semi-transparent dark layer
                        Container(
                          color: Colors.black.withOpacity(0.4),
                        ),
                        // Dialog quiz
                        Center(
                          child: Obx(
                            () => CustomSubmitTaskCourse(
                              type: 'ujian',
                              titleQuiz: controller.quizById.value.quiz?.title,
                              isLoading: quizQuestionController
                                  .isLoadingSubmitQuestion.value,
                              onSubmit: () {
                                /* used for close dialog and back to open quiz view then update
                                the value finshedQuiz for display the table
                                */
                                controller.isOpenSubmitQuiz.value = false;
                                // controller.finishQUiz();
                                controller.socketService.disconnect();
                                quizQuestionController.finishQuiz();
                                controller.getQuizProgress();
                              },
                              message: quizQuestionController.listQuizReview
                                      .any((quiz) => quiz.answer == null)
                                  ? 'Some questions are unanswered, answer them to maximize your results'
                                  : 'All questions are answered, you can submit your quiz now!',
                              onClose: () {
                                controller.isOpenSubmitQuiz.value = false;
                              },
                              onCancel: () {
                                controller.isOpenSubmitQuiz.value = false;
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              Obx(() {
                if (controller.socketService.message == 'attention') {
                  controller.socketService.countAttention.value++;
                  return Positioned.fill(
                    child: Stack(
                      children: [
                        // Blur + Semi-transparent dark layer
                        Container(
                          color: Colors.black.withOpacity(0.4),
                        ),
                        // Dialog quiz
                        Center(
                          child: CustomContainer(
                            width: 818,
                            bgColor: darkBlueColor.withOpacity(0.90),
                            padding: EdgeInsets.symmetric(vertical: 40.h),
                            widget: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/peringatan.svg',
                                  width: 50.w,
                                  height: 50.h,
                                ),
                                30.verticalSpace,
                                CustomTextWigdet(
                                  title: 'Peringatan!',
                                  fontSize: 32,
                                  fontWeight: FontWeight.w500,
                                  textColor: whiteColor,
                                ),
                                30.verticalSpace,
                                CustomTextWigdet(
                                  title:
                                      'Fokus pada ujian. Menoleh atau berpaling dari layar dilarang. Jika melewati 3 pelanggaran, anda akan dikeluarkan dari ujian.',
                                  fontSize: 20,
                                  textAlign: TextAlign.center,
                                  fontWeight: FontWeight.w400,
                                  textColor: whiteColor.withOpacity(0.5),
                                ),
                                30.verticalSpace,
                                CustomTextWigdet(
                                  title:
                                      'Pelanggaran ${controller.socketService.countAttention.value} dari 3',
                                  fontSize: 20,
                                  textAlign: TextAlign.center,
                                  fontWeight: FontWeight.w400,
                                  textColor: whiteColor.withOpacity(0.5),
                                ),
                                40.verticalSpace,
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.socketService.message.value = '';
                                    if (controller.socketService.countAttention
                                            .value >=
                                        3) {
                                      controller.socketService.countAttention
                                          .value = 0;
                                      controller.socketService.disconnect();
                                      quizQuestionController.finishQuiz();
                                      controller.getQuizProgress();
                                    }
                                  },
                                  isOutlined: false,
                                  borderColor: blueColor,
                                  bgColor: blueColor,
                                  title: 'Saya Mengerti',
                                  fontSize: 16,
                                  fontColor: whiteColor,
                                  widthButton: 300,
                                  heightButton: 45,
                                  radius: 4,
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return const SizedBox.shrink();
                }
              })
            ],
          ),
        ),
      ),
    );
  }

  TextField cardEssay() {
    return TextField(
      controller: quizQuestionController.essayAnswerController,
      maxLines: 6,
      style: TextStyle(
        color: whiteColor,
        fontSize: 9,
      ),
      onChanged: (value) {
        quizQuestionController.charactersCount.value++;
        if (quizQuestionController.charactersCount.value >= 50) {
          quizQuestionController.charactersCount.value = 0;
          quizQuestionController.selectQuestion(
            id: quizQuestionController.quizQuestion.value.id,
            answer: quizQuestionController.essayAnswerController.text,
          );
        }
      },
      decoration: InputDecoration(
        filled: true,
        fillColor: baseBlueColor.withOpacity(0.2),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.transparent,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.transparent,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: blueColor,
          ),
        ),
        hintText: 'Type your answer here...',
        hintStyle: TextStyle(
          color: whiteColor.withOpacity(0.5),
          fontSize: 9,
        ),
      ),
    );
  }

  Widget cardChoice(
      {Function()? onTap,
      String? id,
      String? answerKey,
      String? answerText,
      bool isSelected = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: GestureDetector(
        onTap: onTap,
        child: Obx(
          () => CustomContainer(
            padding: const EdgeInsets.all(10),
            bgColor: id == quizQuestionController.idSelectedQuestion.value
                ? secondBlueColor.withOpacity(0.2)
                : secondBlueColor.withOpacity(0.10),
            radius: 10,
            widget: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomContainer(
                  width: 16.w,
                  height: 16.h,
                  radius: 100,
                  padding: const EdgeInsets.all(2),
                  border: Border.all(
                    color: whiteColor,
                    width: 1,
                  ),
                  widget: Center(
                    child: CustomContainer(
                      width: 8.w,
                      height: 8.h,
                      bgColor:
                          id == quizQuestionController.idSelectedQuestion.value
                              ? whiteColor
                              : Colors.transparent,
                      radius: 100,
                    ),
                  ),
                ),
                10.horizontalSpace,
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomTextWigdet(
                        title: answerKey ?? '',
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: id ==
                                quizQuestionController.idSelectedQuestion.value
                            ? whiteColor
                            : whiteColor.withOpacity(0.5),
                      ),
                      10.horizontalSpace,
                      Flexible(
                        child: CustomTextWigdet(
                          title: (answerText ?? '')
                              .replaceAll(RegExp(r'<[^>]*>'), ''),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          textColor: id ==
                                  quizQuestionController
                                      .idSelectedQuestion.value
                              ? whiteColor
                              : whiteColor.withOpacity(0.5),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
