import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/assesment_course/controllers/assesment_course_controller.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_content_task.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_header_task.dart';
import 'package:mides_skadik/widgets/course/class/tasks/custom_list_navigation_task.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../../themes/colors.dart';
import '../../../../../widgets/components/buttons/custom_filled_button_widget.dart';
import '../../../../../widgets/course/class/tasks/custom_information_task.dart';

class OpenAssessmentCourseView extends GetView<AssesmentCourseController> {
  final String? imageUrl;
  const OpenAssessmentCourseView({super.key, this.imageUrl});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      isCourse: true,
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 40.h),
        child: Column(
          children: [
            Row(
              children: [
                CustomFilledButtonWidget(
                  onPressed: () {
                    Get.back();
                  },
                  isOutlined: true,
                  withIcon: true,
                  borderColor: greyColor,
                  assetName: 'assets/icons/left.svg',
                  title: 'Back',
                  fontColor: whiteColor,
                  fontSize: 20,
                  widthButton: 150,
                  heightIcon: 20,
                  fontWeight: FontWeight.w500,
                  radius: 4,
                ),
                const Spacer(),
                CustomFilledButtonWidget(
                  onPressed: () {},
                  isOutlined: true,
                  borderColor: greyColor,
                  title: 'Mark Completed',
                  fontColor: whiteColor,
                  fontSize: 20,
                  widthButton: 250,
                  fontWeight: FontWeight.w500,
                  radius: 4,
                )
              ],
            ),
            32.verticalSpace,
            Obx(
              () => CustomHeaderTask(
                imageUrl: imageUrl ?? controller.quizById.value.quiz?.file,
                titleHeader:
                    controller.quizById.value.quiz?.submapel?.name ?? '-',
                userName: '${controller.userName}',
              ),
            ),
            32.verticalSpace,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomListNavigationTask(
                  title: 'List Assessment',
                  subTitle: 'Total Assessment',
                  total: 10,
                  listWidget: Obx(
                    () => Skeletonizer(
                      enabled: controller.quizById.value.quizNavigate == null,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ...?controller.quizById.value.quizNavigate
                              ?.asMap()
                              .entries
                              .map((entry) {
                            // final index = entry.key;
                            final item = entry.value;
                            return GestureDetector(
                              onTap: () {
                                controller.selectedNavigation(id: item.id);
                              },
                              child: CustomContainer(
                                bgColor:
                                    controller.idNavigation.value == item.id
                                        ? baseBlueColor.withOpacity(0.3)
                                        : Colors.transparent,
                                width: double.infinity,
                                radius: 8,
                                widget: Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 12.h, horizontal: 20.w),
                                  child: CustomTextWigdet(
                                    title: item.title ?? '',
                                    fontSize: 18,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    fontWeight: FontWeight.w500,
                                    textColor:
                                        controller.idNavigation.value == item.id
                                            ? whiteColor
                                            : secondWhiteColor.withOpacity(0.5),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),
                ),
                24.horizontalSpace,
                Expanded(
                  child: Obx(
                    () => Skeletonizer(
                      enabled: controller.quizById.value.quiz == null,
                      child: Column(
                        children: [
                          CustomContainer(
                            bgColor: baseBlueColor.withOpacity(0.3),
                            width: double.infinity,
                            radius: 10,
                            widget: Padding(
                              padding: EdgeInsets.all(24.r),
                              child: Column(
                                children: [
                                  CustomContentTask(
                                    contentTitle:
                                        controller.quizById.value.quiz?.title ??
                                            '-',
                                    statusProgress: controller
                                            .quizById.value.quiz?.status
                                            ?.toUpperCase() ??
                                        'NOT STARTED',
                                    widgets: [
                                      CustomContainer(
                                        width: double.infinity,
                                        bgColor: baseBlueColor.withOpacity(0.2),
                                        radius: 8,
                                        widget: Padding(
                                          padding: EdgeInsets.all(20.r),
                                          child: Column(
                                            children: [
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/calendar.svg',
                                                  name: 'Due',
                                                  value: getFormattedDate(
                                                      controller
                                                              .quizById
                                                              .value
                                                              .quiz
                                                              ?.startDate ??
                                                          DateTime.now())),
                                              12.verticalSpace,
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/timer.svg',
                                                  name: 'Duration',
                                                  value:
                                                      '${controller.quizById.value.quiz?.duration?.toString()} menit'),
                                              12.verticalSpace,
                                              const CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/spellcheck.svg',
                                                  name: 'Allowed Attempts',
                                                  value: '2'),
                                              12.verticalSpace,
                                              CustomInformationTask(
                                                  assetName:
                                                      'assets/icons/question_answer.svg',
                                                  name: 'Questions Total',
                                                  value:
                                                      '${controller.quizById.value.quiz?.count?.quizQuestions}'),
                                            ],
                                          ),
                                        ),
                                      ),
                                      44.verticalSpace,
                                      // Conditions for data summary is empty or not
                                      CustomContainer(
                                        bgColor: baseBlueColor.withOpacity(0.3),
                                        width: double.infinity,
                                        radius: 10,
                                        padding: EdgeInsets.all(20.r),
                                        widget: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            CustomTextWigdet(
                                              title: 'CATATAN',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.5),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  'Kerjakan dengan sungguh-sungguh, jujur, dan penuh tanggung jawab.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor: whiteColor,
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  'Ujian ini merupakan bagian dari penilaian kompetensi yang harus diselesaikan secara mandiri tanpa bantuan pihak lain.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            20.verticalSpace,
                                            CustomTextWigdet(
                                              title: 'KETENTUAN UJIAN',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.5),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '1. Waktu ujian bersifat terbatas dan akan dimulai segera setelah Anda menekan tombol "Mulai Ujian".',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '2. Tidak diperkenankan membuka catatan, berdiskusi, atau menggunakan perangkat tambahan yang tidak diizinkan.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '3. Setiap soal hanya dapat dijawab satu kali. Jawaban yang telah dikirimkan tidak dapat diubah.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '4. Pastikan koneksi internet stabil selama ujian berlangsung.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            20.verticalSpace,
                                            CustomTextWigdet(
                                              title: 'SANGSI PELANGGARAN',
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '1. Peserta yang terbukti melakukan kecurangan atau pelanggaran teknis akan diblokir dari sesi ujian ini.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            12.verticalSpace,
                                            CustomTextWigdet(
                                              title:
                                                  '2. Ujian tidak dapat diulang tanpa izin dari atasan atau panitia pelaksana.',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w500,
                                              textColor:
                                                  whiteColor.withOpacity(0.6),
                                            ),
                                            20.verticalSpace,
                                            CustomContainer(
                                              width: double.infinity,
                                              padding: EdgeInsets.symmetric(
                                                  vertical: 10.h,
                                                  horizontal: 20.w),
                                              bgColor: secondBlueColor
                                                  .withOpacity(0.2),
                                              widget: CustomTextWigdet(
                                                  title:
                                                      'Fokus, Disiplin, dan Tunjukkan Integritas Anda sebagai Prajurit TNI.',
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                  textColor: whiteColor),
                                            )
                                          ],
                                        ),
                                      ),
                                      44.verticalSpace,
                                      Obx(
                                        () => CustomFilledButtonWidget(
                                          onPressed: () {
                                            if (controller.quizProgress.value
                                                    .status !=
                                                'Active') {
                                              controller.startQuizId(
                                                quizId: controller.quizById
                                                        .value.quiz?.id ??
                                                    '',
                                              );
                                            }
                                          },
                                          title: controller.quizProgress.value
                                                      .quizStatus ==
                                                  'Completed'
                                              ? 'Completed'
                                              : 'Attempt Now',
                                          fontColor: controller.quizProgress
                                                      .value.quizStatus ==
                                                  'Completed'
                                              ? greyColor
                                              : whiteColor,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                          bgColor: controller.quizProgress.value
                                                      .quizStatus ==
                                                  'Completed'
                                              ? secondDarkBlueColor
                                              : blueColor,
                                          radius: 4,
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          60.verticalSpace,
                          Obx(
                            () => Row(
                              children: [
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.previousQuiz();
                                  },
                                  isOutlined: controller.listButton.value == 1
                                      ? false
                                      : true,
                                  borderColor: greyColor,
                                  bgColor: controller.listButton.value == 1
                                      ? greyColor
                                      : Colors.transparent,
                                  title: 'Previous',
                                  fontSize: 16,
                                  fontColor: whiteColor,
                                  widthButton: 105,
                                  heightButton: 45,
                                  radius: 4,
                                ),
                                const Spacer(),
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.nextQuiz();
                                  },
                                  bgColor: controller.listButton.value == 3
                                      ? greyColor
                                      : blueColor,
                                  title: 'Next',
                                  fontSize: 16,
                                  fontColor: whiteColor,
                                  widthButton: 105,
                                  heightButton: 45,
                                  radius: 4,
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget columnTextDescription({
    String? title,
    String? description1,
    String? description2,
    double? fontSizeTitle,
    double? fontSizeDesc1,
    double? fontSizeDesc2,
    FontWeight? fontWeightTitle,
    FontWeight? fontWeightDesc1,
    FontWeight? fontWeightDesc2,
    Color? textColorTitle,
    Color? textColorDesc1,
    Color? textColorDesc2,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomTextWigdet(
          title: title ?? '',
          fontSize: fontSizeTitle ?? 14,
          fontWeight: fontWeightTitle ?? FontWeight.normal,
          textColor: textColorTitle ?? secondWhiteColor.withOpacity(0.5),
        ),
        (title == null || title.isEmpty)
            ? const SizedBox.shrink()
            : 12.verticalSpace,
        CustomTextWigdet(
          title: description1 ?? '',
          fontSize: fontSizeDesc1,
          fontWeight: fontWeightDesc1,
          textColor: textColorDesc1 ?? secondWhiteColor.withOpacity(0.5),
        ),
        CustomTextWigdet(
          title: description2 ?? '',
          fontSize: fontSizeDesc2,
          fontWeight: fontWeightDesc2,
          textColor: textColorDesc2 ?? secondWhiteColor.withOpacity(0.5),
        ),
      ],
    );
  }
}
