import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/class/custom_card_tasks_course.dart';
import 'package:mides_skadik/widgets/course/class/custom_filter_course.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/assesment_course_controller.dart';

class AssesmentCourseView extends GetView<AssesmentCourseController> {
  const AssesmentCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    Get.put(AssesmentCourseController());
    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 60.h, horizontal: 60.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  140.verticalSpace,
                  CustomTextWigdet(
                    title: 'Assessment',
                    fontSize: 32,
                    fontWeight: FontWeight.w700,
                    textColor: whiteColor,
                  ),
                  40.verticalSpace,
                  Obx(
                    () => Row(
                      children: [
                        CustomFilledButtonWidget(
                          onPressed: () {
                            controller.selectAssessment(0);
                          },
                          title: 'All',
                          fontColor: secondWhiteColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          bgColor:
                              controller.selectedTabAssesmentIndex.value == 0
                                  ? baseBlueColor
                                  : whiteColor.withOpacity(0.05),
                          widthButton: 70,
                          heightButton: 48,
                          radius: 8,
                        ),
                        8.horizontalSpace,
                        CustomFilledButtonWidget(
                          onPressed: () {
                            controller.selectAssessment(1);
                          },
                          title: 'Upcoming',
                          fontColor: secondWhiteColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          bgColor:
                              controller.selectedTabAssesmentIndex.value == 1
                                  ? baseBlueColor
                                  : whiteColor.withOpacity(0.05),
                          widthButton: 135,
                          heightButton: 48,
                          radius: 8,
                        ),
                        8.horizontalSpace,
                        CustomFilledButtonWidget(
                          onPressed: () {
                            controller.selectAssessment(2);
                          },
                          title: 'Completed',
                          fontColor: secondWhiteColor,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          bgColor:
                              controller.selectedTabAssesmentIndex.value == 2
                                  ? baseBlueColor
                                  : whiteColor.withOpacity(0.05),
                          widthButton: 142,
                          heightButton: 48,
                          radius: 8,
                        ),
                      ],
                    ),
                  ),
                  40.verticalSpace,
                  Row(
                    children: [
                      CustomFilledButtonWidget(
                        onPressed: () {
                          controller.openFilterAssesment();
                        },
                        withIcon: true,
                        assetName: 'assets/icons/filter.svg',
                        widthIcon: 24,
                        heightIcon: 24,
                        title: 'Add Filter',
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        fontColor: whiteColor,
                        bgColor: whiteColor.withOpacity(0.05),
                        widthButton: 200,
                        heightButton: 48,
                        radius: 8,
                      ),
                      8.horizontalSpace,
                      CustomTextFieldWidget(
                        radius: 8,
                        obscureText: false,
                        colorField: whiteColor.withOpacity(0.05),
                        colorText: whiteColor,
                        hintText: 'Search',
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        colorTextHint: whiteColor.withOpacity(0.5),
                        assetNameIcon: 'assets/icons/search.svg',
                        controller: controller.searchController,
                        onChanged: (value) {
                          controller.searchController.text = value;
                          controller.fillterUjian();
                        },
                        iconWidth: 5,
                        iconHeight: 5,
                        widthField: 240,
                        heightField: 48,
                        contentPadding: EdgeInsetsDirectional.only(
                          top: 16.h,
                        ),
                      ),
                    ],
                  ),
                  40.verticalSpace,
                  Obx(
                    () => controller.isLoading.value
                        ? Center(
                            child: CircularProgressIndicator(
                              color: whiteColor,
                            ),
                          )
                        : controller.listQuiz.isEmpty
                            ? Center(
                                child: CustomTextWigdet(
                                  title: 'No data available',
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  textColor: whiteColor.withOpacity(0.5),
                                ),
                              )
                            : Expanded(
                                child: RefreshIndicator(
                                  onRefresh: () async {
                                    controller.fetchData();
                                  },
                                  color: blueColor,
                                  child: SizedBox(
                                    height: 1630.h,
                                    child: SingleChildScrollView(
                                      child: Wrap(
                                        spacing: 20.w,
                                        runSpacing: 20.h,
                                        children: [
                                          ...controller.listQuiz
                                              .map(
                                                (e) => CustomCardTasksCourse(
                                                  onPressed: () {
                                                    if (!(e.startDate != null &&
                                                        DateTime.now().isAfter(
                                                            e.startDate!))) {
                                                      controller.attemptQuiz(
                                                        id: e.id,
                                                      );
                                                    }
                                                  },
                                                  hasStatus: true,
                                                  title: e.title,
                                                  subStudies: e.submapel?.name,
                                                  subjectName:
                                                      e.submapel?.mapel?.name,
                                                  date: e.startDate,
                                                  status: e.status,
                                                  totalQuestion:
                                                      e.count?.quizQuestions,
                                                ),
                                              )
                                              .toList(),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                  ),
                ],
              ),
            ),
            Positioned(
              top: 450.h,
              left: 60.w,
              child: Obx(() => controller.isOpen.value
                  ? CustomFilterCourse(
                      onPressedTime: (p0) {
                        controller.isOpen.value = false;
                        controller.selectedFilterTime.value = p0;
                        controller.fillterUjian();
                      },
                      selectedTime: controller.selectedFilterTime.value,
                      onPressedDeadline: (p0) {
                        controller.isOpen.value = false;
                        controller.selectedFilterDeadline.value = p0;
                        controller.fillterUjian();
                      },
                      selectedDeadline: controller.selectedFilterDeadline.value,
                      onPressedReset: () {
                        controller.isOpen.value = false;
                        controller.selectedFilterTime.value = '';
                        controller.selectedFilterDeadline.value = '';
                        controller.fillterUjian();
                      },
                    )
                  : const SizedBox()),
            ),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
