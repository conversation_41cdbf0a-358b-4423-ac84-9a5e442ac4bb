import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_by_id_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_progress_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_question_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/start_quiz_model.dart';
import 'package:mides_skadik/app/data/services/course/quiz_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/soket_service.dart';
import 'package:mides_skadik/app/modules/course/assesment_course/views/open_assessment_course_view.dart';
import 'package:mides_skadik/app/modules/course/assesment_course/views/running_assessment_course_view.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/quiz_question_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/share_screen_controller.dart';

class AssesmentCourseController extends GetxController {
  var isOpen = false.obs;
  var selectedTabAssesmentIndex = 0.obs;
  var reviewDialogOpen = false.obs;
  var alertDialogOpen = true.obs;
  var isCanceled = false.obs;
  var isLoading = false.obs;
  var isLoadingAttempt = false.obs;
  var currentPage = 1.obs;
  var todalData = 0.obs;
  var isLoadingMoreData = false.obs;
  var idNavigation = ''.obs;
  var listButton = 1.obs;
  var userName = ''.obs;
  var isOpenSubmitQuiz = false.obs;
  var selectedFilterTime = ''.obs;
  var selectedFilterDeadline = ''.obs;

  RxList<QuizNavigate> idNavigationList = <QuizNavigate>[].obs;
  RxList<QuizModel> listQuiz = <QuizModel>[].obs;
  Rx<QuizByIdModel> quizById = const QuizByIdModel().obs;
  Rx<QuizQuestionModel> quizQuestion = const QuizQuestionModel().obs;
  Rx<StartQuizModel> startQuiz = const StartQuizModel().obs;
  Rx<QuizProgressModel> quizProgress = const QuizProgressModel().obs;
  final SocketService socketService = SocketService();
  final TextEditingController searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchData();
  }

  @override
  void onClose() {
    super.onClose();
    socketService.disconnect();

    // Clean up ShareScreenController if exists
    if (Get.isRegistered<ShareScreenController>()) {
      try {
        Get.find<ShareScreenController>().close();
        Get.delete<ShareScreenController>();
        LogService.log.i('✅ ShareScreenController cleaned up');
      } catch (e) {
        LogService.log.e('❌ Error cleaning up ShareScreenController: $e');
      }
    }

    LogService.log.i('Keluar dari Assesment Course Controller');
  }

  openReviewDialog() {
    reviewDialogOpen.value = !reviewDialogOpen.value;
  }

  openAlertDialog() {
    alertDialogOpen.value = !alertDialogOpen.value;
  }

  openFilterAssesment() {
    isOpen.value = !isOpen.value;
    LogService.log.i('open filter');
  }

  selectAssessment(int index) {
    selectedTabAssesmentIndex.value = index;
    if (index == 0) {
      fetchData();
    } else if (index == 1) {
      fetchData(status: 'InProgress');
    } else if (index == 2) {
      fetchData(status: 'Completed');
    }
  }

  void fetchMoreData() {
    if (todalData.value == listQuiz.length) {
      return;
    }
    currentPage.value++;
    getListQuiz();
  }

  void fillterUjian() {
    if (selectedTabAssesmentIndex.value == 0) {
      fetchData(
          time: selectedFilterTime.value,
          deadline: selectedFilterDeadline.value);
    } else if (selectedTabAssesmentIndex.value == 1) {
      fetchData(
          status: 'InProgress',
          time: selectedFilterTime.value,
          deadline: selectedFilterDeadline.value);
    } else if (selectedTabAssesmentIndex.value == 2) {
      fetchData(
          status: 'Completed',
          time: selectedFilterTime.value,
          deadline: selectedFilterDeadline.value);
    }
  }

  /// =========================================
  ///  Fetch Data Quiz List
  void fetchData(
      {String status = '', String time = '', String deadline = ''}) async {
    listQuiz.clear();
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    userName.value = user['name'];
    var response = await QuizService().getQuizList(
        page: currentPage.value,
        search: searchController.text,
        type: 'Ujian',
        status: status,
        time: time,
        deadline: deadline);
    LogService.log.i('cek data $response');

    if (response.isSuccess) {
      for (var item in response.resultValue!) {
        bool empty = listQuiz.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          listQuiz.add(item);
        }
      }
      isLoading.value = false;
    }
  }

  /// =========================================
  /// Get List Quiz
  void getListQuiz() async {
    try {
      isLoadingMoreData.value = true;
      var response = await QuizService()
          .getQuizList(page: currentPage.value, type: 'Ujian');

      if (response.isSuccess) {
        todalData.value = response.resultTotalData!;
        for (var item in response.resultValue!) {
          bool empty =
              listQuiz.where((element) => element.id == item.id).isEmpty;
          if (empty) {
            listQuiz.add(item);
          }
        }
        isLoadingMoreData.value = false;
      }
    } catch (e) {
      isLoadingMoreData.value = false;
    }
  }

  /// =========================================
  /// Attempt Quiz
  void attemptQuiz({String? id, String? imgUrl}) async {
    isLoading.value = true;
    quizById.value = const QuizByIdModel();
    idNavigationList.clear();
    var response = await QuizService().getQuizListById(id: id);
    if (response.isSuccess) {
      quizById.value = response.resultValue!;
      idNavigation.value = id!;
      idNavigationList.addAll(quizById.value.quizNavigate!);
      for (var item in quizById.value.quizNavigate!) {
        bool empty =
            idNavigationList.where((element) => element.id == item.id).isEmpty;
        if (empty) {
          idNavigationList.add(item);
        }
      }
      getQuizProgress();
      Get.to(() => OpenAssessmentCourseView(
            imageUrl: imgUrl,
          ));
      isLoading.value = false;
    }
  }

  /// =========================================
  /// Selected Navigation
  void selectedNavigation({String? id}) async {
    isLoading.value = true;
    quizById.value = const QuizByIdModel();
    idNavigation.value = id!;
    attemptQuiz(id: id);
    getQuizProgress();
  }

  /// =========================================
  /// Start Quiz
  void startQuizId({required String quizId}) async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    LogService.log.i('user id ${user['id']}');
    LogService.log.i('quiz id $quizId');
    socketService.connect(roomId: user['id'].toString(), ujianId: quizId);
    socketService.countAttention.value = 0;
    var response = await QuizService().postStartQuiz(
      userId: user['id'].toString(),
      quizId: quizId,
    );
    if (response.isSuccess) {
      startQuiz.value = response.resultValue!;
      isLoading.value = false;
      // getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
      if (Get.isRegistered<QuizQuestionController>()) {
        Get.find<QuizQuestionController>()
            .getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
        Get.find<QuizQuestionController>().remainingTime.value =
            startQuiz.value.duration ?? 0;
        Get.find<QuizQuestionController>().idStartQuiz.value =
            startQuiz.value.id ?? '';
        Get.find<QuizQuestionController>().randomQuestionIds.value =
            startQuiz.value.randomQuestionIds ?? [];
      } else {
        Get.put(QuizQuestionController())
            .getQuizQuestionId(id: startQuiz.value.randomQuestionIds![0]);
        Get.put(QuizQuestionController()).randomQuestionIds.value =
            startQuiz.value.randomQuestionIds ?? [];
        Get.put(QuizQuestionController()).remainingTime.value =
            startQuiz.value.duration ?? 0;
        Get.put(QuizQuestionController()).idStartQuiz.value =
            startQuiz.value.id ?? '';
        Get.put(QuizQuestionController()).numberQuestion.value = 1;
      }
      // Initialize ShareScreenController safely
      try {
        if (Get.isRegistered<ShareScreenController>()) {
          Get.find<ShareScreenController>().close();
          Get.delete<ShareScreenController>();
        }

        final shareScreenController = Get.put(ShareScreenController());

        shareScreenController.join();
        LogService.log.i('✅ ShareScreenController initialized successfully');
      } catch (e) {
        LogService.log.e('❌ Error initializing ShareScreenController: $e');
      }

      Get.to(() => RunningAssessmentCourseView());
    } else {
      isLoading.value = false;
      // Get.snackbar("Error", response.errorMessage.toString());
    }
  }

  /// =========================================
  /// Next
  void nextQuiz() async {
    int currentIndex = idNavigationList
        .indexWhere((element) => element.id == idNavigation.value);
    if (currentIndex != -1 && currentIndex < idNavigationList.length - 1) {
      String? nextId = idNavigationList[currentIndex + 1].id;
      idNavigation.value = nextId!;
      attemptQuiz(id: nextId);
      getQuizProgress();
    }
  }

  /// =========================================
  /// Previous Quiz
  void previousQuiz() {
    int currentIndex = idNavigationList
        .indexWhere((element) => element.id == idNavigation.value);
    if (currentIndex != -1 && currentIndex > 0) {
      String? previousId = idNavigationList[currentIndex - 1].id;
      idNavigation.value = previousId!;
      attemptQuiz(id: previousId);
      getQuizProgress();
    }
  }

  void getQuizProgress() async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    var response = await QuizService().getQuizProgress(
      userId: user['id'],
      quizId: idNavigation.value,
    );

    if (response.isSuccess) {
      quizProgress.value = response.resultValue!;
      isLoading.value = false;
    } else {
      isLoading.value = false;
      // Get.snackbar("Error", response.errorMessage.toString());
    }
  }

  openSubmitQuiz() {
    isOpenSubmitQuiz.value = !isOpenSubmitQuiz.value;
    LogService.log.i('open submit quiz');
  }
}
