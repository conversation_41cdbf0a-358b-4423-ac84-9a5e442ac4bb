import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/schedule_model.dart';
import 'package:mides_skadik/app/data/services/course/schedule/schedule_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class ScheduleCourseController extends GetxController {
  final selectTabSchedule = 0.obs;
  final isOpenDetailMapel = false.obs;
  final scheduleService = ScheduleService();
  final isLoading = false.obs;
  final appointments = <Appointment>[].obs;
  final selectedAppointment = Rx<Appointment?>(null);
  final currentMonth = DateTime.now().month.obs;
  final currentYear = DateTime.now().year.obs;
  final monthlySchedules = <MonthlySchedule>[].obs;
  final downloadProgress = 0.0.obs;
  final isDownloading = false.obs;
  final isOpenAttendanceHistory = false.obs;

  // 🔧 data source untuk kalender
  final calendarDataSource = MeetingDataSource([]).obs;

  void selectTab(int index) {
    selectTabSchedule.value = index;
  }

  void openDetailMapel() {
    isOpenDetailMapel.value = true;
  }

  void closeDetailMapel() {
    isOpenDetailMapel.value = false;
  }

  void setSelectedAppointment(Appointment? appointment) {
    selectedAppointment.value = appointment;
    openDetailMapel();
  }

  void fetchMonthlySchedule() async {
    isLoading.value = true;
    LogService.log.i(
        "Fetching monthly schedule for: ${currentMonth.value}/${currentYear.value}");

    // Ambil tahun pendidikan dari local storage
    final tahunPendidikanId =
        await scheduleService.localStorage.get("thn_pendidikan_id");
    LogService.log.i('Cek tahun pendidikan $tahunPendidikanId');

    if (tahunPendidikanId == null) {
      LogService.log.e("Tahun pendidikan tidak ditemukan di local storage");
      appointments.clear();
      calendarDataSource.value = MeetingDataSource([]);
      isLoading.value = false;
      return;
    }

    final result = await scheduleService.getMonthlySchedule(
      month: currentMonth.value,
      year: currentYear.value,
      tahunPendidikanId: tahunPendidikanId.toString(),
    );

    if (result.isSuccess && result.resultValue != null) {
      monthlySchedules.value = result.resultValue!;
      appointments.value = result.resultValue!.map((item) {
        final date = item.date.toLocal();
        final startRaw = item.pbmStart.startTime.toLocal();
        final endRaw = item.pbmEnd.endTime.toLocal();

        final startTime = DateTime(
            date.year, date.month, date.day, startRaw.hour, startRaw.minute);
        final endTime = DateTime(
            date.year, date.month, date.day, endRaw.hour, endRaw.minute);

        return Appointment(
          startTime: startTime,
          endTime: endTime,
          subject: item.mapel.name.toUpperCase(),
          notes: item.location,
          color: item.status == "Active"
              ? const Color(0xFF4CAF50)
              : const Color(0xFF2196F3),
          id: item,
        );
      }).toList();

      calendarDataSource.value = MeetingDataSource(appointments.toList());
    } else {
      LogService.log.e("API failed: ${result.errorMessage ?? 'Unknown error'}");
      appointments.clear();
      calendarDataSource.value = MeetingDataSource([]);
    }

    isLoading.value = false;
  }

  void updateMonthYearAndFetch(int newMonth, int newYear) {
    if (currentMonth.value != newMonth || currentYear.value != newYear) {
      currentMonth.value = newMonth;
      currentYear.value = newYear;
      fetchMonthlySchedule();
    }
  }

  @override
  void onInit() {
    super.onInit();
    fetchMonthlySchedule();
  }
}

class MeetingDataSource extends CalendarDataSource {
  MeetingDataSource(List<Appointment> source) {
    appointments = source;
  }
}
