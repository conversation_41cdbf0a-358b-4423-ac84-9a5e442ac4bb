import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/schedule_course/views/schedule_course_landscape.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/schedule/calender_scehdule/calender_schedule.dart';
import 'package:mides_skadik/widgets/course/schedule/detail_mapel/detail_mapel.dart';
import 'package:mides_skadik/widgets/course/schedule/event/detail_card/detail_card.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event_controller.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../controllers/schedule_course_controller.dart';

class ScheduleCourseView extends GetView<ScheduleCourseController> {
  const ScheduleCourseView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ScheduleCourseController());
    final eventController = Get.put(EventController());

    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape) {
      return const ScheduleCourseLandscape();
    }

    return SafeArea(
      child: CustomScaffold(
        body: LayoutBuilder(builder: (context, constraints) {
          return Stack(
            children: [
              Column(
                children: [
                  90.verticalSpace,
                  Padding(
                    padding:
                        EdgeInsets.symmetric(vertical: 60.h, horizontal: 60.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWigdet(
                          title: 'Schedule',
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          textColor: whiteColor,
                        ),
                        40.verticalSpace,
                        Obx(
                          () => Row(
                            children: [
                              CustomFilledButtonWidget(
                                onPressed: () => controller.selectTab(0),
                                title: 'Schedule',
                                fontColor: secondWhiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                bgColor: controller.selectTabSchedule.value == 0
                                    ? baseBlueColor
                                    : whiteColor.withOpacity(0.05),
                                widthButton: 159,
                                heightButton: 48,
                                radius: 8,
                              ),
                              8.horizontalSpace,
                              CustomFilledButtonWidget(
                                onPressed: () => controller.selectTab(1),
                                title: 'Event',
                                fontColor: secondWhiteColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w400,
                                bgColor: controller.selectTabSchedule.value == 1
                                    ? baseBlueColor
                                    : whiteColor.withOpacity(0.05),
                                widthButton: 159,
                                heightButton: 48,
                                radius: 8,
                              ),
                            ],
                          ),
                        ),
                        40.verticalSpace,
                        Obx(() {
                          if (controller.selectTabSchedule.value == 0) {
                            return const CalenderSchedule();
                          } else if (controller.selectTabSchedule.value == 1) {
                            return const Event();
                          } else {
                            return const SizedBox();
                          }
                        }),
                      ],
                    ),
                  )
                ],
              ),

              /// Detail Event Card from EventController
              Obx(() {
                if (eventController.isOpenDetailCardEvent.value &&
                    eventController.selectedEvent.value != null) {
                  return Positioned.fill(
                    child: Stack(
                      children: [
                        BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                          child: CustomContainer(
                            bgColor: blackColor.withOpacity(0.8),
                            widget: Center(
                              child: DetailCard(
                                event: eventController.selectedEvent.value!,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),

              Obx(() {
                if (controller.isOpenDetailMapel.value &&
                    controller.selectedAppointment.value != null) {
                  return Stack(
                    children: [
                      Positioned.fill(
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
                          child: Container(
                            color: blackColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                      // Ini langsung berada di dalam Stack, jadi Positioned akan aktif
                      // ! Uncomment before push
                      Positioned(
                        top: 70,
                        right: 0,
                        bottom: 0,
                        child: DetailMapel(
                          appointment: controller.selectedAppointment.value!,
                        ),
                      ),
                    ],
                  );
                }
                return const SizedBox.shrink();
              }),
              const CustomAppBarCourse(height: 90),
            ],
          );
        }),
      ),
    );
  }
}
