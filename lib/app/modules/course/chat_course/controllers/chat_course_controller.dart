import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/chat_user_by_id_model.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/message_chat_model.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/list_user_chat_model.dart';
import 'package:mides_skadik/app/data/services/course/chat/chat_user_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:ntp/ntp.dart';
import 'package:socket_io_client/socket_io_client.dart' as sio;

class ChatCourseController extends GetxController {
  RxInt page = 1.obs;
  TextEditingController searchController = TextEditingController();
  TextEditingController searchChatController = TextEditingController();
  RxList<ChatUserByIdModel> userChatById = <ChatUserByIdModel>[].obs;
  RxList<ListChatUserModel> listUserChat = <ListChatUserModel>[].obs;

  RxString filter = 'All'.obs;
  RxString searchUser = ''.obs;
  RxString searchChat = ''.obs;
  RxString searchChatDebounce = ''.obs;
  RxBool isNewChat = false.obs;
  RxBool isSearchChat = false.obs;

  var selectTabChatIndex = 0.obs;
  var userName = ''.obs;
  var userId = ''.obs;
  var selectedUserId = ''.obs;
  var selectedUserName = ''.obs;
  var selectedUserImage = ''.obs;
  var selectedUserNrp = ''.obs;
  var replyIdChat = RxnString();
  var chatReply = ''.obs;
  var isStartedChat = false.obs;
  var dx = 0.0.obs;
  var triggerReply = false.obs;
  var isLoadingChat = false.obs;
  var isLoadingListUser = false.obs;
  var hasAutoScroll = false.obs;
  var isAtBottom = false.obs;
  var isLoadingChatUser = false.obs;
  var isLoadingMoreChat = false.obs;
  var isLastPage = false.obs;
  var chatWidgets = <Widget>[].obs;
  var chatLists = <Map<String, dynamic>>[].obs;
  var messageLists = <String, List<MessageChatModel>>{}.obs;
  var scrollController = ScrollController();

  var shownDates = <String>{};
  var messageController = TextEditingController();
  final chatService = ChatUserService();

  DateTime? timeNow;

  // ! Getter for message
  List<MessageChatModel> get messageBySelectedUser =>
      messageLists[selectedUserId.value] ?? [];

  // ! Scroll to Bottom
  void scrollToBottom() {
    if (scrollController.hasClients) {
      scrollController.jumpTo(scrollController.position.maxScrollExtent);
    }
  }

  // ! Setup scroll listener
  void setUpScrollListener() {
    scrollController.addListener(() {
      final maxScroll = scrollController.position.maxScrollExtent;
      final current = scrollController.offset;

      const threshold = 300;

      isAtBottom.value = (maxScroll - current) < threshold;
    });
  }

  // * Configuration websocket
  sio.Socket socket = sio.io(
      baseSocketUrl,
      sio.OptionBuilder()
          .setTransports(['websocket'])
          .disableAutoConnect()
          .build());

  @override
  void onInit() async {
    userName.value = await LocalStorage().get('user_name');
    timeNow = await NTP.now();

    await _initUserId();
    _initSocketListener();
    setUpScrollListener();

    searchController.addListener(() {
      searchUser.value = searchController.text;
    });

    searchChatController.addListener(() {
      searchChat.value = searchChatController.text;
    });

    debounce(searchUser, (queue) {
      if (queue.isNotEmpty) {
        if (isNewChat.value) getListUsersChat();
        _getSearchUserChat(queue);
      } else {
        if (isNewChat.value) getListUsersChat();
        _getSearchUserChat('');
      }
    }, time: const Duration(seconds: 1));

    debounce(searchChat, (queue) {
      if (queue.length < 2) {
        searchChatDebounce.value = '';
        return;
      }

      searchChatDebounce.value = queue;
    }, time: const Duration(seconds: 1));
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
    socket.disconnect();
    socket.dispose();
    searchController.dispose();
  }

  selectTabChatButton(int index) {
    selectTabChatIndex.value = index;
  }

  Future<void> selectChatUser(
      String id, String name, String image, String nrp) async {
    selectedUserId.value = id;
    selectedUserName.value = name;
    selectedUserImage.value = image;
    selectedUserNrp.value = nrp;
    hasAutoScroll.value = false;
    page.value = 1;

    await chatGet(id);

    final unreadMessages = messageLists[id]
        ?.where(
            (msg) => msg.receiver?.id == userId.value && msg.status != 'Read')
        .toList();

    if (unreadMessages != null && unreadMessages.isNotEmpty) {
      for (var msg in unreadMessages) {
        _markAsRead(msg.id); // Emit socket event untuk tiap pesan
      }
    }

    Future.delayed(const Duration(milliseconds: 100), () {
      if (!hasAutoScroll.value) {
        scrollToBottom();
        hasAutoScroll.value = true;
      }
    });
  }

  // * Get Chat User By ID
  Future<void> getChatUserById() async {
    isLoadingChatUser.value = true;

    final result = await chatService.getChatUserById(userId: userId.value);

    if (result.isSuccess) {
      userChatById.value = result.resultValue ?? [];
    } else {
      userChatById.value = [];
    }

    isLoadingChatUser.value = false;
  }

  // * Get List User
  Future<void> getListUsersChat() async {
    isLoadingListUser.value = true;

    final result = await chatService.listUserChat(
        name: searchUser.value, role: filter.value);

    if (result.isSuccess) {
      listUserChat.value = result.resultValue ?? [];
    } else {
      listUserChat.value = [];
    }
    isLoadingListUser.value = false;
  }

  // * Sent message and add to lists
  void sendMessage({required String message}) {
    if (messageController.text.trim().isEmpty) return;

    socket.emit('send-message', {
      'senderId': userId.value,
      'receiverId': selectedUserId.value,
      'content': message,
      'replyToId': replyIdChat.value
    });

    messageController.clear();

    if (isAtBottom.value) {
      Future.delayed(const Duration(milliseconds: 50), () => scrollToBottom());
    }
  }

  // * Date Label on chat
  String formatDateLabel(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      return 'TODAY';
    } else if (messageDate ==
        today.subtract(
          const Duration(days: 1),
        )) {
      return 'YESTERDAY';
    } else {
      return DateFormat('EEE, dd MMM').format(date);
    }
  }

  bool isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // * Reply chat
  void doReply(String chatId) {
    final currentMessageList = messageLists[selectedUserId.value] ?? [];

    final data = currentMessageList.firstWhere((msg) => msg.id == chatId,
        orElse: () =>
            MessageChatModel(id: '', content: '', status: '', replyToId: ''));

    if (data != null) {
      chatReply.value = data.content;
      replyIdChat.value = chatId;
      triggerReply.value = true;
    }
  }

  // * Get chat history
  void getHistoryChat(String partnerId) {
    isLoadingChat.value = true;

    socket.off('chat-history');
    messageLists.clear();

    socket.on('chat-history', (data) {
      LogService.log.i("Chat History: $data");
      final List<MessageChatModel> loadMessage = List<MessageChatModel>.from(
        data.map((e) => MessageChatModel.fromJson(e)),
      );

      messageLists[partnerId] = loadMessage;
      isLoadingChat.value = false;
    });

    socket.emit(
        'get-chat-history', {"userId": userId.value, "partnerId": partnerId});
  }

  // * Get chat history
  Future<void> chatGet(String partnerId) async {
    messageLists.clear();
    isLoadingChat.value = true;

    final result = await chatService.getRiwayatChat(
        userId: userId.value, partnerId: partnerId);

    if (result.isSuccess) {
      messageLists[partnerId] = result.resultValue ?? [];
    } else {
      messageLists[partnerId] = [];
    }

    isLoadingChat.value = false;
  }

  // * Get chat history older
  Future<void> getOlderChat(String partnerId) async {
    if (isLastPage.value) return;

    isLoadingMoreChat.value = true;

    final currentPage = page.value += 1;
    LogService.log.d('Page $currentPage');

    final result = await chatService.getRiwayatChat(
        userId: userId.value, partnerId: partnerId, page: currentPage);

    if (result.isSuccess) {
      if (result.resultValue != null) {
        if (result.resultValue!.isEmpty) {
          page.value = currentPage - 1;
          isLastPage.value = true;
          isLoadingMoreChat.value = false;
          return;
        }
      }
      final newChat = result.resultValue ?? [];
      final current = messageLists[partnerId] ?? [];

      final combine = [...newChat, ...current];

      messageLists[partnerId] = combine;
    } else {
      messageLists[partnerId] = [];
    }
    isLoadingMoreChat.value = false;

    if (searchChatDebounce.value.length >= 2) {
      searchChatDebounce.refresh();
    }
  }

  // ! Initial User ID
  Future<void> _initUserId() async {
    final idUser = await LocalStorage().get('user_id');
    userId.value = idUser;
    LogService.log.d('User ID: ${userId.value}');
  }

  // ! Initial Socket
  void _initSocketListener() {
    socket.connect();

    socket.onConnect((_) {
      LogService.log.d('Connected to Server');
      socket.emit('join', userId.value);
      socket.emit('get-list-chat', {
        'userId': userId.value,
        'role': filter.value,
        'search': searchUser.value
      });
    });

    socket.onDisconnect((_) => LogService.log.e("Disconnect from Server"));

    socket.on('receive-message', _handleReceiveMessage);
    socket.on('message-read', _handleMessageRead);
    socket.on('list-chat', _getListChat);
  }

  // * Handle message Read
  void _handleMessageRead(dynamic data) {
    final messageId = data['id'];
    LogService.log.i("Update Message: $data");

    _updateMessageStatus(messageId, data['status']);

    socket.emit('get-list-chat', {
      'userId': userId.value,
      'role': filter.value,
      'search': searchUser.value
    });
  }

  // * Handle receive message
  void _handleReceiveMessage(dynamic data) {
    LogService.log.i("data server: $data");

    final isMe = data['sender']['id'] == userId.value;
    final isToMe = data['receiver']['id'] == userId.value;
    final messages = MessageChatModel.fromJson(data);
    final partnerId = isMe ? data['receiver']['id'] : data['sender']['id'];

    LogService.log.i("Partner iD: $partnerId");
    final isOpenChatWithSender = selectedUserId.value == partnerId;

    LogService.log.i(
        "IsOpen: $isOpenChatWithSender, ${selectedUserId.value}, $partnerId");

    if (isMe && isToMe) {
      _addOrUpdateMessage(partnerId, messages.copyWith(status: 'Read'));
      _markAsRead(messages.id);
    } else if (!isMe && isToMe) {
      if (isOpenChatWithSender && data['status'] != 'Read') {
        LogService.log.i("Message Read: $data");
        _markAsRead(messages.id);
        _addOrUpdateMessage(partnerId, messages.copyWith(status: 'Read'));
      } else {
        _addOrUpdateMessage(partnerId, messages);
      }
    } else {
      _addOrUpdateMessage(partnerId, messages);
    }

    if (isAtBottom.value) {
      Future.delayed(const Duration(milliseconds: 100), () => scrollToBottom());
    }

    socket.emit('get-list-chat', {
      'userId': userId.value,
      'role': filter.value,
      'search': searchUser.value
    });
  }

  // * Add or Update message chat
  void _addOrUpdateMessage(String id, MessageChatModel messageModel) {
    final currentMessageList = messageLists[id] ?? [];

    int idx = currentMessageList.indexWhere((msg) => msg.id == messageModel.id);
    if (idx == -1) {
      currentMessageList.add(messageModel);
      LogService.log.i("Masuk sini Add");
    } else {
      currentMessageList[idx] = messageModel;
      LogService.log.i("Masuk sini Update");
    }

    messageLists[id] = List.from(currentMessageList);
  }

  // * Update status message
  void _updateMessageStatus(String messageId, String status) {
    final partnerId = selectedUserId.value;
    if (partnerId == null) return;

    final currentMessageList = messageLists[partnerId] ?? [];
    int idx = currentMessageList.indexWhere((msg) => msg.id == messageId);
    LogService.log.i("Status Updated $idx");

    if (idx != -1) {
      final updateMessage = currentMessageList[idx].copyWith(status: status);
      currentMessageList[idx] = updateMessage;
      messageLists[partnerId] = List.from(currentMessageList);
      LogService.log.i("Status Updated");
    }
  }

  // * Mark as read socket for update chat status
  void _markAsRead(String messageId) {
    socket.emit('mark-as-read', {"messageId": messageId});
    // socket.on('message-read', _handleMessageRead);
  }

  // * Get List Chat by Socket
  void _getListChat(dynamic data) {
    List<dynamic> msg = data as List;
    userChatById.assignAll(
      msg
          .map((e) => ChatUserByIdModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  // * Get User Chat List
  void _getSearchUserChat(String search) {
    socket.emit('get-list-chat',
        {'userId': userId.value, 'role': filter.value, 'search': search});
  }

  // * Get User Chat List by Filter
  void getUserChatListByFilter() {
    socket.emit('get-list-chat', {
      'userId': userId.value,
      'role': filter.value,
      'search': searchUser.value
    });
  }

  // * Loading More Data Widget
  Widget buildLoadMoreHeader() {
    return Obx(() {
      if (isLoadingMoreChat.value) {
        return const CustomLoadingWidget();
      }

      return Center(
        child: CustomFilledButtonWidget(
          widthButton: 400,
          bgColor: baseBlueColor,
          fontColor: secondWhiteColor,
          radius: 8,
          onPressed: () {
            getOlderChat(selectedUserId.value);
          },
          title: 'Tampilkan Pesan Lainnya',
        ),
      );
    });
  }

  // * Show DataChip
  bool shouldShowDataChip(int idx, List<MessageChatModel> messages) {
    if (idx == 1) return true;
    final current = messages[idx - 1].createdAt;
    final prev = messages[idx - 2].createdAt;

    return !isSameDay(current!, prev!);
  }
}
