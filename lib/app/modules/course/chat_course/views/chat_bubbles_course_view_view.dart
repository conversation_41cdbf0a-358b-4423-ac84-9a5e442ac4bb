import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/chat_course/controllers/chat_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/chat/custom_chat_bubble.dart';
import 'package:mides_skadik/widgets/course/chat/message_bar_chat.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:swipe_to/swipe_to.dart';

import '../../../../../widgets/course/chat/custom_chat_user.dart';

class ChatBubblesCourseViewView extends GetView<ChatCourseController> {
  const ChatBubblesCourseViewView({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      useAppBar: true,
      isCourse: true,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 60.h),
        child: Column(
          children: [
            Row(
              children: [
                CustomFilledButtonWidget(
                  onPressed: () {
                    Get.back();
                  },
                  withIcon: true,
                  onlyIcon: true,
                  assetName: 'assets/icons/left.svg',
                  widthButton: 50.w,
                  heightIcon: 50.h,
                  bgColor: Colors.transparent,
                ),
                10.horizontalSpace,
                const CustomTextWigdet(
                  title: 'Message',
                  fontSize: 32,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
            37.verticalSpace,
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: CustomContainer(
                      bgColor: baseBlueColor.withOpacity(0.2),
                      width: double.infinity,
                      height: double.infinity,
                      radius: 12,
                      widget: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 20.w, vertical: 24.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const CustomTextWigdet(
                              title: 'ChatRoom',
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                            ),
                            20.verticalSpace,
                            CustomTextFieldWidget(
                              controller: controller.searchController,
                              colorText: whiteColor,
                              colorField: baseBlueColor.withOpacity(0.2),
                              assetNameIcon: 'assets/icons/search.svg',
                              hintText: 'Search with name or NRP',
                              colorTextHint: secondWhiteColor.withOpacity(0.2),
                              iconWidth: 18,
                              fontSize: 18,
                              radius: 8,
                              heightField: 48,
                              contentPadding: EdgeInsetsDirectional.zero,
                            ),
                            16.verticalSpace,
                            Obx(
                              () => Row(
                                children: [
                                  CustomFilledButtonWidget(
                                    onPressed: () {
                                      controller.selectTabChatButton(0);
                                      controller.filter.value = 'All';

                                      if (controller.isNewChat.value) {
                                        controller.filter.value = '';
                                        controller.getListUsersChat();
                                        return;
                                      }

                                      controller.getUserChatListByFilter();
                                    },
                                    title: 'All',
                                    fontColor: secondWhiteColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    bgColor:
                                        controller.selectTabChatIndex.value == 0
                                            ? baseBlueColor
                                            : whiteColor.withOpacity(0.05),
                                    widthButton: 61,
                                    heightButton: 48,
                                    radius: 8,
                                  ),
                                  8.horizontalSpace,
                                  CustomFilledButtonWidget(
                                    onPressed: () {
                                      controller.selectTabChatButton(1);
                                      controller.filter.value = 'Student';

                                      if (controller.isNewChat.value) {
                                        controller.getListUsersChat();
                                        return;
                                      }

                                      controller.getUserChatListByFilter();
                                    },
                                    title: 'Student',
                                    fontColor: secondWhiteColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    bgColor:
                                        controller.selectTabChatIndex.value == 1
                                            ? baseBlueColor
                                            : whiteColor.withOpacity(0.05),
                                    widthButton: 108,
                                    heightButton: 48,
                                    radius: 8,
                                  ),
                                  8.horizontalSpace,
                                  CustomFilledButtonWidget(
                                    onPressed: () {
                                      controller.selectTabChatButton(2);
                                      controller.filter.value = 'Lecturer';

                                      if (controller.isNewChat.value) {
                                        controller.getListUsersChat();
                                        return;
                                      }

                                      controller.getUserChatListByFilter();
                                    },
                                    title: 'Lecturer',
                                    fontColor: secondWhiteColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    bgColor:
                                        controller.selectTabChatIndex.value == 2
                                            ? baseBlueColor
                                            : whiteColor.withOpacity(0.05),
                                    widthButton: 121,
                                    heightButton: 48,
                                    radius: 8,
                                  ),
                                ],
                              ),
                            ),
                            20.verticalSpace,
                            Obx(
                              () {
                                final userListChatById =
                                    controller.userChatById;
                                final userListChat = controller.listUserChat;
                                final userList = controller.listUserChat;

                                if (controller.isLoadingChatUser.value) {
                                  return const CustomLoadingWidget();
                                }

                                if (controller.isLoadingListUser.value) {
                                  return const CustomLoadingWidget();
                                }

                                if (!controller.isLoadingChatUser.value &&
                                    userListChatById.isEmpty &&
                                    userListChat.isEmpty) {
                                  return SizedBox(
                                    width: double.infinity,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          'assets/icons/empty_state-message.svg',
                                        ),
                                        20.verticalSpace,
                                        const CustomTextWigdet(
                                          title:
                                              'You haven\'t chatted with anyone yet',
                                          fontSize: 18,
                                          textAlign: TextAlign.center,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        8.verticalSpace,
                                        CustomTextWigdet(
                                          title:
                                              'Search for your name or NRP in the search field, then start your first conversation here.',
                                          fontSize: 16,
                                          textAlign: TextAlign.center,
                                          textColor:
                                              secondWhiteColor.withOpacity(0.5),
                                          fontWeight: FontWeight.w400,
                                        ),
                                        24.verticalSpace,
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.filter.value = '';
                                            controller.isNewChat.value =
                                                !controller.isNewChat.value;
                                            controller.getListUsersChat();
                                          },
                                          title: 'Start a convertion',
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          fontColor: whiteColor,
                                          bgColor: blueColor,
                                          radius: 4,
                                          widthButton: 193,
                                        )
                                      ],
                                    ),
                                  );
                                }

                                if (controller.isNewChat.value) {
                                  return Flexible(
                                    child: Stack(
                                      children: [
                                        SizedBox(
                                          child: ListView.builder(
                                            itemCount: userList.length,
                                            itemBuilder: (context, index) {
                                              final userItem =
                                                  userList.isNotEmpty
                                                      ? userList[index]
                                                      : null;
                                              return Padding(
                                                padding: EdgeInsets.only(
                                                    bottom: 20.h),
                                                child: CustomChatUser(
                                                  isHeaderChat: true,
                                                  idStudent: '${userItem?.id}',
                                                  nameStudent:
                                                      '${userItem?.name}',
                                                  imageUrl:
                                                      userItem?.imageProfile ??
                                                          '',
                                                  onTap: () {
                                                    controller.triggerReply
                                                        .value = false;
                                                    controller.isNewChat.value =
                                                        false;
                                                    controller.selectChatUser(
                                                        '${userItem?.id}',
                                                        '${userItem?.name}',
                                                        userItem?.imageProfile ??
                                                            '',
                                                        userItem?.nrp ?? '');
                                                  },
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                        Align(
                                          alignment: Alignment.bottomCenter,
                                          child: CustomFilledButtonWidget(
                                            onPressed: () {
                                              // controller.filter.value = 'All';
                                              controller.isNewChat.value =
                                                  !controller.isNewChat.value;
                                              controller
                                                  .getUserChatListByFilter();
                                            },
                                            title: 'Back to Chat',
                                            fontColor: secondWhiteColor,
                                            heightButton: 48,
                                            heightIcon: 24,
                                            radius: 8,
                                            bgColor: baseBlueColor.withValues(
                                                alpha: .5),
                                          ),
                                        )
                                      ],
                                    ),
                                  );
                                }
                                return Flexible(
                                  // height: 1590.h,
                                  child: Stack(
                                    children: [
                                      SizedBox(
                                        child: ListView.builder(
                                          itemCount: userListChatById.length,
                                          itemBuilder: (context, index) {
                                            final userItem =
                                                userListChatById.isNotEmpty
                                                    ? userListChatById[index]
                                                    : null;
                                            return Padding(
                                              padding:
                                                  EdgeInsets.only(bottom: 20.h),
                                              child: CustomChatUser(
                                                idStudent:
                                                    '${userItem?.user?.id}',
                                                nameStudent:
                                                    '${userItem?.user?.name}',
                                                imageUrl: userItem
                                                        ?.user?.imageProfile ??
                                                    '',
                                                lastMessage:
                                                    '${userItem?.lastMessage?.content}',
                                                lastMessageTime: timeRangeChat(
                                                    controller.timeNow!,
                                                    userItem?.lastMessage
                                                            ?.createdAt ??
                                                        controller.timeNow!),
                                                unreadCount:
                                                    userItem?.totalDelivered,
                                                isRead: userItem
                                                        ?.lastMessage?.status ==
                                                    'Read',
                                                isMe: userItem?.lastMessage
                                                        ?.senderId ==
                                                    controller.userId.value,
                                                onTap: () {
                                                  controller.triggerReply
                                                      .value = false;
                                                  controller.selectChatUser(
                                                      '${userItem?.user?.id}',
                                                      '${userItem?.user?.name}',
                                                      userItem?.user
                                                              ?.imageProfile ??
                                                          '',
                                                      userItem?.user?.nrp ??
                                                          '');
                                                },
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      Align(
                                        alignment: Alignment.bottomRight,
                                        child: CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.selectTabChatButton(0);
                                            controller.filter.value = '';
                                            controller.isSearchChat.value =
                                                false;
                                            controller.searchChatController
                                                .clear();
                                            controller.isNewChat.value =
                                                !controller.isNewChat.value;
                                            controller.getListUsersChat();
                                          },
                                          onlyIcon: true,
                                          withIcon: true,
                                          assetName:
                                              'assets/icons/add_new_message.svg',
                                          widthButton: 48,
                                          heightButton: 48,
                                          heightIcon: 24,
                                          radius: 8,
                                          bgColor: baseBlueColor.withValues(
                                              alpha: .5),
                                        ),
                                      )
                                    ],
                                  ),
                                );
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  16.horizontalSpace,
                  Expanded(
                    flex: 2,
                    child: CustomContainer(
                      bgColor: baseBlueColor.withOpacity(0.2),
                      width: double.infinity,
                      height: double.infinity,
                      radius: 12,
                      widget: Obx(
                        () => controller.selectedUserId.value.isEmpty
                            ? Padding(
                                padding:
                                    EdgeInsets.symmetric(horizontal: 110.w),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                        'assets/icons/chat ilus.svg'),
                                    32.verticalSpace,
                                    CustomTextWigdet(
                                      title:
                                          'Hi, ${controller.userName.value}, Welcome to Message!',
                                      textAlign: TextAlign.center,
                                      fontSize: 30,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    12.verticalSpace,
                                    const CustomTextWigdet(
                                      title:
                                          'Establish conversations with colleagues, trainees, patents, or lecturers practically and efficiently, directly from this application.',
                                      textAlign: TextAlign.center,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    )
                                  ],
                                ),
                              )
                            : Stack(
                                children: [
                                  Column(
                                    children: [
                                      CustomContainer(
                                        bgColor: baseBlueColor.withOpacity(0.2),
                                        width: double.infinity,
                                        widget: Padding(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 28.w,
                                              vertical: 21.5.h),
                                          child: Obx(
                                            () {
                                              if (controller
                                                  .isSearchChat.value) {
                                                return CustomTextFieldWidget(
                                                  heightField: 50,
                                                  controller: controller
                                                      .searchChatController,
                                                  hintText: 'Search chat',
                                                  colorTextHint:
                                                      secondWhiteColor
                                                          .withValues(
                                                              alpha: .4),
                                                  fontSize: 22,
                                                  fontWeight: FontWeight.w500,
                                                  colorField: baseBlueColor
                                                      .withValues(alpha: .4),
                                                  colorText: whiteColor,
                                                  assetNameIcon:
                                                      'assets/icons/search.svg',
                                                  borderSide: BorderSide(
                                                      color: whiteColor),
                                                  focusBorderSide: BorderSide(
                                                      color: whiteColor),
                                                  suffixAssetNameIcon:
                                                      'assets/icons/close2.svg',
                                                  suffixIconConstraints:
                                                      const BoxConstraints(
                                                    maxHeight: 20,
                                                  ),
                                                  onTapSuffix: () {
                                                    controller.isSearchChat
                                                        .value = false;
                                                    controller
                                                        .searchChatController
                                                        .clear();
                                                  },
                                                  radius: 8,
                                                  contentPadding:
                                                      const EdgeInsetsDirectional
                                                          .only(
                                                          start: 10, end: 10),
                                                );
                                              }
                                              return Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CustomChatUser(
                                                    isHeaderChat: true,
                                                    idStudent: controller
                                                        .selectedUserId.value,
                                                    nameStudent: controller
                                                        .selectedUserName.value,
                                                    imageUrl: controller
                                                        .selectedUserImage
                                                        .value,
                                                    nrpStudent: controller
                                                        .selectedUserNrp.value,
                                                  ),
                                                  IconButton(
                                                    onPressed: () {
                                                      controller.isSearchChat
                                                          .value = true;
                                                    },
                                                    icon: SvgPicture.asset(
                                                      'assets/icons/search.svg',
                                                      height: 24.h,
                                                      width: 24.w,
                                                    ),
                                                  ),
                                                ],
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      Flexible(
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(bottom: 63),
                                          child: SizedBox(
                                            width: double.infinity,
                                            child: Obx(
                                              () {
                                                final query = controller
                                                    .searchChatDebounce.value;
                                                final messageLists =
                                                    controller.messageLists[
                                                            controller
                                                                .selectedUserId
                                                                .value] ??
                                                        [];

                                                if (controller
                                                    .isLoadingChat.value) {
                                                  return const CustomLoadingWidget();
                                                }

                                                if (messageLists.isEmpty) {
                                                  return const Center(
                                                    child: CustomTextWigdet(
                                                      title: 'Belum ada pesan',
                                                      textColor: Colors.grey,
                                                      fontSize: 18,
                                                    ),
                                                  );
                                                }
                                                return ListView.builder(
                                                  controller: controller
                                                      .scrollController,
                                                  itemCount:
                                                      messageLists.length + 1,
                                                  itemBuilder: (context, idx) {
                                                    if (idx == 0) {
                                                      return controller
                                                          .buildLoadMoreHeader();
                                                    }

                                                    final msg =
                                                        messageLists[idx - 1];

                                                    bool showDatechip =
                                                        controller
                                                            .shouldShowDataChip(
                                                                idx,
                                                                messageLists);

                                                    return Column(
                                                      children: [
                                                        if (showDatechip)
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              Container(
                                                                width: 73.w,
                                                                height: 1.h,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  gradient: LinearGradient(
                                                                      colors: [
                                                                        secondBlueColor
                                                                            .withOpacity(0.2),
                                                                        secondBlueColor
                                                                      ],
                                                                      begin: Alignment
                                                                          .centerLeft,
                                                                      end: Alignment
                                                                          .centerRight),
                                                                ),
                                                              ),
                                                              10.horizontalSpace,
                                                              CustomTextWigdet(
                                                                title: controller
                                                                    .formatDateLabel(
                                                                        msg.createdAt!),
                                                                fontSize: 16,
                                                                textColor:
                                                                    secondWhiteColor
                                                                        .withOpacity(
                                                                            0.5),
                                                              ),
                                                              10.horizontalSpace,
                                                              Container(
                                                                width: 73.w,
                                                                height: 1.h,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  gradient: LinearGradient(
                                                                      colors: [
                                                                        secondBlueColor,
                                                                        secondBlueColor
                                                                            .withOpacity(0.2),
                                                                      ],
                                                                      begin: Alignment
                                                                          .centerLeft,
                                                                      end: Alignment
                                                                          .centerRight),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        SwipeTo(
                                                          iconColor:
                                                              secondWhiteColor,
                                                          onRightSwipe: (_) {
                                                            controller.doReply(
                                                                msg.id);
                                                          },
                                                          child:
                                                              CustomChatBubble(
                                                            message:
                                                                msg.content,
                                                            query: query,
                                                            isMe: msg.sender
                                                                    ?.id ==
                                                                controller
                                                                    .userId
                                                                    .value,
                                                            sent: msg.status ==
                                                                "Sent",
                                                            delivered:
                                                                msg.status ==
                                                                    'Delivered',
                                                            seen: msg.status ==
                                                                'Read',
                                                            chatId: msg.id,
                                                            name: controller
                                                                        .userId
                                                                        .value ==
                                                                    msg.replyTo
                                                                        ?.senderId
                                                                ? msg.sender
                                                                    ?.name
                                                                : msg.receiver
                                                                    ?.name,
                                                            replyTo: msg.replyTo
                                                                ?.content,
                                                            isReply:
                                                                msg.replyToId !=
                                                                    null,
                                                            time: formatTimeOnly(
                                                                msg.createdAt ??
                                                                    controller
                                                                        .timeNow!),
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  Obx(
                                    () => MessageBarChat(
                                      onSend: (message) {
                                        controller.messageController.text =
                                            message;
                                        controller.sendMessage(
                                            message: controller
                                                .messageController.text);

                                        if (controller.triggerReply.value) {
                                          controller.triggerReply.value = false;
                                          controller.replyIdChat.value = null;
                                          return;
                                        }
                                      },
                                      replying: controller.triggerReply.value,
                                      replyingTo: controller.chatReply.value,
                                      name: controller.userName.value,
                                      replyWidgetColor: baseBlueColor,
                                      replyCloseColor: whiteColor,
                                      messageBarHintText: 'Type message',
                                      userBorder: false,
                                      radiusTextField: 0,
                                      messageBarColor:
                                          baseBlueColor.withOpacity(0.25),
                                      textFieldColor:
                                          baseBlueColor.withOpacity(0.1),
                                      onTapCloseReply: () {
                                        controller.triggerReply.value = false;
                                        controller.replyIdChat.value = null;
                                      },
                                      textFieldTextStyle: TextStyle(
                                        fontFamily: "Inter",
                                        color: whiteColor,
                                        fontSize: 18.sp,
                                        fontStyle: FontStyle.normal,
                                        fontWeight: FontWeight.w400,
                                        decoration: TextDecoration.none,
                                      ),
                                      messageBarHintStyle: TextStyle(
                                        fontFamily: "Inter",
                                        color: secondWhiteColor,
                                        fontSize: 18.sp,
                                        fontStyle: FontStyle.normal,
                                        fontWeight: FontWeight.w400,
                                        decoration: TextDecoration.none,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                      ),
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
