import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/chat/custom_chat_room.dart';
import 'package:mides_skadik/widgets/course/chat/custom_chat_user.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/chat_course_controller.dart';

class ChatCourseView extends GetView<ChatCourseController> {
  const ChatCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: CustomScaffold(
        body: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 60.h, horizontal: 60.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      140.verticalSpace,
                      Row(
                        children: [
                          SvgPicture.asset(
                            'assets/icons/back.svg',
                            width: 24.w,
                            height: 24.h,
                          ),
                          8.horizontalSpace,
                          CustomTextWigdet(
                            title: 'Message',
                            fontSize: 32,
                            fontWeight: FontWeight.w700,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                      32.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomContainer(
                            width: 420,
                            height: 1800,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            radius: 8,
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 24.h, horizontal: 20.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextWigdet(
                                    title: 'ChatRoom',
                                    fontSize: 24,
                                    fontWeight: FontWeight.w600,
                                    textColor: whiteColor,
                                  ),
                                  20.verticalSpace,
                                  CustomTextFieldWidget(
                                    radius: 8,
                                    obscureText: true,
                                    colorField: whiteColor.withOpacity(0.05),
                                    colorText: whiteColor,
                                    hintText: 'Search with name or NIK',
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    colorTextHint: whiteColor.withOpacity(0.5),
                                    assetNameIcon: 'assets/icons/search.svg',
                                    colorSuffixIcon:
                                        whiteColor.withOpacity(0.5),
                                    iconWidth: 5,
                                    iconHeight: 5,
                                    widthField: double.infinity,
                                    heightField: 48,
                                    contentPadding: EdgeInsetsDirectional.only(
                                      top: 16.h,
                                    ),
                                  ),
                                  16.verticalSpace,
                                  Obx(
                                    () => Row(
                                      children: [
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.selectTabChatButton(0);
                                          },
                                          title: 'All',
                                          fontColor: secondWhiteColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          bgColor: controller.selectTabChatIndex
                                                      .value ==
                                                  0
                                              ? baseBlueColor
                                              : whiteColor.withOpacity(0.05),
                                          widthButton: 61,
                                          heightButton: 48,
                                          radius: 8,
                                        ),
                                        8.horizontalSpace,
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.selectTabChatButton(1);
                                          },
                                          title: 'Pasis',
                                          fontColor: secondWhiteColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          bgColor: controller.selectTabChatIndex
                                                      .value ==
                                                  1
                                              ? baseBlueColor
                                              : whiteColor.withOpacity(0.05),
                                          widthButton: 85,
                                          heightButton: 48,
                                          radius: 8,
                                        ),
                                        8.horizontalSpace,
                                        CustomFilledButtonWidget(
                                          onPressed: () {
                                            controller.selectTabChatButton(2);
                                          },
                                          title: 'Dosen',
                                          fontColor: secondWhiteColor,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                          bgColor: controller.selectTabChatIndex
                                                      .value ==
                                                  2
                                              ? baseBlueColor
                                              : whiteColor.withOpacity(0.05),
                                          widthButton: 121,
                                          heightButton: 48,
                                          radius: 8,
                                        ),
                                      ],
                                    ),
                                  ),
                                  25.verticalSpace,
                                  SizedBox(
                                    height: 1550.h,
                                    child: ListView.builder(
                                      itemCount: 50,
                                      itemBuilder: (context, index) {
                                        return Padding(
                                          padding:
                                              EdgeInsets.only(bottom: 20.h),
                                          child: CustomChatUser(
                                            idStudent: 'id_$index',
                                            nameStudent: 'Siswa $index',
                                            imageUrl: '',
                                            lastMessage: 'Pesan terakhir',
                                            lastMessageTime: 'Hari ini',
                                            unreadCount: index,
                                            isRead: false,
                                            onTap: () {
                                              // controller.selectChatUser(
                                              //   'id_$index',
                                              //   'Siswa $index',
                                              //   '',
                                              // );
                                            },
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          CustomContainer(
                            width: 884,
                            height: 1800,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            radius: 8,
                            widget: Obx(
                              () {
                                return controller.selectedUserId.isNotEmpty
                                    ? CustomChatRoom(
                                        nameStudent:
                                            controller.selectedUserName.value,
                                        imageUrl:
                                            controller.selectedUserImage.value,
                                      )
                                    : Center(
                                        child: CustomTextWigdet(
                                          title:
                                              'Pilih pengguna untuk mulai chat',
                                          fontSize: 20,
                                          fontWeight: FontWeight.w400,
                                          textColor:
                                              whiteColor.withOpacity(0.6),
                                        ),
                                      );
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const CustomAppBarCourse(height: 60),
          ],
        ),
      ),
    );
  }
}
