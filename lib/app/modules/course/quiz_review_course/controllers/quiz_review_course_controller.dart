import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_progress_model.dart';
import 'package:mides_skadik/app/data/services/course/quiz_service.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';

class QuizReviewCourseController extends GetxController {
  RxList<Map<String, dynamic>> listQuiz = <Map<String, dynamic>>[].obs;
  Rx<QuizProgressModel> quizProgress = const QuizProgressModel().obs;
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    getQuizProgress();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void showAnswer({required int numberQuiz, required dynamic answerKey}) {
    listQuiz[numberQuiz]["answer"].forEach((key, value) {
      if (answerKey == key) {
        value["selected"] = true;
      } else {
        value["selected"] = false;
      }
    });
    listQuiz.refresh();
    debugPrint(
        "Selected Answer: ${listQuiz[numberQuiz]["answer"]} \nQuestion: ${listQuiz[numberQuiz]["question"]} \nNumber Quiz: $numberQuiz");
  }

  void getQuizProgress() async {
    isLoading.value = true;
    var user = await LocalStorage().get('user');
    var response = await QuizService().getQuizProgress(
      userId: Get.arguments['userId'] ?? user['id'],
      quizId: Get.arguments['quizId'] ?? '',
    );

    if (response.isSuccess) {
      quizProgress.value = response.resultValue!;
      isLoading.value = false;
    } else {
      isLoading.value = false;
      Get.snackbar("Error", response.errorMessage.toString());
    }
  }
}
