import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/open_quiz_course_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';
import 'package:mides_skadik/widgets/course/class/custom_card_questions_task_course.dart';
import 'package:mides_skadik/widgets/course/custom_information_boards_course.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import '../controllers/quiz_review_course_controller.dart';

class QuizReviewCourseView extends GetView<QuizReviewCourseController> {
  const QuizReviewCourseView({super.key});
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const CustomAppBarCourse(height: 60),
            Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CustomFilledButtonWidget(
                          onPressed: () {
                            Get.off(() => const OpenQuizCourseView());
                          },
                          isOutlined: true,
                          withIcon: true,
                          borderColor: greyColor,
                          assetName: 'assets/icons/left.svg',
                          title: 'Back',
                          fontColor: whiteColor,
                          fontSize: 16,
                          widthButton: 115,
                          heightIcon: 20,
                          fontWeight: FontWeight.w500,
                          radius: 4,
                        ),
                        const Spacer(),
                        CustomFilledButtonWidget(
                          onPressed: () {},
                          isOutlined: true,
                          borderColor: greyColor,
                          title: 'Mark Completed',
                          fontColor: whiteColor,
                          fontSize: 16,
                          widthButton: 165,
                          fontWeight: FontWeight.w500,
                          radius: 4,
                        )
                      ],
                    ),
                    32.verticalSpace,
                    CustomTextWigdet(
                      title: Get.arguments['title'] ?? '',
                      fontWeight: FontWeight.w700,
                      fontSize: 32,
                      fontStyle: FontStyle.normal,
                    ),
                    32.verticalSpace,
                    CustomContainer(
                      bgColor: secondBlueColor.withOpacity(0.10),
                      padding: const EdgeInsets.all(20),
                      radius: 10,
                      widget: Obx(
                        () => Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Column(
                                children: [
                                  CustomInformationBoardsCourse(
                                    startFrom: DateFormat(
                                            'EEEE, MMMM, dd, yyyy, HH:mm')
                                        .format(controller
                                                .quizProgress.value.startDate ??
                                            DateTime.now()),
                                    status:
                                        controller.quizProgress.value.status,
                                    doneBy: DateFormat(
                                            'EEEE, MMMM, dd, yyyy, HH:mm')
                                        .format(controller
                                                .quizProgress.value.endDate ??
                                            DateTime.now()),
                                    timeSpend: formatRemainingTime(controller
                                            .quizProgress.value.spendTime ??
                                        0),
                                    score: controller
                                        .quizProgress.value.totalScore
                                        .toString(),
                                  ),
                                  20.verticalSpace,
                                  SizedBox(
                                    height:
                                        Get.height / 2, // Set a finite height
                                    child: Obx(
                                      () => ListView(
                                        children: [
                                          ...?controller.quizProgress.value
                                              .userAnswerQuizzes
                                              ?.map((e) {
                                            return CustomCardQuestionsTaskCourse(
                                              index: controller.listQuiz
                                                  .indexOf(e),
                                              numberQuiz: (controller
                                                      .quizProgress
                                                      .value
                                                      .userAnswerQuizzes
                                                      ?.indexOf(e))! +
                                                  1,
                                              question: '-',
                                              answer: e.quizQuestion,
                                              quizAnswers: e,
                                            );
                                          }),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            20.horizontalSpace,
                            CustomContainer(
                              width: 344,
                              bgColor: secondBlueColor.withOpacity(0.10),
                              padding: EdgeInsets.all(10.r),
                              radius: 10,
                              widget: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextWigdet(
                                    title: 'Quiz Navigation',
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    textColor: whiteColor,
                                  ),
                                  10.verticalSpace,
                                  Obx(
                                    () => Wrap(
                                      spacing: 10.w,
                                      runSpacing: 10.h,
                                      children: [
                                        if (controller.quizProgress.value
                                                .randomQuestionIds !=
                                            null)
                                          ...controller.quizProgress.value
                                              .userAnswerQuizzes!
                                              .map((e) {
                                            return GestureDetector(
                                              onTap: () {
                                                // controller.navigateToQuestion(index);
                                              },
                                              child: CustomContainer(
                                                width: 56,
                                                height: 60,
                                                radius: 8,
                                                bgColor: e.isCorrect == true
                                                    ? Colors.green
                                                    : redColor,
                                                widget: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Center(
                                                      child: CustomTextWigdet(
                                                        title: (controller
                                                                    .quizProgress
                                                                    .value
                                                                    .userAnswerQuizzes!
                                                                    .indexOf(
                                                                        e) +
                                                                1)
                                                            .toString(),
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        textColor: whiteColor,
                                                      ),
                                                    ),
                                                    Padding(
                                                      padding:
                                                          EdgeInsets.all(2.r),
                                                      child: CustomContainer(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(1.5),
                                                        bgColor: greyColor
                                                            .withOpacity(0.3),
                                                        radius: 4,
                                                        widget: Center(
                                                          child: e.isCorrect ==
                                                                  true
                                                              ? SvgPicture
                                                                  .asset(
                                                                  'assets/icons/check_course.svg',
                                                                  width: 16.w,
                                                                  height: 16.h,
                                                                  color:
                                                                      whiteColor,
                                                                )
                                                              : SvgPicture
                                                                  .asset(
                                                                  'assets/icons/icon_X.svg',
                                                                  width: 20.w,
                                                                  height: 20.h,
                                                                  color:
                                                                      whiteColor,
                                                                ),
                                                        ),
                                                      ),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            );
                                          }),
                                      ],
                                    ),
                                  ),
                                  24.verticalSpace,
                                  CustomFilledButtonWidget(
                                    onPressed: () {
                                      Get.off(() => const OpenQuizCourseView());
                                    },
                                    title: 'Finish Review',
                                    fontSize: 24,
                                    fontWeight: FontWeight.w400,
                                    fontColor: whiteColor,
                                    isOutlined: true,
                                    outlineWidth: 0.7,
                                    borderColor: whiteColor,
                                    radius: 4,
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ))
          ],
        ),
      ),
    );
  }
}
