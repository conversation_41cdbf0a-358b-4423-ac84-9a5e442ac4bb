import 'package:carousel_slider/carousel_controller.dart';
import 'package:get/get.dart';

class ChooseScreenController extends GetxController {
  final List<String> imgList = [
    'assets/images/card_banner.png',
  ];
  final current = 0.obs;
  final CarouselSliderController carouselSliderController =
      CarouselSliderController();
  final count = 0.obs;
  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void increment() => count.value++;
}
