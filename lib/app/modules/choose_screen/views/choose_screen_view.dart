import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/choose_screen/views/choose_screen_landscape.dart';
import 'package:mides_skadik/app/modules/course/clases_course/bindings/clases_course_binding.dart';
import 'package:mides_skadik/app/modules/video_editor/views/video_editor_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import '../controllers/choose_screen_controller.dart';

class ChooseScreenView extends StatelessWidget {
  const ChooseScreenView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ChooseScreenController());

    final orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      return const ChooseScreenViewLandscape();
    }
    return CustomScaffold(
      background: "assets/images/bg-menu.png",
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.only(
              top: 100.h,
              left: 16.w,
              right: 16.w,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                20.verticalSpace,

                /// CarouselSlider
                CustomContainer(
                  width: double.infinity,
                  height: 950,
                  bgColor: blackColor.withValues(alpha: 0.6),
                  radius: 8,
                  widget: CarouselSlider(
                    items: controller.imgList
                        .map(
                          (item) => CustomContainer(
                            margin: EdgeInsets.all(5.r),
                            height: 500,
                            widget: ClipRRect(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5.r)),
                              child: Stack(
                                children: <Widget>[
                                  if (item.contains(
                                      'assets/images/card_banner.png'))
                                    Image.asset(item,
                                        fit: BoxFit.cover, width: 1.sw)
                                  else
                                    Image.network(item,
                                        fit: BoxFit.cover, width: 1.sw),
                                ],
                              ),
                            ),
                          ),
                        )
                        .toList(),
                    carouselController: controller.carouselSliderController,
                    options: CarouselOptions(
                      autoPlay: true,
                      enlargeCenterPage: true,
                      aspectRatio: 2.0,
                      viewportFraction: 0.9,
                      onPageChanged: (index, reason) {
                        controller.current.value = index;
                      },
                    ),
                  ),
                ),

                20.verticalSpace,

                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: controller.imgList.asMap().entries.map((entry) {
                    return GestureDetector(
                      onTap: () => controller.carouselSliderController
                          .animateToPage(entry.key),
                      child: Obx(() => CustomContainer(
                            width: 14,
                            height: 14,
                            margin: EdgeInsets.symmetric(horizontal: 4.w),
                            shape: BoxShape.circle,
                            bgColor: controller.current.value == entry.key
                                ? Colors.white.withOpacity(0.9)
                                : Colors.white.withOpacity(0.4),
                          )),
                    );
                  }).toList(),
                ),

                30.verticalSpace,

                /// Menu grid
                CustomContainer(
                  radius: 12,
                  padding: EdgeInsets.all(16.w),
                  bgColor: Colors.black.withOpacity(0.5),
                  widget: LayoutBuilder(
                    builder: (context, constraints) {
                      final isTablet = constraints.maxWidth > 600;
                      final crossAxisCount = isTablet ? 3 : 1;

                      final List<Map<String, dynamic>> menuItems = [
                        {
                          'title': 'Course',
                          'onTap': () => Get.to(
                              () => const BottomNavBar(choosenScreen: 'Course'),
                              binding: ClasesCourseBinding()),
                          'icon': 'assets/icons/course.png',
                        },
                        {
                          'title': 'Media Player',
                          'onTap': () => Get.to(() => const BottomNavBar(
                              choosenScreen: 'Media Player')),
                          'icon': 'assets/icons/media_player.png',
                        },
                        {
                          'title': 'Video Editing',
                          'onTap': () => Get.to(() => const VideoEditorView()),
                          'icon': 'assets/icons/editing.png',
                        },
                      ];

                      return GridView.builder(
                        itemCount: menuItems.length,
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: crossAxisCount,
                          crossAxisSpacing: 16.w,
                          mainAxisSpacing: 16.h,
                          childAspectRatio: isTablet ? 1.1 : 3,
                        ),
                        itemBuilder: (context, index) {
                          final item = menuItems[index];
                          return GestureDetector(
                            onTap: item['onTap'],
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  item['icon'],
                                  width: 200,
                                  height: 200,
                                ),
                                8.verticalSpace,
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
