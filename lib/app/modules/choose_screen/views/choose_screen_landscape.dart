import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/bindings/clases_course_binding.dart';
import 'package:mides_skadik/app/modules/video_editor/views/video_editor_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import '../controllers/choose_screen_controller.dart';

class ChooseScreenViewLandscape extends GetView<ChooseScreenController> {
  const ChooseScreenViewLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(ChooseScreenController());

    return CustomScaffold(
      background: "assets/images/bg-menu.png",
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 32.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                "assets/images/logo.png",
                width: 100.w,
                height: 100.h,
              ),
              10.verticalSpace,

              /// CarouselSlider
              CustomContainer(
                width: double.infinity,
                height: 900,
                bgColor: blackColor.withValues(alpha: 0.6),
                radius: 8,
                widget: CarouselSlider(
                  items: controller.imgList
                      .map(
                        (item) => CustomContainer(
                          margin: EdgeInsets.all(50.r),
                          height: 500,
                          widget: ClipRRect(
                            borderRadius:
                                BorderRadius.all(Radius.circular(5.r)),
                            child: Stack(
                              children: <Widget>[
                                if (item
                                    .contains('assets/images/card_banner.png'))
                                  Image.asset(item,
                                      fit: BoxFit.cover, width: 1.sw)
                                else
                                  Image.network(item,
                                      fit: BoxFit.cover, width: 1.sw),
                              ],
                            ),
                          ),
                        ),
                      )
                      .toList(),
                  carouselController: controller.carouselSliderController,
                  options: CarouselOptions(
                    autoPlay: true,
                    enlargeCenterPage: true,
                    aspectRatio: 2.0,
                    viewportFraction: 0.9,
                    onPageChanged: (index, reason) {
                      controller.current.value = index;
                    },
                  ),
                ),
              ),

              20.verticalSpace,

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: controller.imgList.asMap().entries.map((entry) {
                  return GestureDetector(
                    onTap: () => controller.carouselSliderController
                        .animateToPage(entry.key),
                    child: Obx(() => CustomContainer(
                          width: 14,
                          height: 14,
                          margin: EdgeInsets.symmetric(horizontal: 4.w),
                          shape: BoxShape.circle,
                          bgColor: controller.current.value == entry.key
                              ? Colors.white.withOpacity(0.9)
                              : Colors.white.withOpacity(0.4),
                        )),
                  );
                }).toList(),
              ),

              20.verticalSpace,

              /// Menu grid
              Expanded(
                child: CustomContainer(
                  radius: 12,
                  height: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  bgColor: Colors.black.withOpacity(0.5),
                  widget: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => Get.to(
                            () => const BottomNavBar(choosenScreen: 'Course'),
                            binding: ClasesCourseBinding(),
                          ),
                          child: Center(
                            child: Image.asset(
                              'assets/images/card_course_landscape.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => Get.to(() => const BottomNavBar(
                              choosenScreen: 'Media Player')),
                          child: Center(
                            child: Image.asset(
                              'assets/images/card_media_player_landscape.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: GestureDetector(
                          onTap: () => Get.to(() => const VideoEditorView()),
                          child: Center(
                            child: Image.asset(
                              'assets/images/card_editing_landscape.png',
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
