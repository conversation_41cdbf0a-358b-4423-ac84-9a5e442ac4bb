import 'package:get/get.dart';

import '../modules/choose_screen/bindings/choose_screen_binding.dart';
import '../modules/choose_screen/views/choose_screen_view.dart';
import '../modules/course/assesment_course/bindings/assesment_course_binding.dart';
import '../modules/course/assesment_course/views/assesment_course_view.dart';
import '../modules/course/attendance_course/bindings/attendance_course_binding.dart';
import '../modules/course/attendance_course/views/attendance_course_view.dart';
import '../modules/course/chat_course/bindings/chat_course_binding.dart';
import '../modules/course/chat_course/views/chat_bubbles_course_view_view.dart';
import '../modules/course/clases_course/bindings/clases_course_binding.dart';
import '../modules/course/clases_course/views/clases_course_view.dart';
import '../modules/course/dashboard_course/bindings/dashboard_course_binding.dart';
import '../modules/course/dashboard_course/views/dashboard_course_view.dart';
import '../modules/course/feedback_course/bindings/feedback_course_binding.dart';
import '../modules/course/feedback_course/views/feedback_course_view.dart';
import '../modules/course/grades_course/bindings/grades_course_binding.dart';
import '../modules/course/grades_course/views/grades_course_view.dart';
import '../modules/course/history_attendance_course/bindings/history_attendance_course_binding.dart';
import '../modules/course/history_attendance_course/views/history_attendance_course_view.dart';
import '../modules/course/notifcation_course/bindings/notifcation_course_binding.dart';
import '../modules/course/notifcation_course/views/notifcation_course_view.dart';
import '../modules/course/profile_course/bindings/profile_course_binding.dart';
import '../modules/course/profile_course/views/profile_course_view.dart';
import '../modules/course/quiz_review_course/bindings/quiz_review_course_binding.dart';
import '../modules/course/quiz_review_course/views/quiz_review_course_view.dart';
import '../modules/course/schedule_course/bindings/schedule_course_binding.dart';
import '../modules/course/schedule_course/views/schedule_course_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/media_player/bookmarks/bindings/bookmarks_binding.dart';
import '../modules/media_player/bookmarks/views/bookmarks_view.dart';
import '../modules/media_player/clipping/bindings/clipping_binding.dart';
import '../modules/media_player/clipping/views/clipping_view.dart';
import '../modules/media_player/collections/bindings/collections_binding.dart';
import '../modules/media_player/collections/views/collections_view.dart';
import '../modules/media_player/dashboard/bindings/dashboard_binding.dart';
import '../modules/media_player/dashboard/views/dashboard_view.dart';
import '../modules/media_player/for_you/bindings/for_you_binding.dart';
import '../modules/media_player/for_you/views/for_you_view.dart';
import '../modules/media_player/history/bindings/history_binding.dart';
import '../modules/media_player/history/views/history_view.dart';
import '../modules/media_player/home/<USER>/home_binding.dart';
import '../modules/media_player/home/<USER>/home_view.dart';
import '../modules/media_player/library/bindings/library_binding.dart';
import '../modules/media_player/library/views/library_view.dart';
import '../modules/media_player/my_video/bindings/my_video_binding.dart';
import '../modules/media_player/my_video/views/my_video_view.dart';
import '../modules/media_player/new_content/bindings/new_content_binding.dart';
import '../modules/media_player/new_content/views/new_content_view.dart';
import '../modules/media_player/notification/bindings/notification_binding.dart';
import '../modules/media_player/notification/views/notification_view.dart';
import '../modules/media_player/playlist/bindings/playlist_binding.dart';
import '../modules/media_player/playlist/views/playlist_view.dart';
import '../modules/media_player/playlists/bindings/playlists_binding.dart';
import '../modules/media_player/playlists/views/playlists_view.dart';
import '../modules/media_player/searchdata/bindings/searchdata_binding.dart';
import '../modules/media_player/searchdata/views/searchdata_view.dart';
import '../modules/media_player/upload_video/bindings/upload_video_binding.dart';
import '../modules/media_player/upload_video/views/upload_video_view.dart';
import '../modules/media_player/video_player/bindings/video_player_binding.dart';
import '../modules/media_player/video_player/views/video_player_view.dart';
import '../modules/splash_screen/bindings/splash_screen_binding.dart';
import '../modules/splash_screen/views/splash_screen_view.dart';
import '../modules/video_editor/bindings/video_editor_binding.dart';
import '../modules/video_editor/views/video_editor_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH_SCREEN;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => const HomeView(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.DASHBOARD,
      page: () => const DashboardView(),
      binding: DashboardBinding(),
    ),
    GetPage(
      name: _Paths.SEARCHDATA,
      page: () => const SearchdataView(),
      binding: SearchdataBinding(),
    ),
    GetPage(
      name: _Paths.LIBRARY,
      page: () => const LibraryView(),
      binding: LibraryBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFICATION,
      page: () => const NotificationView(),
      binding: NotificationBinding(),
    ),
    GetPage(
      name: _Paths.NEW_CONTENT,
      page: () => const NewContentView(),
      binding: NewContentBinding(),
    ),
    GetPage(
      name: _Paths.BOOKMARKS,
      page: () => const BookmarksView(),
      binding: BookmarksBinding(),
    ),
    GetPage(
      name: _Paths.CLIPPING,
      page: () => const ClippingView(),
      binding: ClippingBinding(),
    ),
    GetPage(
      name: _Paths.MY_VIDEO,
      page: () => const MyVideoView(),
      binding: MyVideoBinding(),
    ),
    GetPage(
      name: _Paths.HISTORY,
      page: () => const HistoryView(),
      binding: HistoryBinding(),
    ),
    GetPage(
      name: _Paths.PLAYLIST,
      page: () => const PlaylistView(),
      binding: PlaylistBinding(),
    ),
    GetPage(
      name: _Paths.VIDEO_PLAYER,
      page: () => VideoPlayerView(),
      binding: VideoPlayerBinding(),
    ),
    GetPage(
      name: _Paths.FOR_YOU,
      page: () => const ForYouView(),
      binding: ForYouBinding(),
    ),
    GetPage(
      name: _Paths.UPLOAD_VIDEO,
      page: () => const UploadVideoView(),
      binding: UploadVideoBinding(),
    ),
    GetPage(
      name: _Paths.COLLECTIONS,
      page: () => const CollectionsView(),
      binding: CollectionsBinding(),
    ),
    GetPage(
      name: _Paths.PLAYLISTS,
      page: () => const PlaylistsView(),
      binding: PlaylistsBinding(),
    ),
    GetPage(
      name: _Paths.DASHBOARD_COURSE,
      page: () => const DashboardCourseView(),
      binding: DashboardCourseBinding(),
    ),
    GetPage(
      name: _Paths.NOTIFCATION_COURSE,
      page: () => const NotifcationCourseView(),
      binding: NotifcationCourseBinding(),
    ),
    GetPage(
      name: _Paths.CHOOSE_SCREEN,
      page: () => const ChooseScreenView(),
      binding: ChooseScreenBinding(),
    ),
    GetPage(
      name: _Paths.CLASES_COURSE,
      page: () => const ClasesCourseView(),
      binding: ClasesCourseBinding(),
    ),
    GetPage(
      name: _Paths.SCHEDULE_COURSE,
      page: () => const ScheduleCourseView(),
      binding: ScheduleCourseBinding(),
    ),
    GetPage(
      name: '/attendance-course',
      page: () => const AttendanceCourseView(),
      binding: AttendanceCourseBinding(),
    ),
    GetPage(
      name: _Paths.ASSESMENT_COURSE,
      page: () => const AssesmentCourseView(),
      binding: AssesmentCourseBinding(),
    ),
    GetPage(
      name: _Paths.GRADES_COURSE,
      page: () => const GradesCourseView(),
      binding: GradesCourseBinding(),
    ),
    GetPage(
      name: _Paths.HISTORY_ATTENDANCE_COURSE,
      page: () => const HistoryAttendanceCourseView(),
      binding: HistoryAttendanceCourseBinding(),
    ),
    GetPage(
      name: _Paths.QUIZ_REVIEW_COURSE,
      page: () => const QuizReviewCourseView(),
      binding: QuizReviewCourseBinding(),
    ),
    GetPage(
      name: _Paths.SPLASH_SCREEN,
      page: () => SplashScreenView(),
      binding: SplashScreenBinding(),
    ),
    GetPage(
      name: _Paths.CHAT_COURSE,
      page: () => const ChatBubblesCourseViewView(),
      binding: ChatCourseBinding(),
    ),
    GetPage(
      name: _Paths.PROFILE_COURSE,
      page: () => const ProfileCourseView(),
      binding: ProfileCourseBinding(),
    ),
    GetPage(
      name: _Paths.FEEDBACK_COURSE,
      page: () => const FeedbackCourseView(),
      binding: FeedbackCourseBinding(),
    ),
    GetPage(
      name: _Paths.VIDEO_EDITOR,
      page: () => const VideoEditorView(),
      binding: VideoEditorBinding(),
    ),
  ];
}
