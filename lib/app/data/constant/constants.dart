class AppConstants {
  // Colors - Professional Video Editor Theme
  static const primaryColor = 0xFF2196F3;
  static const secondaryColor = 0xFF37474F;
  static const backgroundColor = 0xFF0D1117; // Darker background
  static const surfaceColor = 0xFF1C2128; // Slightly lighter surface
  static const accentColor = 0xFF58A6FF; // Blue accent

  // Timeline specific colors
  static const timelineBackground = 0xFF161B22;
  static const trackBackground = 0xFF21262D;
  static const playheadColor = 0xFFFF6B6B;

  // Media type colors
  static const videoTrackColor = 0xFF4FC3F7; // Light blue
  static const audioTrackColor = 0xFF81C784; // Light green
  static const imageTrackColor = 0xFFFFB74D; // Orange
  static const textTrackColor = 0xFFBA68C8; // Purple

  // Dimensions
  static const double timelineHeight =
      230.0; // Increased for zoom controls + timeline
  static const double toolbarHeight = 80.0; // Increased for better controls
  static const double previewAspectRatio = 16 / 9;
  static const double trackHeight = 40.0; // Height of each track
  static const double timeRulerHeight = 30.0; // Height of time ruler
  static const double trackLabelWidth = 100.0; // Width of track labels

  // Video settings
  static const int maxVideoDurationSeconds = 300; // 5 minutes
  static const int minVideoDurationSeconds = 1;
  static const double maxVideoSizeMB = 100.0;

  // Text overlay settings
  static const double defaultTextSize = 24.0;
  static const double minTextSize = 12.0;
  static const double maxTextSize = 72.0;

  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // File extensions
  static const List<String> supportedVideoFormats = [
    'mp4',
    'mov',
    'avi',
    'mkv',
    'wmv',
    '3gp'
  ];

  static const List<String> supportedAudioFormats = [
    'mp3',
    'wav',
    'aac',
    'm4a',
    'ogg'
  ];

  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp'
  ];
}
