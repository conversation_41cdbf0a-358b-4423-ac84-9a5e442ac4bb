import 'package:intl/intl.dart';

class DateFormatUtil {
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final isToday = dateTime.year == now.year && dateTime.month == now.month && dateTime.day == now.day;

    if (isToday) {
      return 'Today';
    }

    return DateFormat('dd MMMM yyyy').format(dateTime);
  }

  static String formatDateTimeFromString(String dateTime) {
    DateTime date = DateTime.parse(dateTime);
    return formatDateTime(date);
  }
}
