import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class BottomSheetUtil {
  static void showBottomSheet({
    required List<BottomSheetItem> items,
  }) {
    Get.bottomSheet(
      CustomContainer(
        bgColor: Color(0xFF1f2834),
        borderRadius: BorderRadius.circular(12),
        margin: EdgeInsets.fromLTRB(0, 0, 0, Get.bottomBarHeight),
        widget: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomContainer(
              bgColor: Color(0xFF1a222d),
              padding: EdgeInsets.fromLTRB(0, 20, 0, 20),
              widget: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomContainer(
                    width: 100,
                    height: 8.h,
                    bgColor: whiteColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ],
              ),
            ),
            ...items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  item,
                  if (index < items.length - 1)
                    CustomContainer(
                      bgColor: Color(0xFF1a222d),
                      height: 8.h,
                      width: Get.width,
                    ),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}

class BottomSheetItem extends StatelessWidget {
  const BottomSheetItem({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.iconColor,
    this.textColor,
  });

  final String icon;
  final String title;
  final VoidCallback onTap;

  final Color? iconColor;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: SvgPicture.asset(
        icon,
        color: iconColor ?? whiteColor,
        width: 50.w,
        height: 50.h,
        fit: BoxFit.contain,
      ),
      title: CustomTextWigdet(
        title: title,
        fontSize: 32.sp,
        textColor: textColor ?? whiteColor,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }
}
