import 'dart:io';
import 'dart:typed_data';
import 'package:file_picker/file_picker.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class FileDownloadUtil {
  static Future<Uint8List?> readBytes(String url) async {
    try {
      // Send HTTP GET request
      final token = await LocalStorage().get('token');
      final headers = {'Authorization': 'Bearer $token'};
      var response = await http.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        // Get storage directory
        Directory? dir;
        if (Platform.isAndroid) {
          dir = await getExternalStorageDirectory();
        } else if (Platform.isIOS) {
          dir = await getApplicationDocumentsDirectory();
        }

        if (dir == null) return null;

        return response.bodyBytes;
      } else {
        print('Failed to download. Status: ${response.statusCode}');
      }
    } catch (e) {
      print('Download error: $e');
    }
  }

  static Future<void> downloadFile(
    String url, {
    String? fileName,
  }) async {
    try {
      var bytes = await readBytes(url);

      final savePath = await FilePicker.platform.saveFile(
        dialogTitle: 'Save file as...',
        fileName: fileName,
        bytes: bytes,
      );

      if (savePath == null) {
        print('User cancelled save');
        return;
      }

      LogService.log.d("savePath: $savePath");

      SnackbarUtil.showOnce(title: "Saved", message: "File saved");

      return;
    } catch (e) {
      print('Error downloading with SAF: $e');

      SnackbarUtil.showOnce(
          title: "Failed", message: "Failed to download file");
    }
  }
}
