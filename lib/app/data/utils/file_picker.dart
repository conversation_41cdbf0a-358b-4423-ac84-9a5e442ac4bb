import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/choose_gallery_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/choose_gallery_view.dart';

class FilePickerUtil {
  static Future<String?> pickVideo() async {
    String? path = await Get.to(const ChooseGalleryView(), binding: ChooseGalleryBinding(), arguments: {
      'ext': ['mp4']
    });

    return path;
  }

  static Future<String?> pickImage() async {
    String? path = await Get.to(const ChooseGalleryView(), binding: ChooseGalleryBinding(), arguments: {
      'ext': ['jpg', 'png', 'jpeg', 'webp']
    });

    return path;
  }
}
