import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class OrientationController extends GetxController {
  var isLandscape = false.obs;

  @override
  void onInit() {
    super.onInit();
    _setOrientation();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);

    SystemChannels.lifecycle.setMessageHandler((msg) {
      _setOrientation();
      return Future.value('');
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final orientation = MediaQuery.of(Get.context!).orientation;
      _setOrientationMediaQuery(orientation);
    });
  }

  void _setOrientation() {
    if (Get.context != null) {
      final size = MediaQuery.of(Get.context!).size;
      isLandscape.value = size.width > size.height;
    }
  }

  void _setOrientationMediaQuery(Orientation orientation) {
    isLandscape.value = orientation == Orientation.landscape;
  }

  void updateOrientation(Orientation orientation) {
    _setOrientationMediaQuery(orientation);
  }
}
