import 'package:get/get.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter/services.dart';

class SocketService {
  IO.Socket? socket;
  var message = ''.obs;
  var countAttention = 0.obs;
  var isConnected = false.obs;
  void connect({required String roomId, required String ujianId}) {
    LogService.log.i('Connecting to socket server...');
    try {
      socket = IO.io(baseSocketUrl, <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
        'forceNew': true,
        'timeout': 20000,
        'reconnection': true,
        'reconnectionAttempts': 5,
        'reconnectionDelay': 1000,
        'maxReconnectionDelay': 5000,
        'randomizationFactor': 0.5,
      });

      socket!.connect();
      isConnected.value = true;

      socket!.onConnect((_) {
        LogService.log.i('Connected to socket server');
        joinRoom(roomId, ujianId);
      });

      socket!.onDisconnect((_) {
        LogService.log.i('Disconnected from socket server');
        isConnected.value = false;
      });

      socket!.onConnectError((error) {
        LogService.log.e('Socket connection error: $error');
        isConnected.value = false;
      });

      socket!.onError((error) {
        LogService.log.e('Socket error: $error');
      });

      socket!.onReconnect((attempt) {
        LogService.log.i('Socket reconnected on attempt: $attempt');
        isConnected.value = true;
      });

      socket!.onReconnectError((error) {
        LogService.log.e('Socket reconnection error: $error');
      });

      socket!.on('receive-command', (data) {
        LogService.log.i('Received message: ${data['command']}');
        switch (data['command']) {
          case 'restart':
            LogService.log.i('Restart command received');
            setReceivedMessage('restart');
            const platform = MethodChannel('com.example.example/screen_share');
            platform.invokeMethod('restartService');
            break;
          case 'shutDown':
            LogService.log.i('Shut down command received');
            setReceivedMessage('shutDown');
            const platform = MethodChannel('com.example.example/screen_share');
            platform.invokeMethod('shutdownService');
            break;
          case 'terminate':
            LogService.log.i('Terminate command received');
            setReceivedMessage('terminate');
            break;
          case 'attention':
            LogService.log.i('Attention command received');
            setReceivedMessage('attention');
            break;
          default:
            LogService.log.i('Unknown command received');
            setReceivedMessage('Unknown command');
            break;
        }
      });
    } catch (e) {
      LogService.log.e('Error connecting to socket: $e');
      isConnected.value = false;
    }
  }

  // join a room
  void joinRoom(String roomId, String ujianId) {
    if (socket != null && isConnected.value) {
      socket!.emit('join', roomId);
      socket!.emit('join', ujianId);
      LogService.log.i('Joined room: $roomId');
    } else {
      LogService.log.e('Cannot join room: Socket not connected');
    }
  }

  // Send a message to the server
  void sendMessage(String message) {
    if (socket != null && isConnected.value) {
      socket!.emit('message', message);
    } else {
      LogService.log.e('Cannot send message: Socket not connected');
    }
  }

  // Disconnect from the server
  void disconnect() {
    if (socket != null) {
      LogService.log.i('Disconnecting from socket server...');
      socket!.disconnect();
      isConnected.value = false;
    }
  }

  void setReceivedMessage(String message) {
    LogService.log.i('Received message Set: $message');
    this.message.value = message;
    if (message == 'attention') {
      countAttention.value++;
    }
  }
}

// Enum for message types
enum SocketMessageType {
  restart,
  shutDown,
  terminate,
  attention,
}
