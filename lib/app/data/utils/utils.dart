import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/clipping_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_filter.dart';
import 'package:path/path.dart' as p;
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/material.dart';

Future<http.Response> functionPost(
    {required String url, required Object object, dynamic token = ''}) async {
  final response = await http
      .post(
    Uri.parse(url),
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
      'Authorization': '$token',
    },
    body: j<PERSON><PERSON><PERSON><PERSON>(object),
  )
      .timeout(
    const Duration(seconds: 30),
    onTimeout: () {
      throw Exception('Request timeout after 30 seconds');
    },
  );
  return response;
}

Future<http.StreamedResponse> functionPostMultiPart({
  required String url,
  dynamic token = '',
  required String filePath,
  required String fileFieldName,
  Map<String, String>? fields,
}) async {
  final file = File(filePath);

  if (!await file.exists()) {
    LogService.log.e('❌ File tidak ditemukan: $filePath');
    throw Exception('File tidak ditemukan');
  }

  final fileLength = await file.length();
  if (fileLength > 20 * 1024 * 1024) {
    throw Exception('Ukuran file melebihi batas 20MB');
  }

  final fileBytes = await file.readAsBytes();
  final mimeType = lookupMimeType(filePath) ?? 'application/octet-stream';
  final fileName = p.basename(filePath).replaceAll(RegExp(r'[^\w\.\-]'), '_');

  LogService.log.i('File Debug Info:');
  LogService.log.i('Nama File: $fileName');
  LogService.log.i('📏 Ukuran File: $fileLength bytes');
  LogService.log.i('🧪 MIME Type: $mimeType');
  LogService.log.i('🧷 Path: $filePath');

  final uri = Uri.parse(url);
  final request = http.MultipartRequest('POST', uri);

  if (token != null && token.toString().isNotEmpty) {
    request.headers['Authorization'] = token;
  }

  request.files.add(
    http.MultipartFile.fromBytes(
      fileFieldName,
      fileBytes,
      filename: fileName,
      contentType: MediaType.parse(mimeType),
    ),
  );

  if (fields != null) {
    request.fields.addAll(fields);
    LogService.log.i('📦 Fields ditambahkan ke form: $fields');
  }

  LogService.log.i(
      "🧩 File Sent: field='$fileFieldName', filename='$fileName', type='$mimeType'");
  return await request.send();
}

Future<http.Response> functionGet(String url, dynamic token) async {
  try {
    final response = await http.get(Uri.parse(url), headers: {
      HttpHeaders.authorizationHeader: '$token',
    }).timeout(
      const Duration(seconds: 60),
      onTimeout: () {
        throw Exception('Request timeout after 60 seconds');
      },
    );
    return response;
  } catch (e) {
    print('Error occurred: $e');
    rethrow;
  }
}

Future<http.Response> functionDelete(
  String url,
  dynamic token, {
  Object? object,
}) async {
  final response = await http.delete(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: '$token',
    },
    body: jsonEncode(object),
  );

  LogService.log.i('DELETE URL: $url');
  LogService.log.i('DELETE Object: $object');
  LogService.log.i('DELETE Response: ${response.body}');

  return response;
}

String getFormattedDate(DateTime? date) {
  if (date == null) return 'Monday, April 1, 2024 · 23:00';
  return DateFormat('EEEE, d MMMM yyyy – HH:mm', 'id_ID').format(date);
}

String getTimeDifference(DateTime startDate) {
  // Ambil waktu saat ini
  DateTime now = DateTime.now();

  // Hitung selisih antara startDate dan waktu sekarang
  Duration difference = startDate.difference(now);

  // Ambil jam dan menit dari selisih waktu
  int hours = difference.inHours;
  int minutes = difference.inMinutes % 60;

  // Menyusun tampilan sesuai dengan apakah jam dan menit bernilai 0 atau tidak
  String timeLeft = '';

  // Jika jam lebih dari 0, tampilkan jam
  if (hours > 0) {
    timeLeft += '$hours jam';
  }

  // Jika menit lebih dari 0, tampilkan menit
  if (minutes > 0) {
    if (timeLeft.isNotEmpty) {
      timeLeft += ' ';
    }
    timeLeft += '$minutes menit';
  }

  // Jika kedua-duanya 0, tampilkan 'Sudah selesai' atau bisa diubah sesuai keinginan
  if (timeLeft.isEmpty) {
    timeLeft = 'Sudah selesai';
  }

  return timeLeft;
}

String formatRemainingTime(int seconds) {
  final hours = (seconds ~/ 3600).toString().padLeft(2, '0');
  final minutes = ((seconds % 3600) ~/ 60).toString().padLeft(2, '0');
  final secs = (seconds % 60).toString().padLeft(2, '0');
  return "$hours : $minutes : $secs";
}

String timeRange(DateTime timeNow, DateTime time) {
  final range = timeNow.difference(time);

  if (range.inHours > 0 && range.inHours < 24) {
    return "${range.inHours} jam yang lalu";
  }

  if (range.inHours >= 24 && range.inDays < 30) {
    return "${range.inDays} hari yang lalu";
  }

  if (range.inDays > 30) {
    return DateFormat('dd-MMMM-yyyy').format(time);
  }
  return "Hari ini";
}

String formatDuration(String? duration) {
  final split = duration?.split(':');

  if (split?.length != 3) return duration ?? '00:00';

  final hours = int.parse(split?[0] ?? '0');
  final minute = split?[1];
  final second = split?[2];

  return hours == 0 ? '$minute:$second' : '$hours:$minute:$second';
}

String formatTimeOnly(DateTime time, {bool? withSeconds = false}) {
  if (withSeconds == true) return DateFormat('HH:mm:ss').format(time);
  return DateFormat('HH:mm').format(time);
}

String formattedDateOnly(DateTime? date,
    {String? format = 'dd MMMM yyyy', bool? isCapitalize = false}) {
  if (date == null) return '1 Januari 1970';
  var dt = DateFormat(format, 'id_ID').format(date);
  return isCapitalize ?? false ? dt.toUpperCase() : dt;
}

String getDaysInDate(DateTime? date) {
  if (date == null) return 'Monday';
  return DateFormat.EEEE().format(date);
}

String timeRangeChat(DateTime now, DateTime time) {
  final range = now.difference(time);
  if (range.inDays == 1) {
    return 'Yesterday';
  }

  if (range.inDays > 1) {
    return formattedDateOnly(time, format: 'dd/MM/yyyy');
  }

  return formatTimeOnly(time);
}

int convertDurationToSeconds(String duration) {
  List<String> parts = duration.split(':');
  int hours = int.parse(parts[0]);
  int mnt = int.parse(parts[1]);
  int sec = int.parse(parts[2]);

  return Duration(hours: hours, minutes: mnt, seconds: sec).inSeconds;
}

Duration stringToDuration(String duration) {
  List<String> parts = duration.split(':');
  int hours = int.parse(parts[0]);
  int mnt = int.parse(parts[1]);
  int sec = int.parse(parts[2]);

  return Duration(hours: hours, minutes: mnt, seconds: sec);
}

bool isSameDay(DateTime a, DateTime b) {
  return a.year == b.year && a.month == b.month && a.day == b.day;
}

String getWeekName(int week) {
  const days = [
    'Senin',
    'Selasa',
    'Rabu',
    'Kamis',
    'Jum\'at',
    'Sabtu',
    'Minggu'
  ];
  return days[week - 1];
}

Future<void> downloadVideo(String url, String id) async {
  try {
    final dio = Dio();
    final directory = await getDownloadsDirectory();
    if (directory == null) {
      LogService.log.e('❌ Could not get downloads directory');
      throw Exception('Could not get downloads directory');
    }

    // Generate a unique filename based on the URL's hash to ensure consistency
    final String uniqueFileName = 'video_content_${url.hashCode}.mp4';
    final String targetSavePath = p.join(directory.path, uniqueFileName);

    LogService.log
        .i('Checking for existing file: $targetSavePath for URL: $url');

    // Check if the file already exists
    if (await File(targetSavePath).exists()) {
      LogService.log.i('Video already downloaded: $targetSavePath');
      // Show a message that the video is already downloaded and offer to open it
      showModalBottomSheet(
        context: Get.context!,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: whiteColor, // Ensure whiteColor is defined in your scope
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(16.0)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Informasi Unduhan',
                  style: Theme.of(Get.context!).textTheme.titleLarge,
                ),
                const SizedBox(height: 16.0),
                Text(
                    'Video ini sudah diunduh sebelumnya.\nNama file: $uniqueFileName'),
                const SizedBox(height: 24.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomButtonFilter(
                        title: 'Tutup',
                        isSelected: false,
                        bgColor: Colors.red,
                        onPressed: () {
                          Get.back();
                        }),
                    const SizedBox(width: 16.0),
                    CustomButtonFilter(
                        title: 'Buka',
                        isSelected: true,
                        textColor: whiteColor,
                        bgColor: secondBlueColor,
                        onPressed: () async {
                          Get.back();
                          Get.to(ClippingView(
                            file: File(targetSavePath),
                            vodId: id,
                          ));
                        })
                  ],
                ),
              ],
            ),
          );
        },
      );
      return; // Exit because the file is already downloaded
    }

    // If the file does not exist, proceed with the download
    LogService.log.i('Downloading video from: $url to $targetSavePath');
    var progress = 0.obs;

    showModalBottomSheet(
      context: Get.context!,
      backgroundColor: Colors.transparent,
      isDismissible: false, // Prevent dismissing while downloading
      enableDrag: false, // Prevent dragging while downloading
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: whiteColor, // Ensure whiteColor is defined
            borderRadius:
                const BorderRadius.vertical(top: Radius.circular(16.0)),
          ),
          child: Obx(
            () => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  progress.value == 100
                      ? 'Unduhan Selesai Siap Untuk Di Clip'
                      : 'Mengunduh Video Untuk Di Clip',
                  style: Theme.of(Get.context!).textTheme.titleLarge,
                ),
                const SizedBox(height: 16.0),
                Text(progress.value == 100
                    ? 'Video berhasil diunduh: $uniqueFileName'
                    : 'Progres: ${progress.value}%'),
                const SizedBox(height: 4.0),
                if (progress.value < 100)
                  LinearProgressIndicator(
                    value: progress.value / 100.0,
                    minHeight: 10,
                    backgroundColor: Colors.grey[300],
                    valueColor:
                        const AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                const SizedBox(height: 24.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CustomButtonFilter(
                        title: progress < 100 ? 'Batalkan' : 'Tutup',
                        isSelected: false,
                        bgColor: Colors.red,
                        onPressed: () {
                          if (progress.value < 100) {
                            dio.close(); // Cancel the download
                            try {
                              File(targetSavePath).deleteSync();
                              LogService.log
                                  .i('🗑️ File deleted: $targetSavePath');
                            } catch (e) {
                              LogService.log.e('❌ Error deleting file: $e');
                            }
                            Get.back(); // Close the progress sheet
                          } else {
                            Get.back(); // Close the progress sheet
                          }
                        }),
                    if (progress.value == 100) ...[
                      const SizedBox(width: 16.0),
                      CustomButtonFilter(
                          title: 'Buka',
                          isSelected: true,
                          textColor: whiteColor,
                          bgColor: secondBlueColor,
                          onPressed: () async {
                            Get.back();
                            Get.to(ClippingView(
                              file: File(targetSavePath),
                              vodId: id,
                            ));
                          })
                    ]
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    await dio.download(
      url,
      targetSavePath, // Use the new URL-derived save path
      options: Options(
        receiveTimeout: const Duration(minutes: 5),
      ),
      onReceiveProgress: (received, total) {
        if (total > 0) {
          int currentProgress = (received / total * 100).toInt();
          progress.value = currentProgress;
          // LogService.log.i('Download progress: $currentProgress% ($received/$total bytes)');
        } else {
          // Total size unknown, cannot show percentage progress accurately
          // LogService.log.i('Download progress: $received bytes received (total size unknown)');
        }
      },
    );

    // After dio.download completes without error, the download is successful.
    // Ensure UI reflects 100% completion.
    if (await File(targetSavePath).exists()) {
      // Double check file exists
      if (progress.value < 100) {
        progress.value = 100; // Ensure UI shows 100%
      }
      LogService.log.i('Video downloaded successfully: $targetSavePath');
    } else {
      // This case should ideally not happen if dio.download succeeded without error.
      LogService.log.e(
          '❌ Download reported success, but file not found at $targetSavePath');
      if (Get.isBottomSheetOpen ?? false) Get.back(); // Close sheet
      Get.snackbar("Error", "Terjadi kesalahan pasca unduhan.",
          snackPosition: SnackPosition.BOTTOM);
    }
  } catch (e, st) {
    LogService.log.e('❌ Failed to download video: $e\n$st');
    if (Get.isBottomSheetOpen ?? false) {
      Get.back(); // Close progress sheet if it's open due to an error
    }
    Get.snackbar(
      "Error",
      "Gagal mengunduh video. Silakan coba lagi.",
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }
}

Future<http.Response> functionPut(
    {required String url, required Object object, dynamic token = ''}) async {
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
      'Authorization': '$token',
    },
    body: jsonEncode(object),
  );
  return response;
}

const List<String> nouns = [
  'time',
  'year',
  'people',
  'way',
  'day',
  'man',
  'thing',
  'woman',
  'life',
  'child',
  'world',
  'school',
  'state',
  'family',
  'student',
  'group',
  'country',
  'problem',
  'hand',
  'part',
  'place',
  'case',
  'week',
  'company',
  'system',
  'program',
  'question',
  'work',
  'government',
  'number',
  'night',
  'point',
  'home',
  'water',
  'room',
  'mother',
  'area',
  'money',
  'story',
  'fact',
  'month',
  'lot',
  'right',
  'study',
  'book',
  'eye',
  'job',
  'word',
  'business',
  'issue',
  'side',
  'kind',
  'head',
  'house',
  'service',
  'friend',
  'father',
  'power',
  'hour',
  'game',
  'line',
  'end',
  'member',
  'law',
  'car',
  'city',
  'community',
  'name',
  'president',
  'team',
  'minute',
  'idea',
  'kid',
  'body',
  'information',
  'back',
  'parent',
  'face',
  'remote_stream',
  'level',
  'office',
  'door',
  'health',
  'person',
  'art',
  'war',
  'history',
  'party',
  'result',
  'change',
  'morning',
  'reason',
  'research',
  'girl',
  'guy',
  'food',
  'moment',
  'air',
  'teacher',
  'force',
  'education',
  'foot',
  'boy',
  'age',
  'policy',
  'process',
  'music',
  'market',
  'sense',
  'nation',
  'plan',
  'college',
  'interest',
  'death',
  'experience',
  'effect',
  'use',
  'class',
  'control',
  'care',
  'field',
  'development',
  'role',
  'effort',
  'rate',
  'heart',
  'drug',
  'show',
  'leader',
  'light',
  'voice',
  'wife',
  'police',
  'mind',
  'price',
  'report',
  'decision',
  'son',
  'view',
  'relationship',
  'town',
  'road',
  'arm',
  'difference',
  'value',
  'building',
  'action',
  'model',
  'season',
  'society',
  'tax',
  'director',
  'position',
  'player',
  'record',
  'paper',
  'space',
  'ground',
  'form',
  'event',
  'official',
  'matter',
  'center',
  'couple',
  'site',
  'project',
  'activity',
  'star',
  'table',
  'need',
  'court',
  'American',
  'oil',
  'situation',
  'cost',
  'industry',
  'figure',
  'street',
  'image',
  'phone',
  'data',
  'picture',
  'practice',
  'piece',
  'land',
  'product',
  'doctor',
  'wall',
  'patient',
  'worker',
  'news',
  'test',
  'movie',
  'north',
  'love',
  'support',
  'technology',
  'step',
  'baby',
  'computer',
  'type',
  'attention',
  'film',
  'Republican',
  'tree',
  'source',
  'organization',
  'hair',
  'look',
  'century',
  'evidence',
  'window',
  'culture',
  'chance',
  'brother',
  'energy',
  'period',
  'course',
  'summer',
  'plant',
  'opportunity',
  'term',
  'letter',
  'condition',
  'choice',
  'rule',
  'daughter',
  'administration',
  'south',
  'husband',
  'Congress',
  'floor',
  'campaign',
  'material',
  'population',
  'call',
  'economy',
  'hospital',
  'church',
  'risk',
  'fire',
  'future',
  'defense',
  'security',
  'bank',
  'west',
  'sport',
  'board',
  'subject',
  'officer',
  'rest',
  'behavior',
  'performance',
  'top',
  'goal',
  'second',
  'bed',
  'order',
  'author',
  'blood',
  'agency',
  'nature',
  'color',
  'store',
  'sound',
  'movement',
  'page',
  'race',
  'concern',
  'series',
  'language',
  'response',
  'animal',
  'factor',
  'decade',
  'article',
  'east',
  'artist',
  'scene',
  'stock',
  'career',
  'treatment',
  'approach',
  'size',
  'dog',
  'fund',
  'media',
  'sign',
  'thought',
  'list',
  'individual',
  'quality',
  'pressure',
  'answer',
  'resource',
  'meeting',
  'disease',
  'success',
  'cup',
  'amount',
  'ability',
  'staff',
  'character',
  'growth',
  'loss',
  'degree',
  'attack',
  'region',
  'television',
  'box',
  'TV',
  'training',
  'trade',
  'deal',
  'election',
  'feeling',
  'standard',
  'bill',
  'message',
  'analysis',
  'benefit',
  'sex',
  'lawyer',
  'section',
  'glass',
  'skill',
  'sister',
  'professor',
  'operation',
  'crime',
  'stage',
  'authority',
  'design',
  'sort',
  'one',
  'knowledge',
  'gun',
  'station',
  'strategy',
  'truth',
  'song',
  'example',
  'environment',
  'leg',
  'public',
  'executive',
  'set',
  'rock',
  'note',
  'manager',
  'help',
  'network',
  'science',
  'memory',
  'card',
  'seat',
  'cell',
  'trial',
  'expert',
  'spring',
  'firm',
  'Democrat',
  'radio',
  'management',
  'ball',
  'talk',
  'theory',
  'impact',
  'statement',
  'charge',
  'direction',
  'weapon',
  'employee',
  'peace',
  'base',
  'pain',
  'play',
  'measure',
  'interview',
  'chair',
  'fish',
  'camera',
  'structure',
  'politics',
  'bit',
  'weight',
  'candidate',
  'production',
  'trip',
  'evening',
  'conference',
  'unit',
  'style',
  'adult',
  'range',
  'past',
  'edge',
  'writer',
  'trouble',
  'challenge',
  'fear',
  'shoulder',
  'institution',
  'sea',
  'dream',
  'bar',
  'property',
  'stuff',
  'detail',
  'method',
  'magazine',
  'hotel',
  'soldier',
  'cause',
  'bag',
  'heat',
  'fall',
  'marriage',
  'surface',
  'purpose',
  'pattern',
  'skin',
  'agent',
  'owner',
  'machine',
  'gas',
  'generation',
  'cancer',
  'item',
  'reality',
  'coach',
  'Mrs',
  'yard',
  'violence',
  'investment',
  'discussion',
  'finger',
  'garden',
  'collection',
  'task',
  'partner',
  'kitchen',
  'consumer',
  'shot',
  'budget',
  'painting',
  'scientist',
  'agreement',
  'capital',
  'mouth',
  'victim',
  'newspaper',
  'threat',
  'responsibility',
  'attorney',
  'score',
  'account',
  'break',
  'audience',
  'dinner',
  'vote',
  'debate',
  'citizen',
  'majority',
  'wind',
  'mission',
  'customer',
  'speech',
  'option',
  'participant',
  'forest',
  'video',
  'Senate',
  'reform',
  'access',
  'restaurant',
  'judge',
  'relation',
  'bird',
  'opinion',
  'credit',
  'corner',
  'version',
  'safety',
  'neighborhood',
  'act',
  'troop',
  'income',
  'species',
  'track',
  'hope',
  'sky',
  'freedom',
  'plane',
  'object',
  'attitude',
  'labor',
  'concept',
  'client',
  'conversation',
  'variety',
  'turn',
  'investigation',
  'researcher',
  'press',
  'conflict',
  'spirit',
  'argument',
  'camp',
  'brain',
  'feature',
  'afternoon',
  'weekend',
  'possibility',
  'insurance',
  'department',
  'battle',
  'beginning',
  'date',
  'crisis',
  'fan',
  'hole',
  'element',
  'vision',
  'status',
  'ship',
  'solution',
  'stone',
  'scale',
  'university',
  'driver',
  'attempt',
  'park',
  'spot',
  'lack',
  'ice',
  'boat',
  'sun',
  'distance',
  'wood',
  'truck',
  'return',
  'mountain',
  'survey',
  'tradition',
  'winter',
  'village',
  'sales',
  'communication',
  'run',
  'screen',
  'resident',
  'gold',
  'club',
  'farm',
  'increase',
  'middle',
  'presence',
  'district',
  'shape',
  'reader',
  'contract',
  'crowd',
  'apartment',
  'strength',
  'band',
  'horse',
  'target',
  'prison',
  'guard',
  'demand',
  'reporter',
  'text',
  'share',
  'tool',
  'vehicle',
  'flight',
  'facility',
  'understanding',
  'advantage',
  'leadership',
  'pound',
  'basis',
  'guest',
  'sample',
  'block',
  'protection',
  'while',
  'identity',
  'title',
  'lesson',
  'faith',
  'river',
  'living',
  'technique',
  'path',
  'ear',
  'shop',
  'folk',
  'principle',
  'border',
  'competition',
  'claim',
  'equipment',
  'critic',
  'aspect',
  'failure',
  'Christmas',
  'comment',
  'affair',
  'procedure',
  'chairman',
  'baseball',
  'egg',
  'belief',
  'murder',
  'gift',
  'religion',
  'review',
  'editor',
  'coffee',
  'document',
  'speed',
  'influence',
  'youth',
  'wave',
  'move',
  'quarter',
  'background',
  'reaction',
  'suit',
  'perspective',
  'construction',
  'intelligence',
  'connection',
  'shoe',
  'grade',
  'context',
  'committee',
  'mistake',
  'focus',
  'smile',
  'location',
  'clothes',
  'neighbor',
  'drive',
  'function',
  'bone',
  'average',
  'wine',
  'voter',
  'mean',
  'learning',
  'bus',
  'hell',
  'category',
  'victory',
  'key',
  'visit',
  'Internet',
  'medicine',
  'tour',
  'photo',
  'finding',
  'classroom',
  'contact',
  'justice',
  'pair',
  'exercise',
  'knee',
  'flower',
  'tape',
  'supply',
  'cut',
  'will',
  'actor',
  'birth',
  'search',
  'democracy',
  'circle',
  'device',
  'progress',
  'front',
  'bottom',
  'island',
  'exchange',
  'studio',
  'lady',
  'colleague',
  'application',
  'neck',
  'damage',
  'plastic',
  'plate',
  'writing',
  'start',
  'expression',
  'football',
  'chicken',
  'army',
  'abuse',
  'theater',
  'map',
  'session',
  'danger',
  'literature',
  'rain',
  'desire',
  'assessment',
  'injury',
  'respect',
  'fuel',
  'leaf',
  'instruction',
  'fight',
  'pool',
  'lead',
  'engine',
  'salt',
  'importance',
  'metal',
  'fat',
  'ticket',
  'software',
  'lip',
  'reading',
  'lunch',
  'farmer',
  'sugar',
  'planet',
  'enemy',
  'athlete',
  'soul',
  'panel',
  'meaning',
  'mom',
  'instrument',
  'weather',
  'commitment',
  'pocket',
  'temperature',
  'surprise',
  'poll',
  'proposal',
  'consequence',
  'half',
  'breath',
  'sight',
  'cover',
  'balance',
  'minority',
  'works',
  'teaching',
  'aid',
  'advice',
  'photograph',
  'trail',
  'novel',
  'code',
  'jury',
  'breast',
  'human',
  'theme',
  'storm',
  'union',
  'desk',
  'thanks',
  'fruit',
  'conclusion',
  'shadow',
  'analyst',
  'dance',
  'limit',
  'regulation',
  'being',
  'ring',
  'revenue',
  'county',
  'appearance',
  'package',
  'difficulty',
  'bridge',
  'train',
  'thinking',
  'trend',
  'visitor',
  'loan',
  'investor',
  'profit',
  'crew',
  'accident',
  'male',
  'meal',
  'hearing',
  'traffic',
  'muscle',
  'notion',
  'earth',
  'chest',
  'cash',
  'museum',
  'beauty',
  'emergency',
  'stress',
  'content',
  'root',
  'nose',
  'bottle',
  'setting',
  'dress',
  'file',
  'outcome',
  'ad',
  'duty',
  'sheet',
  'extent',
  'component',
  'contrast',
  'zone',
  'airport',
  'chief',
  'shirt',
  'pilot',
  'cat',
  'contribution',
  'capacity',
  'estate',
  'guide',
  'circumstance',
  'snow',
  'politician',
  'percentage',
  'meat',
  'soil',
  'surgery',
  'basketball',
  'golf',
  'chain',
  'address',
  'branch',
  'combination',
  'governor',
  'relief',
  'user',
  'dad',
  'manner',
  'silence',
  'rating',
  'motion',
  'gender',
  'fee',
  'landscape',
  'bowl',
  'frame',
  'host',
  'hall',
  'ocean',
  'row',
  'producer',
  'regime',
  'division',
  'appeal',
  'mirror',
  'tooth',
  'length',
  'topic',
  'variable',
  'telephone',
  'perception',
  'confidence',
  'bedroom',
  'secret',
  'debt',
  'tank',
  'nurse',
  'coverage',
  'opposition',
  'bond',
  'pleasure',
  'master',
  'era',
  'requirement',
  'check',
  'stand',
  'fun',
  'expectation',
  'wing',
  'struggle',
  'judgment',
  'beer',
  'English',
  'reference',
  'tear',
  'doubt',
  'minister',
  'hero',
  'cloud',
  'winner',
  'volume',
  'travel',
  'seed',
  'fashion',
  'pepper',
  'intervention',
  'copy',
  'tip',
  'welfare',
  'vegetable',
  'dish',
  'beach',
  'improvement',
  'opening',
  'route',
  'league',
  'core',
  'rise',
  'tie',
  'holiday',
  'resolution',
  'household',
  'abortion',
  'witness',
  'sector',
  'representative',
  'black',
  'incident',
  'flow',
  'faculty',
  'waste',
  'mass',
  'experiment',
  'bomb',
  'tone',
  'engineer',
  'wheel',
  'female',
  'promise',
  'cable',
  'AIDS',
  'Jew',
  'cream',
  'secretary',
  'gate',
  'hill',
  'noise',
  'grass',
  'hat',
  'legislation',
  'achievement',
  'fishing',
  'drink',
  'talent',
  'taste',
  'characteristic',
  'milk',
  'sentence',
  'height',
  'physician',
  'sleep',
  'ride',
  'explanation',
  'campus',
  'potential',
  'immigrant',
  'alternative',
  'interaction',
  'column',
  'personality',
  'signal',
  'curriculum',
  'honor',
  'passenger',
  'assistance',
  'association',
  'lab',
  'offer',
  'criticism',
  'asset',
  'depression',
  'journalist',
  'prayer',
  'scholar',
  'warning',
  'climate',
  'cheese',
  'observation',
  'childhood',
  'payment',
  'sir',
  'cigarette',
  'definition',
  'priority',
  'bread',
  'creation',
  'graduate',
  'request',
  'emotion',
  'universe',
  'gap',
  'prosecutor',
  'mark',
  'green',
  'airline',
  'library',
  'agenda',
  'factory',
  'selection',
  'roof',
  'expense',
  'initiative',
  'diet',
  'funding',
  'therapy',
  'schedule',
  'housing',
  'post',
  'dark',
  'steel',
  'chip',
  'self',
  'bike',
  'tea',
  'comparison',
  'settlement',
  'layer',
  'planning',
  'description',
  'wedding',
  'portion',
  'territory',
  'opponent',
  'link',
  'lake',
  'tension',
  'display',
  'alcohol',
  'saving',
  'gain',
  'desert',
  'error',
  'release',
  'cop',
  'walk',
  'sand',
  'hit',
  'print',
  'passage',
  'transition',
  'existence',
  'album',
  'participation',
  'atmosphere',
  'cycle',
  'whole',
  'resistance',
  'discovery',
  'exposure',
  'stream',
  'sale',
  'trust',
  'pot',
  'coalition',
  'tale',
  'knife',
  'phase',
  'present',
  'joke',
  'coat',
  'symptom',
  'manufacturer',
  'philosophy',
  'potato',
  'foundation',
  'pass',
  'negotiation',
  'good',
  'occasion',
  'dust',
  'investigator',
  'jacket',
  'reduction',
  'shift',
  'suicide',
  'touch',
  'substance',
  'discipline',
  'iron',
  'passion',
  'volunteer',
  'gene',
  'enforcement',
  'sauce',
  'independence',
  'marketing',
  'priest',
  'advance',
  'employer',
  'shock',
  'illness',
  'cap',
  'habit',
  'juice',
  'involvement',
  'Indian',
  'disaster',
  'parking',
  'prospect',
  'boss',
  'complaint',
  'championship',
  'mystery',
  'poverty',
  'entry',
  'spending',
  'king',
  'symbol',
  'maker',
  'mood',
  'emphasis',
  'boot',
  'entertainment',
  'bean',
  'evaluation',
  'creature',
  'commander',
  'arrangement',
  'total',
  'anger',
  'peak',
  'disorder',
  'missile',
  'wire',
  'round',
  'distribution',
  'transportation',
  'twin',
  'command',
  'commission',
  'interpretation',
  'breakfast',
  'stop',
  'engineering',
  'luck',
  'clinic',
  'veteran',
  'tablespoon',
  'tourist',
  'tomato',
  'exception',
  'butter',
  'deficit',
  'bathroom',
  'objective',
  'ally',
  'journey',
  'reputation',
  'mixture',
  'tower',
  'smoke',
  'dimension',
  'toy',
  'prisoner',
  'peer',
  'designer',
  'personnel',
  'educator',
  'relative',
  'immigration',
  'belt',
  'teaspoon',
  'birthday',
  'implication',
  'coast',
  'supporter',
  'silver',
  'teenager',
  'recognition',
  'retirement',
  'flag',
  'recovery',
  'watch',
  'gentleman',
  'corn',
  'moon',
  'throat',
  'salary',
  'observer',
  'publication',
  'crop',
  'strike',
  'phenomenon',
  'anxiety',
  'convention',
  'exhibition',
  'viewer',
  'pan',
  'consultant',
  'administrator',
  'mayor',
  'consideration',
  'CEO',
  'estimate',
  'buck',
  'poem',
  'grandmother',
  'enterprise',
  'testing',
  'stomach',
  'suggestion',
  'mail',
  'recipe',
  'preparation',
  'concert',
  'intention',
  'channel',
  'tube',
  'drawing',
  'protein',
  'absence',
  'roll',
  'jail',
  'diversity',
  'pace',
  'employment',
  'speaker',
  'impression',
  'essay',
  'respondent',
  'cake',
  'historian',
  'specialist',
  'origin',
  'approval',
  'mine',
  'drop',
  'count',
  'depth',
  'wealth',
  'disability',
  'shell',
  'professional',
  'pack',
  'onion',
  'deputy',
  'brand',
  'award',
  'criteria',
  'dealer',
  'utility',
  'highway',
  'routine',
  'wage',
  'phrase',
  'ingredient',
  'stake',
  'fiber',
  'activist',
  'terrorism',
  'refugee',
  'hip',
  'corporation',
  'assumption',
  'gear',
  'barrier',
  'provision',
  'killer',
  'gang',
  'chemical',
  'label',
  'teen',
  'index',
  'vacation',
  'advocate',
  'draft',
  'heaven',
  'drama',
  'satellite',
  'wonder',
  'clock',
  'chocolate',
  'ceiling',
  'advertising',
  'button',
  'bell',
  'rank',
  'darkness',
  'clothing',
  'fence',
  'portrait',
  'paint',
  'survival',
  'lawsuit',
  'testimony',
  'bunch',
  'beat',
  'burden',
  'chamber',
  'furniture',
  'cooperation',
  'string',
  'ceremony',
  'cheek',
  'profile',
  'mechanism',
  'penalty',
  'match',
  'resort',
  'destruction',
  'bear',
  'tissue',
  'pant',
  'stranger',
  'infection',
  'cabinet',
  'apple',
  'virus',
  'dispute',
  'fortune',
  'assistant',
  'statistics',
  'shopping',
  'cousin',
  'white',
  'port',
  'electricity',
  'adviser',
  'pay',
  'spokesman',
  'incentive',
  'slave',
  'terror',
  'expansion',
  'elite',
  'dirt',
  'rice',
  'bullet',
  'Bible',
  'chart',
  'decline',
  'conservative',
  'stick',
  'concentration',
  'champion',
  'scenario',
  'telescope',
  'reflection',
  'revolution',
  'strip',
  'tournament',
  'fiction',
  'lifetime',
  'recommendation',
  'senator',
  'hunting',
  'salad',
  'boundary',
  'satisfaction',
  'journal',
  'bench',
  'lover',
  'awareness',
  'general',
  'deck',
  'pole',
  'mode',
  'dialogue',
  'founder',
  'pride',
  'aircraft',
  'delivery',
  'platform',
  'finance',
  'joy',
  'worth',
  'singer',
  'shooting',
  'offense',
  'counter',
  'DNA',
  'smell',
  'transfer',
  'protest',
  'crash',
  'craft',
  'treaty',
  'terrorist',
  'insight',
  'lie',
  'episode',
  'fault',
  'mix',
  'assault',
  'stair',
  'adventure',
  'proof',
  'headquarters',
  'violation',
  'tongue',
  'license',
  'hold',
  'shelter',
  'controversy',
  'entrance',
  'favorite',
  'tragedy',
  'net',
  'funeral',
  'profession',
  'establishment',
  'imagination',
  'mask',
  'ui',
  'introduction',
  'representation',
  'deer',
  'partnership',
  'pollution',
  'emission',
  'fate',
  'earnings',
  'oven',
  'distinction',
  'segment',
  'poet',
  'variation',
  'comfort',
  'honey',
  'correspondent',
  'musician',
  'significance',
  'load',
  'vessel',
  'storage',
  'leather',
  'evolution',
  'tribe',
  'shelf',
  'can',
  'grandfather',
  'lawn',
  'buyer',
  'dining',
  'wisdom',
  'council',
  'instance',
  'garlic',
  'capability',
  'poetry',
  'celebrity',
  'stability',
  'fantasy',
  'plot',
  'framework',
  'gesture',
  'psychology',
  'counselor',
  'chapter',
  'fellow',
  'divorce',
  'pipe',
  'math',
  'shade',
  'tail',
  'obligation',
  'angle',
  'palm',
  'custom',
  'economist',
  'soup',
  'celebration',
  'composition',
  'pile',
  'carbon',
  'scheme',
  'crack',
  'frequency',
  'tobacco',
  'survivor',
  'psychologist',
  'galaxy',
  'ski',
  'limitation',
  'appointment',
  'preference',
  'meter',
  'explosion',
  'arrest',
  'fighter',
  'admission',
  'hunter',
  'friendship',
  'aide',
  'infant',
  'porch',
  'tendency',
  'uniform',
  'formation',
  'scholarship',
  'reservation',
  'efficiency',
  'mall',
  'scandal',
  'PC',
  'heel',
  'privacy',
  'fabric',
  'contest',
  'proportion',
  'guideline',
  'rifle',
  'maintenance',
  'conviction',
  'trick',
  'tent',
  'examination',
  'publisher',
  'French',
  'myth',
  'cow',
  'standing',
  'tennis',
  'nerve',
  'barrel',
  'bombing',
  'membership',
  'ratio',
  'menu',
  'purchase',
  'lifestyle',
  'humor',
  'glove',
  'suspect',
  'narrative',
  'photographer',
  'helicopter',
  'Catholic',
  'provider',
  'delay',
  'stroke',
  'scope',
  'punishment',
  'handful',
  'horizon',
  'girlfriend',
  'cholesterol',
  'adjustment',
  'taxpayer',
  'principal',
  'motivation',
  'assignment',
  'restriction',
  'Palestinian',
  'laboratory',
  'workshop',
  'auto',
  'cotton',
  'motor',
  'flavor',
  'sequence',
  'demonstration',
  'jet',
  'consumption',
  'blade',
  'medication',
  'cabin',
  'edition',
  'valley',
  'pitch',
  'pine',
  'manufacturing',
  'Christian',
  'complex',
  'chef',
  'discrimination',
  'German',
  'boom',
  'heritage',
  'God',
  'shit',
  'lemon',
  'economics',
  'nut',
  'legacy',
  'extension',
  'fly',
  'battery',
  'arrival',
  'orientation',
  'inflation',
  'flame',
  'cluster',
  'wound',
  'shower',
  'operating',
  'flesh',
  'garage',
  'operator',
  'instructor',
  'comedy',
  'mortgage',
  'sanction',
  'habitat',
  'grain',
  'consciousness',
  'measurement',
  'province',
  'ethics',
  'nomination',
  'permission',
  'actress',
  'summit',
  'acid',
  'odds',
  'frustration',
  'medium',
  'grant',
  'shore',
  'lung',
  'discourse',
  'basket',
  'fighting',
  'competitor',
  'powder',
  'ghost',
  'cookie',
  'carrier',
  'cooking',
  'swing',
  'orange',
  'pet',
  'miracle',
  'rhythm',
  'killing',
  'sin',
  'charity',
  'script',
  'tactic',
  'identification',
  'transformation',
  'headline',
  'venture',
  'invasion',
  'military',
  'piano',
  'grocery',
  'intensity',
  'blanket',
  'margin',
  'quarterback',
  'mouse',
  'rope',
  'prescription',
  'brick',
  'patch',
  'consensus',
  'horror',
  'recording',
  'painter',
  'pie',
  'sake',
  'gaze',
  'courage',
  'pregnancy',
  'clue',
  'win',
  'confusion',
  'slice',
  'occupation',
  'coal',
  'criminal',
  'formula',
  'uncle',
  'square',
  'captain',
  'gallery',
  'soccer',
  'defendant',
  'tunnel',
  'fitness',
  'lap',
  'grave',
  'toe',
  'container',
  'virtue',
  'architect',
  'makeup',
  'inquiry',
  'rose',
  'indication',
  'rail',
  'anniversary',
  'couch',
  'alliance',
  'hypothesis',
  'boyfriend',
  'mess',
  'legend',
  'adolescent',
  'norm',
  'remark',
  'reward',
  'organ',
  'laughter',
  'northwest',
  'counseling',
  'receiver',
  'ritual',
  'insect',
  'salmon',
  'favor',
  'trading',
  'combat',
  'stem',
  'surgeon',
  'physics',
  'rape',
  'counsel',
  'brush',
  'jeans',
  'log',
  'pill',
  'sculpture',
  'compound',
  'flour',
  'slope',
  'presidency',
  'serving',
  'bishop',
  'drinking',
  'cry',
  'acceptance',
  'collapse',
  'pump',
  'candy',
  'evil',
  'final',
  'medal',
  'export',
  'midnight',
  'curve',
  'integrity',
  'logic',
  'essence',
  'closet',
  'interior',
  'corridor',
  'pitcher',
  'snake',
  'cross',
  'weakness',
  'pig',
  'cold',
  'unemployment',
  'civilization',
  'pop',
  'correlation',
  'humanity',
  'developer',
  'excitement',
  'beef',
  'Islam',
  'stretch',
  'architecture',
  'elbow',
  'Muslim',
  'allegation',
  'airplane',
  'duck',
  'dose',
  'lecture',
  'van',
  'bay',
  'suburb',
  'sandwich',
  'trunk',
  'rumor',
  'implementation',
  'cloth',
  'effectiveness',
  'lens',
  'reach',
  'inspector',
  'fraud',
  'companion',
  'nail',
  'array',
  'rat',
  'hallway',
  'cave',
  'southwest',
  'monster',
  'obstacle',
  'encounter',
  'herb',
  'integration',
  'crystal',
  'recession',
  'wish',
  'motive',
  'flood',
  'pen',
  'ownership',
  'nightmare',
  'notice',
  'inspection',
  'supervisor',
  'arena',
  'laugh',
  'diagnosis',
  'possession',
  'basement',
  'prosecution',
  'announcement',
  'warrior',
  'prediction',
  'bacteria',
  'questionnaire',
  'mud',
  'infrastructure',
  'privilege',
  'temple',
  'broadcast',
  'wrist',
  'curtain',
  'monitor',
  'pond',
  'domain',
  'guilt',
  'cattle',
  'walking',
  'playoff',
  'skirt',
  'database',
  'aim',
  'limb',
  'ideology',
  'harm',
  'railroad',
  'radiation',
  'horn',
  'innovation',
  'strain',
  'guitar',
  'replacement',
  'dancer',
  'amendment',
  'pad',
  'transmission',
  'grace',
  'colony',
  'adoption',
  'slide',
  'civilian',
  'towel',
  'particle',
  'glance',
  'prize',
  'landing',
  'conduct',
  'blue',
  'bat',
  'alarm',
  'festival',
  'grip',
  'freshman',
  'sweat',
  'European',
  'separation',
  'southeast',
  'ballot',
  'rhetoric',
  'vitamin',
  'enthusiasm',
  'wilderness',
  'mandate',
  'pause',
  'excuse',
  'uncertainty',
  'chaos',
  'canvas',
  'lobby',
  'format',
  'trait',
  'currency',
  'turkey',
  'reserve',
  'beam',
  'astronomer',
  'corruption',
  'contractor',
  'doctrine',
  'thumb',
  'unity',
  'compromise',
  'rush',
  'complexity',
  'fork',
  'disk',
  'suspicion',
  'lock',
  'finish',
  'residence',
  'shame',
  'sidewalk',
  'Olympics',
  'signature',
  'rebel',
  'spouse',
  'fluid',
  'pension',
  'sodium',
  'blow',
  'promotion',
  'forehead',
  'hook',
  'detective',
  'traveler',
  'compensation',
  'exit',
  'attraction',
  'pickup',
  'needle',
  'belly',
  'portfolio',
  'shuttle',
  'timing',
  'engagement',
  'ankle',
  'transaction',
  'counterpart',
  'rider',
  'doll',
  'noon',
  'exhibit',
  'carbohydrate',
  'liberty',
  'poster',
  'theology',
  'oxygen',
  'magic',
  'sum',
  'businessman',
  'determination',
  'donor',
  'pastor',
  'jazz',
  'opera',
  'Japanese',
  'bite',
  'acquisition',
  'pit',
  'wildlife',
  'giant',
  'primary',
  'equity',
  'doorway',
  'departure',
  'elevator',
  'guidance',
  'happiness',
  'statue',
  'pursuit',
  'repair',
  'gym',
  'clerk',
  'Israeli',
  'envelope',
  'reporting',
  'destination',
  'fist',
  'exploration',
  'bath',
  'rescue',
  'indicator',
  'sunlight',
  'feedback',
  'spectrum',
  'laser',
  'starting',
  'expertise',
  'tune',
  'eating',
  'hint',
  'parade',
  'realm',
  'ban',
  'therapist',
  'pizza',
  'recipient',
  'accounting',
  'bias',
  'metaphor',
  'candle',
  'handle',
  'worry',
  'entity',
  'suffering',
  'feel',
  'lamp',
  'garbage',
  'servant',
  'addition',
  'inside',
  'reception',
  'chin',
  'necessity',
  'racism',
  'starter',
  'banking',
  'gravity',
  'prevention',
  'Arab',
  'performer',
  'intent',
  'inventory',
  'assembly',
  'silk',
  'magnitude',
  'hostage',
  'collector',
  'popularity',
  'kiss',
  'alien',
  'equation',
  'angel',
  'switch',
  'offering',
  'rage',
  'photography',
  'toilet',
  'Russian',
  'wake',
  'gathering',
  'automobile',
  'dawn',
  'tide',
  'romance',
  'hardware',
  'pillow',
  'kit',
  'cook',
  'spread',
  'continent',
  'circuit',
  'sink',
  'ruling',
  'shortage',
  'trap',
  'fool',
  'deadline',
  'processing',
  'ranch',
  'diamond',
  'credibility',
  'import',
  'sentiment',
  'cart',
  'elder',
  'pro',
  'inspiration',
  'quantity',
  'trailer',
  'mate',
  'genius',
  'monument',
  'bid',
  'quest',
  'sacrifice',
  'invitation',
  'accuracy',
  'juror',
  'broker',
  'treasure',
  'loyalty',
  'gasoline',
  'output',
  'nominee',
  'diabetes',
  'jaw',
  'grief',
  'rocket',
  'inmate',
  'dynamics',
  'bow',
  'senior',
  'dignity',
  'carpet',
  'bubble',
  'buddy',
  'barn',
  'sword',
  'flash',
  'glory',
  'drum',
  'queen',
  'dilemma',
  'input',
  'northeast',
  'liability',
  'merchant',
  'stadium',
  'defeat',
  'withdrawal',
  'refrigerator',
  'nest',
  'lane',
  'ancestor',
  'steam',
  'accent',
  'escape',
  'cage',
  'shrimp',
  'homeland',
  'rack',
  'costume',
  'wolf',
  'courtroom',
  'statute',
  'cartoon',
  'productivity',
  'seal',
  'bug',
  'aunt',
  'agriculture',
  'bankruptcy',
  'vaccine',
  'bonus',
  'collaboration',
  'orbit',
  'patience',
  'voting',
  'patrol',
  'willingness',
  'revelation',
  'rent',
  'jewelry',
  'hay',
  'trace',
  'wagon',
  'reliability',
  'ass',
  'bush',
  'clip',
  'thigh',
  'bull',
  'drawer',
  'sheep',
  'coordinator',
  'runner',
  'empire',
  'cab',
  'exam',
  'documentary',
  'biology',
  'web',
  'conspiracy',
  'catch',
  'casualty',
  'republic',
  'execution',
  'whale',
  'instinct',
  'teammate',
  'aluminum',
  'ministry',
  'verdict',
  'skull',
  'ease',
  'bee',
  'practitioner',
  'loop',
  'puzzle',
  'mushroom',
  'subsidy',
  'mathematics',
  'mechanic',
  'jar',
  'earthquake',
  'pork',
  'creativity',
  'dessert',
  'sympathy',
  'fisherman',
  'isolation',
  'sock',
  'jump',
  'entrepreneur',
  'syndrome',
  'bureau',
  'workplace',
  'ambition',
  'touchdown',
  'breeze',
  'Christianity',
  'translation',
  'gut',
  'booth',
  'helmet',
  'waist',
  'lion',
  'accomplishment',
  'panic',
  'cast',
  'cliff',
  'cord',
  'cocaine',
  'illusion',
  'appreciation',
  'commissioner',
  'flexibility',
  'casino',
  'tumor',
  'pulse',
  'equivalent',
  'donation',
  'diary',
  'sibling',
  'irony',
  'spoon',
  'midst',
  'alley',
  'soap',
  'rival',
  'pin',
  'hockey',
  'supplier',
  'momentum',
  'purse',
  'liquid',
  'icon',
  'elephant',
  'legislature',
  'associate',
  'franchise',
  'bicycle',
  'fever',
  'filter',
  'rabbit',
  'coin',
  'organism',
  'sensation',
  'stay',
  'minimum',
  'conservation',
  'backyard',
  'charter',
  'stove',
  'consent',
  'reminder',
  'placement',
  'dough',
  'grandchild',
  'dam',
  'outfit',
  'columnist',
  'workout',
  'patent',
  'quote',
  'trash',
  'hormone',
  'texture',
  'pencil',
  'frontier',
  'spray',
  'bet',
  'custody',
  'banker',
  'beast',
  'oak',
  'notebook',
  'attendance',
  'speculation',
  'shark',
  'mill',
  'installation',
  'tag',
  'swimming',
  'fleet',
  'catalog',
  'outsider',
  'stance',
  'sensitivity',
  'debut',
  'confrontation',
  'ideal',
  'constitution',
  'trainer',
  'Thanksgiving',
  'scent',
  'stack',
  'eyebrow',
  'sack',
  'tray',
  'pioneer',
  'textbook',
  'dot',
  'wheat',
  'kingdom',
  'aisle',
  'protocol',
  'marketplace',
  'terrain',
  'pasta',
  'genre',
  'merit',
  'planner',
  'chunk',
  'discount',
  'ladder',
  'jungle',
  'migration',
  'breathing',
  'hurricane',
  'retailer',
  'coup',
  'ambassador',
  'density',
  'curiosity',
  'aggression',
  'stimulus',
  'journalism',
  'robot',
  'feather',
  'sphere',
  'publicity',
  'major',
  'validity',
  'ecosystem',
  'collar',
  'weed',
  'compliance',
  'streak',
  'builder',
  'glimpse',
  'premise',
  'specialty',
  'artifact',
  'monkey',
  'mentor',
  'listener',
  'lightning',
  'sleeve',
  'disappointment',
  'rib',
  'debris',
  'rod',
  'liberal',
  'ash',
  'parish',
  'slavery',
  'commodity',
  'cure',
  'mineral',
  'hunger',
  'equality',
  'cemetery',
  'harassment',
  'fame',
  'likelihood',
  'carrot',
  'toll',
  'rim',
  'wheelchair',
  'squad',
  'processor',
  'sponsor',
  'grin',
  'chill',
  'refuge',
  'legislator',
  'rally',
  'programming',
  'outlet',
  'vendor',
  'peanut',
  'intellectual',
  'conception',
  'auction',
  'steak',
  'triumph',
  'shareholder',
  'conscience',
  'calculation',
  'interval',
  'jurisdiction',
  'constraint',
  'expedition',
  'similarity',
  'butt',
  'lid',
  'bulk',
  'mortality',
  'conversion',
  'patron',
  'liver',
  'harmony',
  'tolerance',
  'instant',
  'goat',
  'blessing',
  'banana',
  'running',
  'palace',
  'peasant',
  'grandparent',
  'lawmaker',
  'supermarket',
  'cruise',
  'plain',
  'calendar',
  'widow',
  'deposit',
  'beard',
  'brake',
  'screening',
  'impulse',
  'fur',
  'predator',
  'forum',
  'dancing',
  'removal',
  'autonomy',
  'thread',
  'landmark',
  'offender',
  'fraction',
  'tourism',
  'threshold',
  'suite',
  'regulator',
  'straw',
  'globe',
  'objection',
  'chemistry',
  'blast',
  'denial',
  'rental',
  'fragment',
  'warmth',
  'undergraduate',
  'headache',
  'policeman',
  'yield',
  'projection',
  'mention',
  'graduation',
  'mansion',
  'regard',
  'grape',
  'cottage',
  'driveway',
  'charm',
  'sexuality',
  'clay',
  'balloon',
  'invention',
  'ego',
  'fare',
  'homework',
  'disc',
  'sofa',
  'guarantee',
  'availability',
  'radar',
  'leave',
  'permit',
  'sweater',
  'rehabilitation',
  'retreat',
  'molecule',
  'youngster',
  'premium',
  'accountability',
  'fatigue',
  'marker',
  'bucket',
  'confession',
  'marble',
  'twist',
  'defender',
  'transport',
  'surveillance',
  'technician',
  'arrow',
  'trauma',
  'ribbon',
  'meantime',
  'harvest',
  'spy',
  'slot',
  'riot',
  'nutrient',
  'citizenship',
  'sovereignty',
  'ridge',
  'lighting',
  'contributor',
  'transit',
  'seminar',
  'electronics',
  'shorts',
  'accusation',
  'cue',
  'bride',
  'biography',
  'hazard',
  'tile',
  'foreigner',
  'launch',
  'convenience',
  'delight',
  'timber',
  'plea',
  'bulb',
  'devil',
  'bolt',
  'cargo',
  'spine',
  'seller',
  'dock',
  'fog',
  'diplomat',
  'summary',
  'missionary',
  'epidemic',
  'warehouse',
  'butterfly',
  'bronze',
  'praise',
  'vacuum',
  'stereotype',
  'sensor',
  'laundry',
  'manual',
  'pistol',
  'plaintiff',
  'apology',
];
