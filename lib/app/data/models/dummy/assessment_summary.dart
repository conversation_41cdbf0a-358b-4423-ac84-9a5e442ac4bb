class AssessmentSummary {
  final DateTime completionTime;
  final String duration;
  final String questionAnswered;
  final dynamic score;
  final dynamic review;

  AssessmentSummary(
      {required this.completionTime,
      required this.duration,
      required this.questionAnswered,
      required this.score,
      required this.review});

  factory AssessmentSummary.fromJson(Map<String, dynamic> json) {
    return AssessmentSummary(
        completionTime: DateTime.parse(json['completion-time']),
        duration: json['duration'],
        questionAnswered: json['question-answered'],
        score: json['score'],
        review: json['review']);
  }
}
