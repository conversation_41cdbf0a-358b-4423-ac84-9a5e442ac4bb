class Chat {
  final String idUser;
  final String message;
  final DateTime date;

  Chat({
    required this.idUser,
    required this.message,
    required this.date,
  });

  factory Chat.fromJson(Map<String, dynamic> json) => Chat(
        idUser: json["id-user"],
        message: json["message"],
        date: DateTime.parse(json["date"]),
      );

  Map<String, dynamic> toJson() => {
        "id-user": idUser,
        "message": message,
        "date": date.toIso8601String(),
      };
}
