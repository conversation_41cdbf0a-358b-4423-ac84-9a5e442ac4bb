class QuizSummary {
  final int attempt;
  final DateTime timeSubmit;
  final int incorrect;
  final int score;
  final bool review;

  QuizSummary({
    required this.attempt,
    required this.timeSubmit,
    required this.incorrect,
    required this.score,
    required this.review,
  });

  factory QuizSummary.fromJson(Map<String, dynamic> json) {
    return QuizSummary(
      attempt: json['attempt'],
      timeSubmit: DateTime.parse(json['time-submit']),
      incorrect: json['incorrect'],
      score: json['score'],
      review: json['review'],
    );
  }
}
