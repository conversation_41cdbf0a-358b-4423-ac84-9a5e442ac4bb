class SearchVodModel {
  SearchVodModel({
    this.id,
    this.title,
    this.videoUrl,
    this.thumbnailUrl,
    this.totalView,
    this.uploader,
    this.uploadDate,
    this.duration,
    this.desc,
    this.visibility,
    this.status,
    this.statusNote,
    this.isCommentEnabled,
    this.tag,
  });

  final String? id;
  final String? title;
  final String? videoUrl;
  final String? thumbnailUrl;
  final int? totalView;
  final String? uploader;
  final DateTime? uploadDate;
  final String? duration;
  final String? desc;
  final String? visibility;
  final String? status;
  final String? statusNote;
  final bool? isCommentEnabled;
  final List<Tag>? tag;

  factory SearchVodModel.fromJson(Map<String, dynamic> json) {
    return SearchVodModel(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      videoUrl: json["video_url"] ?? "",
      thumbnailUrl: json["thumbnail_url"] ?? "",
      totalView: json["total_view"] ?? 0,
      uploader: json["uploader"] ?? "",
      uploadDate: DateTime.tryParse(json["upload_date"] ?? ""),
      duration: json["duration"] ?? "",
      desc: json["desc"] ?? "",
      visibility: json["visibility"] ?? "",
      status: json["status"] ?? "",
      statusNote: json["status_note"] ?? "",
      isCommentEnabled: json["is_comment_enabled"] ?? false,
      tag: json["tag"] == null
          ? []
          : List<Tag>.from(json["tag"]!.map((x) => Tag.fromJson(x))),
    );
  }
}

class Tag {
  Tag({
    this.id,
    this.name,
    this.description,
    this.isPublic,
    this.createdBy,
    this.createdById,
    this.createdAt,
    this.updatedAt,
  });

  String? id;
  String? name;
  String? description;
  bool? isPublic;
  String? createdBy;
  String? createdById;
  DateTime? createdAt;
  DateTime? updatedAt;

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      isPublic: json["is_public"] ?? false,
      createdBy: json["created_by"] ?? "",
      createdById: json["created_by_id"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }
}
