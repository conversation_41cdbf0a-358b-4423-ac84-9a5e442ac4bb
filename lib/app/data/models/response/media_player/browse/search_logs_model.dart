class SearchLogsModel {
  SearchLogsModel({
    required this.id,
    required this.searchValue,
    required this.totalSearch,
    required this.createdAt,
    required this.createdById,
  });

  final String id;
  final String searchValue;
  final int totalSearch;
  final DateTime? createdAt;
  final String createdById;

  factory SearchLogsModel.fromJson(Map<String, dynamic> json) {
    return SearchLogsModel(
      id: json["id"] ?? "",
      searchValue: json["search_value"] ?? "",
      totalSearch: json["total_search"] ?? 0,
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      createdById: json["created_by_id"] ?? "",
    );
  }
}
