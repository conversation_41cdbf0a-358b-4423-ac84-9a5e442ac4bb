import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';

class PlaybackModel {
  String? id;
  String? lastWatchedTime;
  bool? isCompleted;
  DateTime? createdAt;
  DateTime? updatedAt;
  VodModel? vod;

  PlaybackModel(
      {this.id,
      this.lastWatchedTime,
      this.isCompleted,
      this.createdAt,
      this.updatedAt,
      this.vod});

  PlaybackModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    lastWatchedTime = json['last_watched_time'];
    isCompleted = json['is_completed'];
    createdAt = DateTime.tryParse(json["created_at"] ?? "");
    updatedAt = DateTime.tryParse(json["updated_at"] ?? "");
    vod = json['vod'] != null ? VodModel.fromJson(json['vod']) : null;
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {};
    data['id'] = this.id;
    data['last_watched_time'] = this.lastWatchedTime;
    data['is_completed'] = this.isCompleted;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    if (this.vod != null) {
      data['vod'] = this.toJson();
    }
    return data;
  }
}

class CreatedUser {
  String? id;
  String? nrp;
  String? nosis;
  String? type;
  String? username;
  String? pasisName;
  String? gender;
  String? force;
  String? imageProfile;
  String? lastLogin;

  CreatedUser({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  CreatedUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nrp = json['nrp'];
    nosis = json['nosis'];
    type = json['type'];
    username = json['username'];
    pasisName = json['pasis_name'];
    gender = json['gender'];
    force = json['force'];
    imageProfile = json['image_profile'];
    lastLogin = json['last_login'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nrp'] = this.nrp;
    data['nosis'] = this.nosis;
    data['type'] = this.type;
    data['username'] = this.username;
    data['pasis_name'] = this.pasisName;
    data['gender'] = this.gender;
    data['force'] = this.force;
    data['image_profile'] = this.imageProfile;
    data['last_login'] = this.lastLogin;
    return data;
  }
}
