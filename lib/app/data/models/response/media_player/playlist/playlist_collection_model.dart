import 'package:equatable/equatable.dart';

class Vod extends Equatable {
  Vod({
    this.id,
    this.title,
    this.videoUrl,
    this.videoSourceUrl,
    this.thumbnailUrl,
    this.totalView,
    this.totalLike,
    this.totalDislike,
    this.totalRating,
    this.uploader,
    this.uploadDate,
    this.duration,
    this.desc,
    this.visibility,
    this.status,
    this.statusNote,
    this.isCommentEnabled,
    this.tag,
  });

  final String? id;
  final String? title;
  final String? videoUrl;
  final String? videoSourceUrl;
  final String? thumbnailUrl;
  final int? totalView;
  final int? totalLike;
  final int? totalDislike;
  final int? totalRating;
  final Uploader? uploader;
  final DateTime? uploadDate;
  final String? duration;
  final String? desc;
  final String? visibility;
  final String? status;
  final String? statusNote;
  final bool? isCommentEnabled;
  final List<PlaylistCollectionModel>? tag;

  factory Vod.fromJson(Map<String, dynamic> json) {
    return Vod(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      videoUrl: json["video_url"] ?? "",
      videoSourceUrl: json["video_source_url"] ?? "",
      thumbnailUrl: json["thumbnail_url"] ?? "",
      totalView: json["total_view"] ?? 0,
      totalLike: json["total_like"] ?? 0,
      totalDislike: json["total_dislike"] ?? 0,
      totalRating: json["total_rating"] ?? 0,
      uploader:
          json["uploader"] == null ? null : Uploader.fromJson(json["uploader"]),
      uploadDate: DateTime.tryParse(json["upload_date"] ?? ""),
      duration: json["duration"] ?? "",
      desc: json["desc"] ?? "",
      visibility: json["visibility"] ?? "",
      status: json["status"] ?? "",
      statusNote: json["status_note"] ?? "",
      isCommentEnabled: json["is_comment_enabled"] ?? false,
      tag: json["tag"] == null
          ? []
          : List<PlaylistCollectionModel>.from(
              json["tag"]!.map((x) => PlaylistCollectionModel.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        videoUrl,
        videoSourceUrl,
        thumbnailUrl,
        totalView,
        totalLike,
        totalDislike,
        totalRating,
        uploader,
        uploadDate,
        duration,
        desc,
        visibility,
        status,
        statusNote,
        isCommentEnabled,
        tag,
      ];
}

class PlaylistCollectionModel extends Equatable {
  PlaylistCollectionModel({
    this.id,
    this.name,
    this.isSelected = false,
    this.description,
    this.visibility,
    this.createdById,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.vods,
    this.isPublic,
  });

  final String? id;
  final String? name;
  bool isSelected;
  final String? description;
  final String? visibility;
  final String? createdById;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Vod>? vods;
  final bool? isPublic;

  factory PlaylistCollectionModel.fromJson(Map<String, dynamic> json) {
    return PlaylistCollectionModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      visibility: json["visibility"] ?? "",
      createdById: json["created_by_id"] ?? "",
      createdBy: json["created_by"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      vods: json["vods"] == null
          ? []
          : List<Vod>.from(json["vods"]!.map((x) => Vod.fromJson(x))),
      isPublic: json["is_public"] ?? false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        visibility,
        createdById,
        createdBy,
        createdAt,
        updatedAt,
        vods,
        isPublic,
      ];
}

class Uploader extends Equatable {
  Uploader({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? type;
  final String? username;
  final String? pasisName;
  final String? gender;
  final String? force;
  final String? imageProfile;
  final DateTime? lastLogin;

  factory Uploader.fromJson(Map<String, dynamic> json) {
    return Uploader(
      id: json["id"] ?? "",
      nrp: json["nrp"] ?? "",
      nosis: json["nosis"] ?? "",
      type: json["type"] ?? "",
      username: json["username"] ?? "",
      pasisName: json["pasis_name"] ?? "",
      gender: json["gender"] ?? "",
      force: json["force"] ?? "",
      imageProfile: json["image_profile"] ?? "",
      lastLogin: DateTime.tryParse(json["last_login"] ?? ""),
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        nosis,
        type,
        username,
        pasisName,
        gender,
        force,
        imageProfile,
        lastLogin,
      ];
}
