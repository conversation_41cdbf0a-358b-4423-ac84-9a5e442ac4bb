class ClipModel {
  String? id;
  String? name;
  String? description;
  String? clipUrl;
  String? createdAt;
  CreatedUser? createdUser;
  SourceVod? sourceVod;

  ClipModel({this.id, this.name, this.description, this.clipUrl, this.createdAt, this.createdUser, this.sourceVod});

  ClipModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    clipUrl = json['clip_url'];
    createdAt = json['created_at'];
    createdUser = json['created_user'] != null ? new CreatedUser.fromJson(json['created_user']) : null;
    sourceVod = json['source_vod'] != null ? new SourceVod.fromJson(json['source_vod']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['clip_url'] = this.clipUrl;
    data['created_at'] = this.createdAt;
    if (this.createdUser != null) {
      data['created_user'] = this.createdUser!.toJson();
    }
    if (this.sourceVod != null) {
      data['source_vod'] = this.sourceVod!.toJson();
    }
    return data;
  }
}

class CreatedUser {
  String? id;
  String? nrp;
  String? nosis;
  String? type;
  String? username;
  String? pasisName;
  String? gender;
  String? force;
  String? imageProfile;
  String? lastLogin;

  CreatedUser({this.id, this.nrp, this.nosis, this.type, this.username, this.pasisName, this.gender, this.force, this.imageProfile, this.lastLogin});

  CreatedUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nrp = json['nrp'];
    nosis = json['nosis'];
    type = json['type'];
    username = json['username'];
    pasisName = json['pasis_name'];
    gender = json['gender'];
    force = json['force'];
    imageProfile = json['image_profile'];
    lastLogin = json['last_login'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nrp'] = this.nrp;
    data['nosis'] = this.nosis;
    data['type'] = this.type;
    data['username'] = this.username;
    data['pasis_name'] = this.pasisName;
    data['gender'] = this.gender;
    data['force'] = this.force;
    data['image_profile'] = this.imageProfile;
    data['last_login'] = this.lastLogin;
    return data;
  }
}

class SourceVod {
  String? id;
  String? title;
  String? videoUrl;
  String? videoSourceUrl;
  String? thumbnailUrl;
  int? totalView;
  int? totalLike;
  int? totalDislike;
  CreatedUser? uploader;
  String? uploadDate;
  String? duration;
  String? desc;
  String? visibility;
  String? status;
  String? statusNote;
  bool? isCommentEnabled;
  List<Tag>? tag;

  SourceVod(
      {this.id,
      this.title,
      this.videoUrl,
      this.videoSourceUrl,
      this.thumbnailUrl,
      this.totalView,
      this.totalLike,
      this.totalDislike,
      this.uploader,
      this.uploadDate,
      this.duration,
      this.desc,
      this.visibility,
      this.status,
      this.statusNote,
      this.isCommentEnabled,
      this.tag});

  SourceVod.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    videoUrl = json['video_url'];
    videoSourceUrl = json['video_source_url'];
    thumbnailUrl = json['thumbnail_url'];
    totalView = json['total_view'];
    totalLike = json['total_like'];
    totalDislike = json['total_dislike'];
    uploader = json['uploader'] != null ? new CreatedUser.fromJson(json['uploader']) : null;
    uploadDate = json['upload_date'];
    duration = json['duration'];
    desc = json['desc'];
    visibility = json['visibility'];
    status = json['status'];
    statusNote = json['status_note'];
    isCommentEnabled = json['is_comment_enabled'];
    if (json['tag'] != null) {
      tag = <Tag>[];
      json['tag'].forEach((v) {
        tag!.add(new Tag.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['video_url'] = this.videoUrl;
    data['video_source_url'] = this.videoSourceUrl;
    data['thumbnail_url'] = this.thumbnailUrl;
    data['total_view'] = this.totalView;
    data['total_like'] = this.totalLike;
    data['total_dislike'] = this.totalDislike;
    if (this.uploader != null) {
      data['uploader'] = this.uploader!.toJson();
    }
    data['upload_date'] = this.uploadDate;
    data['duration'] = this.duration;
    data['desc'] = this.desc;
    data['visibility'] = this.visibility;
    data['status'] = this.status;
    data['status_note'] = this.statusNote;
    data['is_comment_enabled'] = this.isCommentEnabled;
    if (this.tag != null) {
      data['tag'] = this.tag!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Tag {
  String? id;
  String? name;
  String? description;
  bool? isPublic;
  String? createdBy;
  String? createdById;
  String? createdAt;
  String? updatedAt;

  Tag({this.id, this.name, this.description, this.isPublic, this.createdBy, this.createdById, this.createdAt, this.updatedAt});

  Tag.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    isPublic = json['is_public'];
    createdBy = json['created_by'];
    createdById = json['created_by_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['is_public'] = this.isPublic;
    data['created_by'] = this.createdBy;
    data['created_by_id'] = this.createdById;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
