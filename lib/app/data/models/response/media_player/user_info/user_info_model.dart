class UserInfoMediaPlayer {
  UserInfoMediaPlayer({
    this.exp,
    this.iat,
    this.id,
    this.imageProfile,
    this.name,
    this.role,
  });

  final int? exp;
  final int? iat;
  final String? id;
  final String? imageProfile;
  final String? name;
  final String? role;

  factory UserInfoMediaPlayer.fromJson(Map<String, dynamic> json) {
    return UserInfoMediaPlayer(
      exp: json["exp"] ?? 0,
      iat: json["iat"] ?? 0,
      id: json["id"] ?? "",
      imageProfile: json["imageProfile"],
      name: json["name"] ?? "",
      role: json["role"] ?? "",
    );
  }
}
