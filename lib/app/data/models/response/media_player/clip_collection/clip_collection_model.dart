import 'package:equatable/equatable.dart';

class ClipCollectionModel extends Equatable {
  ClipCollectionModel({
    this.id,
    this.name,
    this.description,
    this.createdAt,
    this.createdUser,
    this.updatedAt,
    this.clips,
    this.isSelected = false,
  });

  final String? id;
  final String? name;
  bool isSelected;
  final String? description;
  final DateTime? createdAt;
  final CreatedUser? createdUser;
  final DateTime? updatedAt;
  final List<dynamic>? clips;

  factory ClipCollectionModel.fromJson(Map<String, dynamic> json) {
    return ClipCollectionModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      createdUser: json["created_user"] == null
          ? null
          : CreatedUser.fromJson(json["created_user"]),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      clips: json["clips"] == null
          ? []
          : List<dynamic>.from(json["clips"]!.map((x) => x)),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        createdAt,
        createdUser,
        updatedAt,
        clips,
      ];
}

class CreatedUser extends Equatable {
  CreatedUser({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? type;
  final String? username;
  final String? pasisName;
  final String? gender;
  final String? force;
  final String? imageProfile;
  final dynamic lastLogin;

  factory CreatedUser.fromJson(Map<String, dynamic> json) {
    return CreatedUser(
      id: json["id"] ?? "",
      nrp: json["nrp"] ?? "",
      nosis: json["nosis"] ?? "",
      type: json["type"] ?? "",
      username: json["username"] ?? "",
      pasisName: json["pasis_name"] ?? "",
      gender: json["gender"] ?? "",
      force: json["force"] ?? "",
      imageProfile: json["image_profile"] ?? "",
      lastLogin: json["last_login"],
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        nosis,
        type,
        username,
        pasisName,
        gender,
        force,
        imageProfile,
        lastLogin,
      ];
}
