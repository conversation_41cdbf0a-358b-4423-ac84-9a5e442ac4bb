class VodModel {
  VodModel({
    this.id,
    this.title,
    this.videoUrl,
    this.videoSourceUrl,
    this.thumbnailUrl,
    this.totalView,
    this.totalLike,
    this.totalDislike,
    this.totalRating,
    this.uploader,
    this.uploadDate,
    this.duration,
    this.desc,
    this.visibility,
    this.status,
    this.statusNote,
    this.isCommentEnabled,
    this.tag,
  });

  final String? id;
  final String? title;
  final String? videoUrl;
  final String? videoSourceUrl;
  final String? thumbnailUrl;
  final int? totalView;
  final dynamic totalLike;
  final dynamic totalDislike;
  final dynamic totalRating;
  final Uploader? uploader;
  final DateTime? uploadDate;
  final String? duration;
  final String? desc;
  final String? visibility;
  final String? status;
  final String? statusNote;
  final bool? isCommentEnabled;
  final List<Tag>? tag;

  List<String> get tagNames {
    return tag
            ?.where((t) => t.name != null && t.name!.isNotEmpty)
            .map((t) => t.name!)
            .toList() ??
        [];
  }

  List<String> get tagId {
    return tag
            ?.where((t) => t.name != null && t.id!.isNotEmpty)
            .map((t) => t.id!)
            .toList() ??
        [];
  }

  factory VodModel.fromJson(Map<String, dynamic> json) {
    return VodModel(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      videoUrl: json["video_url"] ?? "",
      videoSourceUrl: json["video_source_url"] ?? "",
      thumbnailUrl: json["thumbnail_url"] ?? "",
      totalView: json["total_view"] ?? 0,
      totalLike: json["total_like"] ?? 0,
      totalDislike: json["total_dislike"] ?? 0,
      totalRating: json["total_rating"] ?? 0,
      uploader:
          json["uploader"] == null ? null : Uploader.fromJson(json["uploader"]),
      uploadDate: DateTime.tryParse(json["upload_date"] ?? ""),
      duration: json["duration"] ?? "",
      desc: json["desc"] ?? "",
      visibility: json["visibility"] ?? "",
      status: json["status"] ?? "",
      statusNote: json["status_note"] ?? "",
      isCommentEnabled: json["is_comment_enabled"] ?? false,
      tag: json["tag"] == null
          ? []
          : List<Tag>.from(json["tag"]!.map((x) => Tag.fromJson(x))),
    );
  }
}

class Tag {
  Tag({
    this.id,
    this.name,
    this.isPublic,
    this.createdBy,
    this.createdById,
    this.createdAt,
    this.vods,
  });

  final String? id;
  final String? name;
  final bool? isPublic;
  final String? createdBy;
  final String? createdById;
  final DateTime? createdAt;
  final List<dynamic>? vods;

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      isPublic: json["is_public"] ?? false,
      createdBy: json["created_by"] ?? "",
      createdById: json["created_by_id"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      vods: json["vods"] == null
          ? []
          : List<dynamic>.from(json["vods"]!.map((x) => x)),
    );
  }
}

class Uploader {
  Uploader({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? type;
  final String? username;
  final String? pasisName;
  final String? gender;
  final String? force;
  final String? imageProfile;
  final String? lastLogin;

  factory Uploader.fromJson(Map<String, dynamic> json) {
    return Uploader(
      id: json["id"] ?? "",
      nrp: json["nrp"] ?? "",
      nosis: json["nosis"] ?? "",
      type: json["type"] ?? "",
      username: json["username"] ?? "",
      pasisName: json["pasis_name"] ?? "",
      gender: json["gender"] ?? "",
      force: json["force"] ?? "",
      imageProfile: json["image_profile"] ?? "",
      lastLogin: json["last_login"] ?? "0",
    );
  }
}
