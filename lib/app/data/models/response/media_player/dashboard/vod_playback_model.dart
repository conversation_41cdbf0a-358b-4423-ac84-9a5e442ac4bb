class PlaybackVodModel {
  PlaybackVodModel({
    this.id,
    this.title,
    this.videoUrl,
    this.thumbnailUrl,
    this.totalView,
    this.uploader,
    this.uploadDate,
    this.duration,
    this.durationSaved,
    this.desc,
    this.visibility,
    this.status,
    this.statusNote,
    this.isCommentEnabled,
    this.tag,
  });

  final String? id;
  final String? title;
  final String? videoUrl;
  final String? thumbnailUrl;
  final int? totalView;
  final String? uploader;
  final DateTime? uploadDate;
  final String? duration;
  final String? durationSaved;
  final String? desc;
  final String? visibility;
  final String? status;
  final String? statusNote;
  final bool? isCommentEnabled;
  final List<Tag>? tag;

  List<String> get tagNames {
    return tag
            ?.where((t) => t.name != null && t.name!.isNotEmpty)
            .map((t) => t.name!)
            .toList() ??
        [];
  }

  factory PlaybackVodModel.fromJson(Map<String, dynamic> json) {
    return PlaybackVodModel(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      videoUrl: json["video_url"] ?? "",
      thumbnailUrl: json["thumbnail_url"] ?? "",
      totalView: json["total_view"] ?? 0,
      uploader: json["uploader"] ?? "",
      uploadDate: DateTime.tryParse(json["upload_date"] ?? ""),
      duration: json["duration"] ?? "",
      durationSaved: json["duration_saved"] ?? "",
      desc: json["desc"] ?? "",
      visibility: json["visibility"] ?? "",
      status: json["status"] ?? "",
      statusNote: json["status_note"] ?? "",
      isCommentEnabled: json["is_comment_enabled"] ?? false,
      tag: json["tag"] == null
          ? []
          : List<Tag>.from(json["tag"]!.map((x) => Tag.fromJson(x))),
    );
  }
}

class Tag {
  Tag({
    this.id,
    this.name,
    this.isPublic,
    this.createdBy,
    this.createdById,
    this.createdAt,
    this.vods,
  });

  final String? id;
  final String? name;
  final bool? isPublic;
  final String? createdBy;
  final String? createdById;
  final DateTime? createdAt;
  final List<dynamic>? vods;

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      isPublic: json["is_public"] ?? false,
      createdBy: json["created_by"] ?? "",
      createdById: json["created_by_id"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      vods: json["vods"] == null
          ? []
          : List<dynamic>.from(json["vods"]!.map((x) => x)),
    );
  }
}
