import 'package:equatable/equatable.dart';

class CommentModel extends Equatable {
  CommentModel({
    this.id,
    this.parentId,
    this.vodId,
    this.comment,
    this.createdAt,
    this.createdUser,
    this.reply,
    this.isOpenReply = false,
  });

  final String? id;
  final String? parentId;
  final String? vodId;
  bool isOpenReply;
  final String? comment;
  final DateTime? createdAt;
  final CreatedUser? createdUser;
  final List<CommentModel>? reply;

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json["id"] ?? "",
      parentId: json["parent_id"] ?? "",
      vodId: json["vod_id"] ?? "",
      comment: json["comment"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      createdUser: json["created_user"] == null
          ? null
          : CreatedUser.fromJson(json["created_user"]),
      reply: json["reply"] == null
          ? []
          : List<CommentModel>.from(
              json["reply"]!.map((x) => CommentModel.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        id,
        parentId,
        vodId,
        comment,
        createdAt,
        createdUser,
        reply,
      ];
}

class CreatedUser extends Equatable {
  CreatedUser({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? type;
  final String? username;
  final String? pasisName;
  final String? gender;
  final String? force;
  final String? imageProfile;
  final DateTime? lastLogin;

  factory CreatedUser.fromJson(Map<String, dynamic> json) {
    return CreatedUser(
      id: json["id"] ?? "",
      nrp: json["nrp"] ?? "",
      nosis: json["nosis"] ?? "",
      type: json["type"] ?? "",
      username: json["username"] ?? "",
      pasisName: json["pasis_name"] ?? "",
      gender: json["gender"] ?? "",
      force: json["force"] ?? "",
      imageProfile: json["image_profile"] ?? "",
      lastLogin: DateTime.tryParse(json["last_login"] ?? ""),
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        nosis,
        type,
        username,
        pasisName,
        gender,
        force,
        imageProfile,
        lastLogin,
      ];
}
