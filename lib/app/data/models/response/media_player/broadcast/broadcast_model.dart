import 'package:equatable/equatable.dart';

class BroadcastModel extends Equatable {
  BroadcastModel({
    this.id,
    this.title,
    this.broadcastUrl,
    this.thumbnailUrl,
    this.description,
    this.status,
    this.createdAt,
    this.createdUser,
  });

  final String? id;
  final String? title;
  final String? thumbnailUrl;
  final String? broadcastUrl;
  final String? description;
  final String? status;
  final DateTime? createdAt;
  final CreatedUser? createdUser;

  factory BroadcastModel.fromJson(Map<String, dynamic> json) {
    return BroadcastModel(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      thumbnailUrl: json["thumbnail_url"] ?? "",
      broadcastUrl: json["broadcast_url"] ?? "",
      description: json["description"] ?? "",
      status: json["status"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      createdUser: json["created_user"] == null
          ? null
          : CreatedUser.fromJson(json["created_user"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        broadcastUrl,
        description,
        status,
        createdAt,
        createdUser,
      ];
}

class CreatedUser extends Equatable {
  CreatedUser({
    this.id,
    this.nrp,
    this.nosis,
    this.type,
    this.username,
    this.pasisName,
    this.gender,
    this.force,
    this.imageProfile,
    this.lastLogin,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? type;
  final String? username;
  final String? pasisName;
  final String? gender;
  final String? force;
  final String? imageProfile;
  final DateTime? lastLogin;

  factory CreatedUser.fromJson(Map<String, dynamic> json) {
    return CreatedUser(
      id: json["id"] ?? "",
      nrp: json["nrp"] ?? "",
      nosis: json["nosis"] ?? "",
      type: json["type"] ?? "",
      username: json["username"] ?? "",
      pasisName: json["pasis_name"] ?? "",
      gender: json["gender"] ?? "",
      force: json["force"] ?? "",
      imageProfile: json["image_profile"] ?? "",
      lastLogin: DateTime.tryParse(json["last_login"] ?? ""),
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        nosis,
        type,
        username,
        pasisName,
        gender,
        force,
        imageProfile,
        lastLogin,
      ];
}
