class MediaPlayerCategory {
  String? id;
  String? name;
  String? description;
  bool? isPublic;
  String? createdBy;
  String? createdById;
  String? createdAt;
  String? updatedAt;

  MediaPlayerCategory({this.id, this.name, this.description, this.isPublic, this.createdBy, this.createdById, this.createdAt, this.updatedAt});

  MediaPlayerCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    isPublic = json['is_public'];
    createdBy = json['created_by'];
    createdById = json['created_by_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['is_public'] = this.isPublic;
    data['created_by'] = this.createdBy;
    data['created_by_id'] = this.createdById;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
