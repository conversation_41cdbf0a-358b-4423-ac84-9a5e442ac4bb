class AllCategoryModel {
  AllCategoryModel({
    this.id,
    this.name,
    this.description,
    this.isPublic,
    this.createdBy,
    this.createdById,
    this.createdAt,
    this.updatedAt,
  });

  String? id;
  String? name;
  String? description;
  bool? isPublic;
  String? createdBy;
  String? createdById;
  DateTime? createdAt;
  DateTime? updatedAt;

  factory AllCategoryModel.fromJson(Map<String, dynamic> json) {
    return AllCategoryModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      isPublic: json["is_public"] ?? false,
      createdBy: json["created_by"] ?? "",
      createdById: json["created_by_id"] ?? "",
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }
}
