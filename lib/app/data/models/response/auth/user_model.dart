class UserModel {
  UserModel({
    this.token,
    this.id,
    this.name,
    this.imageProfile,
    this.role,
  });

  final String? token;
  final String? id;
  final String? name;
  final dynamic imageProfile;
  final String? role;

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      token: json["token"],
      id: json["id"],
      name: json["name"],
      imageProfile: json["imageProfile"],
      role: json["role"],
    );
  }
}
