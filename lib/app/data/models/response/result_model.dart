sealed class ResultModel<T> {
  const ResultModel();

  const factory ResultModel.success(T value,
      [int? totalData, String? message]) = Success;
  const factory ResultModel.failed(String message,
      [Map<String, dynamic>? errors]) = Failed;

  bool get isSuccess => this is Success<T>;
  bool get isFailed => this is Failed<T>;

  T? get resultValue => isSuccess ? (this as Success<T>).value : null;
  int? get resultTotalData => isSuccess ? (this as Success<T>).totalData : null;
  String? get errorMessage => isFailed ? (this as Failed<T>).message : null;
  String? get succesMessage => isSuccess ? (this as Success<T>).message : null;
  Map<String, dynamic>? get errors =>
      isFailed ? (this as Failed<T>).errors : null;
}

class Success<T> extends ResultModel<T> {
  final T value;
  final int? totalData;
  final String? message;

  const Success(this.value, [this.totalData, this.message]);
}

class Failed<T> extends ResultModel<T> {
  final String message;
  @override
  final Map<String, dynamic>? errors;

  const Failed(this.message, [this.errors]);
}
