import 'package:equatable/equatable.dart';

class MapelModel extends Equatable {
  String? id;
  String? name;
  String? status;
  Matkul? matkul;
  List<SubMapel>? subMapels;

  MapelModel({
    this.id,
    this.name,
    this.status,
    this.matkul,
    this.subMapels,
  });

  factory MapelModel.fromJson(Map<String, dynamic> json) => MapelModel(
        id: json["id"],
        name: json["name"],
        status: json["status"],
        matkul: Matkul.from<PERSON>son(json["matkul"]),
        subMapels: List<SubMapel>.from(
            json["subMapels"].map((x) => SubMapel.fromJson(x))),
      );

  @override
  List<Object?> get props => [
        id,
        name,
        status,
        matkul,
        subMapels,
      ];
}

class Matkul extends Equatable {
  String? id;
  String? name;

  Matkul({
    this.id,
    this.name,
  });

  factory Matkul.fromJson(Map<String, dynamic> json) => Matkul(
        id: json["id"],
        name: json["name"],
      );

  @override
  List<Object?> get props => [
        id,
        name,
      ];
}

class SubMapel extends Equatable {
  String? id;
  String? name;
  int? sks;
  int? hn;
  String? code;

  SubMapel({
    this.id,
    this.name,
    this.sks,
    this.hn,
    this.code,
  });

  factory SubMapel.fromJson(Map<String, dynamic> json) => SubMapel(
        id: json["id"],
        name: json["name"],
        sks: json["SKS"],
        hn: json["HN"],
        code: json["code"],
      );

  @override
  List<Object?> get props => [
        id,
        name,
        sks,
        hn,
        code,
      ];
}
