import 'package:equatable/equatable.dart';

class QuizReviewModel extends Equatable {
  const QuizReviewModel({
    this.questionId,
    this.answer,
  });

  final String? questionId;
  final String? answer;

  factory QuizReviewModel.fromJson(Map<String, dynamic> json) {
    return QuizReviewModel(
      questionId: json["questionId"] ?? "",
      answer: json["answer"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        questionId,
        answer,
      ];
}
