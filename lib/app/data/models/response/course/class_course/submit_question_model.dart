import 'package:equatable/equatable.dart';

class SubmitQuestionModel extends Equatable {
  const SubmitQuestionModel({
    this.id,
    this.answer,
    this.userId,
    this.quizQuestionId,
    this.userTakeQuizId,
  });

  final String? id;
  final String? answer;
  final String? userId;
  final String? quizQuestionId;
  final String? userTakeQuizId;

  factory SubmitQuestionModel.fromJson(Map<String, dynamic> json) {
    return SubmitQuestionModel(
      id: json["id"] ?? "",
      answer: json["answer"] ?? "",
      userId: json["userId"] ?? "",
      quizQuestionId: json["quizQuestionId"] ?? "",
      userTakeQuizId: json["userTakeQuizId"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        answer,
        userId,
        quizQuestionId,
        userTakeQuizId,
      ];
}
