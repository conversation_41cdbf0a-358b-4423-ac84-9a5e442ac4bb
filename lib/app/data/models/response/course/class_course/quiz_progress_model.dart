import 'package:equatable/equatable.dart';

class QuizProgressModel extends Equatable {
  const QuizProgressModel({
    this.totalScore,
    this.totalIncorrect,
    this.id,
    this.quizStatus,
    this.status,
    this.spendTime,
    this.randomQuestionIds,
    this.userId,
    this.quizId,
    this.startDate,
    this.endDate,
    this.userAnswerQuizzes,
  });

  final int? totalScore;
  final int? totalIncorrect;
  final String? quizStatus; // Renamed from 'status' to 'quizStatus' for clarity
  final String? id;
  final String? status;
  final int? spendTime;
  final List<String>? randomQuestionIds;
  final String? userId;
  final String? quizId;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<UserAnswerQuizz>? userAnswerQuizzes;

  factory QuizProgressModel.fromJson(Map<String, dynamic> json) {
    return QuizProgressModel(
      totalScore: json["totalScore"] ?? 0,
      totalIncorrect: json["totalIncorrect"] ?? 0,
      id: json["id"] ?? "",
      quizStatus: json["quizStatus"] ?? "", // Use 'quizStatus' for clarity
      status: json["status"] ?? "",
      spendTime: json["spendTime"] ?? 0,
      randomQuestionIds: json["randomQuestionIds"] == null
          ? []
          : List<String>.from(json["randomQuestionIds"]!.map((x) => x)),
      userId: json["userId"] ?? "",
      quizId: json["quizId"] ?? "",
      startDate: DateTime.tryParse(json["startDate"] ?? ""),
      endDate: DateTime.tryParse(json["endDate"] ?? ""),
      userAnswerQuizzes: json["userAnswerQuizzes"] == null
          ? []
          : List<UserAnswerQuizz>.from(json["userAnswerQuizzes"]!
              .map((x) => UserAnswerQuizz.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        totalScore,
        totalIncorrect,
        id,
        status,
        spendTime,
        randomQuestionIds,
        userId,
        quizId,
        startDate,
        endDate,
        userAnswerQuizzes,
      ];
}

class UserAnswerQuizz extends Equatable {
  const UserAnswerQuizz({
    this.id,
    this.answer,
    this.isCorrect,
    this.weight,
    this.quizQuestion,
  });

  final String? id;
  final String? answer;
  final bool? isCorrect;
  final int? weight;
  final QuizQuestion? quizQuestion;

  factory UserAnswerQuizz.fromJson(Map<String, dynamic> json) {
    return UserAnswerQuizz(
      id: json["id"] ?? "",
      answer: json["answer"] ?? "",
      isCorrect: json["isCorrect"] ?? false,
      weight: json["weight"] ?? 0,
      quizQuestion: json["quizQuestion"] == null
          ? null
          : QuizQuestion.fromJson(json["quizQuestion"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        answer,
        isCorrect,
        weight,
        quizQuestion,
      ];
}

class QuizQuestion extends Equatable {
  const QuizQuestion({
    this.id,
    this.question,
    this.file,
    this.type,
    this.status,
    this.weight,
    this.createdAt,
    this.updatedAt,
    this.quizId,
    this.quizAnswers,
  });

  final String? id;
  final String? question;
  final dynamic file;
  final String? type;
  final String? status;
  final int? weight;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? quizId;
  final List<QuizAnswer>? quizAnswers;

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      id: json["id"] ?? "",
      question: json["question"] ?? "",
      file: json["file"],
      type: json["type"] ?? "",
      status: json["status"] ?? "",
      weight: json["weight"] ?? 0,
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      quizId: json["quizId"] ?? "",
      quizAnswers: json["quizAnswers"] == null
          ? []
          : List<QuizAnswer>.from(
              json["quizAnswers"]!.map((x) => QuizAnswer.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        id,
        question,
        file,
        type,
        status,
        weight,
        createdAt,
        updatedAt,
        quizId,
        quizAnswers,
      ];
}

class QuizAnswer extends Equatable {
  const QuizAnswer({
    this.id,
    this.answer,
    this.isCorrect,
  });

  final String? id;
  final String? answer;
  final bool? isCorrect;

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      id: json["id"] ?? "",
      answer: json["answer"] ?? "",
      isCorrect: json["isCorrect"] ?? false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        answer,
        isCorrect,
      ];
}
