import 'package:equatable/equatable.dart';

class StartQuizModel extends Equatable {
  const StartQuizModel({
    this.id,
    this.status,
    this.randomQuestionIds,
    this.userId,
    this.quizId,
    this.startDate,
    this.endDate,
    this.duration,
  });

  final String? id;
  final String? status;
  final List<String>? randomQuestionIds;
  final String? userId;
  final String? quizId;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? duration;

  factory StartQuizModel.fromJson(Map<String, dynamic> json) {
    return StartQuizModel(
      id: json["id"] ?? "",
      status: json["status"] ?? "",
      randomQuestionIds: json["randomQuestionIds"] == null
          ? []
          : List<String>.from(json["randomQuestionIds"]!.map((x) => x)),
      userId: json["userId"] ?? "",
      quizId: json["quizId"] ?? "",
      startDate: DateTime.tryParse(json["startDate"] ?? ""),
      endDate: DateTime.tryParse(json["endDate"] ?? ""),
      duration: json["duration"] ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        status,
        randomQuestionIds,
        userId,
        quizId,
        startDate,
        endDate,
        duration,
      ];
}
