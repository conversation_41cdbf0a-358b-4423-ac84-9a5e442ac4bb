import 'package:equatable/equatable.dart';

class QuizModel extends Equatable {
  const QuizModel({
    this.id,
    this.title,
    this.file,
    this.description,
    this.startDate,
    this.count,
    this.submapel,
    this.tahunPendidikanId,
    this.duration,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.type,
    this.createdBy,
  });

  final String? id;
  final String? title;
  final String? file;
  final dynamic description;
  final DateTime? startDate;
  final Count? count;
  final Submapel? submapel;
  final String? tahunPendidikanId;
  final int? duration;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;
  final String? type;
  final CreatedBy? createdBy;

  factory QuizModel.fromJson(Map<String, dynamic> json) {
    return QuizModel(
      id: json["id"] ?? "",
      title: json["title"] ?? "",
      file: json["file"] ?? "",
      description: json["description"],
      startDate: DateTime.tryParse(json["startDate"] ?? ""),
      count: json["_count"] == null ? null : Count.fromJson(json["_count"]),
      submapel:
          json["submapel"] == null ? null : Submapel.fromJson(json["submapel"]),
      tahunPendidikanId: json["tahunPendidikanId"] ?? "",
      duration: json["duration"] ?? 0,
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
      type: json["type"] ?? "",
      createdBy: json["createdBy"] == null
          ? null
          : CreatedBy.fromJson(json["createdBy"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        file,
        description,
        startDate,
        count,
        submapel,
        tahunPendidikanId,
        duration,
        createdAt,
        updatedAt,
        status,
        type,
        createdBy,
      ];
}

class Count extends Equatable {
  const Count({
    this.quizQuestions,
  });

  final int? quizQuestions;

  factory Count.fromJson(Map<String, dynamic> json) {
    return Count(
      quizQuestions: json["quizQuestions"] ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        quizQuestions,
      ];
}

class CreatedBy extends Equatable {
  const CreatedBy({
    this.id,
    this.name,
  });

  final String? id;
  final String? name;

  factory CreatedBy.fromJson(Map<String, dynamic> json) {
    return CreatedBy(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
      ];
}

class Submapel extends Equatable {
  const Submapel({
    this.id,
    this.name,
    this.mapel,
  });

  final String? id;
  final String? name;
  final CreatedBy? mapel;

  factory Submapel.fromJson(Map<String, dynamic> json) {
    return Submapel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      mapel: json["mapel"] == null ? null : CreatedBy.fromJson(json["mapel"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        mapel,
      ];
}
