import 'package:equatable/equatable.dart';

class QuizQuestionModel extends Equatable {
  const QuizQuestionModel({
    this.id,
    this.question,
    this.file,
    this.type,
    this.status,
    this.weight,
    this.createdAt,
    this.updatedAt,
    this.quizId,
    this.quizAnswers,
    this.answer,
  });

  final String? id;
  final String? question;
  final dynamic file;
  final String? type;
  final String? status;
  final int? weight;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? quizId;
  final List<QuizAnswer>? quizAnswers;
  final dynamic answer;

  factory QuizQuestionModel.fromJson(Map<String, dynamic> json) {
    return QuizQuestionModel(
      id: json["id"] ?? "",
      question: json["question"] ?? "",
      file: json["file"],
      type: json["type"] ?? "",
      status: json["status"] ?? "",
      weight: json["weight"] ?? 0,
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      quizId: json["quizId"] ?? "",
      quizAnswers: json["quizAnswers"] == null
          ? []
          : List<QuizAnswer>.from(
              json["quizAnswers"]!.map((x) => QuizAnswer.fromJson(x))),
      answer: json["answer"],
    );
  }

  @override
  List<Object?> get props => [
        id,
        question,
        file,
        type,
        status,
        weight,
        createdAt,
        updatedAt,
        quizId,
        quizAnswers,
        answer,
      ];
}

class QuizAnswer extends Equatable {
  const QuizAnswer({
    this.id,
    this.answer,
  });

  final String? id;
  final String? answer;

  factory QuizAnswer.fromJson(Map<String, dynamic> json) {
    return QuizAnswer(
      id: json["id"] ?? "",
      answer: json["answer"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        answer,
      ];
}
