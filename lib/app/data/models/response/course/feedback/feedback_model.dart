import 'package:equatable/equatable.dart';

class FeedbackModel extends Equatable {
  FeedbackModel({
    required this.id,
    required this.content,
    required this.image1,
    required this.image2,
    required this.image3,
    required this.createdAt,
    required this.deletedAt,
    required this.user,
  });

  final String id;
  final String content;
  final String image1;
  final String image2;
  final String image3;
  final DateTime? createdAt;
  final dynamic deletedAt;
  final User? user;

  factory FeedbackModel.fromJson(Map<String, dynamic> json) {
    return FeedbackModel(
      id: json["id"] ?? "",
      content: json["content"] ?? "",
      image1: json["image1"] ?? "",
      image2: json["image2"] ?? "",
      image3: json["image3"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      deletedAt: json["deletedAt"],
      user: json["user"] == null ? null : User.fromJson(json["user"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        content,
        image1,
        image2,
        image3,
        createdAt,
        deletedAt,
        user,
      ];
}

class User extends Equatable {
  User({
    required this.id,
    required this.name,
    required this.imageProfile,
    required this.role,
  });

  final String id;
  final String name;
  final String imageProfile;
  final String role;

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      imageProfile: json["imageProfile"] ?? "",
      role: json["role"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        imageProfile,
        role,
      ];
}
