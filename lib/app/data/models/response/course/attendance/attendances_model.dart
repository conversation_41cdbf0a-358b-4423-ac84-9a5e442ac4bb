import 'package:equatable/equatable.dart';

class AttendanceModel extends Equatable {
  AttendanceModel({
    required this.id,
    required this.date,
    required this.clockIn,
    required this.clockOut,
    required this.clockInPhoto,
    required this.clockOutPhoto,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;
  final DateTime? date;
  final DateTime? clockIn;
  final DateTime? clockOut;
  final String clockInPhoto;
  final String clockOutPhoto;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    return AttendanceModel(
      id: json["id"] ?? "",
      date: DateTime.tryParse(json["date"] ?? ""),
      clockIn: DateTime.tryParse(json["clockIn"] ?? ""),
      clockOut:
          json["clockOut"] != null ? DateTime.tryParse(json["clockOut"]) : null,
      clockInPhoto: json["clockInPhoto"] ?? "",
      clockOutPhoto: json["clockOutPhoto"] ?? "",
      status: json["status"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
    );
  }

  @override
  List<Object?> get props => [
        id,
        date,
        clockIn,
        clockOut,
        clockInPhoto,
        clockOutPhoto,
        status,
        createdAt,
        updatedAt,
      ];
}
