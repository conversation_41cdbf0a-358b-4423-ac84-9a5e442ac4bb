import 'package:equatable/equatable.dart';

class SubmissonAssignmentModel extends Equatable {
  final String? id;
  final String? file;
  final String? status;
  final DateTime? createdAt;
  final Assignment? assignment;

  const SubmissonAssignmentModel({
    this.id,
    this.file,
    this.status,
    this.createdAt,
    this.assignment,
  });

  factory SubmissonAssignmentModel.fromJson(Map<String, dynamic> json) =>
      SubmissonAssignmentModel(
        id: json["id"],
        file: json["file"],
        status: json["status"],
        createdAt: DateTime.parse(json["createdAt"]),
        assignment: Assignment.fromJson(json["assignment"]),
      );

  @override
  List<Object?> get props => [
        id,
        file,
        status,
        createdAt,
        assignment,
      ];
}

class Assignment extends Equatable {
  final String? title;
  final DateTime? endDate;
  final Submapel? submapel;

  const Assignment({
    this.title,
    this.endDate,
    this.submapel,
  });

  factory Assignment.fromJson(Map<String, dynamic> json) => Assignment(
        title: json["title"],
        endDate: DateTime.parse(json["endDate"]),
        submapel: Submapel.fromJson(json["submapel"]),
      );

  @override
  List<Object?> get props => [
        title,
        endDate,
        submapel,
      ];
}

class Submapel extends Equatable {
  final String? name;
  final Mapel? mapel;

  const Submapel({
    this.name,
    this.mapel,
  });

  factory Submapel.fromJson(Map<String, dynamic> json) => Submapel(
        name: json["name"],
        mapel: Mapel.fromJson(json["mapel"]),
      );

  @override
  List<Object?> get props => [
        name,
        mapel,
      ];
}

class Mapel extends Equatable {
  final String? name;

  const Mapel({
    this.name,
  });

  factory Mapel.fromJson(Map<String, dynamic> json) => Mapel(
        name: json["name"],
      );

  @override
  List<Object?> get props => [
        name,
      ];
}
