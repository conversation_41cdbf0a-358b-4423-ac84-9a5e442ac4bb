import 'package:equatable/equatable.dart';

class HanjarModel extends Equatable {
  final String? id;
  final String? title;
  final String? type;
  final String? file;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;
  final SubMapel? subMapel;
  final bool? isCompleted;

  const HanjarModel({
    this.id,
    this.title,
    this.type,
    this.file,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.subMapel,
    this.isCompleted,
  });

  factory HanjarModel.fromJson(Map<String, dynamic> json) => HanjarModel(
        id: json["id"] ?? "",
        title: json["title"] ?? "",
        type: json["type"] ?? "",
        file: json["file"] ?? "",
        createdAt: DateTime.parse(json["createdAt"] ?? ""),
        updatedAt: DateTime.parse(json["updatedAt"] ?? ""),
        status: json["status"] ?? "",
        subMapel: SubMapel.fromJson(json["subMapel"] ?? ""),
        isCompleted: json["isCompleted"] ?? false,
      );

  @override
  List<Object?> get props => [
        id,
        title,
        type,
        file,
        createdAt,
        updatedAt,
        status,
        subMapel,
        isCompleted,
      ];
}

class SubMapel extends Equatable {
  final String? id;
  final String? name;
  final String? code;
  final String? status;

  const SubMapel({
    this.id,
    this.name,
    this.code,
    this.status,
  });

  factory SubMapel.fromJson(Map<String, dynamic> json) => SubMapel(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        status: json["status"],
      );

  @override
  List<Object?> get props => [
        id,
        name,
        code,
        status,
      ];
}
