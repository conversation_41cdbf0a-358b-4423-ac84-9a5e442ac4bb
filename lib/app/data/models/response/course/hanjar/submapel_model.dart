import 'package:equatable/equatable.dart';

class SubmapelModel extends Equatable {
  String? id;
  String? name;
  int? sks;
  int? hn;
  String? code;
  String? status;
  String? userCreatedId;
  CreatedBy? createdBy;
  Count? count;
  Mapel? mapel;

  SubmapelModel({
    this.id,
    this.name,
    this.sks,
    this.hn,
    this.code,
    this.status,
    this.userCreatedId,
    this.createdBy,
    this.count,
    this.mapel,
  });

  factory SubmapelModel.fromJson(Map<String, dynamic> json) => SubmapelModel(
        id: json["id"],
        name: json["name"],
        sks: json["SKS"],
        hn: json["HN"],
        code: json["code"],
        status: json["status"],
        userCreatedId: json["userCreatedId"],
        createdBy: CreatedBy.fromJson(json["createdBy"]),
        count: Count.fromJson(json["_count"]),
        mapel: Mapel.fromJson(json["mapel"]),
      );

  @override
  List<Object?> get props => [
        id,
        name,
        sks,
        hn,
        code,
        status,
        userCreatedId,
        createdBy,
        count,
        mapel,
      ];
}

class Count extends Equatable {
  int? assignments;

  Count({
    this.assignments,
  });

  factory Count.fromJson(Map<String, dynamic> json) => Count(
        assignments: json["assignments"],
      );

  @override
  List<Object?> get props => [assignments];
}

class CreatedBy extends Equatable {
  String? id;
  String? name;
  dynamic imageProfile;

  CreatedBy({
    this.id,
    this.name,
    this.imageProfile,
  });

  factory CreatedBy.fromJson(Map<String, dynamic> json) => CreatedBy(
        id: json["id"],
        name: json["name"],
        imageProfile: json["imageProfile"],
      );

  @override
  List<Object?> get props => [
        id,
        name,
        imageProfile,
      ];
}

class Mapel extends Equatable {
  String? id;
  String? name;
  Matkul? matkul;

  Mapel({
    this.id,
    this.name,
    this.matkul,
  });

  factory Mapel.fromJson(Map<String, dynamic> json) => Mapel(
        id: json["id"],
        name: json["name"],
        matkul: Matkul.fromJson(json["matkul"]),
      );

  @override
  List<Object?> get props => [
        id,
        name,
        matkul,
      ];
}

class Matkul extends Equatable {
  String? id;
  String? name;
  String? tahunPendidikanId;

  Matkul({
    this.id,
    this.name,
    this.tahunPendidikanId,
  });

  factory Matkul.fromJson(Map<String, dynamic> json) => Matkul(
        id: json["id"],
        name: json["name"],
        tahunPendidikanId: json["tahunPendidikanId"],
      );

  @override
  List<Object?> get props => [
        id,
        name,
        tahunPendidikanId,
      ];
}
