import 'package:equatable/equatable.dart';

class PersonalityModel extends Equatable {
  PersonalityModel({
    required this.id,
    required this.name,
    required this.instructorName,
    required this.instructorRank,
    required this.instructorNrp,
    required this.nrp,
    required this.nosis,
    required this.pangkat,
    required this.defaultScore,
    required this.finalScore,
    required this.scoreKualitatif,
    required this.aspects,
  });

  final String id;
  final String name;
  final String instructorName;
  final String instructorRank;
  final String instructorNrp;
  final String nrp;
  final String nosis;
  final String pangkat;
  final int defaultScore;
  final double finalScore;
  final String scoreKualitatif;
  final List<Aspect> aspects;

  factory PersonalityModel.fromJson(Map<String, dynamic> json) {
    final instructor = json["instructor"] ?? {};

    return PersonalityModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      instructorName: instructor["name"] ?? "",
      instructorRank: instructor["rank"] ?? "",
      instructorNrp: instructor["nrp"] ?? "",
      nrp: json["NRP"] ?? "",
      nosis: json["nosis"] ?? "",
      pangkat: json["pangkat"] ?? "",
      defaultScore: json["defaultScore"] ?? 0,
      finalScore: (json["finalScore"] ?? 0.0).toDouble(),
      scoreKualitatif: json["scoreKualitatif"] ?? "",
      aspects: (json["aspects"] ?? [])
          .map<Aspect>((x) => Aspect.fromJson(x))
          .toList(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        instructorName,
        instructorRank,
        instructorNrp,
        nrp,
        nosis,
        pangkat,
        defaultScore,
        finalScore,
        scoreKualitatif,
        aspects,
      ];
}

class Aspect extends Equatable {
  Aspect({
    required this.id,
    required this.name,
    required this.score,
  });

  final String id;
  final String name;
  final double score;

  factory Aspect.fromJson(Map<String, dynamic> json) {
    return Aspect(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      score: (json["score"] as num?)?.toDouble() ?? 0.0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        score,
      ];
}
