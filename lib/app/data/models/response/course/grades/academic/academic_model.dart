import 'package:equatable/equatable.dart';

class AcademicModel extends Equatable {
  const AcademicModel({
    required this.id,
    required this.name,
    required this.nrp,
    required this.nosis,
    required this.pangkat,
    required this.academicGrades,
  });

  final String id;
  final String name;
  final String nrp;
  final String nosis;
  final String pangkat;
  final List<AcademicGrade> academicGrades;

  factory AcademicModel.fromJson(Map<String, dynamic> json) {
    return AcademicModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      nrp: json["NRP"] ?? "",
      nosis: json["nosis"] ?? "",
      pangkat: json["pangkat"] ?? "",
      academicGrades: json["academicGrades"] == null
          ? []
          : List<AcademicGrade>.from(
              json["academicGrades"]!.map((x) => AcademicGrade.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nrp,
        nosis,
        pangkat,
        academicGrades,
      ];
}

class AcademicGrade extends Equatable {
  AcademicGrade({
    required this.matkulId,
    required this.matkulName,
    required this.mapels,
  });

  final String matkulId;
  final String matkulName;
  final List<Mapel> mapels;

  factory AcademicGrade.fromJson(Map<String, dynamic> json) {
    return AcademicGrade(
      matkulId: json["matkulId"] ?? "",
      matkulName: json["matkulName"] ?? "",
      mapels: json["mapels"] == null
          ? []
          : List<Mapel>.from(json["mapels"]!.map((x) => Mapel.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        matkulId,
        matkulName,
        mapels,
      ];
}

class Mapel extends Equatable {
  Mapel({
    required this.mapelId,
    required this.mapelName,
    required this.subMapels,
  });

  final String mapelId;
  final String mapelName;
  final List<SubMapel> subMapels;

  factory Mapel.fromJson(Map<String, dynamic> json) {
    return Mapel(
      mapelId: json["mapelId"] ?? "",
      mapelName: json["mapelName"] ?? "",
      subMapels: json["subMapels"] == null
          ? []
          : List<SubMapel>.from(
              json["subMapels"]!.map((x) => SubMapel.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        mapelId,
        mapelName,
        subMapels,
      ];
}

class SubMapel extends Equatable {
  SubMapel({
    required this.id,
    required this.name,
    required this.nilaiPelajaran,
    required this.hargaNilai,
    required this.nilaiPrestasi,
    required this.assessments,
  });

  final String id;
  final String name;
  final String nilaiPelajaran;
  final int hargaNilai;
  final String nilaiPrestasi;
  final List<Assessment> assessments;

  factory SubMapel.fromJson(Map<String, dynamic> json) {
    return SubMapel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      nilaiPelajaran: json["nilaiPelajaran"] ?? "",
      hargaNilai: json["hargaNilai"] ?? 0,
      nilaiPrestasi: json["nilaiPrestasi"] ?? "",
      assessments: json["assessments"] == null
          ? []
          : List<Assessment>.from(
              json["assessments"]!.map((x) => Assessment.fromJson(x))),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nilaiPelajaran,
        hargaNilai,
        nilaiPrestasi,
        assessments,
      ];
}

class Assessment extends Equatable {
  Assessment({
    required this.type,
    required this.scores,
    required this.averageScore,
  });

  final String type;
  final List<Score> scores;
  final String averageScore;

  factory Assessment.fromJson(Map<String, dynamic> json) {
    return Assessment(
      type: json["type"] ?? "",
      scores: json["scores"] == null
          ? []
          : List<Score>.from(json["scores"]!.map((x) => Score.fromJson(x))),
      averageScore: json["averageScore"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        type,
        scores,
        averageScore,
      ];
}

class Score extends Equatable {
  Score({
    required this.name,
    required this.score,
  });

  final String name;
  final String score;

  factory Score.fromJson(Map<String, dynamic> json) {
    return Score(
      name: json["name"] ?? "",
      score: json["score"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        name,
        score,
      ];
}
