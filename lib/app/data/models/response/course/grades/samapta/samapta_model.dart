import 'package:equatable/equatable.dart';

class SamaptaModel extends Equatable {
  SamaptaModel({
    required this.id,
    required this.name,
    required this.instructorN<PERSON>,
    required this.instructorNrp,
    required this.nrp,
    required this.nosis,
    required this.pangkat,
    required this.age,
    required this.bloodPressure,
    required this.heightWeight,
    required this.scoreA,
    required this.scoreB,
    required this.scoreAb,
    required this.finalScore,
    required this.scoreKualitatif,
    required this.aspectsByType,
  });

  final String id;
  final String name;
  final String instructorName;
  final String instructorNrp;
  final String nrp;
  final String nosis;
  final String pangkat;
  final int age;
  final String bloodPressure;
  final String heightWeight;
  final int scoreA;
  final int scoreB;
  final double scoreAb;
  final double finalScore;
  final String scoreKualitatif;
  final List<AspectsByType> aspectsByType;

  factory SamaptaModel.fromJson(Map<String, dynamic> json) {
    final instructor = json["instructor"] ?? {};
    return SamaptaModel(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      instructor<PERSON>ame: instructor["name"] ?? "",
      instructorNrp: instructor["nrp"] ?? "",
      nrp: json["NRP"] ?? "",
      nosis: json["nosis"] ?? "",
      pangkat: json["pangkat"] ?? "",
      age: json["age"] ?? 0,
      bloodPressure: json["bloodPressure"] ?? "",
      heightWeight: json["heightWeight"] ?? "",
      scoreA: json["scoreA"] ?? 0,
      scoreB: json["scoreB"] ?? 0,
      scoreAb: (json["scoreAB"] ?? 0).toDouble(),
      finalScore: (json["finalScore"] ?? 0).toDouble(),
      scoreKualitatif: json["scoreKualitatif"] ?? "",
      aspectsByType: (json["aspectsByType"] ?? [])
          .map<AspectsByType>((x) => AspectsByType.fromJson(x))
          .toList(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        instructorName,
        instructorNrp,
        nrp,
        nosis,
        pangkat,
        age,
        bloodPressure,
        heightWeight,
        scoreA,
        scoreB,
        scoreAb,
        finalScore,
        scoreKualitatif,
        aspectsByType,
      ];
}

class AspectsByType extends Equatable {
  AspectsByType({
    required this.type,
    required this.aspects,
  });

  final String type;
  final List<Aspect> aspects;

  factory AspectsByType.fromJson(Map<String, dynamic> json) {
    return AspectsByType(
      type: json["type"] ?? "",
      aspects: (json["aspects"] ?? [])
          .map<Aspect>((x) => Aspect.fromJson(x))
          .toList(),
    );
  }

  @override
  List<Object?> get props => [
        type,
        aspects,
      ];
}

class Aspect extends Equatable {
  Aspect({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.point,
    required this.score,
  });

  final String id;
  final String name;
  final String description;
  final String type;
  final int point;
  final int score;

  factory Aspect.fromJson(Map<String, dynamic> json) {
    return Aspect(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      type: json["type"] ?? "",
      point: json["point"] ?? 0,
      score: json["score"] ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        point,
        score,
      ];
}
