import 'package:equatable/equatable.dart';

class AccumulatedScore extends Equatable {
  const AccumulatedScore({
    required this.id,
    required this.name,
    required this.nrp,
    required this.nosis,
    required this.pangkat,
    required this.imageProfile,
    required this.scores,
  });

  final String id;
  final String name;
  final String nrp;
  final String nosis;
  final String pangkat;
  final String imageProfile;
  final Map<String, double> scores;

  factory AccumulatedScore.fromJson(Map<String, dynamic> json) {
    return AccumulatedScore(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
      nrp: json["NRP"] ?? "",
      nosis: json["nosis"] ?? "",
      pangkat: json["pangkat"] ?? "",
      imageProfile: json["imageProfile"] ?? "",
      scores: Map<String, double>.from(json["scores"].map(
        (key, value) => MapEntry(key, (value as num).toDouble()),
      )),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        nrp,
        nosis,
        pangkat,
        imageProfile,
        scores,
      ];
}
