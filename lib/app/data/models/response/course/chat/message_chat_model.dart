class MessageChatModel {
  MessageChatModel({
    required this.id,
    required this.content,
    required this.status,
    required this.replyToId,
    this.sender,
    this.receiver,
    this.replyTo,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String content;
  final String status;
  final dynamic replyToId;
  final Receiver? sender;
  final Receiver? receiver;
  final ReplyTo? replyTo;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory MessageChatModel.fromJson(Map<String, dynamic> json) {
    return MessageChatModel(
      id: json["id"] ?? "",
      content: json["content"] ?? "",
      status: json["status"] ?? "",
      replyToId: json["replyToId"],
      sender: json["sender"] == null ? null : Receiver.fromJson(json["sender"]),
      receiver:
          json["receiver"] == null ? null : Receiver.fromJson(json["receiver"]),
      replyTo:
          json["replyTo"] == null ? null : ReplyTo.fromJson(json["replyTo"]),
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
    );
  }

  MessageChatModel copyWith({
    String? id,
    String? content,
    String? status,
    dynamic replyToId,
    Receiver? sender,
    Receiver? receiver,
    ReplyTo? replyTo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MessageChatModel(
      id: id ?? this.id,
      content: content ?? this.content,
      status: status ?? this.status,
      replyToId: replyToId ?? this.replyToId,
      sender: sender ?? this.sender,
      receiver: receiver ?? this.receiver,
      replyTo: replyTo ?? this.replyTo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class Receiver {
  Receiver({
    required this.id,
    required this.name,
  });

  final String id;
  final String name;

  factory Receiver.fromJson(Map<String, dynamic> json) {
    return Receiver(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
    );
  }

  Receiver copyWith({
    String? id,
    String? name,
  }) {
    return Receiver(
      id: id ?? this.id,
      name: name ?? this.name,
    );
  }
}

class ReplyTo {
  ReplyTo({
    required this.id,
    required this.content,
    required this.status,
    required this.senderId,
    required this.receiverId,
    required this.replyToId,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;
  final String content;
  final String status;
  final String senderId;
  final String receiverId;
  final dynamic replyToId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory ReplyTo.fromJson(Map<String, dynamic> json) {
    return ReplyTo(
      id: json["id"] ?? "",
      content: json["content"] ?? "",
      status: json["status"] ?? "",
      senderId: json["senderId"] ?? "",
      receiverId: json["receiverId"] ?? "",
      replyToId: json["replyToId"],
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
    );
  }

  ReplyTo copyWith({
    String? id,
    String? content,
    String? status,
    String? senderId,
    String? receiverId,
    dynamic replyToId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReplyTo(
      id: id ?? this.id,
      content: content ?? this.content,
      status: status ?? this.status,
      senderId: senderId ?? this.senderId,
      receiverId: receiverId ?? this.receiverId,
      replyToId: replyToId ?? this.replyToId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
