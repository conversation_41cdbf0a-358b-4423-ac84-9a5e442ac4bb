import 'package:equatable/equatable.dart';

class ListChatUserModel extends Equatable {
  const ListChatUserModel({
    this.id,
    this.nrp,
    this.name,
    this.role,
    this.imageProfile,
  });

  final String? id;
  final String? nrp;
  final String? name;
  final String? role;
  final String? imageProfile;

  factory ListChatUserModel.fromJson(Map<String, dynamic> json) {
    return ListChatUserModel(
      id: json["id"] ?? "",
      nrp: json["NRP"] ?? "",
      name: json["name"] ?? "",
      role: json["role"] ?? "",
      imageProfile: json["imageProfile"],
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        name,
        role,
        imageProfile,
      ];
}
