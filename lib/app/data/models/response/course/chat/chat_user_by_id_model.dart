class ChatUserByIdModel {
  ChatUserByIdModel({
    this.user,
    this.lastMessage,
    this.totalDelivered,
  });

  final User? user;
  final LastMessage? lastMessage;
  final int? totalDelivered;

  factory ChatUserByIdModel.fromJson(Map<String, dynamic> json) {
    return ChatUserByIdModel(
      user: json["user"] == null ? null : User.from<PERSON><PERSON>(json["user"]),
      lastMessage: json["lastMessage"] == null
          ? null
          : LastMessage.fromJson(json["lastMessage"]),
      totalDelivered: json["totalDelivered"] ?? 0,
    );
  }
}

class LastMessage {
  LastMessage({
    this.content,
    this.createdAt,
    this.status,
    this.senderId,
    this.receiverId,
  });

  final String? content;
  final DateTime? createdAt;
  final String? status;
  final String? senderId;
  final String? receiverId;

  factory LastMessage.fromJson(Map<String, dynamic> json) {
    return LastMessage(
      content: json["content"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      status: json["status"] ?? "",
      senderId: json["senderId"] ?? "",
      receiverId: json["receiverId"] ?? "",
    );
  }
}

class User {
  User({
    this.id,
    this.nrp,
    this.imageProfile,
    this.name,
  });

  final String? id;
  final String? nrp;
  final String? imageProfile;
  final String? name;

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"] ?? "",
      nrp: json["NRP"] ?? "",
      imageProfile: json["imageProfile"],
      name: json["name"] ?? "",
    );
  }
}
