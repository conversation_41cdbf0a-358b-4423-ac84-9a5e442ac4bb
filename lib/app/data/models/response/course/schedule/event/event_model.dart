class CalendarEventResponse {
  final bool status;
  final String message;
  final List<CalendarEvent> data;
  final Pagination pagination;

  CalendarEventResponse({
    required this.status,
    required this.message,
    required this.data,
    required this.pagination,
  });

  factory CalendarEventResponse.fromJson(Map<String, dynamic> json) =>
      CalendarEventResponse(
        status: json['status'],
        message: json['message'],
        data: List<CalendarEvent>.from(
            json['data'].map((x) => CalendarEvent.fromJson(x))),
        pagination: Pagination.fromJson(json['pagination']),
      );
}

class CalendarEvent {
  final String id;
  final String title;
  final String? description;
  final DateTime date;
  final String startTime;
  final String endTime;
  final String? location;
  final String? thumbnail;
  final String? note;
  final String participantType;
  final String? file;
  final List<User> users;
  final TahunPendidikan? tahunPendidikan;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? eventStatus; // ✅ ditambahkan

  CalendarEvent({
    required this.id,
    required this.title,
    this.description,
    required this.date,
    required this.startTime,
    required this.endTime,
    this.location,
    this.thumbnail,
    this.note,
    required this.participantType,
    this.file,
    required this.users,
    this.tahunPendidikan,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.eventStatus,
  });

  factory CalendarEvent.fromJson(Map<String, dynamic> json) => CalendarEvent(
        id: json['id'],
        title: json['title'],
        description: json['description'],
        date: DateTime.parse(json['date']),
        startTime: json['startTime'],
        endTime: json['endTime'],
        location: json['location'],
        thumbnail: json['thumbnail'],
        note: json['note'],
        participantType: json['participantType'],
        file: json['file'],
        users: List<User>.from(json['users'].map((x) => User.fromJson(x))),
        tahunPendidikan: json['tahunPendidikan'] != null
            ? TahunPendidikan.fromJson(json['tahunPendidikan'])
            : null,
        status: json['status'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        eventStatus: json['eventStatus'],
      );
}

class User {
  final String id;
  final String name;

  User({
    required this.id,
    required this.name,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'],
        name: json['name'],
      );
}

class TahunPendidikan {
  final String id;
  final String year;
  final String force;
  final String jenisPendidikan;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status;

  TahunPendidikan({
    required this.id,
    required this.year,
    required this.force,
    required this.jenisPendidikan,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  factory TahunPendidikan.fromJson(Map<String, dynamic> json) =>
      TahunPendidikan(
        id: json['id'],
        year: json['year'],
        force: json['force'],
        jenisPendidikan: json['jenisPendidikan'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        status: json['status'],
      );
}

class Pagination {
  final int page;
  final int limit;
  final int totalRows;
  final int totalPages;

  Pagination({
    required this.page,
    required this.limit,
    required this.totalRows,
    required this.totalPages,
  });

  factory Pagination.fromJson(Map<String, dynamic> json) => Pagination(
        page: json['page'],
        limit: json['limit'],
        totalRows: json['totalRows'],
        totalPages: json['totalPages'],
      );
}
