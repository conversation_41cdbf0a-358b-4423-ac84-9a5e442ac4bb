class ScheduleModel {
  final bool status;
  final String message;
  final List<MonthlySchedule> datas;

  ScheduleModel({
    required this.status,
    required this.message,
    required this.datas,
  });

  factory ScheduleModel.fromJson(Map<String, dynamic> json) {
    return ScheduleModel(
      status: json["status"] ?? false,
      message: json["message"] ?? '',
      datas: List<MonthlySchedule>.from(
        (json["datas"] ?? []).map((x) => MonthlySchedule.fromJson(x)),
      ),
    );
  }
}

class MonthlySchedule {
  final String id;
  final DateTime date;
  final String location;
  final String metode;
  final Mapel mapel;
  final PBM pbmStart;
  final PBM pbmEnd;
  final Lecturer lecturer;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  MonthlySchedule({
    required this.id,
    required this.date,
    required this.location,
    required this.metode,
    required this.mapel,
    required this.pbmStart,
    required this.pbmEnd,
    required this.lecturer,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MonthlySchedule.from<PERSON>son(Map<String, dynamic> json) {
    return MonthlySchedule(
      id: json["id"] ?? '',
      date: DateTime.tryParse(json["date"] ?? '') ?? DateTime.now(),
      location: json["location"] ?? '',
      metode: json["metode"] ?? '',
      mapel: Mapel.fromJson(json["mapel"] ?? {}),
      pbmStart: PBM.fromJson(json["pbmStart"] ?? {}),
      pbmEnd: PBM.fromJson(json["pbmEnd"] ?? {}),
      lecturer: Lecturer.fromJson(json["lecturer"] ?? {}),
      status: json["status"] ?? '',
      createdAt: DateTime.tryParse(json["createdAt"] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? '') ?? DateTime.now(),
    );
  }
}

class Mapel {
  final String id;
  final String name;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String userCreatedId;
  final String? matkulId;
  final List<SubMapel> subMapels;

  Mapel({
    required this.id,
    required this.name,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.userCreatedId,
    required this.matkulId,
    required this.subMapels,
  });

  factory Mapel.fromJson(Map<String, dynamic> json) {
    return Mapel(
      id: json["id"] ?? '',
      name: json["name"] ?? '',
      status: json["status"] ?? '',
      createdAt: DateTime.tryParse(json["createdAt"] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? '') ?? DateTime.now(),
      userCreatedId: json["userCreatedId"] ?? '',
      matkulId: json["matkulId"],
      subMapels: List<SubMapel>.from(
        (json["subMapels"] ?? []).map((x) => SubMapel.fromJson(x)),
      ),
    );
  }
}

class SubMapel {
  final String id;
  final String name;
  final int sks;
  final int hn;
  final String code;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<Hanjar> hanjars;

  SubMapel({
    required this.id,
    required this.name,
    required this.sks,
    required this.hn,
    required this.code,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.hanjars,
  });

  factory SubMapel.fromJson(Map<String, dynamic> json) {
    return SubMapel(
      id: json["id"] ?? '',
      name: json["name"] ?? '',
      sks: json["SKS"] ?? 0,
      hn: json["HN"] ?? 0,
      code: json["code"] ?? '',
      status: json["status"] ?? '',
      createdAt: DateTime.tryParse(json["createdAt"] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? '') ?? DateTime.now(),
      hanjars: List<Hanjar>.from(
        (json["hanjars"] ?? []).map((x) => Hanjar.fromJson(x)),
      ),
    );
  }
}

class Hanjar {
  final String id;
  final String title;
  final String type;
  final String file;
  final String status;
  final DateTime createdAt;
  final DateTime updatedAt;

  Hanjar({
    required this.id,
    required this.title,
    required this.type,
    required this.file,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Hanjar.fromJson(Map<String, dynamic> json) {
    return Hanjar(
      id: json["id"] ?? '',
      title: json["title"] ?? '',
      type: json["type"] ?? '',
      file: json["file"] ?? '',
      status: json["status"] ?? '',
      createdAt: DateTime.tryParse(json["createdAt"] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? '') ?? DateTime.now(),
    );
  }
}

class PBM {
  final String id;
  final String name;
  final String day;
  final DateTime startTime;
  final DateTime endTime;
  final DateTime createdAt;
  final DateTime updatedAt;

  PBM({
    required this.id,
    required this.name,
    required this.day,
    required this.startTime,
    required this.endTime,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PBM.fromJson(Map<String, dynamic> json) {
    return PBM(
      id: json["id"] ?? '',
      name: json["name"] ?? '',
      day: json["day"] ?? '',
      startTime: DateTime.tryParse(json["startTime"] ?? '') ?? DateTime.now(),
      endTime: DateTime.tryParse(json["endTime"] ?? '') ?? DateTime.now(),
      createdAt: DateTime.tryParse(json["createdAt"] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? '') ?? DateTime.now(),
    );
  }
}

class Lecturer {
  final String id;
  final String name;
  final String? imageProfile;

  Lecturer({
    required this.id,
    required this.name,
    this.imageProfile,
  });

  factory Lecturer.fromJson(Map<String, dynamic> json) {
    return Lecturer(
      id: json["id"] ?? '',
      name: json["name"] ?? '',
      imageProfile: json["imageProfile"],
    );
  }
}
