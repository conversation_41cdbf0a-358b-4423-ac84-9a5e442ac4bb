import 'package:equatable/equatable.dart';

class AssignmentModel extends Equatable {
  final String? id;
  final String? title;
  final String? file;
  final String? thumbnail;
  final String? description;
  final String? subMapelId;
  final DateTime? startDate;
  final DateTime? endDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;
  final SubMapel? subMapel;
  final TahunPendidikan? tahunPendidikan;
  final CreatedBy? createdBy;
  final int? timeRemaining;
  final int? completion;
  final int? totalStudent;

  const AssignmentModel({
    this.id,
    this.title,
    this.file,
    this.thumbnail,
    this.description,
    this.subMapelId,
    this.startDate,
    this.endDate,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.subMapel,
    this.tahunPendidikan,
    this.createdBy,
    this.timeRemaining,
    this.completion,
    this.totalStudent,
  });

  factory AssignmentModel.fromJson(Map<String, dynamic> json) =>
      AssignmentModel(
        id: json["id"] ?? "",
        title: json["title"] ?? "",
        file: json["file"] ?? "",
        thumbnail: json["thumbnail"] ?? "",
        description: json["description"] ?? "",
        subMapelId: json["subMapelId"] ?? "",
        startDate: DateTime.parse(json["startDate"] ?? ""),
        endDate: DateTime.parse(json["endDate"] ?? ""),
        createdAt: DateTime.parse(json["createdAt"] ?? ""),
        updatedAt: DateTime.parse(json["updatedAt"] ?? ""),
        status: json["status"] ?? "",
        subMapel: SubMapel.fromJson(json["subMapel"] ?? ""),
        tahunPendidikan:
            TahunPendidikan.fromJson(json["tahunPendidikan"] ?? ""),
        createdBy: CreatedBy.fromJson(json["createdBy"] ?? ""),
        timeRemaining: json["timeRemaining"] ?? "",
        completion: json["completion"] ?? "",
        totalStudent: json["totalStudent"] ?? "",
      );

  @override
  List<Object?> get props => [
        id,
        title,
        file,
        thumbnail,
        description,
        subMapelId,
        startDate,
        endDate,
        createdAt,
        updatedAt,
        status,
        subMapel,
        tahunPendidikan,
        createdBy,
        timeRemaining,
        completion,
        totalStudent,
      ];
}

class CreatedBy extends Equatable {
  final String? id;
  final String? name;
  final String? nrp;
  final dynamic imageProfile;

  const CreatedBy({
    this.id,
    this.name,
    this.nrp,
    this.imageProfile,
  });

  factory CreatedBy.fromJson(Map<String?, dynamic> json) => CreatedBy(
        id: json["id"] ?? "",
        name: json["name"] ?? "",
        nrp: json["NRP"] ?? "",
        imageProfile: json["imageProfile"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "NRP": nrp,
        "imageProfile": imageProfile,
      };

  @override
  List<Object?> get props => [
        id,
        name,
        nrp,
        imageProfile,
      ];
}

class SubMapel extends Equatable {
  final String? id;
  final String? name;
  final Mapel? mapel;

  const SubMapel({
    this.id,
    this.name,
    this.mapel,
  });

  factory SubMapel.fromJson(Map<String, dynamic> json) => SubMapel(
        id: json["id"] ?? "",
        name: json["name"] ?? "",
        mapel: Mapel.fromJson(json["mapel"] ?? ""),
      );

  @override
  List<Object?> get props => [
        id,
        name,
        mapel,
      ];
}

class Mapel extends Equatable {
  final String? id;
  final String? name;
  final Matkul? matkul;

  const Mapel({
    this.id,
    this.name,
    this.matkul,
  });

  factory Mapel.fromJson(Map<String, dynamic> json) => Mapel(
        id: json["id"] ?? "",
        name: json["name"] ?? "",
        matkul: Matkul.fromJson(json["matkul"] ?? ""),
      );

  @override
  List<Object?> get props => [
        id,
        name,
        matkul,
      ];
}

class Matkul extends Equatable {
  final String? id;
  final String? name;

  const Matkul({
    this.id,
    this.name,
  });

  factory Matkul.fromJson(Map<String, dynamic> json) => Matkul(
        id: json["id"] ?? "",
        name: json["name"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };

  @override
  List<Object?> get props => [
        id,
        name,
      ];
}

class TahunPendidikan extends Equatable {
  final String? id;
  final String? year;
  final String? jenisPendidikan;

  const TahunPendidikan({
    this.id,
    this.year,
    this.jenisPendidikan,
  });

  factory TahunPendidikan.fromJson(Map<String, dynamic> json) =>
      TahunPendidikan(
        id: json["id"],
        year: json["year"],
        jenisPendidikan: json["jenisPendidikan"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "year": year,
        "jenisPendidikan": jenisPendidikan,
      };

  @override
  List<Object?> get props => [
        id,
        year,
        jenisPendidikan,
      ];
}
