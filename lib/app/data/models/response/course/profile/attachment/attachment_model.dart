import 'package:equatable/equatable.dart';

class AttachmentModel extends Equatable {
  AttachmentModel({
    this.id,
    this.userId,
    this.name,
    this.file,
    this.createdAt,
    this.updatedAt,
    this.status,
  });

  final String? id;
  final String? userId;
  final String? name;
  final String? file;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;

  factory AttachmentModel.fromJson(Map<String, dynamic> json) {
    return AttachmentModel(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      name: json["name"] ?? "",
      file: json["file"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        file,
        createdAt,
        updatedAt,
        status,
      ];
}
