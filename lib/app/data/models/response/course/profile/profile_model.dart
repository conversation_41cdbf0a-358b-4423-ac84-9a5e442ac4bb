import 'package:equatable/equatable.dart';

class ProfileModel extends Equatable {
  const ProfileModel({
    this.id,
    this.nrp,
    this.nosis,
    this.name,
    this.username,
    this.email,
    this.phoneNumber,
    this.imageProfile,
    this.gender,
    this.pangkat,
    this.tahunPendidikan,
    this.status,
    this.personalProfile,
    this.generalEducations = const [],
    this.militaryEducations = const [],
    this.courses = const [],
    this.languageSpokens = const [],
    this.rankHistories = const [],
    this.familyInformation,
    this.attachments = const [],
    this.profileCompletionPercentage = 0,
  });

  final String? id;
  final String? nrp;
  final String? nosis;
  final String? name;
  final String? username;
  final String? email;
  final String? phoneNumber;
  final String? imageProfile;
  final String? gender;
  final Pangkat? pangkat;
  final TahunPendidikan? tahunPendidikan;
  final String? status;
  final Map<String, dynamic>? personalProfile;
  final List<Map<String, dynamic>> generalEducations;
  final List<Map<String, dynamic>> militaryEducations;
  final List<Map<String, dynamic>> courses;
  final List<Map<String, dynamic>> languageSpokens;
  final List<Map<String, dynamic>> rankHistories;
  final Map<String, dynamic>? familyInformation;
  final List<Map<String, dynamic>> attachments;
  final int profileCompletionPercentage;

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      id: json["id"] ?? "",
      nrp: json["NRP"] ?? "",
      nosis: json["nosis"] ?? "",
      name: json["name"] ?? "",
      username: json["username"] ?? "",
      email: json["email"] ?? "",
      phoneNumber: json["phoneNumber"] ?? "",
      imageProfile: json["imageProfile"] ?? "",
      gender: json["gender"] ?? "",
      pangkat:
          json["pangkat"] != null ? Pangkat.fromJson(json["pangkat"]) : null,
      tahunPendidikan: json["tahunPendidikan"] != null
          ? TahunPendidikan.fromJson(json["tahunPendidikan"])
          : null,
      status: json["status"] ?? "",
      personalProfile: json["personalProfile"] ?? {},
      generalEducations: List<Map<String, dynamic>>.from(
          json["generalEducations"]?.map((x) => x) ?? []),
      militaryEducations: List<Map<String, dynamic>>.from(
          json["militaryEducations"]?.map((x) => x) ?? []),
      courses:
          List<Map<String, dynamic>>.from(json["courses"]?.map((x) => x) ?? []),
      languageSpokens: List<Map<String, dynamic>>.from(
          json["languageSpokens"]?.map((x) => x) ?? []),
      rankHistories: List<Map<String, dynamic>>.from(
          json["rankHistories"]?.map((x) => x) ?? []),
      familyInformation: json["familyInformation"] ?? {},
      attachments: List<Map<String, dynamic>>.from(
          json["attachments"]?.map((x) => x) ?? []),
      profileCompletionPercentage: json["profileCompletionPercentage"] ?? 0,
    );
  }

  @override
  List<Object?> get props => [
        id,
        nrp,
        nosis,
        name,
        username,
        email,
        phoneNumber,
        imageProfile,
        gender,
        pangkat,
        tahunPendidikan,
        status,
        personalProfile,
        generalEducations,
        militaryEducations,
        courses,
        languageSpokens,
        rankHistories,
        familyInformation,
        attachments,
        profileCompletionPercentage,
      ];
}

class Pangkat extends Equatable {
  const Pangkat({
    required this.id,
    required this.name,
  });

  final String id;
  final String name;

  factory Pangkat.fromJson(Map<String, dynamic> json) {
    return Pangkat(
      id: json["id"] ?? "",
      name: json["name"] ?? "",
    );
  }

  @override
  List<Object?> get props => [id, name];
}

class TahunPendidikan extends Equatable {
  const TahunPendidikan({
    required this.id,
    required this.year,
    required this.force,
    required this.jenisPendidikan,
  });

  final String id;
  final String year;
  final String force;
  final String jenisPendidikan;

  factory TahunPendidikan.fromJson(Map<String, dynamic> json) {
    return TahunPendidikan(
      id: json["id"] ?? "",
      year: json["year"] ?? "",
      force: json["force"] ?? "",
      jenisPendidikan: json["jenisPendidikan"] ?? "",
    );
  }

  @override
  List<Object?> get props => [id, year, force, jenisPendidikan];
}
