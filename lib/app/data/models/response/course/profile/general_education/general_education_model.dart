import 'package:equatable/equatable.dart';

class GeneralEducationModel extends Equatable {
  const GeneralEducationModel({
    this.id,
    this.userId,
    this.schoolName,
    this.yearCompleted,
    this.diploma,
    this.createdAt,
    this.updatedAt,
    this.status,
  });

  final String? id;
  final String? userId;
  final String? schoolName;
  final int? yearCompleted;
  final String? diploma;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;

  factory GeneralEducationModel.fromJson(Map<String, dynamic> json) {
    return GeneralEducationModel(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      schoolName: json["schoolName"] ?? "",
      yearCompleted: json["yearCompleted"] ?? 0,
      diploma: json["diploma"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        schoolName,
        yearCompleted,
        diploma,
        createdAt,
        updatedAt,
        status,
      ];
}
