import 'package:equatable/equatable.dart';

class FamilyInformationModel extends Equatable {
  FamilyInformationModel({
    this.id,
    this.userId,
    this.fatherName,
    this.fatherOccupation,
    this.motherName,
    this.motherMobileNumber,
    this.parentsAddress,
    this.spouseName,
    this.spousePlaceOfBirth,
    this.spouseDateOfBirth,
    this.spousePlaceOfMarriage,
    this.spouseDateOfMarriage,
    this.spouseOccupation,
    this.spouseEducationTitle,
    this.spousePhoneNumber,
    this.spouseHomeAddress,
    this.fatherInLawName,
    this.fatherInLawOccupation,
    this.motherInLawName,
    this.motherInLawPhoneNumber,
    this.inLawsAddress,
    this.createdAt,
    this.updatedAt,
    this.status,
  });

  final String? id;
  final String? userId;
  final String? fatherName;
  final String? fatherOccupation;
  final String? motherName;
  final String? motherMobileNumber;
  final String? parentsAddress;
  final String? spouseName;
  final String? spousePlaceOfBirth;
  final DateTime? spouseDateOfBirth;
  final String? spousePlaceOfMarriage;
  final DateTime? spouseDateOfMarriage;
  final String? spouseOccupation;
  final String? spouseEducationTitle;
  final String? spousePhoneNumber;
  final String? spouseHomeAddress;
  final String? fatherInLawName;
  final String? fatherInLawOccupation;
  final String? motherInLawName;
  final String? motherInLawPhoneNumber;
  final String? inLawsAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;

  factory FamilyInformationModel.fromJson(Map<String, dynamic> json) {
    return FamilyInformationModel(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      fatherName: json["fatherName"] ?? "",
      fatherOccupation: json["fatherOccupation"] ?? "",
      motherName: json["motherName"] ?? "",
      motherMobileNumber: json["motherMobileNumber"] ?? "",
      parentsAddress: json["parentsAddress"] ?? "",
      spouseName: json["spouseName"] ?? "",
      spousePlaceOfBirth: json["spousePlaceOfBirth"] ?? "",
      spouseDateOfBirth: DateTime.tryParse(json["spouseDateOfBirth"] ?? ""),
      spousePlaceOfMarriage: json["spousePlaceOfMarriage"] ?? "",
      spouseDateOfMarriage:
          DateTime.tryParse(json["spouseDateOfMarriage"] ?? ""),
      spouseOccupation: json["spouseOccupation"] ?? "",
      spouseEducationTitle: json["spouseEducationTitle"] ?? "",
      spousePhoneNumber: json["spousePhoneNumber"] ?? "",
      spouseHomeAddress: json["spouseHomeAddress"] ?? "",
      fatherInLawName: json["fatherInLawName"] ?? "",
      fatherInLawOccupation: json["fatherInLawOccupation"] ?? "",
      motherInLawName: json["motherInLawName"] ?? "",
      motherInLawPhoneNumber: json["motherInLawPhoneNumber"] ?? "",
      inLawsAddress: json["inLawsAddress"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        fatherName,
        fatherOccupation,
        motherName,
        motherMobileNumber,
        parentsAddress,
        spouseName,
        spousePlaceOfBirth,
        spouseDateOfBirth,
        spousePlaceOfMarriage,
        spouseDateOfMarriage,
        spouseOccupation,
        spouseEducationTitle,
        spousePhoneNumber,
        spouseHomeAddress,
        fatherInLawName,
        fatherInLawOccupation,
        motherInLawName,
        motherInLawPhoneNumber,
        inLawsAddress,
        createdAt,
        updatedAt,
        status,
      ];
}
