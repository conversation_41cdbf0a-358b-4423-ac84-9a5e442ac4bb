import 'package:equatable/equatable.dart';

class PersonalProfileModel extends Equatable {
  PersonalProfileModel({
    required this.id,
    required this.personalProfile,
  });

  final String id;
  final PersonalProfile? personalProfile;

  factory PersonalProfileModel.fromJson(Map<String, dynamic> json) {
    return PersonalProfileModel(
      id: json["id"] ?? "",
      personalProfile: json["personalProfile"] == null
          ? null
          : PersonalProfile.fromJson(json["personalProfile"]),
    );
  }

  @override
  List<Object?> get props => [
        id,
        personalProfile,
      ];
}

class PersonalProfile extends Equatable {
  PersonalProfile({
    required this.id,
    required this.userId,
    required this.fullName,
    required this.corps,
    required this.nrp,
    required this.rank,
    required this.placeOfBirth,
    required this.dateOfBirth,
    required this.religion,
    required this.ethnicity,
    required this.bloodType,
    required this.nationality,
    required this.height,
    required this.weight,
    required this.hobbies,
    required this.homeAddress,
    required this.homePhoneNumber,
    required this.mobileNumber,
    required this.emailAddress,
    required this.lastPosition,
    required this.commissioningSource,
    required this.graduationYear,
    required this.effectiveStartDate,
    required this.currentUnit,
    required this.unitAddress,
    required this.unitPhoneNumber,
    required this.unitFaxNumber,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  final String id;
  final String userId;
  final String fullName;
  final String corps;
  final String nrp;
  final String rank;
  final String placeOfBirth;
  final DateTime? dateOfBirth;
  final String religion;
  final String ethnicity;
  final String bloodType;
  final String nationality;
  final int height;
  final int weight;
  final String hobbies;
  final String homeAddress;
  final String homePhoneNumber;
  final String mobileNumber;
  final String emailAddress;
  final String lastPosition;
  final String commissioningSource;
  final String graduationYear;
  final DateTime? effectiveStartDate;
  final String currentUnit;
  final String unitAddress;
  final String unitPhoneNumber;
  final String unitFaxNumber;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String status;

  factory PersonalProfile.fromJson(Map<String, dynamic> json) {
    return PersonalProfile(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      fullName: json["fullName"] ?? "",
      corps: json["corps"] ?? "",
      nrp: json["NRP"] ?? "",
      rank: json["rank"] ?? "",
      placeOfBirth: json["placeOfBirth"] ?? "",
      dateOfBirth: DateTime.tryParse(json["dateOfBirth"] ?? ""),
      religion: json["religion"] ?? "",
      ethnicity: json["ethnicity"] ?? "",
      bloodType: json["bloodType"] ?? "",
      nationality: json["nationality"] ?? "",
      height: json["height"] ?? 0,
      weight: json["weight"] ?? 0,
      hobbies: json["hobbies"] ?? "",
      homeAddress: json["homeAddress"] ?? "",
      homePhoneNumber: json["homePhoneNumber"] ?? "",
      mobileNumber: json["mobileNumber"] ?? "",
      emailAddress: json["emailAddress"] ?? "",
      lastPosition: json["lastPosition"] ?? "",
      commissioningSource: json["commissioningSource"] ?? "",
      graduationYear: json["graduationYear"] ?? "",
      effectiveStartDate: DateTime.tryParse(json["effectiveStartDate"] ?? ""),
      currentUnit: json["currentUnit"] ?? "",
      unitAddress: json["unitAddress"] ?? "",
      unitPhoneNumber: json["unitPhoneNumber"] ?? "",
      unitFaxNumber: json["unitFaxNumber"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        fullName,
        corps,
        nrp,
        rank,
        placeOfBirth,
        dateOfBirth,
        religion,
        ethnicity,
        bloodType,
        nationality,
        height,
        weight,
        hobbies,
        homeAddress,
        homePhoneNumber,
        mobileNumber,
        emailAddress,
        lastPosition,
        commissioningSource,
        graduationYear,
        effectiveStartDate,
        currentUnit,
        unitAddress,
        unitPhoneNumber,
        unitFaxNumber,
        createdAt,
        updatedAt,
        status,
      ];
}
