import 'package:equatable/equatable.dart';

class CourseModel extends Equatable {
  CourseModel({
    this.id,
    this.userId,
    this.courseName,
    this.yearStarted,
    this.yearCompleted,
    this.certificate,
    this.createdAt,
    this.updatedAt,
    this.status,
  });

  final String? id;
  final String? userId;
  final String? courseName;
  final int? yearStarted;
  final int? yearCompleted;
  final String? certificate;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;

  factory CourseModel.fromJson(Map<String, dynamic> json) {
    return CourseModel(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      courseName: json["courseName"] ?? "",
      yearStarted: json["yearStarted"] ?? 0,
      yearCompleted: json["yearCompleted"] ?? 0,
      certificate: json["certificate"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
      updatedAt: DateTime.tryParse(json["updatedAt"] ?? ""),
      status: json["status"] ?? "",
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        courseName,
        yearStarted,
        yearCompleted,
        certificate,
        createdAt,
        updatedAt,
        status,
      ];
}
