import 'package:equatable/equatable.dart';

class MilitaryEducationModel extends Equatable {
  MilitaryEducationModel({
    this.id,
    this.userId,
    this.militaryEducation,
    this.generation,
    this.yearCompleted,
    this.diploma,
    this.createdAt,
  });

  final String? id;
  final String? userId;
  final String? militaryEducation;
  final String? generation;
  final int? yearCompleted;
  final String? diploma;
  final DateTime? createdAt;

  factory MilitaryEducationModel.fromJson(Map<String, dynamic> json) {
    return MilitaryEducationModel(
      id: json["id"] ?? "",
      userId: json["userId"] ?? "",
      militaryEducation: json["militaryEducation"] ?? "",
      generation: json["generation"] ?? "",
      yearCompleted: json["yearCompleted"] ?? 0,
      diploma: json["diploma"] ?? "",
      createdAt: DateTime.tryParse(json["createdAt"] ?? ""),
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        militaryEducation,
        generation,
        yearCompleted,
        diploma,
        createdAt,
      ];
}
