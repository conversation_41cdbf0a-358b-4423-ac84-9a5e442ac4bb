class LastHanjarModel {
  final String id;
  final String title;
  final String type;
  final String file;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status;
  final SubMapel subMapel;

  LastHanjarModel({
    required this.id,
    required this.title,
    required this.type,
    required this.file,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.subMapel,
  });

  factory LastHanjarModel.fromJson(Map<String, dynamic> json) {
    return LastHanjarModel(
      id: json['id'],
      title: json['title'],
      type: json['type'],
      file: json['file'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      status: json['status'],
      subMapel: SubMapel.fromJson(json['subMapel']),
    );
  }
}

class SubMapel {
  final String id;
  final String name;
  final String code;
  final String status;

  SubMapel({
    required this.id,
    required this.name,
    required this.code,
    required this.status,
  });

  factory SubMapel.fromJson(Map<String, dynamic> json) {
    return SubMapel(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      status: json['status'],
    );
  }
}
