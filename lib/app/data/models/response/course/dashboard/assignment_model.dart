class AssignmentModel {
  String? id;
  String? title;
  String? file;
  String? thumbnail;
  String? description;
  String? subMapelId;
  DateTime? startDate;
  DateTime? endDate;
  String? createdAt;
  String? updatedAt;
  String? status;
  Submapel? submapel;
  TahunPendidikan? tahunPendidikan;
  CreatedBy? createdBy;
  int? completion;
  int? totalStudent;

  AssignmentModel(
      {this.id,
      this.title,
      this.file,
      this.thumbnail,
      this.description,
      this.subMapelId,
      this.startDate,
      this.endDate,
      this.createdAt,
      this.updatedAt,
      this.status,
      this.submapel,
      this.tahunPendidikan,
      this.createdBy,
      this.completion,
      this.totalStudent});

  AssignmentModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    file = json['file'];
    thumbnail = json['thumbnail'];
    description = json['description'];
    subMapelId = json['subMapelId'];
    startDate = json['startDate'] != null ? DateTime.tryParse(json['startDate']) : null;
    endDate = json['endDate'] != null ? DateTime.tryParse(json['endDate']) : null;
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    status = json['status'];
    submapel = json['submapel'] != null ? new Submapel.fromJson(json['submapel']) : null;
    tahunPendidikan = json['tahunPendidikan'] != null ? new TahunPendidikan.fromJson(json['tahunPendidikan']) : null;
    createdBy = json['createdBy'] != null ? new CreatedBy.fromJson(json['createdBy']) : null;
    completion = json['completion'];
    totalStudent = json['totalStudent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['file'] = this.file;
    data['thumbnail'] = this.thumbnail;
    data['description'] = this.description;
    data['subMapelId'] = this.subMapelId;
    data['startDate'] = this.startDate;
    data['endDate'] = this.endDate;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['status'] = this.status;
    if (this.submapel != null) {
      data['submapel'] = this.submapel!.toJson();
    }
    if (this.tahunPendidikan != null) {
      data['tahunPendidikan'] = this.tahunPendidikan!.toJson();
    }
    if (this.createdBy != null) {
      data['createdBy'] = this.createdBy!.toJson();
    }
    data['completion'] = this.completion;
    data['totalStudent'] = this.totalStudent;
    return data;
  }
}

class Submapel {
  String? id;
  String? name;
  Mapel? mapel;

  Submapel({this.id, this.name, this.mapel});

  Submapel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    mapel = json['mapel'] != null ? new Mapel.fromJson(json['mapel']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    if (this.mapel != null) {
      data['mapel'] = this.mapel!.toJson();
    }
    return data;
  }
}

class Mapel {
  String? id;
  String? name;
  Matkul? matkul;

  Mapel({this.id, this.name, this.matkul});

  Mapel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    matkul = json['matkul'] != null ? new Matkul.fromJson(json['matkul']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    if (this.matkul != null) {
      data['matkul'] = this.matkul!.toJson();
    }
    return data;
  }
}

class Matkul {
  String? id;
  String? name;

  Matkul({this.id, this.name});

  Matkul.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    return data;
  }
}

class TahunPendidikan {
  String? id;
  String? year;
  String? jenisPendidikan;

  TahunPendidikan({this.id, this.year, this.jenisPendidikan});

  TahunPendidikan.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    year = json['year'];
    jenisPendidikan = json['jenisPendidikan'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['year'] = this.year;
    data['jenisPendidikan'] = this.jenisPendidikan;
    return data;
  }
}

class CreatedBy {
  String? id;
  String? name;
  String? nRP;
  String? imageProfile;

  CreatedBy({this.id, this.name, this.nRP});

  CreatedBy.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nRP = json['NRP'];
    imageProfile = json['imageProfile'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['NRP'] = this.nRP;
    data['imageProfile'] = this.imageProfile;
    return data;
  }
}
