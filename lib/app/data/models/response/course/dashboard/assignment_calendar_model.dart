class AssignmentCalendarModel {
  String? id;
  String? title;
  String? description;
  DateTime? date;
  String? status;

  AssignmentCalendarModel({this.id, this.title, this.description, this.date, this.status});

  AssignmentCalendarModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    date = json['date'] != null ? DateTime.tryParse(json['date']) : null;
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['description'] = this.description;
    data['date'] = this.date;
    data['status'] = this.status;
    return data;
  }
}
