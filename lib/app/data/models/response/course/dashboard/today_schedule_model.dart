class TodayScheduleModel {
  final String id;
  final String title;
  final String lecturer;
  final DateTime date;
  final StartTimeModel startTime;
  final EndTimeModel endTime;
  final String location;
  final String metode;

  TodayScheduleModel({
    required this.id,
    required this.title,
    required this.lecturer,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.location,
    required this.metode,
  });

  factory TodayScheduleModel.fromJson(Map<String, dynamic> json) {
    return TodayScheduleModel(
      id: json['id'],
      title: json['title'],
      lecturer: json['lecturer'],
      date: DateTime.parse(json['date']),
      startTime: StartTimeModel.fromJson(json['startTime']),
      endTime: EndTimeModel.fromJson(json['endTime']),
      location: json['location'],
      metode: json['metode'],
    );
  }
}

class StartTimeModel {
  final String id;
  final String name;
  final DateTime startTime;

  StartTimeModel({
    required this.id,
    required this.name,
    required this.startTime,
  });

  factory StartTimeModel.fromJson(Map<String, dynamic> json) {
    return StartTimeModel(
      id: json['id'],
      name: json['name'],
      startTime: DateTime.parse(json['startTime']),
    );
  }
}

class EndTimeModel {
  final String id;
  final String name;
  final DateTime endTime;

  EndTimeModel({
    required this.id,
    required this.name,
    required this.endTime,
  });

  factory EndTimeModel.fromJson(Map<String, dynamic> json) {
    return EndTimeModel(
      id: json['id'],
      name: json['name'],
      endTime: DateTime.parse(json['endTime']),
    );
  }
}
