class ScheduleMonthlyModel {
  String? id;
  DateTime? date;
  String? location;
  String? metode;
  Mapel? mapel;
  PbmStart? pbmStart;
  PbmStart? pbmEnd;
  Lecturer? lecturer;
  String? status;
  String? createdAt;
  String? updatedAt;

  ScheduleMonthlyModel({this.id, this.date, this.location, this.metode, this.mapel, this.pbmStart, this.pbmEnd, this.lecturer, this.status, this.createdAt, this.updatedAt});

  ScheduleMonthlyModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['date'] != null ? DateTime.tryParse(json['date']) : null;
    location = json['location'];
    metode = json['metode'];
    mapel = json['mapel'] != null ? new Mapel.fromJson(json['mapel']) : null;
    pbmStart = json['pbmStart'] != null ? new PbmStart.fromJson(json['pbmStart']) : null;
    pbmEnd = json['pbmEnd'] != null ? new PbmStart.fromJson(json['pbmEnd']) : null;
    lecturer = json['lecturer'] != null ? new Lecturer.fromJson(json['lecturer']) : null;
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['date'] = this.date;
    data['location'] = this.location;
    data['metode'] = this.metode;
    if (this.mapel != null) {
      data['mapel'] = this.mapel!.toJson();
    }
    if (this.pbmStart != null) {
      data['pbmStart'] = this.pbmStart!.toJson();
    }
    if (this.pbmEnd != null) {
      data['pbmEnd'] = this.pbmEnd!.toJson();
    }
    if (this.lecturer != null) {
      data['lecturer'] = this.lecturer!.toJson();
    }
    data['status'] = this.status;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}

class Mapel {
  String? id;
  String? name;
  String? status;
  String? createdAt;
  String? updatedAt;
  String? userCreatedId;
  String? matkulId;

  Mapel({this.id, this.name, this.status, this.createdAt, this.updatedAt, this.userCreatedId, this.matkulId});

  Mapel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    status = json['status'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    userCreatedId = json['userCreatedId'];
    matkulId = json['matkulId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['status'] = this.status;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['userCreatedId'] = this.userCreatedId;
    data['matkulId'] = this.matkulId;
    return data;
  }
}

class PbmStart {
  String? id;
  String? name;
  String? day;
  DateTime? startTime;
  DateTime? endTime;
  DateTime? createdAt;
  DateTime? updatedAt;

  PbmStart({this.id, this.name, this.day, this.startTime, this.endTime, this.createdAt, this.updatedAt});

  PbmStart.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    day = json['day'];
    startTime = DateTime.tryParse(json['startTime']);
    endTime = DateTime.tryParse(json['endTime']);
    createdAt = DateTime.tryParse(json['createdAt']);
    updatedAt = DateTime.tryParse(json['updatedAt']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['day'] = this.day;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    return data;
  }
}

class Lecturer {
  String? id;
  String? name;
  String? imageProfile;

  Lecturer({
    this.id,
    this.name,
    this.imageProfile,
  });

  Lecturer.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    imageProfile = json['imageProfile'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['imageProfile'] = this.imageProfile;
    return data;
  }
}
