import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/dummy/assessment_summary.dart';
import 'package:mides_skadik/app/data/models/dummy/chat.dart';
import 'package:mides_skadik/app/data/models/dummy/quiz_summary.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/chat_user_model.dart';

class DataDummy {
  final RxList<QuizSummary> dataQuizSummary = <QuizSummary>[].obs;

  final dataAssessmentSummary = {
    "data": [
      {
        "completion-time": "2025-04-06 14:09:00",
        "duration": "24 min, 30 sec",
        "question-answered": "20 out of 20",
        "score": null,
        "review": null
      }
    ]
  };

  final chatUsers = {
    "data": [
      {
        "id-user": "111000",
        "message": "Lagi ngapain bro?",
        "date": "2025-04-10T09:09:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Ngerjain tugas kemarin ini, banyak banget.",
        "date": "2025-04-10T10:10:20.323Z",
      },
      {
        "id-user": "111000",
        "message": "Waduh tugas apaan itu, kok gw lupa ada tugas ya.",
        "date": "2025-04-12T11:10:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Tugas yang mengenai Navigasi Udara, deadline besok btw.",
        "date": "2025-04-20T11:11:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Kerjain dah daripada lu besok ga dibolehin ikut pelajaran",
        "date": "2025-04-22T12:11:04.323Z",
      },
      // Today
      {
        "id-user": "111000",
        "message": "Lagi ngapain bro?",
        "date": "2025-04-23T09:09:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Ngerjain tugas kemarin ini, banyak banget.",
        "date": "2025-04-23T10:10:20.323Z",
      },
      {
        "id-user": "111000",
        "message": "Waduh tugas apaan itu, kok gw lupa ada tugas ya.",
        "date": "2025-04-23T11:10:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Tugas yang mengenai Navigasi Udara, deadline besok btw.",
        "date": "2025-04-23T11:11:04.323Z",
      },
      {
        "id-user": "222000",
        "message": "Kerjain dah daripada lu besok ga dibolehin ikut pelajaran",
        "date": "2025-04-23T12:11:04.323Z",
      },
    ]
  };

  final listUser = [
    {"id": "cm9nk1su3000dt00hxtqje2yd", "name": "Hendri"},
    {"id": "cm99mseno0004t80i9nq3yanc", "name": "Cha"},
    {"id": "cmaafxl590003sx0ixouv3ug5", "name": "Jajalan"},
  ];

  List<QuizSummary> get quizSummary {
    return (dataQuizSummary as List)
        .map((e) => QuizSummary.fromJson(e))
        .toList();
  }

  List<AssessmentSummary> get assessmentSummary {
    return (dataAssessmentSummary as List)
        .map((e) => AssessmentSummary.fromJson(e))
        .toList();
  }

  void setDataQuizSummary(data) {
    dataQuizSummary.clear();
    dataQuizSummary.addAll(data);
    LogService.log.i("Data quiz summary updated: $dataQuizSummary");
  }

  List<Chat> get chatUser {
    return (chatUsers['data'] as List).map((e) => Chat.fromJson(e)).toList();
  }

  List<ChatUserModel> get listUsers {
    return (listUser as List).map((e) => ChatUserModel.fromJson(e)).toList();
  }
}
