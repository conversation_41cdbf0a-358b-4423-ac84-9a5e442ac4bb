import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/auth/user_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class LoginService {
  /* Instance storageService */
  StorageService storageService = StorageService();
  /* Instance localStorage */
  LocalStorage localStorage = LocalStorage();

  /* The method login takes in a String email and a String password and returns a Future<UserModel> */
  Future<ResultModel<UserModel>> login(
      {required String username, required String password}) async {
    debugPrint('===================== Login Service =====================');
    String url = "$baseApiUrl$urlLogin";

    var body = <String, String>{
      'NRP': username,
      'password': password,
    };

    http.Response response = await functionPost(url: url, object: body);
    debugPrint('===================== Body =====================');
    debugPrint(jsonDecode(response.body).toString());
    debugPrint('===================== End Login Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      return ResultModel<UserModel>.failed(
          result['message'].toString(), result['data']);
    } else {
      /* The variable localStorage is assigned the value of the postLocalStorageLogin method */
      localStorage = await storageService.postLocalStorageLogin(response);
      return ResultModel<UserModel>.success(UserModel.fromJson(result['data']));
    }
  }
}
