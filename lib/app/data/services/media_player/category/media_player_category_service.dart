import 'dart:convert';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/category/media_player_category_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/utils/utils.dart';

class MediaPlayerCategoryService {
  /* Instance storageService */
  StorageService storageService = StorageService();
  /* Instance localStorage */
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<MediaPlayerCategory>>> getList(
      {int? page = 1, int? limit = 100}) async {
    print('===================== Assignment Service =====================');
    String url = "$baseApiMpUrl/category?page=$page&limit=$limit&isPublic=yes";
    // "$baseApiUrl$urlAssignment?type=Assignment&search=$search&page=1&limit=$length";
    print('URL: $url');
    dynamic token = await LocalStorage().get('token');

    http.Response response = await functionGet(url, 'Bearer $token');

    print('===================== Body =====================');
    print(jsonDecode(response.body).toString());
    print('===================== End Assignment Service =====================');
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      var data = result['data']
          .map<MediaPlayerCategory>((e) => MediaPlayerCategory.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }
}
