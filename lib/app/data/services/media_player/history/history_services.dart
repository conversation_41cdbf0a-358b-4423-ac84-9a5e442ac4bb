import 'dart:convert';

import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class HistoryServices {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<PlaybackModel>>> getHistory(
      {String? search = '', int? page = 1, int? limit = 10}) async {
    final url = '$baseApiMpUrl$urlPlayback?v=$search&page=$page&limit=$limit';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i('History Message: $datas');

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<PlaybackModel>((e) => PlaybackModel.fromJson(e))
          .toList();
      return ResultModel.success(data, datas['meta']['total_data']);
    }
  }
}
