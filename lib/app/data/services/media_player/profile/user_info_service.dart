import 'dart:convert';

import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/user_info/user_info_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class UserInfoService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  // TODO: GET USER DATA
  Future<ResultModel<UserInfoMediaPlayer>> getUserData() async {
    final url = "$baseApiMpUrl$urlUserInfo";
    LogService.log.d('URL: $url');
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.d('URL: $datas');

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = UserInfoMediaPlayer.fromJson(datas['data']);
      return ResultModel.success(data);
    }
  }

  // TODO: UPDATE USER LOGIN
  Future<ResultModel<bool>> updateLastLogin() async {
    final url = '$baseApiMpUrl$urlUpdateLastLogin';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');

    if (response.statusCode != 200) {
      return const ResultModel.failed('Failed to Update Login');
    } else {
      return const ResultModel.success(true, 0, 'Success to Update Last Login');
    }
  }
}
