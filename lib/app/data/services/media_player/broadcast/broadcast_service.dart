import 'dart:convert';

import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/models/response/media_player/broadcast/broadcast_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/broadcast/comment_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class BroadcastService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<BroadcastModel>>> getAllBroadcast({
    int? page = 1,
    int? limit = 10,
  }) async {
    final url = '$baseApiMpUrl$urlBroadcast';
    LogService.log.i("URL SEARCH LOGS: $url");
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i("RESPONSE SEARCH LOGS: ${datas.toString()}");
    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<BroadcastModel>((e) => BroadcastModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }

  Future<ResultModel<List<CommentModel>>> getComment({
    String? id,
  }) async {
    final url = '$baseApiMpUrl$urlVodComment/$id';
    LogService.log.i("URL SEARCH LOGS: $url");
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i("RESPONSE SEARCH LOGS: ${datas.toString()}");
    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<CommentModel>((e) => CommentModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }

  Future<ResultModel<List<CommentModel>>> getCommentBroadcast({
    String? id,
  }) async {
    final url = '$baseApiMpUrl$urlBroadcastComment/$id';
    LogService.log.i("URL SEARCH LOGS: $url");
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i("RESPONSE SEARCH LOGS: ${datas.toString()}");
    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<CommentModel>((e) => CommentModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }

  Future<ResultModel> postComment({
    required String id,
    String? parentId,
    required String comment,
  }) async {
    String url = "$baseApiMpUrl$urlVodComment";
    LogService.log.d('POST URL: $url');
    dynamic token = await localStorage.get('token');
    LogService.log.d('POST TOKEN: $token');
    LogService.log.d('POST ID: $id');
    LogService.log.d('POST PARENT ID: $parentId');
    LogService.log.d('POST COMMENT: $comment');
    try {
      final response = await functionPost(
        url: url,
        object: {"comment": comment, "parent_id": parentId, "vod_id": id},
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 201) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }
}
