import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_collection_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class PlaylistService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<PlaylistModel>>> getPlaylists({
    int? page = 1,
    int? limit,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlPlaylist?limit=$limit&page=$page";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response = await functionGet(url, 'Bearer $token');
      LogService.log.d('Response: ${response.body}');
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<PlaylistModel>((e) => PlaylistModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> createPlaylist({
    required String name,
    required String description,
    required String visibility,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist";
    LogService.log.d('POST URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPost(
        url: url,
        object: {
          "name": name,
          "description": description,
          "visibility": visibility
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 201 || response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> updatePlaylist({
    required String id,
    required String name,
    required String description,
    required String visibility,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist/$id";
    LogService.log.d('PUT URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPut(
        url: url,
        object: {
          "name": name,
          "description": description,
          "visibility": visibility
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> deletePlaylist({
    required String id,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist/$id";
    LogService.log.d('DELETE URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionDelete(
        url,
        'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> assignVod({
    required String vodId,
    required List<String> playlistIds,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist/$vodId/assign-vod";
    LogService.log.d('PUT URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPut(
        url: url,
        object: {"playlist_ids": playlistIds},
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> removeVod({
    required String playlistId,
    required List<String> vodIds,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist/$playlistId/remove-vod";
    LogService.log.d('DELETE URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionDelete(
        url,
        'Bearer $token',
        object: {"vod_id": vodIds},
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel<List<PlaylistCollectionModel>>> getPlaylistClipCollection({
    int? page = 1,
    int? limit,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlPlaylist?limit=$limit&page=$page";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response = await functionGet(url, 'Bearer $token');
      LogService.log.d('Response: ${response.body}');
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<PlaylistCollectionModel>(
                (e) => PlaylistCollectionModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      LogService.log.e(e.toString());
      return const ResultModel.failed("Error");
    }
  }

  Future<ResultModel> updatePlaylistCollection({
    required String id,
    required List<String> playlistIds,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist/$id/assign-vod";
    LogService.log.d('PUT URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPut(
        url: url,
        object: {
          "playlist_ids": playlistIds,
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> createPlaylistCollection({
    required String name,
    required String description,
    required String visibility,
  }) async {
    String url = "$baseApiMpUrl$urlPlaylist";
    LogService.log.d('POST URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPost(
        url: url,
        object: {
          "name": name,
          "description": description,
          "visibility": visibility
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }
}
