import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/clip_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class VodPlaybackService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<PlaybackModel>>> getAllVodPlayback({
    int? page = 1,
    int? limit,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlPlayback?limit=$limit&page=$page";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response = await functionGet(url, 'Bearer $token');
      LogService.log.d('Response: ${response.body}');
      log(response.body);
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<PlaybackModel>((e) => PlaybackModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      LogService.log.e(e.toString());
      return const ResultModel.failed("Error");
    }
  }

  Future<ResultModel<List<PlaybackModel>>> updatePlayback({
    required String id,
    required int duration,
    required int lastWatchedTime,
    required bool isCompleted,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlPlayback/$id";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response =
          await functionPut(url: url, token: 'Bearer $token', object: {
        "duration": duration,
      });
      LogService.log.d('Response: ${response.body}');
      log(response.body);
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<PlaybackModel>((e) => PlaybackModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      LogService.log.e(e.toString());
      return const ResultModel.failed("Error");
    }
  }

  Future<ResultModel> createRattings({
    required String vodId,
    required double ratting,
  }) async {
    String url = "$baseApiMpUrl$urlVodRattings";
    LogService.log.d('POST URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPost(
        url: url,
        object: {
          "vod_id": vodId,
          "rating": ratting,
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }
}
