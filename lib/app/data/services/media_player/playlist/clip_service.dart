import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/clip_collection/clip_collection_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/clip_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class ClipService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<ClipModel>>> getClips({
    int? page = 1,
    int? limit,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlClip?limit=$limit&page=$page";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response = await functionGet(url, 'Bearer $token');
      LogService.log.d('Response: ${response.body}');
      log(response.body);
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<ClipModel>((e) => ClipModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      LogService.log.e(e.toString());
      return const ResultModel.failed("Error");
    }
  }

  Future<ResultModel<List<ClipCollectionModel>>> getClipCollection({
    int? page = 1,
    int? limit,
  }) async {
    try {
      String url = "$baseApiMpUrl$urlClipCollection?limit=$limit&page=$page";
      LogService.log.d('URL: $url');
      dynamic token = await localStorage.get('token');

      http.Response response = await functionGet(url, 'Bearer $token');
      LogService.log.d('Response: ${response.body}');
      log(response.body);
      var result = jsonDecode(response.body);

      if (response.statusCode != 200) {
        return ResultModel.failed(result['message'].toString());
      } else {
        var data = result['data']
            .map<ClipCollectionModel>((e) => ClipCollectionModel.fromJson(e))
            .toList();
        return ResultModel.success(data, result['meta']['total_data']);
      }
    } catch (e) {
      LogService.log.e(e.toString());
      return const ResultModel.failed("Error");
    }
  }

  Future<ResultModel> updateCollection({
    required String id,
    required List<String> collectionIds,
  }) async {
    String url = "$baseApiMpUrl$urlClipCollection/$id/assign-clip";
    LogService.log.d('PUT URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPut(
        url: url,
        object: {
          "collection_ids": collectionIds,
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }

  Future<ResultModel> createCollection({
    required String name,
    required String description,
  }) async {
    String url = "$baseApiMpUrl$urlClipCollection";
    LogService.log.d('POST URL: $url');
    dynamic token = await localStorage.get('token');

    try {
      final response = await functionPost(
        url: url,
        object: {
          "name": name,
          "description": description,
        },
        token: 'Bearer $token',
      );
      var result = jsonDecode(response.body);
      if (response.statusCode == 200) {
        return ResultModel.success(result['data'], result['message']);
      } else {
        return ResultModel.failed(result['message'].toString());
      }
    } catch (e) {
      return ResultModel.failed(e.toString());
    }
  }
}
