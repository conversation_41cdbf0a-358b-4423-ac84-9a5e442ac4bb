import 'dart:convert';

import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/models/response/media_player/browse/search_logs_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class VodBrowseService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  // TODO: Get Search Logs
  Future<ResultModel<List<SearchLogsModel>>> getSearchLogs({
    int? page = 1,
    int? limit = 10,
    String? filter = 'history',
  }) async {
    final url =
        '$baseApiMpUrl$urlSearchLogs?page=$page&limit=$limit&filter=$filter';
    LogService.log.i("URL SEARCH LOGS: $url");
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<SearchLogsModel>((e) => SearchLogsModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }

  // TODO: Search Video
  Future<ResultModel<List<VodModel>>> searchVod({
    String? search = '',
    String? tagId = '',
    String? sortBy = '',
    int? page = 1,
    int? limit = 15,
  }) async {
    final url =
        '$baseApiMpUrl$urlVod?limit=$limit&page=$page&status=approved&visibility=public&v=$search&tagID=$tagId&sortBy=$sortBy';
    LogService.log.i("URL SEARCH: $url");
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i("SEARCH Result: $datas");

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data =
          datas['data'].map<VodModel>((e) => VodModel.fromJson(e)).toList();
      return ResultModel.success(data, datas['meta']['las_page']);
    }
  }
}
