import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:path/path.dart';

class VodService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  // TODO: Get Popular Video Services
  Future<ResultModel<List<VodModel>>> getPopularVod({
    int? page = 1,
    int? limit,
    String? tag = '',
    String? sortBy = 'popular',
  }) async {
    String url =
        "$baseApiMpUrl$urlVod?limit=$limit&page=$page&sortBy=$sortBy&status=approved&visibility=public&tagID=$tag";
    LogService.log.d('URL: $url');
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      return ResultModel.failed(result['message'].toString());
    } else {
      var data =
          result['data'].map<VodModel>((e) => VodModel.fromJson(e)).toList();
      return ResultModel.success(data, result['meta']['total_data']);
    }
  }

  // TODO: Get Newest Video Services
  Future<ResultModel<List<VodModel>>> getNewestVod({
    int? page = 1,
    int? limit = 10,
    String? tag = '',
  }) async {
    final url =
        "$baseApiMpUrl$urlVod?limit=$limit&page=$page&sortBy=newest&status=approved&visibility=public&tagID=$tag";
    dynamic token = await LocalStorage().get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      var data =
          result['data'].map<VodModel>((e) => VodModel.fromJson(e)).toList();
      return ResultModel.success(data, result['meta']['total_data']);
    }
  }

  // TODO: Get Recommendation Video Services
  Future<ResultModel<List<VodModel>>> getRecommendationVod(
      {int? page, int? limit = 10, String? tag = ''}) async {
    final url =
        '$baseApiMpUrl$urlVod?limit=$limit&page=$page&sortBy=recommendation&status=approved&visibility=public&tagID=$tag';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.i('Recommendation data $datas');

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data =
          datas['data'].map<VodModel>((e) => VodModel.fromJson(e)).toList();
      return ResultModel.success(data, datas['meta']['total_data']);
    }
  }

  // TODO: Get Continue Watch Services
  Future<ResultModel<List<PlaybackModel>>> getContinueWatch(
      {int? page = 1, int? limit = 10, String? isComplete = ''}) async {
    final url =
        '$baseApiMpUrl$urlVodPlayback?page=$page&limit=$limit&is_complete=$isComplete';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);
    LogService.log.d('Continue Watch: $datas');

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['data']
          .map<PlaybackModel>((e) => PlaybackModel.fromJson(e))
          .toList();
      return ResultModel.success(data, datas['meta']['total_data']);
    }
  }

  // TODO: Sent Analitik When Play Video
  Future<void> getVodById({String? vodId = '', String? tagId = ''}) async {
    final url = '$baseApiMpUrl$urlVod/$vodId?tagID=$tagId';
    dynamic token = await localStorage.get('token');

    await functionGet(url, 'Bearer $token');
  }

  // TODO: Get My VoD Service
  Future<ResultModel<List<VodModel>>> getMyVod({
    int? page = 1,
    int? limit = 10,
    String? search = '',
    String? sortBy = '',
  }) async {
    final url =
        '$baseApiMpUrl$urlMyVod?page=$page&limit=$limit&v=$search&sortBy=$sortBy';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, 'Bearer $token');
    final datas = jsonDecode(response.body);

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data =
          datas['data'].map<VodModel>((e) => VodModel.fromJson(e)).toList();
      return ResultModel.success(data, datas['meta']['total_data']);
    }
  }

  // TODO: Upload Video
  Future<bool> createVod({
    required File videoFile,
    required File thumbnailFile,
    required String title,
    required String desc,
    required List<String> tags,
    required String visibility,
    required bool isCommentEnabled,
  }) async {
    dynamic token = await localStorage.get('token');

    final url = Uri.parse(baseApiMpUrl + urlVod);

    final request = http.MultipartRequest('POST', url);

    // Tambahkan header Authorization
    request.headers['Authorization'] = 'Bearer $token';

    // Tambahkan file video
    request.files.add(
      http.MultipartFile(
        'video',
        videoFile.readAsBytes().asStream(),
        videoFile.lengthSync(),
        filename: basename(videoFile.path),
      ),
    );

    // Tambahkan thumbnail
    request.files.add(
      http.MultipartFile(
        'thumbnail',
        thumbnailFile.readAsBytes().asStream(),
        thumbnailFile.lengthSync(),
        filename: basename(thumbnailFile.path),
      ),
    );

    // Tambahkan data text
    request.fields['title'] = title;
    request.fields['desc'] = desc;
    request.fields['visibility'] = visibility;
    request.fields['is_comment_enabled'] = isCommentEnabled ? 'true' : 'false';

    // Tambahkan multiple tag (misal pakai field yang sama berkali-kali)
    for (var tag in tags) {
      request.files.add(http.MultipartFile.fromString('tag', tag));
    }

    // Kirim request
    final response = await request.send();

    final resBody = await response.stream.bytesToString();

    if (response.statusCode == 201) {
      LogService.log.d('Upload sukses!');
      return true;
    } else {
      LogService.log.e('Upload gagal: ${response.statusCode}');
      LogService.log.e('Respon body: $resBody');

      return false;
    }
  }

  // TODO: Edit Video
  Future<bool> updateVideo({
    File? videoFile,
    File? thumbnailFile,
    required String id,
    required String title,
    required String desc,
    required List<String> tags,
    required String visibility,
    required bool isCommentEnabled,
  }) async {
    final url = '$baseApiMpUrl$urlVod/$id';
    dynamic token = await localStorage.get('token');

    final request = http.MultipartRequest('PUT', Uri.parse(url));
    request.headers['Authorization'] = 'Bearer $token';

    if (videoFile != null) {
      request.files.add(http.MultipartFile(
          'video', videoFile.readAsBytes().asStream(), videoFile.lengthSync(),
          filename: basename(videoFile.path)));
    }

    if (thumbnailFile != null) {
      request.files.add(http.MultipartFile('thumbnail',
          thumbnailFile.readAsBytes().asStream(), thumbnailFile.lengthSync(),
          filename: basename(thumbnailFile.path)));
    }

    request.fields['title'] = title;
    request.fields['desc'] = desc;
    request.fields['visibility'] = visibility;
    request.fields['is_comment_enabled'] = isCommentEnabled ? 'true' : 'false';

    for (var tag in tags) {
      request.files.add(http.MultipartFile.fromString('tag', tag));
    }

    final response = await request.send();
    final responseBody = await response.stream.bytesToString();

    if (response.statusCode == 200) {
      LogService.log.d('Upload sukses!');
      return true;
    } else {
      LogService.log.e('Upload gagal: ${response.statusCode}');
      LogService.log.e('Respon body: $responseBody');
      return false;
    }
  }

  // TODO: Update Privacy and Comments
  Future<ResultModel<bool>> updatePrivacyAndComment(
      {required String idVod,
      required String privacy,
      required bool isCommentEnabled}) async {
    Object body = {
      "visibility": privacy,
      "is_comment_enabled": isCommentEnabled
    };

    final url = '$baseApiMpUrl$urlVod/$idVod/comment-visibility';
    dynamic token = await localStorage.get('token');

    http.Response response =
        await functionPut(url: url, object: body, token: 'Bearer $token');

    if (response.statusCode == 200) {
      return const ResultModel.success(
          true, 1, 'Berhasil update Privasi dan Komen');
    } else {
      return const ResultModel.failed('Gagal update Privasi dan Komen');
    }
  }

  // TODO: Delete My Video By ID
  Future<ResultModel<bool>> deleteMyVideoByID({required String idVod}) async {
    final url = '$baseApiMpUrl$urlVod/$idVod';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionDelete(url, 'Bearer $token');

    if (response.statusCode != 200) {
      return const ResultModel.failed('Gagal Menghapus Video.');
    } else {
      return const ResultModel.success(true, 0, 'Berhasil Menghapus Video.');
    }
  }

  Future<String> createClip({
    required File videoFile,
    required String title,
    required String desc,
    required String vodId,
  }) async {
    dynamic token = await localStorage.get('token');

    final url = Uri.parse(baseApiMpUrl + urlClip);

    final request = http.MultipartRequest('POST', url);

    // Tambahkan header Authorization
    request.headers['Authorization'] = 'Bearer $token';

    // Tambahkan file video
    request.files.add(
      http.MultipartFile(
        'clip',
        videoFile.readAsBytes().asStream(),
        videoFile.lengthSync(),
        filename: basename(videoFile.path),
      ),
    );

    // Tambahkan data text
    request.fields['name'] = title;
    request.fields['description'] = desc;
    request.fields['vod_id'] = vodId;

    // Kirim request
    final response = await request.send();

    final resBody = await response.stream.bytesToString();

    var resData = jsonDecode(resBody)['data'].toString();

    if (response.statusCode == 201) {
      LogService.log.d('Upload sukses!');
      return resData;
    } else {
      LogService.log.e('Upload gagal: ${response.statusCode}');
      LogService.log.e('Respon body: $resBody');

      return "";
    }
  }

  // TODO: UPDATE DURATION WHEN PLAYING VIDEO
  Future<bool> updatePlayback(
      {int? duration = 10, required String vodId}) async {
    final url = '$baseApiMpUrl$urlPlayback/$vodId';
    dynamic token = await localStorage.get('token');

    final body = {'duration': duration};

    http.Response response =
        await functionPut(url: url, object: body, token: 'Bearer $token');
    final datas = jsonDecode(response.body);

    LogService.log.i('Update Playback $datas');

    if (response.statusCode != 200) {
      ResultModel.failed(datas['message'].toString());
      return false;
    } else {
      const ResultModel.failed('Sukses Update Playback');
      return true;
    }
  }
}
