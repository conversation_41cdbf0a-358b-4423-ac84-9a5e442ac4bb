import 'package:just_audio/just_audio.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class AudioPlayerService extends GetxController {
  static AudioPlayerService get instance => Get.find<AudioPlayerService>();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // Observable variables
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var currentUrl = ''.obs;
  var currentTitle = ''.obs;
  var currentAudioId = ''.obs; // ✅ Track current audio ID

  @override
  void onInit() {
    super.onInit();
    _initializePlayer();
  }

  void _initializePlayer() {
    // Listen to player state changes
    _audioPlayer.playerStateStream.listen((state) {
      isPlaying.value = state.playing;
      isLoading.value = state.processingState == ProcessingState.loading ||
          state.processingState == ProcessingState.buffering;
    });

    // Listen to position changes
    _audioPlayer.positionStream.listen((position) {
      currentPosition.value = position;
    });

    // Listen to duration changes
    _audioPlayer.durationStream.listen((duration) {
      if (duration != null) {
        totalDuration.value = duration;
      }
    });
  }

  Future<void> playAudio(String url, {String? title, String? audioId}) async {
    try {
      LogService.log.i('🎵 Playing audio: $url');

      // Stop current audio if playing different file
      if (currentUrl.value != url && isPlaying.value) {
        await stop();
      }

      currentUrl.value = url;
      currentTitle.value = title ?? 'Audio File';
      currentAudioId.value =
          audioId ?? url.hashCode.toString(); // ✅ Set audio ID
      isLoading.value = true;

      // Set audio source
      await _audioPlayer.setUrl(url);

      // Start playing
      await _audioPlayer.play();

      LogService.log.i('✅ Audio started playing successfully');
    } catch (e) {
      LogService.log.e('❌ Error playing audio: $e');
      isLoading.value = false;
      Get.snackbar(
        'Error',
        'Failed to play audio: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
      LogService.log.i('⏸️ Audio paused');
    } catch (e) {
      LogService.log.e('❌ Error pausing audio: $e');
    }
  }

  Future<void> resume() async {
    try {
      await _audioPlayer.play();
      LogService.log.i('▶️ Audio resumed');
    } catch (e) {
      LogService.log.e('❌ Error resuming audio: $e');
    }
  }

  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      currentPosition.value = Duration.zero;
      currentUrl.value = '';
      currentTitle.value = '';
      currentAudioId.value = ''; // ✅ Clear audio ID
      LogService.log.i('⏹️ Audio stopped');
    } catch (e) {
      LogService.log.e('❌ Error stopping audio: $e');
    }
  }

  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
      LogService.log.i('⏭️ Audio seeked to: ${position.inSeconds}s');
    } catch (e) {
      LogService.log.e('❌ Error seeking audio: $e');
    }
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  double get progress {
    if (totalDuration.value.inMilliseconds == 0) return 0.0;
    return currentPosition.value.inMilliseconds /
        totalDuration.value.inMilliseconds;
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }
}
