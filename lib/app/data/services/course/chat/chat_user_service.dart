import 'dart:convert';

import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/chat_user_by_id_model.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/message_chat_model.dart';
import 'package:mides_skadik/app/data/models/response/course/chat/list_user_chat_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class ChatUserService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  // TODO: Get Riwayat Chat
  Future<ResultModel<List<MessageChatModel>>> getRiwayatChat(
      {required String userId,
      required String partnerId,
      int? limit = 30,
      int? page = 1}) async {
    final url =
        "$baseApiUrl$urlChatHistory/$userId/$partnerId?page=$page&limit=$limit";
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, token);
    final datas = jsonDecode(response.body);
    LogService.log.i("Chat List: $datas");

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['datas']
          .map<MessageChatModel>((e) => MessageChatModel.fromJson(e))
          .toList();

      return ResultModel.success(data);
    }
  }

  // TODO: Get Chat User By ID
  Future<ResultModel<List<ChatUserByIdModel>>> getChatUserById({
    required String userId,
  }) async {
    final url = '$baseApiUrl$urlChatUserById/$userId';
    dynamic token = await localStorage.get('token');

    http.Response resposne = await functionGet(url, token);
    final datas = jsonDecode(resposne.body);

    if (resposne.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['datas']
          .map<ChatUserByIdModel>((e) => ChatUserByIdModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }

  // TODO: Get Search User List
  Future<ResultModel<List<ListChatUserModel>>> listUserChat(
      {int? page,
      int? limit = 20,
      String? name = '',
      String? role = ''}) async {
    final url =
        '$baseApiUrl$urlSearchUserChat?page=$page&limit=$limit&search=$name&role=$role';
    dynamic token = await localStorage.get('token');

    http.Response response = await functionGet(url, token);
    final datas = jsonDecode(response.body);

    if (response.statusCode != 200) {
      return ResultModel.failed(datas['message'].toString());
    } else {
      final data = datas['datas']
          .map<ListChatUserModel>((e) => ListChatUserModel.fromJson(e))
          .toList();
      return ResultModel.success(data);
    }
  }
}
