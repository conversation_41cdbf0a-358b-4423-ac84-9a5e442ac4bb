import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/announcement_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class AnnouncementService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<AnnouncementModel>>> getAllAnnouncements({
    required String thnPendidikanId,
    int page = 1,
    int? limit,
  }) async {
    debugPrint(
        '===================== Announcement Service =====================');
    String url =
        "$baseApiUrl/announcement?page=$page&limit=$limit&tahunPendidikanId=$thnPendidikanId";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint(
        '===================== End Announcement Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<AnnouncementModel> data = result['datas']
          .map<AnnouncementModel>((e) => AnnouncementModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal']);
    }
  }

  Future<ResultModel<List<AnnouncementModel>>> getTodayAnnouncements() async {
    debugPrint(
        '===================== Announcement Service =====================');
    String url = "$baseApiUrl/announcement/today";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint(
        '===================== End Announcement Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<AnnouncementModel> data = result['datas']
          .map<AnnouncementModel>((e) => AnnouncementModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal']);
    }
  }

  Future<ResultModel<AnnouncementModel>> getDetail(String id) async {
    debugPrint(
        '===================== Announcement Service =====================');
    String url = "$baseApiUrl/announcement/$id";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint(
        '===================== End Announcement Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      AnnouncementModel data = AnnouncementModel.fromJson(result['datas']);
      return ResultModel.success(data, result['pageTotal']);
    }
  }
}
