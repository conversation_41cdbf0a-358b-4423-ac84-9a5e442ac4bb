import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/schedule_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class ScheduleService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<MonthlySchedule>>> getMonthlySchedule({
    required int month,
    required int year,
    required String tahunPendidikanId,
  }) async {
    final token = await localStorage.get("token");

    final url = "$baseApiUrl$urlSchedule"
        "?month=$month&year=$year&tahunPendidikanId=$tahunPendidikanId";

    LogService.log.i("Fetching Monthly Schedule: $url");

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      );

      LogService.log.d(
          "Monthly Schedule Response: ${response.statusCode}${response.body}");

      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        LogService.log.i("Decoded JSON: $decoded");

        final model = ScheduleModel.fromJson(decoded);
        LogService.log.i("ScheduleModel parsed: ${model.datas.length} items");

        return ResultModel.success(model.datas);
      } else {
        return ResultModel.failed("Gagal mendapatkan data schedule");
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }
}
