import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/models/response/course/schedule/event/event_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

enum EventStatus { All, Upcoming, Complete }

extension EventStatusExtension on EventStatus {
  String get name => toString().split('.').last;
}

class EventService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<CalendarEvent>>> getListEvent({
    int page = 1,
    EventStatus eventStatus = EventStatus.All,
  }) async {
    final url =
        "$baseApiUrl$urlEvent?page=$page&eventStatus=${eventStatus.name}";
    final token = await localStorage.get("token");
    final http.Response response = await functionGet(url, token);

    if (response.statusCode != 200) {
      return const ResultModel.failed("Terjadi kesalahan server");
    }

    try {
      final jsonResult = jsonDecode(response.body);
      if (jsonResult['status'] != true) {
        return ResultModel.failed(jsonResult['message'] ?? 'Unknown error');
      }

      final data = List<CalendarEvent>.from(
        jsonResult['data'].map((e) => CalendarEvent.fromJson(e)),
      );
      return ResultModel.success(data, jsonResult['pagination']['totalRows']);
    } catch (e) {
      return const ResultModel.failed("Format data dari server tidak valid");
    }
  }
}
