import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_by_id_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_progress_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_question_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_review_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/start_quiz_model.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/submit_question_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class QuizService {
  /* Instance storageService */
  StorageService storageService = StorageService();
  /* Instance localStorage */
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<QuizModel>>> getQuizList(
      {int page = 1,
      int length = 10,
      search = '',
      status = '',
      deadline = '',
      time = '',
      required String type}) async {
    String url =
        "$baseApiUrl$urlQuizFilter?type=$type&search=$search&page=1&limit=$length&quizStatus=$status&deadline=$deadline&time=$time";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log.i(
        "RESPONSE BODY getQuizList : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data =
          result['datas'].map<QuizModel>((e) => QuizModel.fromJson(e)).toList();
      return ResultModel.success(data, result['pageTotal']);
    }
  }

  Future<ResultModel<QuizByIdModel>> getQuizListById({required id}) async {
    String url = "$baseApiUrl$urlQuizFilter/$id";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log.i(
        "RESPONSE BODY getQuizListById : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(QuizByIdModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<StartQuizModel>> postStartQuiz(
      {required String userId, required String quizId}) async {
    String url = "$baseApiUrl$urlStartQuiz";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionPost(
      url: url,
      token: token,
      object: {
        "userId": userId,
        "quizId": quizId,
      },
    );

    LogService.log.i(
        "RESPONSE BODY postStartQuiz : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(StartQuizModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<QuizQuestionModel>> getQuizQuestionId(
      {required id, required String userId}) async {
    String url = "$baseApiUrl$urlQuizQuestion/$id/$userId";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log.i(
        "RESPONSE BODY getQuizQuestionId : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(QuizQuestionModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<SubmitQuestionModel>> postSubmitQuestion(
      {required String userId,
      required String quizQuestionId,
      required String answer,
      required String userTakeQuizId}) async {
    String url = "$baseApiUrl$urlSubmitQuestion";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionPost(
      url: url,
      token: token,
      object: {
        "userId": userId,
        "quizQuestionId": quizQuestionId,
        "answer": answer,
        "userTakeQuizId": userTakeQuizId,
      },
    );

    LogService.log.i(
        "RESPONSE BODY postSubmitQuestion : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(
          SubmitQuestionModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel> postFinishQuiz(
      {required String userId,
      required String quizId,
      required String userTakeQuizId,
      required String spendTime}) async {
    String url = "$baseApiUrl$urlFinishQuiz";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionPost(
      url: url,
      token: token,
      object: {
        "userId": userId,
        "quizId": quizId,
        "userTakeQuizId": userTakeQuizId,
        "spendTime": spendTime
      },
    );

    LogService.log.i(
        "RESPONSE BODY postFinishQuiz : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(result['data'], 0);
    }
  }

  Future<ResultModel<QuizProgressModel>> getQuizProgress(
      {required quizId, required String userId}) async {
    String url = "$baseApiUrl$urlQuizProgress/$userId/$quizId";
    LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log.i(
        "RESPONSE BODY getQuizProgress : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(QuizProgressModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<List<QuizReviewModel>>> getQuizReview(
      {required userTakeQuizId}) async {
    String url = "$baseApiUrl$urlQuizReview/$userTakeQuizId";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log.i(
        "RESPONSE BODY getQuizReview : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    if (response.statusCode != 200) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data = result['data']
          .map<QuizReviewModel>((e) => QuizReviewModel.fromJson(e))
          .toList();
      return ResultModel.success(data, 0);
    }
  }
}
