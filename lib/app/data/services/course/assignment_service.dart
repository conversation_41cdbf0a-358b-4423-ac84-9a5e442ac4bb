import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/assignment_calendar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class AssignmentService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<AssignmentModel>>> getLastestAssignmentNearest() async {
    debugPrint('===================== Assignment Service =====================');
    String url = "$baseApiUrl/assignment";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint('===================== End Assignment Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<AssignmentModel> data = result['datas'].map<AssignmentModel>((e) => AssignmentModel.fromJson(e)).toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel<List<AssignmentCalendarModel>>> getByMonth({
    required int month,
    required int year,
  }) async {
    debugPrint('===================== Assignment Calendar Service =====================');
    String url = "$baseApiUrl/assignment/calendar?month=$month&year=$year";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint('===================== End Assignment Calendar Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      List<AssignmentCalendarModel> data = result['data'].map<AssignmentCalendarModel>((e) => AssignmentCalendarModel.fromJson(e)).toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }
}
