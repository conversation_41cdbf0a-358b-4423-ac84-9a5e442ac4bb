import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/attendance/attendances_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:ntp/ntp.dart';

class HistoryAttendance {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<AttendanceModel>>> getTodayAttendanceHistory() async {
    DateTime now;
    try {
      now = await NTP.now();
      LogService.log.i('Real NTP Time: $now');
    } catch (e) {
      LogService.log.w('Gagal ambil NTP, fallback ke DateTime.now()');
      now = DateTime.now();
    }

    final String todayFormatted =
        "${now.year.toString().padLeft(4, '0')}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";

    final String url =
        "$baseApiUrl$urlAttendance/history?startDate=$todayFormatted";

    debugPrint('URL: $url');
    final dynamic token = await localStorage.get('token');

    final http.Response response = await functionGet(url, token);
    final result = jsonDecode(response.body);

    LogService.log.i('Response Body History: $result');

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      final local = LocalStorage();
      final List<dynamic> datas = result['datas'] ?? [];

      if (datas.isNotEmpty) {
        final List<AttendanceModel> attendances =
            datas.map((e) => AttendanceModel.fromJson(e)).toList();

        AttendanceModel? todayAttendance;
        for (final att in attendances) {
          if (att.date != null) {
            final attendanceDate = att.date!;
            final attendanceDateFormatted =
                "${attendanceDate.year.toString().padLeft(4, '0')}-${attendanceDate.month.toString().padLeft(2, '0')}-${attendanceDate.day.toString().padLeft(2, '0')}";

            LogService.log.i(
                'Attendance Date: $attendanceDateFormatted | NTP Today: $todayFormatted');

            if (attendanceDateFormatted == todayFormatted) {
              todayAttendance = att;
              break;
            }
          }
        }

        if (todayAttendance != null) {
          final todayId = todayAttendance.id ?? '';
          final clockIn = todayAttendance.clockIn;
          final clockOut = todayAttendance.clockOut;

          LogService.log.i('clockIn: $clockIn, clockOut: $clockOut');

          if (clockIn != null && clockOut == null) {
            final clockInDateFormatted =
                "${clockIn.year.toString().padLeft(4, '0')}-${clockIn.month.toString().padLeft(2, '0')}-${clockIn.day.toString().padLeft(2, '0')}";

            if (clockInDateFormatted == todayFormatted) {
              await local.set("today_attendance_id", todayId);
              LogService.log
                  .i('ID kehadiran disimpan (ClockIn today): $todayId');
            } else {
              await local.remove("today_attendance_id");
              LogService.log.w('ClockIn bukan hari ini, ID tidak disimpan.');
            }
          } else {
            await local.remove("today_attendance_id");
            LogService.log.i('Sudah ClockOut atau invalid, ID dihapus.');
          }
        } else {
          await local.remove("today_attendance_id");
          LogService.log.w('Tidak ada attendance yang match hari ini.');
        }

        return ResultModel.success(attendances, null);
      } else {
        await local.remove("today_attendance_id");
        LogService.log.w('Tidak ada data attendance, ID dihapus.');
        return const ResultModel.success([], null);
      }
    }
  }
}
