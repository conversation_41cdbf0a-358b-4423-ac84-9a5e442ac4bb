import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/attendance/attendances_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class AttendanceService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<AttendanceModel>>> getListHistory(
      {int? page, String? startDate, String? endDate}) async {
    LogService.log
        .d('===================== Attendance Service =====================');
    String url =
        "$baseApiUrl/attendance/history?startDate=$startDate&endDate=$endDate&page=$page";
    LogService.log.d('URL ALL HISTORY: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log
        .d('===================== Response Body =====================');
    LogService.log.d(response.body);
    LogService.log.d(
        '===================== End Attendance Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<AttendanceModel> data = result['datas']
          .map<AttendanceModel>((e) => AttendanceModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel<AttendanceModel>> postAttendance(String imagePath) async {
    String url = "$baseApiUrl$urlAttendance/clock-in";
    LogService.log.i('URL: $url');
    LogService.log.d('File Path (Clock In): $imagePath');

    dynamic token = await localStorage.get('token');

    http.StreamedResponse response = await functionPostMultiPart(
      url: url,
      token: token,
      filePath: imagePath,
      fileFieldName: 'photo',
    );

    final responseBody = await response.stream.bytesToString();
    LogService.log.d('Response Body (Clock In): $responseBody');

    var result = jsonDecode(responseBody);
    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      return ResultModel.success(
        AttendanceModel.fromJson(result['data']),
        null,
      );
    }
  }

  Future<ResultModel<AttendanceModel>> postClockOut(
      String attendanceId, String imagePath) async {
    String url = "$baseApiUrl$urlAttendance/clock-out";
    LogService.log.i('URL Clock-Out: $url');
    LogService.log.d('File Path (Clock Out): $imagePath');

    dynamic token = await localStorage.get('token');

    http.StreamedResponse response = await functionPostMultiPart(
      url: url,
      token: token,
      filePath: imagePath,
      fileFieldName: 'photo',
    );

    final responseBody = await response.stream.bytesToString();
    LogService.log.d('Response Body ClockOut: $responseBody');

    try {
      final responseJson = jsonDecode(responseBody);

      if (responseJson['status'] != true) {
        if (responseJson['message'].toString() == 'Token is not valid') {
          await storageService.postSessionLogout();
          return const ResultModel.failed('Token is not valid', {});
        }
        return ResultModel.failed(
            responseJson['message'].toString(), responseJson['datas']);
      } else {
        return ResultModel.success(
          AttendanceModel.fromJson(responseJson['data']),
          null,
        );
      }
    } catch (e) {
      LogService.log.e('Error parsing JSON ClockOut: $e');
      return const ResultModel.failed('Format response tidak sesuai', {});
    }
  }

  Future<ResultModel<List<AttendanceModel>>> getListPreviewHistory() async {
    LogService.log
        .d('===================== Attendance Service =====================');
    String url = "$baseApiUrl$urlAttendance/history";
    LogService.log.d('URL ALL PREVIEW HISTORY: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    LogService.log
        .d('===================== Response Body =====================');
    LogService.log.d(" response all history ${response.body}");
    LogService.log.d(
        '===================== End Attendance Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<AttendanceModel> data = result['datas']
          .map<AttendanceModel>((e) => AttendanceModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }
}
