import 'dart:convert';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/accumulated_grades/accumulated_grades_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class AccumulatedScoreService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<AccumulatedScore>> getAccumulatedScore() async {
    final token = await localStorage.get('token');
    final url = "$baseApiUrl$urlAccumulatedGradesScore";
    LogService.log.d('URL: $url');

    final response = await functionGet(url, token);

    LogService.log.d('Response Grades: ${response.body}');

    final result = jsonDecode(response.body);

    if (result['success'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      final score = AccumulatedScore.fromJson(result['data']);
      return ResultModel.success(score, 1);
    }
  }
}
