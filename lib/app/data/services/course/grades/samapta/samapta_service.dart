import 'dart:convert';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/samapta/samapta_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class SamaptaService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<SamaptaModel>> getSamaptaData() async {
    final token = await localStorage.get('token');
    final url = "$baseApiUrl$urlSamaptaGrades";

    LogService.log.d('GET Samapta URL: $url');

    final response = await functionGet(url, token);
    final result = jsonDecode(response.body);

    LogService.log.d('Response Samapta: ${response.body}');

    if (result['success'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid');
      }
      return ResultModel.failed(
        result['message']?.toString() ?? 'Unknown error',
      );
    } else {
      final samaptaData = SamaptaModel.fromJson(result['data']);
      return ResultModel.success(samaptaData);
    }
  }
}
