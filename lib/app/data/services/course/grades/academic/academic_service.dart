import 'dart:convert';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/academic/academic_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class AcademicService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<AcademicModel>> getAcademicData() async {
    final token = await localStorage.get('token');
    final url = "$baseApiUrl$urlAcademicGrades";

    LogService.log.d('GET Academic URL: $url');

    final response = await functionGet(url, token);
    final result = jsonDecode(response.body);

    LogService.log.d('Response Academic: ${response.body}');

    if (result['success'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid');
      }
      return ResultModel.failed(
          result['message']?.toString() ?? 'Unknown error');
    } else {
      final academicData = AcademicModel.fromJson(result['data']);
      return ResultModel.success(academicData);
    }
  }
}
