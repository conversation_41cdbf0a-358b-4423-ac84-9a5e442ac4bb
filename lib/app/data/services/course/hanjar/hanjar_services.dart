import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/mapel_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/hanjar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/open_hanjar_model.dart';
import 'package:mides_skadik/app/data/models/response/course/hanjar/submapel_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class HanjarServices {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<HanjarModel>>> getAllHanjar() async {
    debugPrint('===================== Hanjar Service =====================');
    String url = "$baseApiUrl/hanjar";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Response Body =====================');
    debugPrint(response.body);
    debugPrint(
        '===================== End Hanjar Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      List<HanjarModel> data = result['data']
          .map<HanjarModel>((e) => HanjarModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel<OpenHanjarModel>> getOpenHanjar(
      {required String id}) async {
    String url = "$baseApiUrl/hanjar/$id";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    // LogService.log.i(
    //     "RESPONSE BODY getQuizListById : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(OpenHanjarModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<List<SubmapelModel>>> getSubMapel(
      {required String id}) async {
    String url = "$baseApiUrl/sub-mapel?mapelId=$id";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(
      url,
      token,
    );

    print("======= Get Submapel =======");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<SubmapelModel> data = result['datas']
          .map<SubmapelModel>((e) => SubmapelModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel<List<MapelModel>>> getMapel() async {
    String url = "$baseApiUrl/mapel";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    print("======= Get Submapel =======");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      List<MapelModel> data = result['datas']
          .map<MapelModel>((e) => MapelModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel> postMark({required String id}) async {
    String url = "$baseApiUrl/hanjar/$id/toggle-complete";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    var response = await http
        .post(Uri.parse(url), headers: {"Authorization": "Bearer $token"});

    // LogService.log.i(
    //     "RESPONSE BODY getQuizListById : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(result);
    }
  }

  void debugPrint(String s) {}
}
