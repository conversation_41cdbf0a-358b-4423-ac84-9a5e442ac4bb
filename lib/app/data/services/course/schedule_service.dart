import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/schedule_monthly_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class ScheduleService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<ScheduleMonthlyModel>>> getTodaySchedule() async {
    debugPrint('===================== Schedule Service =====================');
    String url = "$baseApiUrl/detail-schedule/today";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Body =====================');
    debugPrint(response.body);
    debugPrint(
        '===================== End Schedule Service =====================');
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data = result['datas']
          .map<ScheduleMonthlyModel>((e) => ScheduleMonthlyModel.fromJson(e))
          .toList();
      return ResultModel.success(data, null);
    }
  }

  Future<ResultModel<List<ScheduleMonthlyModel>>> getMontlySchedule({
    required int month,
    required int year,
  }) async {
    debugPrint('===================== Schedule Service =====================');
    String url = "$baseApiUrl/detail-schedule/monthly?month=$month&year=$year";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Body =====================');
    debugPrint(jsonDecode(response.body).toString());
    debugPrint(
        '===================== End Schedule Service =====================');
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data = result['datas']
          .map<ScheduleMonthlyModel>((e) => ScheduleMonthlyModel.fromJson(e))
          .toList();
      return ResultModel.success(data, null);
    }
  }
}
