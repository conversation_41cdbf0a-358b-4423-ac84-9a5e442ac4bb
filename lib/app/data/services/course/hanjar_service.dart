import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/dashboard/last_hanjar_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class HanjarService {
  StorageService storageService = StorageService();
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<LastHanjarModel>>> getLatestHanjar() async {
    debugPrint('===================== Hanjar Service =====================');
    String url = "$baseApiUrl/hanjar/latest";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Body =====================');
    debugPrint(jsonDecode(response.body).toString());
    debugPrint(
        '===================== End Hanjar Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      var data = result['data']
          .map<LastHanjarModel>((e) => LastHanjarModel.fromJson(e))
          .toList();
      return ResultModel.success(data, null);
    }
  }

  Future<ResultModel<List<LastHanjarModel>>> getTodayHanjar(
      {String? tahunPendidikanId = ''}) async {
    debugPrint('===================== Hanjar Service =====================');
    String url = "$baseApiUrl/hanjar?tahunPendidikanId=$tahunPendidikanId";
    debugPrint('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    debugPrint('===================== Body =====================');
    debugPrint(jsonDecode(response.body).toString());
    debugPrint(
        '===================== End Hanjar Service =====================');

    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      var data = result['data']
          .map<LastHanjarModel>((e) => LastHanjarModel.fromJson(e))
          .toList();
      return ResultModel.success(data, null);
    }
  }
}
