import 'dart:convert';
import 'dart:io';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/feedback/feedback_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';

class FeedbackService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<bool>> postFeedback({
    required String content,
    File? image1,
    File? image2,
    File? image3,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlFeedback";

    try {
      LogService.log.i('Submitting Feedback');
      LogService.log.i('Content: $content');

      final fields = {"content": content};

      if (image1 != null) {
        LogService.log.i("Image1 path: ${image1.path}");
        await functionPostMultiPart(
          url: url,
          token: token,
          filePath: image1.path,
          fileFieldName: 'image1',
          fields: fields,
        );
        fields.remove("content");
      } else {
        LogService.log.i("⚠️ Image1 is null");
      }

      if (image2 != null) {
        LogService.log.i("Image2 path: ${image2.path}");
        await functionPostMultiPart(
          url: url,
          token: token,
          filePath: image2.path,
          fileFieldName: 'image2',
          fields: fields.isEmpty ? {"content": content} : null,
        );
      } else {
        LogService.log.i("⚠️ Image2 is null");
      }

      if (image3 != null) {
        LogService.log.i("Image3 path: ${image3.path}");
        await functionPostMultiPart(
          url: url,
          token: token,
          filePath: image3.path,
          fileFieldName: 'image3',
          fields: fields.isEmpty ? {"content": content} : null,
        );
      } else {
        LogService.log.i("⚠️ Image3 is null");
      }

      LogService.log.i("Feedback posted successfully");
      return const ResultModel.success(true, 1);
    } catch (e) {
      LogService.log.e("Exception in postFeedback: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<List<FeedbackModel>>> getFeedbackList() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlFeedback";

    try {
      LogService.log.i('Fetching feedback list from $url');

      final response = await functionGet(url, token);
      LogService.log.d('Feedback List Response: ${response.body}');

      final result = jsonDecode(response.body);
      if (response.statusCode == 200 && result['status'] == true) {
        final List<dynamic> data = result['datas'];

        final feedbackList =
            data.map((e) => FeedbackModel.fromJson(e)).toList();
        return ResultModel.success(feedbackList, feedbackList.length);
      } else {
        return ResultModel.failed(result['message'] ?? 'Unknown error');
      }
    } catch (e) {
      LogService.log.e("Exception in getFeedbackList: $e");
      return ResultModel.failed("Exception: $e");
    }
  }
}
