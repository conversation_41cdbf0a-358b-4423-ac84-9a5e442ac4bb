// ignore_for_file: avoid_print, unused_local_variable, prefer_const_constructors

import 'dart:async';
import 'dart:convert';

import 'package:file_picker/file_picker.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/detail_assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/history_assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/course/submisson_assignment_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:http/http.dart' as http;

class AssignmentService {
  /* Instance storageService */
  StorageService storageService = StorageService();
  /* Instance localStorage */
  LocalStorage localStorage = LocalStorage();

  Future<ResultModel<List<AssignmentModel>>> getAssignmentList() async {
    print('===================== Assignment Service =====================');
    String url = "$baseApiUrl/assignment/semester";
    // "$baseApiUrl$urlAssignment?type=Assignment&search=$search&page=1&limit=$length";
    print('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    print('===================== Body =====================');
    print(jsonDecode(response.body).toString());
    print('===================== End Assignment Service =====================');
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data = result['datas']
          .map<AssignmentModel>((e) => AssignmentModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel<List<HistoryAssignmentModel>>>
      getHistoryAssignment() async {
    print(
        '===================== Assignment History Service =====================');
    String url = "$baseApiUrl/assignment/history";
    // "$baseApiUrl$urlAssignment?type=Assignment&search=$search&page=1&limit=$length";
    print('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    print('===================== Body =====================');
    print(jsonDecode(response.body).toString());
    print(
        '===================== End Assignment History Service =====================');
    var result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['datas']);
    } else {
      var data = result['datas']
          .map<HistoryAssignmentModel>(
              (e) => HistoryAssignmentModel.fromJson(e))
          .toList();
      return ResultModel.success(data, result['pageTotal'] ?? 0);
    }
  }

  Future<ResultModel> uploadAssignment(
      {required PlatformFile file, required String id}) async {
    print(
        '===================== Assignment Service Upload =====================');

    String url = "$baseApiUrl/assignment-answer/$id/upload";
    print('URL: $url');

    var uri = Uri.parse(url);
    var request = http.MultipartRequest('POST', uri);

    // Ambil token dari local storage
    dynamic token = await localStorage.get('token');

    // Set header
    request.headers.addAll({
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
      'User-Agent': 'insomnia/11.0.2', // Ini opsional tergantung server
    });

    // Tambahkan file ke request
    if (file.bytes != null) {
      request.files.add(http.MultipartFile.fromBytes(
        'file', // Sesuaikan nama field sesuai dengan yang diminta server (biasanya 'file')
        file.bytes!, // Kirimkan bytes dari file
        filename: file.name, // Nama file sesuai dengan file yang dipilih
        contentType: MediaType('application', 'pdf'),
      ));

      print('File Name: ${file.name}');
      print('File Size: ${file.bytes?.length}');
      print('File bytes: ${file.bytes}'); // Debug untuk melihat bytes
    } else {
      return ResultModel.failed("File tidak valid (bytes null)", {});
    }

    // Kirim request
    var streamedResponse = await request.send();

    print('Request sent: ${streamedResponse.request}');

    // Convert response
    var response = await http.Response.fromStream(streamedResponse);
    print('Response body: ${response.body}');
    print('===================== End Assignment Service =====================');

    // Decode response JSON
    var result = jsonDecode(response.body);

    // Cek status dari response
    if (result['status'] != true) {
      return ResultModel.failed(result['message'].toString(), {});
    } else {
      // Return success result dengan data yang diterima dari server
      var data = result;
      return ResultModel.success(result);
    }
  }

  Future<ResultModel> deleteSubmission(
      {required String id, required String file}) async {
    // var url = Uri.parse("$baseApiUrl/assignment-answer/$id");
    final token = await localStorage.get('token');

    String url = "$baseApiUrl/assignment-answer/$id";

    var uri = Uri.parse(url);
    var request = http.MultipartRequest('DELETE', uri);
    request.headers.addAll(
        {"Content-Type": "application/json", "Authorization": "Bearer $token"});

    // Tambahkan body JSON di sini
    request.files.add(http.MultipartFile.fromString(
      'file',
      file,
      contentType: MediaType('application', 'pdf'),
    ));

    // Kirim request
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);

    final result = jsonDecode(response.body);
    print("====== Delete Submission ======");

    print(result);

    if (response.statusCode == 200) {
      return ResultModel.success(result);
    } else {
      return ResultModel.failed(
          result['message']?.toString() ?? 'Gagal hapus data');
    }
  }

  Future<ResultModel<DetailAssignmentModel>> getAssignementDetail(
      {required String id}) async {
    String url = "$baseApiUrl/assignment/$id";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    print("==== Detail  Assignemnt ====");
    // LogService.log.i(
    //     "RESPONSE BODY getQuizListById : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(
          DetailAssignmentModel.fromJson(result['data']), 0);
    }
  }

  Future<ResultModel<SubmissonAssignmentModel>> getAssignemntAnswer(
      {required String id}) async {
    String url = "$baseApiUrl$urlUploadAssignment/$id";
    // LogService.log.i('URL: $url');
    dynamic token = await localStorage.get('token');
    http.Response response = await functionGet(url, token);

    // LogService.log.i(
    //     "RESPONSE BODY getQuizListById : ${jsonDecode(response.body).toString()}");
    var result = jsonDecode(response.body);

    print(result);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      return ResultModel.success(
          SubmissonAssignmentModel.fromJson(result['data']), 0);
    }
  }
}
