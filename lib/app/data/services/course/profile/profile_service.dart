import 'dart:convert';
import 'dart:io';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/profile_model.dart'; // ini yang benar
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as p;

class ProfileService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<ProfileModel>> getProfile() async {
    LogService.log
        .d('===================== Profile Service =====================');
    String url = "$baseApiUrl$urlProfile/profile";
    LogService.log.d('URL PROFILE: $url');

    final token = await localStorage.get('token');
    final response = await functionGet(url, token);

    LogService.log.d('Response Profile: ${response.body}');
    LogService.log
        .d('===================== End Profile Service =====================');

    final result = jsonDecode(response.body);

    if (result['status'] != true) {
      if (result['message'].toString() == 'Token is not valid') {
        await storageService.postSessionLogout();
        return const ResultModel.failed('Token is not valid', {});
      }
      return ResultModel.failed(result['message'].toString(), result['data']);
    } else {
      final profile = ProfileModel.fromJson(result['data']);
      return ResultModel.success(profile, 1);
    }
  }

  Future<ResultModel<bool>> updateProfile({
    String? name,
    String? email,
    String? username,
    String? phoneNumber,
    File? image,
  }) async {
    LogService.log.d(
        '===================== Update Profile (Multipart) =====================');

    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlProfile/profile";

    try {
      final request = http.MultipartRequest("PATCH", Uri.parse(url));
      request.headers.addAll({
        HttpHeaders.authorizationHeader: token ?? '',
      });

      // Tambahkan field teks jika tidak null/kosong
      if (name != null && name.isNotEmpty) request.fields["name"] = name;
      if (email != null && email.isNotEmpty) request.fields["email"] = email;
      if (username != null && username.isNotEmpty) {
        request.fields["username"] = username;
      }
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        request.fields["phoneNumber"] = phoneNumber;
      }

      // Tambahkan gambar jika ada
      if (image != null) {
        final mimeType =
            lookupMimeType(image.path) ?? 'application/octet-stream';
        final fileName = p.basename(image.path);

        LogService.log.i('Menambahkan image: $fileName ($mimeType)');

        request.files.add(await http.MultipartFile.fromPath(
          'imageProfile',
          image.path,
          contentType: MediaType.parse(mimeType),
        ));
      }

      final streamedResponse = await request.send();
      final responseString = await streamedResponse.stream.bytesToString();

      LogService.log.d("Response body: $responseString");

      if (streamedResponse.statusCode == 200) {
        LogService.log.i("✔️ Profile successfully updated (multipart)");
        return const ResultModel.success(true, 1);
      } else {
        LogService.log
            .e("Gagal update profile: ${streamedResponse.statusCode}");
        return const ResultModel.failed("Failed to update profile", {});
      }
    } catch (e) {
      LogService.log.e("Exception in multipart updateProfile: $e");
      return ResultModel.failed("Exception: $e", {});
    }
  }
}
