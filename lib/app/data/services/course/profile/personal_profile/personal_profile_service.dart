import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class PersonalProfileService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<bool>> postPersonalProfile({
    String? fullName,
    String? corps,
    String? nrp,
    String? rank,
    String? placeOfBirth,
    String? dateOfBirth,
    String? religion,
    String? ethnicity,
    String? bloodType,
    String? nationality,
    int? height,
    int? weight,
    String? hobbies,
    String? homeAddress,
    String? homePhoneNumber,
    String? mobileNumber,
    String? emailAddress,
    String? lastPosition,
    String? commissioningSource,
    String? graduationYear,
    String? effectiveStartDate,
    String? currentUnit,
    String? unitAddress,
    String? unitPhoneNumber,
    String? unitFaxNumber,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlPersonalProfile";

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "fullName": fullName,
          "corps": corps,
          "NRP": nrp,
          "rank": rank,
          "placeOfBirth": placeOfBirth,
          "dateOfBirth": dateOfBirth,
          "religion": religion,
          "ethnicity": ethnicity,
          "bloodType": bloodType,
          "nationality": nationality,
          "height": height,
          "weight": weight,
          "hobbies": hobbies,
          "homeAddress": homeAddress,
          "homePhoneNumber": homePhoneNumber,
          "mobileNumber": mobileNumber,
          "emailAddress": emailAddress,
          "lastPosition": lastPosition,
          "commissioningSource": commissioningSource,
          "graduationYear": graduationYear,
          "effectiveStartDate": effectiveStartDate,
          "currentUnit": currentUnit,
          "unitAddress": unitAddress,
          "unitPhoneNumber": unitPhoneNumber,
          "unitFaxNumber": unitFaxNumber,
        }),
      );

      LogService.log.d('POST Personal Profile: ${response.statusCode}');
      LogService.log.d('Response Body: ${response.body}');

      if (response.statusCode == 201) {
        return const ResultModel.success(true);
      } else {
        final json = jsonDecode(response.body);
        return ResultModel.failed(json['message'] ?? 'Failed to save data');
      }
    } catch (e) {
      LogService.log.e('Exception in postPersonalProfile: $e');
      return ResultModel.failed('Exception: $e');
    }
  }

  Future<ResultModel<Map<String, dynamic>?>> getPersonalProfile() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlPersonalProfile";

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      );

      LogService.log.d('GET Personal Profile: ${response.statusCode}');
      LogService.log.d('Response Body: ${response.body}');
      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // ✅ Selalu return success, meskipun data null
        return ResultModel.success(data['data']);
      } else {
        return ResultModel.failed(
            data['message'] ?? 'Gagal mengambil data profil');
      }
    } catch (e) {
      LogService.log.e('Exception in getPersonalProfile: $e');
      return ResultModel.failed('Exception: $e');
    }
  }
}
