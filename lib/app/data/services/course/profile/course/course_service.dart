import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/course/course_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mime/mime.dart';

class CoursesService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<bool>> postCourse({
    required String courseName,
    required int yearStarted,
    required int yearCompleted,
    required String certificateFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlCourse";

    try {
      final response = await functionPostMultiPart(
        url: url,
        token: token,
        filePath: certificateFilePath,
        fileFieldName: 'certificate',
        fields: {
          "courseName": courseName,
          "yearStarted": yearStarted.toString(),
          "yearCompleted": yearCompleted.toString(),
        },
      );

      final res = await http.Response.fromStream(response);
      LogService.log.d("Course Response: ${res.statusCode} - ${res.body}");
      final json = jsonDecode(res.body);

      if (res.statusCode == 201 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Unknown error');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<List<CourseModel>>> getCourses() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlCourse";

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      final json = jsonDecode(response.body);

      if (response.statusCode == 200 && json['status'] == true) {
        final data = json['data'] as List;
        final courses = data.map((e) => CourseModel.fromJson(e)).toList();
        return ResultModel.success(courses);
      } else {
        return ResultModel.failed(json['message'] ?? 'Failed to load courses');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<bool>> deleteCourse(String id) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlCourse/$id";

    try {
      final response = await http.delete(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      final json = jsonDecode(response.body);

      if (response.statusCode == 200 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Delete failed');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<bool>> editCourse({
    required String id,
    required String courseName,
    required int yearStarted,
    required int yearCompleted,
    String? certificateFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlCourse/$id";

    try {
      final request = http.MultipartRequest('PATCH', Uri.parse(url));
      request.headers['Authorization'] = token;
      request.fields['courseName'] = courseName;
      request.fields['yearStarted'] = yearStarted.toString();
      request.fields['yearCompleted'] = yearCompleted.toString();

      if (certificateFilePath != null &&
          certificateFilePath.isNotEmpty &&
          !certificateFilePath.startsWith('http')) {
        final mimeType =
            lookupMimeType(certificateFilePath) ?? 'application/octet-stream';
        final fileBytes = await File(certificateFilePath).readAsBytes();
        final fileName = certificateFilePath.split('/').last;

        final file = http.MultipartFile.fromBytes(
          'certificate',
          fileBytes,
          filename: fileName,
          contentType: MediaType.parse(mimeType),
        );

        request.files.add(file);
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final json = jsonDecode(response.body);

      if (response.statusCode == 200 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Update failed');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }
}
