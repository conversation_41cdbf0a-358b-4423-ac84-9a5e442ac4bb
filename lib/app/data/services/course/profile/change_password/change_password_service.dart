import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class ChangePasswordService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<bool>> changePassword({
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    LogService.log
        .d('===================== Change Password =====================');

    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlChangrPassword";

    final payload = {
      "oldPassword": oldPassword,
      "newPassword": newPassword,
      "confirmPassword": confirmPassword,
    };

    try {
      final response = await http.patch(
        Uri.parse(url),
        headers: {
          HttpHeaders.authorizationHeader: token ?? '',
          HttpHeaders.contentTypeHeader: 'application/json',
        },
        body: jsonEncode(payload),
      );

      LogService.log.d("Change Password Response: ${response.body}");

      final result = jsonDecode(response.body);
      final status = result['status'] == true;

      if (status) {
        LogService.log.i("🔐 Password successfully changed");
        return const ResultModel.success(true, 1);
      } else {
        LogService.log.w("Failed to change password: ${result['message']}");
        return ResultModel.failed(result['message'].toString(), {});
      }
    } catch (e) {
      LogService.log.e("Exception in changePassword: $e");
      return ResultModel.failed("Exception: $e", {});
    }
  }
}
