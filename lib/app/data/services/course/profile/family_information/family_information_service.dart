import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/family_information/family_information_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class FamilyInformationService {
  final LocalStorage localStorage = LocalStorage();
  final StorageService storageService = StorageService();

  Future<ResultModel<bool>> postFamilyInformation({
    required String fatherName,
    required String fatherOccupation,
    required String motherName,
    required String motherMobileNumber,
    required String parentsAddress,
    required String spouseName,
    required String spousePlaceOfBirth,
    required String spouseDateOfBirth,
    required String spousePlaceOfMarriage,
    required String spouseDateOfMarriage,
    required String spouseOccupation,
    required String spouseEducationTitle,
    required String spousePhoneNumber,
    required String spouseHomeAddress,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlFamilyInformation";

    LogService.log.i('URL Family Information $url');

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "fatherName": fatherName,
          "fatherOccupation": fatherOccupation,
          "motherName": motherName,
          "motherMobileNumber": motherMobileNumber,
          "parentsAddress": parentsAddress,
          "spouseName": spouseName,
          "spousePlaceOfBirth": spousePlaceOfBirth,
          "spouseDateOfBirth": spouseDateOfBirth,
          "spousePlaceOfMarriage": spousePlaceOfMarriage,
          "spouseDateOfMarriage": spouseDateOfMarriage,
          "spouseOccupation": spouseOccupation,
          "spouseEducationTitle": spouseEducationTitle,
          "spousePhoneNumber": spousePhoneNumber,
          "spouseHomeAddress": spouseHomeAddress,
        }),
      );

      LogService.log.d('POST Family Info: ${response.statusCode}');
      LogService.log.d('Response Body: ${response.body}');

      if (response.statusCode == 201) {
        return const ResultModel.success(true);
      } else {
        final json = jsonDecode(response.body);
        return ResultModel.failed(json['message'] ?? 'Failed to save data');
      }
    } catch (e) {
      LogService.log.e('Exception in postFamilyInformation: $e');
      return ResultModel.failed('Exception: $e');
    }
  }

  Future<ResultModel<FamilyInformationModel>> getFamilyInformation() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlFamilyInformation";

    LogService.log.i('GET Family Information URL: $url');

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      );

      LogService.log.d('GET Family Info: ${response.statusCode}');
      LogService.log.d('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final data = FamilyInformationModel.fromJson(json['data']);
        return ResultModel.success(data);
      } else {
        final json = jsonDecode(response.body);
        return ResultModel.failed(json['message'] ?? 'Gagal mengambil data');
      }
    } catch (e) {
      LogService.log.e('Exception in getFamilyInformation: $e');
      return ResultModel.failed('Exception: $e');
    }
  }
}
