import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/attachment/attachment_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mime/mime.dart';

class AttachmentService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  Future<ResultModel<bool>> postAttachment({
    required String name,
    required String filePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlAttachment";
    LogService.log.d('URL ATTACHMENT $url');

    try {
      final response = await functionPostMultiPart(
        url: url,
        token: token,
        filePath: filePath,
        fileFieldName: 'file',
        fields: {
          "name": name,
        },
      );

      final res = await http.Response.fromStream(response);
      LogService.log.d("Attachment Response: ${res.statusCode} - ${res.body}");
      final json = jsonDecode(res.body);

      if (res.statusCode == 201 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Failed to create');
      }
    } catch (e) {
      LogService.log.e("POST Attachment Exception: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<List<AttachmentModel>>> getAttachments() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlAttachment";

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      final json = jsonDecode(response.body);
      if (response.statusCode == 200 && json['status'] == true) {
        final data = json['data'] as List;
        final items = data.map((e) => AttachmentModel.fromJson(e)).toList();
        return ResultModel.success(items);
      } else {
        return ResultModel.failed(json['message'] ?? 'Failed to load data');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<bool>> deleteAttachment(String id) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlAttachment/$id";

    try {
      final response = await http.delete(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      final json = jsonDecode(response.body);
      if (response.statusCode == 200 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Delete failed');
      }
    } catch (e) {
      return ResultModel.failed("Exception: $e");
    }
  }

  Future<ResultModel<bool>> editAttachment({
    required String id,
    required String name,
    String? filePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlAttachment/$id";
    LogService.log.i("PATCH ATTACHMENT $url");

    try {
      final request = http.MultipartRequest('PATCH', Uri.parse(url));
      request.headers['Authorization'] = token;
      request.fields['name'] = name;

      if (filePath != null && filePath.isNotEmpty && !filePath.startsWith('http')) {
        final file = File(filePath);
        final exists = await file.exists();

        if (!exists) {
          throw Exception("File tidak ditemukan di path: $filePath");
        }

        final mimeType = lookupMimeType(filePath) ?? 'application/octet-stream';
        final fileBytes = await file.readAsBytes();
        final fileName = filePath.split('/').last;

        request.files.add(
          http.MultipartFile.fromBytes(
            'file',
            fileBytes,
            filename: fileName,
            contentType: MediaType.parse(mimeType),
          ),
        );
      }

      final response = await request.send();
      final res = await http.Response.fromStream(response);
      final json = jsonDecode(res.body);

      if (res.statusCode == 200 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Update failed');
      }
    } catch (e) {
      LogService.log.e("PATCH Attachment Exception: $e");
      return ResultModel.failed("Exception: $e");
    }
  }
}
