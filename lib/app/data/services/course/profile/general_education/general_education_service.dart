import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/general_education/general_education_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mime/mime.dart';

class GeneralEducationService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  /// ================================
  /// POST
  Future<ResultModel<bool>> postGeneralEducation({
    required String schoolName,
    required int yearCompleted,
    required String diplomaFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlGeneralEducation";
    LogService.log.d('URL GENERAL EDUCATION $url');

    try {
      final response = await functionPostMultiPart(
        url: url,
        token: token,
        filePath: diplomaFilePath,
        fileFieldName: 'diploma',
        fields: {
          "schoolName": schoolName,
          "yearCompleted": yearCompleted.toString(),
        },
      );

      final res = await http.Response.fromStream(response);
      LogService.log
          .d("General Education Response: ${res.statusCode} - ${res.body}");

      if (res.statusCode == 201) {
        final json = jsonDecode(res.body);
        final status = json['status'] == true;

        if (status) {
          LogService.log.i("General education successfully created.");
          return const ResultModel.success(true);
        } else {
          LogService.log
              .w("Failed to create general education: ${json['message']}");
          return ResultModel.failed(json['message'] ?? 'Unknown error');
        }
      } else {
        throw Exception("Server Error: ${res.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Exception in postGeneralEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// GET
  Future<ResultModel<List<GeneralEducationModel>>> getGeneralEducation() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlGeneralEducation";
    LogService.log.d('GET General Education URL: $url');

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      LogService.log.d(
          'Response GET Data General Education (${response.statusCode}): ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final status = json['status'] == true;

        if (status) {
          final data = json['data'] as List;
          final items =
              data.map((e) => GeneralEducationModel.fromJson(e)).toList();
          return ResultModel.success(items);
        } else {
          return ResultModel.failed(json['message'] ?? 'Failed to load data');
        }
      } else {
        return ResultModel.failed('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LogService.log.e("Exception in getGeneralEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// DELETE
  Future<ResultModel<bool>> deleteGeneralEducation({required String id}) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlGeneralEducation/$id";
    LogService.log.d('DELETE General Education URL: $url');

    try {
      final response = await http.delete(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      LogService.log
          .d('Response DELETE (${response.statusCode}): ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final status = json['status'] == true;

        if (status) {
          LogService.log
              .i("General education with ID $id deleted successfully.");
          return const ResultModel.success(true);
        } else {
          LogService.log
              .w("Failed to delete general education: ${json['message']}");
          return ResultModel.failed(json['message'] ?? 'Delete failed');
        }
      } else {
        return ResultModel.failed('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LogService.log.e("Exception in deleteGeneralEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// EDIT
  Future<ResultModel<bool>> editGeneralEducation({
    required String id,
    required String schoolName,
    required int yearCompleted,
    String? diplomaFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlGeneralEducation/$id";
    LogService.log.d('PATCH General Education URL: $url');

    try {
      final request = http.MultipartRequest('PATCH', Uri.parse(url));
      request.headers['Authorization'] = token;
      request.fields['schoolName'] = schoolName;
      request.fields['yearCompleted'] = yearCompleted.toString();

      // PERBAIKAN DI SINI
      if (diplomaFilePath != null &&
          diplomaFilePath.isNotEmpty &&
          !diplomaFilePath.startsWith('http')) {
        final mimeType =
            lookupMimeType(diplomaFilePath) ?? 'application/octet-stream';
        final fileBytes = await File(diplomaFilePath).readAsBytes();
        final fileName = diplomaFilePath.split('/').last;

        final file = http.MultipartFile.fromBytes(
          'diploma',
          fileBytes,
          filename: fileName,
          contentType: MediaType.parse(mimeType),
        );

        request.files.add(file);
        LogService.log
            .d('📎 File berhasil ditambahkan ke request PATCH: $fileName');
      } else {
        LogService.log.w('Tidak ada file baru dikirim atau file adalah URL.');
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      LogService.log.d("PATCH Response: ${response.body}");

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final status = json['status'] == true;

        if (status) {
          LogService.log
              .i("General education with ID $id updated successfully.");
          return const ResultModel.success(true);
        } else {
          LogService.log.w("Failed to update: ${json['message']}");
          return ResultModel.failed(json['message'] ?? 'Update failed');
        }
      } else {
        return ResultModel.failed('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LogService.log.e("Exception in editGeneralEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }
}
