import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mides_skadik/app/data/constant/network_url.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/military_education/military_education_model.dart';
import 'package:mides_skadik/app/data/models/response/result_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/services/localStorage/storage_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mime/mime.dart';

class MilitaryEducationService {
  final StorageService storageService = StorageService();
  final LocalStorage localStorage = LocalStorage();

  /// ================================
  /// POST
  Future<ResultModel<bool>> postMilitaryEducation({
    required String militaryEducation,
    required String generation,
    required int yearCompleted,
    required String diplomaFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlMilitaryEducation";
    LogService.log.d('URL MILITARY EDUCATION $url');

    try {
      final response = await functionPostMultiPart(
        url: url,
        token: token,
        filePath: diplomaFilePath,
        fileFieldName: 'diploma',
        fields: {
          "militaryEducation": militaryEducation,
          "generation": generation,
          "yearCompleted": yearCompleted.toString(),
        },
      );

      final res = await http.Response.fromStream(response);
      LogService.log
          .d("Military Education Response: ${res.statusCode} - ${res.body}");

      if (res.statusCode == 201) {
        final json = jsonDecode(res.body);
        final status = json['status'] == true;

        if (status) {
          LogService.log.i("Military education successfully created.");
          return const ResultModel.success(true);
        } else {
          return ResultModel.failed(json['message'] ?? 'Unknown error');
        }
      } else {
        throw Exception("Server Error: ${res.statusCode}");
      }
    } catch (e) {
      LogService.log.e("Exception in postMilitaryEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// GET
  Future<ResultModel<List<MilitaryEducationModel>>>
      getMilitaryEducation() async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlMilitaryEducation";
    LogService.log.d('GET Military Education URL: $url');

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      LogService.log.d(
          'Response GET Data Military Education (${response.statusCode}): ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final status = json['status'] == true;

        if (status) {
          final data = json['data'] as List;
          final items =
              data.map((e) => MilitaryEducationModel.fromJson(e)).toList();
          return ResultModel.success(items);
        } else {
          return ResultModel.failed(json['message'] ?? 'Failed to load data');
        }
      } else {
        return ResultModel.failed('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LogService.log.e("Exception in getMilitaryEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// DELETE
  Future<ResultModel<bool>> deleteMilitaryEducation(
      {required String id}) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlMilitaryEducation/$id";
    LogService.log.d('DELETE Military Education URL: $url');

    try {
      final response = await http.delete(
        Uri.parse(url),
        headers: {'Authorization': token},
      );

      LogService.log
          .d('Response DELETE (${response.statusCode}): ${response.body}');

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final status = json['status'] == true;

        if (status) {
          LogService.log
              .i("Military education with ID $id deleted successfully.");
          return const ResultModel.success(true);
        } else {
          LogService.log
              .w("Failed to delete military education: ${json['message']}");
          return ResultModel.failed(json['message'] ?? 'Delete failed');
        }
      } else {
        return ResultModel.failed('HTTP ${response.statusCode}');
      }
    } catch (e) {
      LogService.log.e("Exception in deleteMilitaryEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }

  /// ================================
  /// PATCH / EDIT
  Future<ResultModel<bool>> editMilitaryEducation({
    required String id,
    required String militaryEducation,
    required String generation,
    required int yearCompleted,
    String? diplomaFilePath,
  }) async {
    final token = await localStorage.get("token");
    final url = "$baseApiUrl$urlMilitaryEducation/$id";
    LogService.log.d('PATCH Military Education URL: $url');

    try {
      final request = http.MultipartRequest('PATCH', Uri.parse(url));
      request.headers['Authorization'] = token;
      request.fields['militaryEducation'] = militaryEducation;
      request.fields['generation'] = generation;
      request.fields['yearCompleted'] = yearCompleted.toString();

      if (diplomaFilePath != null &&
          diplomaFilePath.isNotEmpty &&
          !diplomaFilePath.startsWith('http')) {
        final mimeType =
            lookupMimeType(diplomaFilePath) ?? 'application/octet-stream';
        final fileBytes = await File(diplomaFilePath).readAsBytes();
        final fileName = diplomaFilePath.split('/').last;

        final file = http.MultipartFile.fromBytes(
          'diploma',
          fileBytes,
          filename: fileName,
          contentType: MediaType.parse(mimeType),
        );

        request.files.add(file);
        LogService.log.d('📎 File berhasil ditambahkan ke PATCH request.');
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final json = jsonDecode(response.body);

      if (response.statusCode == 200 && json['status'] == true) {
        return const ResultModel.success(true);
      } else {
        return ResultModel.failed(json['message'] ?? 'Update failed');
      }
    } catch (e) {
      LogService.log.e("Exception in editMilitaryEducation: $e");
      return ResultModel.failed("Exception: $e");
    }
  }
}
