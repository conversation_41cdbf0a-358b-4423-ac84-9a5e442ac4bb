import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:mides_skadik/app/data/models/response/auth/user_model.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';

/* Author : Dede <PERSON> */
/* The class StorageService */
class StorageService {
  /* The method postLocalStorageLogin takes in a http.Response and returns a Future<LocalStorage> */
  Future<LocalStorage> postLocalStorageLogin(http.Response response) async {
    var body = json.decode(response.body);
    var result = body['data'];
    var localStorage = LocalStorage();
    if (result != "null") {
      // await localStorage.set("isLogin", 1);
      await localStorage.set("token", result['token']);
      await localStorage.set("user", json.encode(result ?? {}));
      await localStorage.set("user_id", result['id']);
      await localStorage.set("user_name", result['name']);
      await localStorage.set("thn_pendidikan_id", result['tahunPendidikanId']);
      // await localStorage.set(
      //     "employee_id", result["user"]['employee_id'] ?? '-');
      // var user = json.decode(json.encode(result['user'] ?? {}));
      // debugPrint("ROLE NAME : ${user['role_name'].toString()}");
      // await localStorage.set(
      //     "permissions", json.encode(result['permissions'] ?? {}));
      // await localStorage.set("user", json.encode(result['user'] ?? {}));
      // await localStorage.set(
      //     "status_logistic",
      //     user['role_name']
      //         .toString()
      //         .toLowerCase()
      //         .contains('logistic')
      //         .toString());
    }
    return localStorage;
  }

  /* The method postSessionLogout returns a Future */
  Future postSessionLogout() async {
    var session = LocalStorage();
    await session.set("token", "");
    await session.set("user", "");
  }

  /// ================================
  /// Simpan Today Attendance ID
  Future<void> saveTodayAttendanceId(String id) async {
    var localStorage = LocalStorage();
    await localStorage.set("today_attendance_id", id);
  }

  /// ================================
  /// Ambil Today Attendance ID
  Future<String?> getTodayAttendanceId() async {
    var localStorage = LocalStorage();
    final id = await localStorage.get("today_attendance_id");
    if (id == null) return null;
    return id.toString();
  }

  /// ================================
  /// Hapus Today Attendance ID
  Future<void> removeTodayAttendanceId() async {
    var localStorage = LocalStorage();
    await localStorage.remove("today_attendance_id");
  }

  Future<UserModel?> getUser() async {
    var session = LocalStorage();

    Map<String, dynamic>? data = await session.get('user');

    if (data == null) {
      return null;
    }

    return UserModel.fromJson(data);
  }
}
