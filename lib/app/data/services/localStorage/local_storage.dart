import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/* Author : Dede <PERSON>dra<PERSON>t */
/* The class LocalStorage */
class LocalStorage {
  /* The private field _session is a Map */
  final Map _session = {};

  /* The getter session returns the _session */
  late SharedPreferences prefs;

  /* The getter session returns the _session */
  Future _initSharedPrefs() async {
    prefs = await SharedPreferences.getInstance();
  }

  /* The getter session returns the _session */
  Future get(key) async {
    await _initSharedPrefs();
    try {
      return json.decode(prefs.get(key).toString());
    } catch (e) {
      return prefs.get(key);
    }
  }

  /* The setter session takes in a key and a value */
  Future set(key, value) async {
    await _initSharedPrefs();

    /* The switch statement checks the type of the value */
    switch (value.runtimeType) {
      case String:
        {
          prefs.setString(key, value);
        }
        break;

      case int:
        {
          prefs.setInt(key, value);
        }
        break;

      case bool:
        {
          prefs.setBool(key, value);
        }
        break;

      case double:
        {
          prefs.setDouble(key, value);
        }
        break;

      case List:
        {
          prefs.setStringList(key, value);
        }
        break;

      default:
        {
          prefs.setString(key, jsonEncode(value.toJson()));
        }
    }

    /* The _session is updated with the key and value */
    _session.putIfAbsent(key, () => value);
  }

  Future<void> remove(String key) async {
    await _initSharedPrefs();
    await prefs.remove(key);
    _session.remove(key);
  }
}
