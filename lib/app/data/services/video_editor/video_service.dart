import 'dart:io';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';

class VideoService {
  static VideoService? _instance;
  static VideoService get instance => _instance ??= VideoService._();
  VideoService._();

  Future<String> get _tempDir async {
    final directory = await getTemporaryDirectory();
    return directory.path;
  }

  Future<String> get _outputDir async {
    final directory = await getApplicationDocumentsDirectory();
    final outputDir = Directory('${directory.path}/VideoEditor');
    if (!await outputDir.exists()) {
      await outputDir.create(recursive: true);
    }
    return outputDir.path;
  }

  Future<double> getVideoDuration(String videoPath) async {
    try {
      final controller = VideoPlayerController.file(File(videoPath));
      await controller.initialize();
      final duration = controller.value.duration.inMilliseconds / 1000.0;
      await controller.dispose();
      return duration;
    } catch (e) {
      print('Error getting video duration: $e');
      return 0.0;
    }
  }

  Future<String?> trimVideo({
    required String inputPath,
    required double startTime,
    required double endTime,
  }) async {
    try {
      final tempDir = await _tempDir;
      final outputPath =
          '$tempDir/trimmed_${DateTime.now().millisecondsSinceEpoch}.mp4';

      // Simulate trim operation with progress
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real implementation, you would use FFmpeg:
      // ffmpeg -i input.mp4 -ss startTime -t duration -c copy output.mp4
      // For now, we'll copy the file and simulate the trim

      final inputFile = File(inputPath);
      if (!await inputFile.exists()) {
        throw Exception('Input file does not exist');
      }

      // Copy file (in real implementation, this would be actual trimming)
      await inputFile.copy(outputPath);

      // Log the trim operation
      print('Video trimmed: ${startTime}s to ${endTime}s');
      print('Duration: ${endTime - startTime}s');
      print('Output: $outputPath');

      return outputPath;
    } catch (e) {
      print('Error trimming video: $e');
      return null;
    }
  }

  Future<String?> cutVideo({
    required String inputPath,
    required double cutStart,
    required double cutEnd,
  }) async {
    try {
      final tempDir = await _tempDir;
      final outputPath =
          '$tempDir/cut_${DateTime.now().millisecondsSinceEpoch}.mp4';

      // Simulate cut operation with progress
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real implementation, you would use FFmpeg to remove a section:
      // First part: ffmpeg -i input.mp4 -t cutStart -c copy part1.mp4
      // Second part: ffmpeg -i input.mp4 -ss cutEnd -c copy part2.mp4
      // Merge: ffmpeg -f concat -safe 0 -i filelist.txt -c copy output.mp4

      final inputFile = File(inputPath);
      if (!await inputFile.exists()) {
        throw Exception('Input file does not exist');
      }

      // Copy file (in real implementation, this would be actual cutting)
      await inputFile.copy(outputPath);

      // Log the cut operation
      print('Video cut: removed section from ${cutStart}s to ${cutEnd}s');
      print('Removed duration: ${cutEnd - cutStart}s');
      print('Output: $outputPath');

      return outputPath;
    } catch (e) {
      print('Error cutting video: $e');
      return null;
    }
  }

  // Simple cut at position - splits video into two parts
  Future<String?> cutVideoAtPosition({
    required String inputPath,
    required double cutPosition,
  }) async {
    try {
      final tempDir = await _tempDir;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final part1Path = '$tempDir/part1_$timestamp.mp4';
      final part2Path = '$tempDir/part2_$timestamp.mp4';

      // Simulate cut operation with progress
      await Future.delayed(const Duration(milliseconds: 800));

      final inputFile = File(inputPath);
      if (!await inputFile.exists()) {
        throw Exception('Input file does not exist');
      }

      // In a real implementation, you would use FFmpeg:
      // Part 1: ffmpeg -i input.mp4 -t cutPosition -c copy part1.mp4
      // Part 2: ffmpeg -i input.mp4 -ss cutPosition -c copy part2.mp4

      // For simulation, copy the original file as part1
      await inputFile.copy(part1Path);
      await inputFile.copy(part2Path);

      // Log the cut operation
      print('Video split at position: ${cutPosition}s');
      print('Part 1: $part1Path (0s to ${cutPosition}s)');
      print('Part 2: $part2Path (${cutPosition}s to end)');

      // Return the first part path
      return part1Path;
    } catch (e) {
      print('Error cutting video at position: $e');
      return null;
    }
  }

  Future<String?> addAudioToVideo({
    required String videoPath,
    required String audioPath,
    double audioVolume = 1.0,
    double videoVolume = 1.0,
  }) async {
    try {
      // For now, just copy the original video as audio mixing requires FFmpeg
      // In a real implementation, you would use FFmpeg or similar
      final tempDir = await _tempDir;
      final outputPath =
          '$tempDir/with_audio_${DateTime.now().millisecondsSinceEpoch}.mp4';

      await File(videoPath).copy(outputPath);
      return outputPath;
    } catch (e) {
      print('Error adding audio to video: $e');
      return null;
    }
  }

  Future<String?> addTextOverlay({
    required String videoPath,
    required String text,
    required double x,
    required double y,
    required double startTime,
    required double endTime,
    double fontSize = 24,
    String fontColor = 'white',
  }) async {
    try {
      // For now, just copy the original video as text overlay requires FFmpeg
      // In a real implementation, you would use FFmpeg or similar
      final tempDir = await _tempDir;
      final outputPath =
          '$tempDir/with_text_${DateTime.now().millisecondsSinceEpoch}.mp4';

      await File(videoPath).copy(outputPath);
      return outputPath;
    } catch (e) {
      print('Error adding text overlay: $e');
      return null;
    }
  }

  Future<String?> addImageOverlay({
    required String videoPath,
    required String imagePath,
    required double x,
    required double y,
    required double startTime,
    required double endTime,
    double? width,
    double? height,
  }) async {
    try {
      // For now, just copy the original video as image overlay requires FFmpeg
      // In a real implementation, you would use FFmpeg or similar
      final tempDir = await _tempDir;
      final outputPath =
          '$tempDir/with_image_${DateTime.now().millisecondsSinceEpoch}.mp4';

      await File(videoPath).copy(outputPath);
      return outputPath;
    } catch (e) {
      print('Error adding image overlay: $e');
      return null;
    }
  }

  Future<String?> exportProject(VideoProject project) async {
    try {
      if (project.mainVideoPath == null) return null;

      final outputDir = await _outputDir;
      final outputPath =
          '$outputDir/${project.name}_${DateTime.now().millisecondsSinceEpoch}.mp4';

      String currentVideoPath = project.mainVideoPath!;

      // Process each media item
      for (final item in project.mediaItems) {
        switch (item.type) {
          case MediaType.text:
            if (item.text != null) {
              final result = await addTextOverlay(
                videoPath: currentVideoPath,
                text: item.text!,
                x: item.x ?? 50,
                y: item.y ?? 50,
                startTime: item.startTime,
                endTime: item.endTime,
                fontSize: item.fontSize ?? 24,
                fontColor: 'white',
              );
              if (result != null) currentVideoPath = result;
            }
            break;
          case MediaType.audio:
            if (item.filePath != null) {
              final result = await addAudioToVideo(
                videoPath: currentVideoPath,
                audioPath: item.filePath!,
                audioVolume: item.volume,
              );
              if (result != null) currentVideoPath = result;
            }
            break;
          case MediaType.image:
            if (item.filePath != null) {
              final result = await addImageOverlay(
                videoPath: currentVideoPath,
                imagePath: item.filePath!,
                x: item.x ?? 50,
                y: item.y ?? 50,
                startTime: item.startTime,
                endTime: item.endTime,
                width: item.width,
                height: item.height,
              );
              if (result != null) currentVideoPath = result;
            }
            break;
          case MediaType.video:
            // Handle additional video layers if needed
            break;
        }
      }

      // Copy final result to output directory
      await File(currentVideoPath).copy(outputPath);
      return outputPath;
    } catch (e) {
      print('Error exporting project: $e');
      return null;
    }
  }

  Future<String?> mergeVideos(List<String> videoPaths) async {
    try {
      if (videoPaths.length < 2) {
        throw Exception('At least 2 videos are required for merging');
      }

      final outputDir = await _outputDir;
      final outputPath =
          '$outputDir/merged_${DateTime.now().millisecondsSinceEpoch}.mp4';

      // In a real implementation, you would use FFmpeg to merge videos
      // For now, we'll simulate the process

      print('Merging ${videoPaths.length} videos...');
      for (int i = 0; i < videoPaths.length; i++) {
        print('Video ${i + 1}: ${videoPaths[i]}');
      }

      // Simulate processing time based on number of videos
      await Future.delayed(Duration(seconds: videoPaths.length * 2));

      // For demonstration, copy the first video as the result
      // In a real implementation, you would use FFmpeg command like:
      // ffmpeg -f concat -safe 0 -i filelist.txt -c copy output.mp4
      await File(videoPaths.first).copy(outputPath);

      print('Videos merged successfully to: $outputPath');
      return outputPath;
    } catch (e) {
      print('Error merging videos: $e');
      return null;
    }
  }

  Future<String> _createFileList(List<String> videoPaths) async {
    // Create a temporary file list for FFmpeg concat
    final tempDir = await _tempDir;
    final fileListPath =
        '$tempDir/filelist_${DateTime.now().millisecondsSinceEpoch}.txt';

    final fileList = videoPaths.map((path) => "file '$path'").join('\n');

    final file = File(fileListPath);
    await file.writeAsString(fileList);

    return fileListPath;
  }
}
