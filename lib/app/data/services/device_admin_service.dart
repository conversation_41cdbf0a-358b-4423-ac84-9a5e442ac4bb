import 'package:flutter/services.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class DeviceAdminService {
  static const MethodChannel _channel = MethodChannel('com.example.example/screen_share');

  /// Check if the app is currently a device administrator
  static Future<bool> isDeviceAdmin() async {
    try {
      final bool isActive = await _channel.invokeMethod('checkDeviceAdmin');
      LogService.log.i('Device Admin Status: $isActive');
      return isActive;
    } catch (e) {
      LogService.log.e('Error checking device admin status: $e');
      return false;
    }
  }

  /// Request device administrator privileges
  static Future<bool> requestDeviceAdmin() async {
    try {
      await _channel.invokeMethod('requestDeviceAdmin');
      LogService.log.i('Device Admin activation requested');
      return true;
    } catch (e) {
      LogService.log.e('Error requesting device admin: $e');
      return false;
    }
  }

  /// Restart the device (requires device admin privileges)
  static Future<bool> restartDevice() async {
    try {
      await _channel.invokeMethod('restartService');
      LogService.log.i('Device restart requested');
      return true;
    } catch (e) {
      LogService.log.e('Error restarting device: $e');
      return false;
    }
  }

  /// Shutdown/Lock the device (requires device admin privileges)
  static Future<bool> shutdownDevice() async {
    try {
      await _channel.invokeMethod('shutdownService');
      LogService.log.i('Device shutdown requested');
      return true;
    } catch (e) {
      LogService.log.e('Error shutting down device: $e');
      return false;
    }
  }
}
