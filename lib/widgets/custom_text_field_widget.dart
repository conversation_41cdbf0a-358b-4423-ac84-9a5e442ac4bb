import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mides_skadik/app/data/utils/phone_number_input_formatter.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:flutter/services.dart';

class CustomTextFieldWidget extends StatelessWidget {
  final String? hintText;
  final double? fontSize;
  final double? radius;
  final int? maxLines;
  final int? maxLength;
  final bool? obscureText;
  final Color? colorField;
  final Color? colorText;
  final Color? colorPrefixIcon;
  final Color? colorTextHint;
  final Color? colorSuffixIcon;
  final BorderSide? borderSide;
  final BorderSide? focusBorderSide;
  final FontWeight? fontWeight;
  final TextEditingController? controller;
  final EdgeInsetsDirectional? contentPadding;
  final ValueChanged<String>? onChanged;
  final double? widthField;
  final double? heightField;
  final String? assetNameIcon;
  final double? iconHeight;
  final double? iconWidth;
  final Widget? prefixIconWidget;
  final String? suffixAssetNameIcon;
  final double? suffixIconHeight;
  final double? suffixIconWidth;
  final BoxConstraints? suffixIconConstraints;
  final bool? readOnly;
  final bool? isPhoneNumber;
  final FocusNode? focusNode;
  final void Function()? onTapSuffix;

  const CustomTextFieldWidget({
    super.key,
    this.hintText,
    this.fontSize,
    this.radius,
    this.obscureText,
    this.colorField,
    this.colorText,
    this.colorTextHint,
    this.colorSuffixIcon,
    this.borderSide,
    this.focusBorderSide,
    this.fontWeight,
    this.controller,
    this.contentPadding,
    this.onChanged,
    this.maxLines,
    this.maxLength,
    this.widthField,
    this.heightField,
    this.assetNameIcon,
    this.iconHeight,
    this.iconWidth,
    this.prefixIconWidget,
    this.colorPrefixIcon,
    this.suffixAssetNameIcon,
    this.suffixIconHeight,
    this.suffixIconWidth,
    this.suffixIconConstraints,
    this.readOnly,
    this.isPhoneNumber,
    this.focusNode,
    this.onTapSuffix,
  });

  @override
  Widget build(BuildContext context) {
    Widget textField = TextField(
      controller: controller,
      obscureText: obscureText ?? false,
      maxLength: maxLength,
      maxLines: maxLines ?? 1,
      readOnly: readOnly ?? false,
      focusNode: focusNode,
      keyboardType:
          isPhoneNumber == true ? TextInputType.number : TextInputType.text,
      inputFormatters: isPhoneNumber == true
          ? [
              FilteringTextInputFormatter.digitsOnly,
              PhoneNumberInputFormatter(),
            ]
          : null,
      style: TextStyle(
        fontFamily: 'Inter',
        fontSize: fontSize?.sp ?? 28.sp,
        fontWeight: fontWeight ?? FontWeight.w400,
        color: colorText ?? blackColor,
      ),
      decoration: InputDecoration(
        filled: true,
        fillColor: colorField,
        hintText: hintText,
        counterText: '',
        hintStyle: TextStyle(
          fontFamily: 'Inter',
          fontSize: fontSize?.sp ?? 28.sp,
          fontWeight: fontWeight ?? FontWeight.w400,
          color: colorTextHint ?? blackColor,
        ),
        // PREFIX ICON SUPPORT
        prefixIcon: prefixIconWidget ??
            (assetNameIcon != null
                ? Padding(
                    padding: EdgeInsets.all(12.w),
                    child: SvgPicture.asset(
                      assetNameIcon!,
                      width: iconWidth?.w,
                      height: iconHeight?.h,
                      color: colorPrefixIcon,
                    ),
                  )
                : null),

        // SUFFIX ICON SUPPORT
        suffixIconConstraints: suffixIconConstraints ??
            (suffixIconWidth != null && suffixIconHeight != null
                ? BoxConstraints(
                    maxWidth: suffixIconWidth!.w,
                    maxHeight: suffixIconHeight!.h,
                  )
                : null),
        suffixIcon: suffixAssetNameIcon != null
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 14.w),
                child: GestureDetector(
                  onTap: onTapSuffix,
                  child: SizedBox(
                    width: suffixIconWidth?.w,
                    height: suffixIconHeight?.h,
                    child: SvgPicture.asset(
                      suffixAssetNameIcon!,
                      fit: BoxFit.contain,
                      color: colorSuffixIcon,
                    ),
                  ),
                ),
              )
            : null,

        border: OutlineInputBorder(
          borderSide: borderSide ?? BorderSide.none,
          borderRadius: BorderRadius.circular(radius?.r ?? 0.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: focusBorderSide ?? BorderSide.none,
          borderRadius: BorderRadius.circular(radius?.r ?? 0.r),
        ),
        contentPadding: contentPadding,
      ),
      onChanged: onChanged,
    );

    return (widthField != null || heightField != null)
        ? SizedBox(
            width: widthField?.w,
            height: heightField?.h,
            child: textField,
          )
        : textField;
  }
}
