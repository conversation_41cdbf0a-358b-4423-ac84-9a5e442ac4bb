import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomSearchBar extends StatelessWidget {
  final String? hintText;
  final String? textSearch;
  final Widget? backIcon;
  final Widget? micIcon;
  final Color? bgColor;
  final Color? hintTextColor;
  final Color? fieldColor;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final bool? isFocus;
  final void Function()? onBack;

  const CustomSearchBar({
    super.key,
    this.hintText,
    this.textSearch = '',
    this.backIcon,
    this.micIcon,
    this.bgColor,
    this.hintTextColor,
    this.fieldColor,
    this.controller,
    this.focusNode,
    this.isFocus = false,
    this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      height: 96,
      bgColor: bgColor ?? secondBlueColor.withOpacity(0.04),
      widget: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Row(
          children: [
            isFocus! || (textSearch == null || textSearch!.isNotEmpty)
                ? CustomFilledButtonWidget(
                    onPressed: onBack,
                    heightButton: 65,
                    widthButton: 65,
                    assetName: "assets/icons/back.svg",
                    withIcon: true,
                    heightIcon: 64.h,
                    widthIcon: 64.w,
                    radius: 8,
                    bgColor: secondBlueColor.withOpacity(0.2),
                    onlyIcon: true,
                  )
                : SvgPicture.asset(
                    'assets/icons/search.svg',
                    width: 48.w,
                    height: 48.h,
                  ),
            16.horizontalSpace,
            isFocus! || (textSearch == null || textSearch!.isNotEmpty)
                ? const SizedBox.shrink()
                : const CustomTextWigdet(
                    title: 'Browse',
                    fontSize: 32,
                    fontWeight: FontWeight.w300,
                  ),
            16.horizontalSpace,
            Flexible(
              child: SizedBox(
                height: 65.h,
                child: CustomTextFieldWidget(
                  controller: controller,
                  colorText: secondWhiteColor,
                  focusNode: focusNode,
                  hintText: "Cari judul, topik, kategori...",
                  colorTextHint: hintTextColor ?? whiteColor.withOpacity(0.35),
                  colorField: fieldColor ?? secondBlueColor.withOpacity(0.2),
                  radius: 8,
                  contentPadding:
                      EdgeInsetsDirectional.only(top: 10.h, start: 10.w),
                ),
              ),
            ),
            16.horizontalSpace,
            GestureDetector(
              onTap: () {},
              child: CustomFilledButtonWidget(
                onPressed: () {},
                heightButton: 65,
                widthButton: 65,
                assetName: "assets/icons/mic.svg",
                withIcon: true,
                heightIcon: 64.h,
                widthIcon: 64.w,
                radius: 8,
                bgColor: secondBlueColor.withOpacity(0.2),
                onlyIcon: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
