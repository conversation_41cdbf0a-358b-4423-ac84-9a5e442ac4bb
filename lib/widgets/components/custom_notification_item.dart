import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_user_info.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomNotificationItem extends StatelessWidget {
  final String type;
  final String? statusApproved;
  final String? title;
  final String? time;
  final String? description;
  final int length;
  final bool? isApprovalVideo;
  final bool? statusApproval;
  final bool? isHistory;
  final Widget? historyItem;
  const CustomNotificationItem(
      {super.key,
      required this.type,
      required this.length,
      this.isApprovalVideo,
      this.statusApproval,
      this.statusApproved,
      this.title,
      this.time,
      this.description,
      this.isHistory,
      this.historyItem});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 24.h),
      child: CustomContainer(
        bgColor: Colors.transparent,
        widget: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextWigdet(
              title: type,
              fontSize: 28,
              fontWeight: FontWeight.w600,
            ),
            16.verticalSpace,
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: length,
              itemBuilder: (it, ctx) {
                return isHistory ?? false
                    ? historyItem!
                    : Padding(
                        padding: EdgeInsets.only(top: 32.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            isApprovalVideo ?? false
                                ? Flexible(
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomContainer(
                                          bgColor: redColor,
                                          width: 10,
                                          height: 180,
                                        ),
                                        16.horizontalSpace,
                                        CustomContainer(
                                          bgColor: blackColor.withOpacity(0.35),
                                          radius: 8,
                                          widget: Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 15.w,
                                                vertical: 15.h),
                                            child: SvgPicture.asset(
                                                'assets/icons/admin.svg',
                                                width: 50.w,
                                                height: 50.h),
                                          ),
                                        ),
                                        16.horizontalSpace,
                                        Flexible(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              CustomTextWigdet(
                                                title:
                                                    '$statusApproved - $title',
                                                fontSize: 22,
                                                fontWeight: FontWeight.w700,
                                                textColor: whiteColor,
                                              ),
                                              4.verticalSpace,
                                              CustomTextWigdet(
                                                title: time ?? '',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w300,
                                                textColor: whiteColor,
                                              ),
                                              16.verticalSpace,
                                              CustomTextWigdet(
                                                title: description ?? '',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w300,
                                                textColor: whiteColor,
                                                maxLines: 3,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  )
                                : const CustomUserInfo(
                                    title: 'Notification Text',
                                    subTitle: '9 menit lalu',
                                    height: 80,
                                    spacing: 16,
                                  ),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomContainer(
                                  width: 365,
                                  height: 180,
                                  radius: 12,
                                  widget: ClipRRect(
                                    borderRadius: BorderRadius.circular(12.r),
                                    child: Image.asset(
                                      'assets/images/airplane.png',
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                30.horizontalSpace,
                                Icon(
                                  Icons.more_vert_rounded,
                                  color: whiteColor,
                                ),
                              ],
                            )
                          ],
                        ),
                      );
              },
            )
          ],
        ),
      ),
    );
  }
}
