import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAppBarMediaPlayer extends GetView implements PreferredSizeWidget {
  final String? title;
  final List<InlineSpan>? richTitle;
  final String? assetIcon;
  final double? height;
  final double? fontSize;
  final bool? withIcon;
  final bool? isHamburgerIcon;
  final FontWeight? fontWeight;
  final void Function()? onBackPressed;
  final List<Widget>? actions;
  final bool? iconWithBackground;

  const CustomAppBarMediaPlayer({
    super.key,
    this.height,
    this.assetIcon,
    this.title,
    this.richTitle,
    this.fontSize,
    this.actions,
    this.fontWeight,
    this.onBackPressed,
    this.withIcon,
    this.isHamburgerIcon,
    this.iconWithBackground,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: greyColor.withOpacity(0.25),
      automaticallyImplyLeading: false,
      actions: actions,
      toolbarHeight: height,
      scrolledUnderElevation: 0,
      title: Row(
        children: [
          if (withIcon ?? false)
            GestureDetector(
              onTap: isHamburgerIcon ?? false ? () => Scaffold.of(context).openDrawer() : () => Navigator.pop(context),
              child: SizedBox(
                width: 72.w,
                height: 72.h,
                child: CustomContainer(
                  bgColor: iconWithBackground != null ? secondBlueColor.withOpacity(0.1) : Colors.transparent,
                  radius: 8,
                  widget: SvgPicture.asset(
                    assetIcon ?? (isHamburgerIcon ?? false ? 'assets/icons/hamburger.svg' : 'assets/icons/back.svg'),
                  ),
                ),
              ),
            ),
          if (withIcon ?? false) 16.horizontalSpace,
          Expanded(
            child: RichText(
              text: TextSpan(
                children: richTitle ??
                    [
                      WidgetSpan(
                        child: CustomTextWigdet(
                          title: title ?? '',
                          fontWeight: fontWeight,
                          fontSize: fontSize ?? 30,
                          textColor: whiteColor,
                        ),
                      ),
                    ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height?.h ?? kToolbarHeight);
}
