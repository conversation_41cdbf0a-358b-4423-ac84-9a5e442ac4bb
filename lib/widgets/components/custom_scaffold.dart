import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/widgets/components/custom_app_bar.dart';
import 'package:mides_skadik/widgets/course/custom_appbar_course.dart';

class CustomScaffold extends GetView {
  final Widget body;
  final String? titleAppBar;
  final List<InlineSpan>? richTitleAppBar;
  final bool? useActions;
  final bool? useAppBar;
  final bool? withIcon;
  final bool? isHamburgerIcon;
  final bool? isCourse;
  final double? heightAppBar;
  final double? fontSizeAppBar;
  final FontWeight? fontWeight;
  final List<Widget>? actions;
  final void Function()? onBackPressed;
  final Widget? bottomNavigationBar;
  final String? customIconPath;
  final String? background;
  final Widget? drawer;
  final bool? iconWithBackground;

  const CustomScaffold({
    required this.body,
    this.titleAppBar,
    this.richTitleAppBar,
    this.useActions,
    this.useAppBar,
    this.actions,
    this.heightAppBar,
    this.fontSizeAppBar,
    this.bottomNavigationBar,
    this.withIcon,
    this.fontWeight,
    this.onBackPressed,
    this.isHamburgerIcon,
    this.customIconPath,
    this.isCourse,
    super.key,
    this.background,
    this.drawer,
    this.iconWithBackground,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: background != null &&
                  ['png', 'jogg', 'jpeg'].contains(background!.split('.').last)
              ? Image.asset(
                  background ?? 'assets/images/background.png',
                  fit: BoxFit.cover,
                )
              : SvgPicture.asset(
                  background ?? 'assets/images/background.svg',
                  fit: BoxFit.cover,
                ),
        ),
        Scaffold(
          backgroundColor: Colors.transparent,
          resizeToAvoidBottomInset: false,
          appBar: useAppBar ?? false
              ? isCourse ?? false
                  ? const CustomAppBarCourse()
                  : CustomAppBarMediaPlayer(
                      title: titleAppBar,
                      fontSize: fontSizeAppBar,
                      richTitle: richTitleAppBar,
                      fontWeight: fontWeight,
                      actions: actions,
                      height: heightAppBar,
                      withIcon: withIcon,
                      assetIcon: withIcon == true
                          ? (customIconPath ?? 'assets/icons/close.svg')
                          : null, // Gunakan customIconPath jika ada
                      isHamburgerIcon: isHamburgerIcon,
                      onBackPressed: onBackPressed,
                      iconWithBackground: iconWithBackground,
                    )
              : null,
          bottomNavigationBar: bottomNavigationBar,
          body: body,
          drawer: drawer,
        ),
      ],
    );
  }
}
