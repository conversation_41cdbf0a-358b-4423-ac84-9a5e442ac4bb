import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:flutter/foundation.dart';

class CustomLogin extends StatefulWidget {
  final double? width;
  const CustomLogin({Key? key, this.width}) : super(key: key);

  @override
  State<CustomLogin> createState() => _CustomLoginState();
}

class _CustomLoginState extends State<CustomLogin> {
  final controller = Get.find<LoginController>();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((v) {});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 4.w,
          sigmaY: 4.h,
        ),
        child: Container(
          width: widget.width ?? Get.width / 1.5,
          clipBehavior: Clip.antiAliasWithSaveLayer,
          decoration: BoxDecoration(
            border: Border.all(
              color: whiteColor.withOpacity(0.7),
              width: 0.1,
              strokeAlign: 1,
            ),
            color: blackColor.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Stack(
            children: [
              Positioned(
                top: -1 * (Get.width / 2),
                left: 0,
                right: 0,
                child: Container(
                  width: Get.width,
                  height: Get.width,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    backgroundBlendMode: BlendMode.lighten,
                    gradient: RadialGradient(
                      center: Alignment.center,
                      radius: 0.4,
                      colors: [
                        blueColor.withOpacity(0.7),
                        Colors.transparent,
                      ],
                      stops: [0.0, 1.0],
                    ),
                  ),
                ),
              ),
              Column(
                children: [
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 64.w, vertical: 64.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomTextWigdet(
                              title: 'M-IDES',
                              fontSize: 92.sp,
                              fontWeight: FontWeight.w600,
                            ),
                            15.horizontalSpace,
                            Image.asset(
                              'assets/images/logo.png',
                              height: 60.h,
                            )
                          ],
                        ),
                        8.verticalSpace,
                        Center(
                          child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                                text: 'Mobile Integrated Digital\n',
                                style: TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 28.sp,
                                    fontWeight: FontWeight.w500),
                                children: [
                                  TextSpan(
                                    text: 'Education System',
                                    style: TextStyle(
                                        fontFamily: 'Inter',
                                        fontSize: 28.sp,
                                        fontStyle: FontStyle.italic,
                                        fontWeight: FontWeight.w300),
                                  )
                                ]),
                          ),
                        ),
                        64.verticalSpace,
                        CustomTextFieldWidget(
                          controller: controller.emailController,
                          radius: 12,
                          colorField: greyColor.withOpacity(0.35),
                          colorText: whiteColor,
                          hintText: 'NRP',
                          colorTextHint: whiteColor.withOpacity(0.5),
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              horizontal: 32.w, vertical: 24.h),
                        ),
                        32.verticalSpace,
                        Obx(
                          () => CustomTextFieldWidget(
                            controller: controller.passwordController,
                            radius: 12,
                            obscureText: controller.obscureText.value,
                            colorField: greyColor.withOpacity(0.35),
                            colorText: whiteColor,
                            hintText: 'Password',
                            isPhoneNumber: true,
                            colorTextHint: whiteColor.withOpacity(0.5),
                            contentPadding: EdgeInsetsDirectional.symmetric(
                                horizontal: 32.w, vertical: 24.h),
                            onChanged: (_) => controller.password.value =
                                controller.passwordController.value.text,
                            suffixAssetNameIcon: controller.obscureText.value
                                ? "assets/icons/eye-hide.svg"
                                : "assets/icons/eye.svg",
                            colorSuffixIcon: whiteColor,
                            suffixIconConstraints: const BoxConstraints(
                                maxWidth: 50, maxHeight: 50),
                            onTapSuffix: () {
                              controller.toggleObscureText();
                            },
                          ),
                        ),
                        32.verticalSpace,
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            CustomTextWigdet(
                              title: 'Forgot Password?',
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                              textColor: whiteColor.withOpacity(0.6),
                            ),
                          ],
                        ),
                        70.verticalSpace,
                        Obx(
                          () => CustomFilledButtonWidget(
                            onPressed: controller.validated().value
                                ? controller.submitLogin
                                : null,
                            title: 'Login',
                            widthButton: double.infinity,
                            isLoading: controller.isLoading.value,
                            fontColor: whiteColor,
                            bgColor:
                                controller.validated().value ? blueColor : null,
                            fontWeight: FontWeight.bold,
                            heightButton: 100,
                            radius: 12,
                          ),
                        ),
                        50.verticalSpace,
                      ],
                    ),
                  ),
                  CustomContainer(
                    bgColor: blackColor.withOpacity(0.2),
                    padding: EdgeInsets.fromLTRB(16, 24, 16, 24),
                    widget: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        16.horizontalSpace,
                        CustomTextWigdet(
                          title: 'End-User License Agreement',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          textColor: whiteColor.withOpacity(0.6),
                        ),
                        CustomTextWigdet(
                          title: 'Privacy Policy',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          textColor: whiteColor.withOpacity(0.6),
                        ),
                        CustomTextWigdet(
                          title: 'Terms of Service',
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          textColor: whiteColor.withOpacity(0.6),
                        ),
                        16.horizontalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
