import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class SnackbarUtil {
  static final RxBool _isSnackbarActive = false.obs;

  static void showOnce({
    required String title,
    required String message,
  }) {
    if (!_isSnackbarActive.value) {
      _isSnackbarActive.value = true;

      Get.snackbar(
        title,
        message,
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.black.withOpacity(0.4),
        titleText: CustomTextWigdet(
          title: title,
          fontSize: 40.sp,
          textColor: whiteColor,
          fontWeight: FontWeight.w700,
        ),
        messageText: CustomTextWigdet(
          title: message,
          fontSize: 32.sp,
          fontWeight: FontWeight.w500,
          textColor: whiteColor,
        ),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        borderRadius: 12,
        duration: const Duration(seconds: 5),
      );

      Future.delayed(const Duration(seconds: 5), () {
        _isSnackbarActive.value = false;
      });
    }
  }
}
