import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/audio_player_service.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AudioPlayerWidget extends StatelessWidget {
  final String audioUrl;
  final String title;
  final bool showFullControls;
  late final String audioId; // ✅ Unique identifier for this audio

  AudioPlayerWidget({
    super.key,
    required this.audioUrl,
    required this.title,
    this.showFullControls = true,
  }) : audioId = audioUrl.hashCode.toString(); // ✅ Generate unique ID

  @override
  Widget build(BuildContext context) {
    final audioService = Get.find<AudioPlayerService>();

    return Obx(() {
      // ✅ More precise check using both URL and unique ID
      final isCurrentAudio = audioService.currentUrl.value == audioUrl &&
          audioService.currentAudioId.value == audioId;
      final isPlaying = isCurrentAudio && audioService.isPlaying.value;
      final isLoading = isCurrentAudio && audioService.isLoading.value;

      // ✅ Only show playing state for the exact current audio
      final shouldShowPlayingState = isCurrentAudio && isPlaying;
      final shouldShowPauseIcon = shouldShowPlayingState;
      final shouldShowStopButton = isCurrentAudio &&
          (isPlaying || audioService.currentPosition.value > Duration.zero);

      return CustomContainer(
        width: double.infinity,
        bgColor: baseBlueColor.withOpacity(0.3),
        radius: 8,
        padding: const EdgeInsets.all(12),
        widget: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and Play Button Row
            Row(
              children: [
                // Play/Pause Button
                GestureDetector(
                  onTap: () async {
                    if (isLoading) return;

                    if (isCurrentAudio) {
                      if (shouldShowPlayingState) {
                        await audioService.pause();
                      } else {
                        await audioService.resume();
                      }
                    } else {
                      await audioService.playAudio(audioUrl,
                          title: title, audioId: audioId);
                    }
                  },
                  child: CustomContainer(
                    width: 40,
                    height: 40,
                    radius: 20,
                    bgColor: secondBlueColor.withOpacity(0.8),
                    widget: Center(
                      child: isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(whiteColor),
                              ),
                            )
                          : Icon(
                              shouldShowPauseIcon
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              color: whiteColor,
                              size: 20,
                            ),
                    ),
                  ),
                ),
                12.horizontalSpace,

                // Title
                Expanded(
                  child: CustomTextWigdet(
                    title: title,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    textColor: whiteColor,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Stop Button (if playing current audio)
                if (shouldShowStopButton)
                  GestureDetector(
                    onTap: () async {
                      await audioService.stop();
                    },
                    child: CustomContainer(
                      width: 32,
                      height: 32,
                      radius: 16,
                      bgColor: redColor.withOpacity(0.8),
                      widget: Center(
                        child: Icon(
                          Icons.stop,
                          color: whiteColor,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Progress Bar and Time (only show if this is the current audio and full controls enabled)
            if (showFullControls &&
                isCurrentAudio &&
                audioService.totalDuration.value > Duration.zero) ...[
              12.verticalSpace,

              // Progress Bar
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: secondBlueColor,
                  inactiveTrackColor: secondWhiteColor.withOpacity(0.3),
                  thumbColor: secondBlueColor,
                  thumbShape:
                      const RoundSliderThumbShape(enabledThumbRadius: 6),
                  trackHeight: 4,
                ),
                child: Slider(
                  value: audioService.progress.clamp(0.0, 1.0),
                  onChanged: (value) {
                    final position = Duration(
                      milliseconds: (value *
                              audioService.totalDuration.value.inMilliseconds)
                          .round(),
                    );
                    audioService.seekTo(position);
                  },
                ),
              ),

              // Time Display
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomTextWigdet(
                    title: audioService
                        .formatDuration(audioService.currentPosition.value),
                    fontSize: 12,
                    textColor: secondWhiteColor,
                  ),
                  CustomTextWigdet(
                    title: audioService
                        .formatDuration(audioService.totalDuration.value),
                    fontSize: 12,
                    textColor: secondWhiteColor,
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    });
  }
}
