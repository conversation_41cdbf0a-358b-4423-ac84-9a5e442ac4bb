import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomCollectionCard extends StatelessWidget {
  final double? width;
  final double? height;
  final String? judulVideo;
  final String? createdDate;
  final String? visibility;
  final bool? selected;
  final ValueChanged<bool?>? onChanged;
  final List<String> thumbnails =
      List.generate(4, (index) => "assets/images/airplane.png");
  final bool? changeThumbnail;

  CustomCollectionCard({
    super.key,
    this.width,
    this.height,
    this.judulVideo,
    this.createdDate,
    this.visibility,
    this.selected,
    this.onChanged,
    this.changeThumbnail,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomContainer(
          width: width ?? 437,
          height: height ?? 550,
          bgColor: secondBlueColor.withOpacity(0.10),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          widget: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (changeThumbnail == true)
                AspectRatio(
                  aspectRatio: 1,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.asset(
                      'assets/images/airplane.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                )
              else
                AspectRatio(
                  aspectRatio: 1,
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 2,
                      mainAxisSpacing: 2,
                    ),
                    itemCount: thumbnails.length,
                    itemBuilder: (context, index) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.asset(
                          thumbnails[index],
                          fit: BoxFit.cover,
                        ),
                      );
                    },
                  ),
                ),
              Expanded(
                flex: 1,
                child: CustomContainer(
                  width: double.infinity,
                  bgColor: secondBlueColor.withOpacity(0.10),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                  widget: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Checkbox(
                          value: selected ?? false,
                          onChanged: onChanged,
                          activeColor: Colors.white,
                          checkColor: secondBlueColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextWigdet(
                              title: judulVideo ?? '-',
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                            8.verticalSpace,
                            CustomTextWigdet(
                              title:
                                  '${visibility ?? '-'} • ${createdDate ?? 'Update hari ini'}',
                              fontSize: 16,
                              fontWeight: FontWeight.w300,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
