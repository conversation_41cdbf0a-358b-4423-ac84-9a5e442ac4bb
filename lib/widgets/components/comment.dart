import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/app/data/models/response/media_player/broadcast/comment_model.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CommentSection extends StatefulWidget {
  final CommentModel commentModel;
  final Function(CommentModel, String)? onSubmitReply;
  final Function(CommentModel)? onToggleReply;

  const CommentSection({
    super.key,
    required this.commentModel,
    this.onSubmitReply,
    this.onToggleReply,
  });

  @override
  State<CommentSection> createState() => _CommentSectionState();
}

class _CommentSectionState extends State<CommentSection> {
  bool isReplying = false;
  bool isSubmittingReply = false;
  final TextEditingController _replyController = TextEditingController();
  final FocusNode _replyFocusNode = FocusNode();

  @override
  void dispose() {
    _replyController.dispose();
    _replyFocusNode.dispose();
    super.dispose();
  }

  // Helper method untuk format waktu
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} hari yang lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam yang lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit yang lalu';
    } else {
      return 'Baru saja';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.r),
      margin: EdgeInsets.symmetric(vertical: 8.h),
      decoration: BoxDecoration(
        color: baseBlueColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: secondWhiteColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header comment (avatar, username, time, more button)
          Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundImage:
                    widget.commentModel.createdUser?.imageProfile != null &&
                            widget.commentModel.createdUser!.imageProfile!
                                .isNotEmpty
                        ? NetworkImage(
                            widget.commentModel.createdUser!.imageProfile!)
                        : const AssetImage('assets/images/Person.jpg')
                            as ImageProvider,
              ),
              10.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: widget.commentModel.createdUser?.pasisName ??
                          "Unknown User",
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                    CustomTextWigdet(
                      title: widget.commentModel.createdAt != null
                          ? _getTimeAgo(widget.commentModel.createdAt!)
                          : "Unknown Time",
                      fontSize: 18,
                      textColor: secondWhiteColor.withOpacity(0.7),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {},
                icon: Icon(
                  Icons.more_vert,
                  color: secondWhiteColor,
                  size: 20.r,
                ),
              ),
            ],
          ),

          12.verticalSpace,

          // Comment content
          CustomTextWigdet(
            title: widget.commentModel.comment ?? "No comment available",
            fontSize: 20,
            textColor: whiteColor,
            maxLines: null,
          ),

          12.verticalSpace,

          // Action buttons (reply, show replies)
          Row(
            children: [
              // Reply button
              GestureDetector(
                onTap: () {
                  setState(() {
                    isReplying = !isReplying;
                  });
                  if (isReplying) {
                    _replyFocusNode.requestFocus();
                  }
                },
                child: Row(
                  children: [
                    Icon(
                      Icons.reply,
                      color: secondWhiteColor,
                      size: 20.r,
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: "Reply",
                      fontSize: 18,
                      textColor: secondWhiteColor,
                    ),
                  ],
                ),
              ),

              20.horizontalSpace,

              // Show/Hide replies button (jika ada replies)
              if (widget.commentModel.reply != null &&
                  widget.commentModel.reply!.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    LogService.log.i(
                        'Toggle reply for comment: ${widget.commentModel.id}');
                    widget.onToggleReply?.call(widget.commentModel);
                  },
                  child: Row(
                    children: [
                      Icon(
                        widget.commentModel.isOpenReply
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color: secondBlueColor,
                        size: 20.r,
                      ),
                      6.horizontalSpace,
                      CustomTextWigdet(
                        title: widget.commentModel.isOpenReply
                            ? "Hide ${widget.commentModel.reply!.length} replies"
                            : "Show ${widget.commentModel.reply!.length} replies",
                        fontSize: 18,
                        textColor: secondBlueColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // Reply input section (jika isReplying true)
          if (isReplying) ...[
            16.verticalSpace,
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: baseBlueColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: secondBlueColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Reply to indicator
                  Row(
                    children: [
                      Icon(
                        Icons.reply,
                        color: secondBlueColor,
                        size: 20.r,
                      ),
                      6.horizontalSpace,
                      CustomTextWigdet(
                        title:
                            "Replying to ${widget.commentModel.createdUser?.pasisName ?? 'Unknown User'}",
                        fontSize: 20,
                        textColor: secondBlueColor,
                        fontWeight: FontWeight.w500,
                      ),
                      const Spacer(),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isReplying = false;
                          });
                          _replyController.clear();
                        },
                        child: Icon(
                          Icons.close,
                          color: secondWhiteColor,
                          size: 20.r,
                        ),
                      ),
                    ],
                  ),

                  12.verticalSpace,

                  // Reply input field
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: BoxDecoration(
                            color: whiteColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20.r),
                            border: Border.all(
                              color: secondWhiteColor.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: TextField(
                            controller: _replyController,
                            focusNode: _replyFocusNode,
                            maxLines: null,
                            minLines: 1,
                            style: TextStyle(
                              color: whiteColor,
                              fontSize: 20.sp,
                            ),
                            decoration: InputDecoration(
                              hintText: "Write a reply...",
                              hintStyle: TextStyle(
                                color: secondWhiteColor.withOpacity(0.6),
                                fontSize: 20.sp,
                              ),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 12.h,
                              ),
                            ),
                          ),
                        ),
                      ),

                      12.horizontalSpace,

                      // Send button
                      GestureDetector(
                        onTap: isSubmittingReply
                            ? null
                            : () async {
                                final replyText = _replyController.text.trim();
                                if (replyText.isNotEmpty) {
                                  setState(() {
                                    isSubmittingReply = true;
                                  });

                                  try {
                                    // Call the submit reply callback
                                    widget.onSubmitReply
                                        ?.call(widget.commentModel, replyText);

                                    // Clear input and close reply interface
                                    _replyController.clear();
                                    setState(() {
                                      isReplying = false;
                                      isSubmittingReply = false;
                                    });
                                  } catch (e) {
                                    // Handle error
                                    setState(() {
                                      isSubmittingReply = false;
                                    });
                                  }
                                }
                              },
                        child: Container(
                          padding: EdgeInsets.all(8.r),
                          decoration: BoxDecoration(
                            color: isSubmittingReply
                                ? secondBlueColor.withOpacity(0.5)
                                : secondBlueColor,
                            borderRadius: BorderRadius.circular(20.r),
                          ),
                          child: isSubmittingReply
                              ? SizedBox(
                                  width: 20.r,
                                  height: 20.r,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        whiteColor),
                                  ),
                                )
                              : Icon(
                                  Icons.send,
                                  color: whiteColor,
                                  size: 20.r,
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],

          // Replies section (jika isOpenReply true)
          if (widget.commentModel.isOpenReply &&
              widget.commentModel.reply != null &&
              widget.commentModel.reply!.isNotEmpty) ...[
            16.verticalSpace,
            Container(
              margin: EdgeInsets.only(left: 32.w),
              padding: EdgeInsets.only(left: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(
                    color: secondWhiteColor.withOpacity(0.3),
                    width: 2,
                  ),
                ),
              ),
              child: Column(
                children: widget.commentModel.reply!.map((reply) {
                  return Container(
                    margin: EdgeInsets.only(bottom: 12.h),
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: baseBlueColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: secondWhiteColor.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Reply header
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 16.r,
                              backgroundImage: reply
                                              .createdUser?.imageProfile !=
                                          null &&
                                      reply
                                          .createdUser!.imageProfile!.isNotEmpty
                                  ? NetworkImage(
                                      reply.createdUser!.imageProfile!)
                                  : const AssetImage('assets/images/Person.jpg')
                                      as ImageProvider,
                            ),
                            8.horizontalSpace,
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextWigdet(
                                    title: reply.createdUser?.pasisName ??
                                        "Unknown User",
                                    fontSize: 20,
                                    fontWeight: FontWeight.w600,
                                    textColor: whiteColor,
                                  ),
                                  CustomTextWigdet(
                                    title: reply.createdAt != null
                                        ? _getTimeAgo(reply.createdAt!)
                                        : "Unknown Time",
                                    fontSize: 18,
                                    textColor:
                                        secondWhiteColor.withOpacity(0.7),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        8.verticalSpace,

                        // Reply content
                        CustomTextWigdet(
                          title: reply.comment ?? "No reply available",
                          fontSize: 20,
                          textColor: whiteColor.withOpacity(0.9),
                          maxLines: null,
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
