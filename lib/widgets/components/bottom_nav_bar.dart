import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/assesment_course/views/assesment_course_view.dart';
import 'package:mides_skadik/app/modules/course/attendance_course/controllers/attendance_course_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/clases_course_view.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/dashboard_course_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/dashboard_course_view.dart';
import 'package:mides_skadik/app/modules/course/grades_course/views/grades_course_view.dart';
import 'package:mides_skadik/app/modules/course/schedule_course/views/schedule_course_view.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/views/dashboard_view.dart';
import 'package:mides_skadik/app/modules/media_player/library/views/library_view.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/clip_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playback_controller.dart';
import 'package:mides_skadik/app/modules/media_player/searchdata/views/searchdata_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/widgets/components/custom_scaffold.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class BottomNavBar extends StatefulWidget {
  final String choosenScreen;
  const BottomNavBar({super.key, required this.choosenScreen});

  @override
  BottomNavBarState createState() => BottomNavBarState();
}

class BottomNavBarState extends State<BottomNavBar> {
  int _selectedIndex = 0;
  final orientation = Get.find<OrientationController>();

  final Map<int, Widget> _coursePages = {};
  final Map<int, Widget> _mediaPages = {};

  final List<Map<String, dynamic>> _menuItemsMediaPlayer = [
    {'icon': "assets/icons/dashoard.svg", 'label': 'Dashboard'},
    {'icon': "assets/icons/search.svg", 'label': 'Browse'},
    {'icon': "assets/icons/library.svg", 'label': 'Library'},
  ];

  final List<Map<String, dynamic>> _menuItemsCourse = [
    {'icon': "assets/icons/dashoard.svg", 'label': 'Dashboard'},
    {'icon': "assets/icons/menu_book.svg", 'label': 'Clasess'},
    {'icon': "assets/icons/calendar.svg", 'label': 'Schedule'},
    {'icon': "assets/icons/description.svg", 'label': 'Assessment'},
    {'icon': "assets/icons/check.svg", 'label': 'Grades'},
  ];

  Widget _createCoursePage(int index) {
    switch (index) {
      case 0:
        return DashboardCourseView(
          onRequestChangePage: (idx) => _onItemTapped(idx),
        );
      case 1:
        return const ClasesCourseView();
      case 2:
        return const ScheduleCourseView();
      case 3:
        return const AssesmentCourseView();
      case 4:
        return const GradesCourseView();
      default:
        return const SizedBox();
    }
  }

  Widget _createMediaPage(int index) {
    switch (index) {
      case 0:
        return const DashboardView();
      case 1:
        return const SearchdataView();
      case 2:
        return const LibraryView();
      default:
        return const SizedBox();
    }
  }

  void _onItemTapped(int index) async {
    final isCourse = widget.choosenScreen.toLowerCase() == "course";

    if (isCourse) {
      final wasAttendance = _selectedIndex == 3;
      final isAttendance = index == 3;
      final isClasses = index == 1;

      // Jika keluar dari halaman attendance
      if (wasAttendance && !isAttendance) {
        if (Get.isRegistered<AttendanceCourseController>()) {
          Get.find<AttendanceCourseController>().disposeCamera();
        }
      }

      // Jika masuk ke halaman attendance
      if (!wasAttendance && isAttendance) {
        if (Get.isRegistered<AttendanceCourseController>()) {
          await Get.find<AttendanceCourseController>().initializeCamera();
        }
      }

      if (index == 0 && widget.choosenScreen.toLowerCase() == "course") {
        // 0 = DashboardCourseView
        if (Get.isRegistered<DashboardCourseController>()) {
          await Get.find<DashboardCourseController>().getAttendanceHistory();
        }
      }

      if (isClasses && widget.choosenScreen.toLowerCase() == "course") {
        // 0 = DashboardCourseView
        if (Get.isRegistered<ClasesCourseController>()) {
          await Get.find<ClasesCourseController>().selectTab(2);
        }
      }
    }

    setState(() {
      _selectedIndex = index;
      if (widget.choosenScreen.toLowerCase() == 'course') {
        _coursePages.putIfAbsent(index, () => _createCoursePage(index));
      } else {
        _mediaPages.putIfAbsent(index, () => _createMediaPage(index));
      }
    });
  }

  @override
  void initState() {
    super.initState();

    final isCourse = widget.choosenScreen.toLowerCase() == "course";
    if (isCourse && !Get.isRegistered<DashboardCourseController>()) {
      Get.put(DashboardCourseController(), permanent: true);
    }

    if (isCourse) {
      _coursePages[0] = _createCoursePage(0);
      if (_selectedIndex == 3) {
        Future.microtask(() async {
          if (Get.isRegistered<AttendanceCourseController>()) {
            await Get.find<AttendanceCourseController>().initializeCamera();
          }
        });
      }
    } else {
      _mediaPages[0] = _createMediaPage(0);

      if (!Get.isRegistered<ClipController>()) {
        Get.put(ClipController(), permanent: true);
      }

      if (!Get.isRegistered<PlaybackController>()) {
        Get.put(PlaybackController(), permanent: true);
      }
    }
  }

  @override
  void dispose() {
    if (widget.choosenScreen.toLowerCase() == "course" && _selectedIndex == 3) {
      if (Get.isRegistered<AttendanceCourseController>()) {
        Get.find<AttendanceCourseController>().disposeCamera();
      }
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orientationController = Get.put(OrientationController());

    return Obx(() {
      bool isPortrait = !orientationController.isLandscape.value;

      return CustomScaffold(
        body: Stack(
          children: [
            IndexedStack(
              index: _selectedIndex,
              children: List.generate(
                widget.choosenScreen.toLowerCase() == "course"
                    ? _menuItemsCourse.length
                    : _menuItemsMediaPlayer.length,
                (index) {
                  return widget.choosenScreen.toLowerCase() == 'course'
                      ? _coursePages[index] ?? const SizedBox()
                      : _mediaPages[index] ?? const SizedBox();
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20.h),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: ClipRRect(
                  borderRadius: isPortrait
                      ? BorderRadius.circular(5.r)
                      : BorderRadius.circular(10.r),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 1, sigmaY: 6),
                    child: Container(
                      padding: isPortrait
                          ? EdgeInsets.symmetric(
                              vertical: 10.h,
                              horizontal: 10.w,
                            )
                          : const EdgeInsets.symmetric(
                              vertical: 15,
                              horizontal: 15,
                            ).h,
                      decoration: BoxDecoration(
                        color: darkBlueColor,
                        borderRadius: isPortrait
                            ? BorderRadius.circular(12.r)
                            : BorderRadius.circular(10.r),
                      ),
                      child: Wrap(
                        alignment: WrapAlignment.center,
                        spacing: isPortrait ? 20.w : 20.w,
                        runSpacing: 5.h,
                        children: widget.choosenScreen.toLowerCase() == 'course'
                            ? _menuItemsCourse.asMap().entries.map((entry) {
                                int index = entry.key;
                                var item = entry.value;
                                bool isSelected = _selectedIndex == index;

                                return GestureDetector(
                                  onTap: () => _onItemTapped(index),
                                  child: AnimatedContainer(
                                    width: isPortrait ? 187.w : 230.w,
                                    height: isPortrait ? 64.h : 65.h,
                                    duration: const Duration(milliseconds: 300),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? whiteColor.withOpacity(0.25)
                                          : Colors.transparent,
                                      borderRadius: isPortrait
                                          ? BorderRadius.circular(5.r)
                                          : BorderRadius.circular(10.r),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          item['icon'] as String,
                                          colorFilter: ColorFilter.mode(
                                            isSelected
                                                ? whiteColor
                                                : whiteColor,
                                            BlendMode.srcIn,
                                          ),
                                          width: isPortrait ? 30.w : 40.w,
                                          height: isPortrait ? 25.w : 40.h,
                                        ),
                                        15.horizontalSpace,
                                        CustomTextWigdet(
                                          title: item['label'] ?? 'Unknown',
                                          fontSize: isPortrait ? 18 : 24,
                                          fontWeight: FontWeight.w600,
                                          textColor: isSelected
                                              ? whiteColor
                                              : whiteColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList()
                            : _menuItemsMediaPlayer
                                .asMap()
                                .entries
                                .map((entry) {
                                int index = entry.key;
                                var item = entry.value;
                                bool isSelected = _selectedIndex == index;

                                return GestureDetector(
                                  onTap: () => _onItemTapped(index),
                                  child: AnimatedContainer(
                                    width: isPortrait ? 187.w : 250.w,
                                    height: isPortrait ? 64.h : 70.h,
                                    duration: const Duration(milliseconds: 300),
                                    decoration: BoxDecoration(
                                      color: isSelected
                                          ? Colors.white.withOpacity(0.25)
                                          : Colors.transparent,
                                      borderRadius: isPortrait
                                          ? BorderRadius.circular(5.r)
                                          : BorderRadius.circular(10.r),
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SvgPicture.asset(
                                          item['icon'] as String,
                                          colorFilter: ColorFilter.mode(
                                            isSelected
                                                ? whiteColor
                                                : whiteColor,
                                            BlendMode.srcIn,
                                          ),
                                          width: isPortrait ? 30.w : 40.w,
                                          height: isPortrait ? 25.w : 40.h,
                                        ),
                                        15.horizontalSpace,
                                        CustomTextWigdet(
                                          title: item['label'] ?? 'Unknown',
                                          fontSize: isPortrait ? 18 : 24,
                                          fontWeight: FontWeight.w600,
                                          textColor: isSelected
                                              ? whiteColor
                                              : whiteColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
