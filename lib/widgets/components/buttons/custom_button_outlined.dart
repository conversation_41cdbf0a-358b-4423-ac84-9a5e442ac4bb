import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomButtonOutlined extends StatelessWidget {
  final VoidCallback? onTap;
  final String label;
  final String? icon;
  final double? height;
  final double? width;

  const CustomButtonOutlined({
    Key? key,
    required this.label,
    this.onTap,
    this.icon,
    this.height,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var labelW = CustomTextWigdet(
      title: label,
      fontSize: 16,
    );

    return SizedBox(
      width: width,
      height: height ?? 26,
      child: icon != null
          ? OutlinedButton.icon(
              onPressed: onTap,
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
              ),
              icon: SvgPicture.asset(
                icon!,
                height: 16,
              ),
              label: labelW,
            )
          : OutlinedButton(
              onPressed: onTap,
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
              ),
              child: labelW,
            ),
    );
  }
}
