import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomDropdown extends StatelessWidget {
  final double? width;
  final double? height;
  final Widget? textSelected;
  final List<Map<String, String>> itemsDropdown;
  final void Function(Map<String, String>)? onSelected;
  const CustomDropdown({
    super.key,
    required this.itemsDropdown,
    this.width,
    this.height,
    this.onSelected,
    this.textSelected,
  });

  @override
  Widget build(BuildContext context) {
    // final controller = Get.put(MyVideoController());
    // controller.sortSelected.value = itemsDropdown.first.values.first;

    return PopupMenuButton<Map<String, String>>(
      onSelected: onSelected,
      color: baseBlueColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      offset: const Offset(0, 0),
      itemBuilder: (context) {
        return itemsDropdown.map((item) {
          return PopupMenuItem<Map<String, String>>(
            value: item,
            child: Row(
              children: [
                CustomTextWigdet(
                  title: item["title"]!,
                  textColor: secondWhiteColor,
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                ),
                if (item["subtitle"]!.isNotEmpty)
                  CustomTextWigdet(
                    title: " /  ${item["subtitle"]}",
                    textColor: Colors.grey,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
              ],
            ),
          );
        }).toList();
      },
      child: IntrinsicWidth(
        child: CustomContainer(
          height: 64,
          bgColor: secondBlueColor.withOpacity(0.10),
          borderRadius: BorderRadius.circular(10.r),
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTextWigdet(
                  title: "Sort: ",
                  textColor: secondWhiteColor,
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                ),
                textSelected ?? const SizedBox.shrink(),
                8.horizontalSpace,
                SvgPicture.asset(
                  "assets/icons/icon_chevron.svg",
                  width: 40.w,
                  height: 40.h,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
