import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomButtonText extends StatelessWidget {
  final String title;
  final bool? isSelected;
  final double fontSize;
  final void Function()? onTap;

  const CustomButtonText(
      {super.key,
      required this.title,
      this.isSelected,
      this.onTap,
      this.fontSize = 24});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () {},
      child: CustomContainer(
        radius: 8,
        bgColor: isSelected ?? false
            ? secondBlueColor.withOpacity(0.2)
            : Colors.transparent,
        height: 55,
        widget: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Center(
            child: CustomTextWigdet(
                title: title,
                fontSize: fontSize,
                textColor: isSelected ?? false
                    ? secondWhiteColor
                    : secondWhiteColor.withOpacity(0.5),
                fontWeight:
                    isSelected ?? false ? FontWeight.w700 : FontWeight.w400),
          ),
        ),
      ),
    );
  }
}
