import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';

enum IconPosition { left, right }

class CustomFilledButtonWidget extends StatelessWidget {
  final String? title;
  final String? assetName;
  final double? fontSize;
  final double? heightButton;
  final double? widthButton;
  final double? widthIcon;
  final double? heightIcon;
  final double? widthIconLoading;
  final double? heightIconLoading;
  final double? radius;
  final double? outlineWidth;
  final Color? fontColor;
  final Color? bgColor;
  final Color? bgSelectedColor;
  final Color? borderColor;
  final Color? iconColor;
  final LinearGradient? gradient;
  final bool? isLoading;
  final bool? withIcon;
  final bool? onlyIcon;
  final bool? isOutlined;
  final bool? isSelected;
  final EdgeInsetsGeometry? padding;
  final FontWeight? fontWeight;
  final void Function()? onPressed;
  final IconPosition? iconPosition;

  const CustomFilledButtonWidget({
    super.key,
    this.title,
    this.heightButton,
    this.widthButton,
    this.fontSize,
    this.radius,
    this.fontWeight,
    this.fontColor,
    required this.onPressed,
    this.bgColor,
    this.bgSelectedColor,
    this.iconColor,
    this.gradient,
    this.isLoading,
    this.withIcon,
    this.assetName,
    this.padding,
    this.widthIcon,
    this.heightIcon,
    this.onlyIcon,
    this.widthIconLoading,
    this.heightIconLoading,
    this.isSelected,
    this.isOutlined = false, // Default false
    this.borderColor, // Optional
    this.outlineWidth = 1.5, // Default 1.5
    this.iconPosition = IconPosition.left,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widthButton?.w,
      height: heightButton?.h ?? 50.h,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(radius?.r ?? 0.r),
        child: Container(
          decoration: BoxDecoration(
            gradient: isOutlined! ? null : gradient,
            color: isOutlined!
                ? Colors.transparent
                : isSelected ?? false
                    ? bgSelectedColor
                    : (gradient == null
                        ? (bgColor ?? whiteColor.withOpacity(0.4))
                        : null),
            border: isOutlined!
                ? Border.all(
                    color: borderColor ?? blueColor,
                    width: outlineWidth ?? 1.5,
                  )
                : null,
            borderRadius: BorderRadius.circular(radius?.r ?? 0.r),
          ),
          padding: padding ?? EdgeInsets.zero,
          child: Center(
            child: isLoading ?? false
                ? CustomLoadingWidget(
                    color: fontColor ?? whiteColor,
                    bgColor: greyColor,
                    width: widthIconLoading,
                    height: heightIconLoading,
                  )
                : withIcon ?? false
                    ? onlyIcon ?? false
                        ? SvgPicture.asset(
                            assetName!,
                            width: widthIcon?.w ?? 16.w,
                            height: heightIcon?.h ?? 16.h,
                            color: iconColor,
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (iconPosition == IconPosition.left)
                                SvgPicture.asset(
                                  assetName!,
                                  width: widthIcon?.w ?? 16.w,
                                  height: heightIcon?.h ?? 16.h,
                                  color: fontColor ?? blackColor,
                                ),
                              if (iconPosition == IconPosition.left)
                                16.horizontalSpace,
                              Text(
                                title ?? '',
                                style: TextStyle(
                                  fontFamily: 'Inter',
                                  fontSize: fontSize?.sp ?? 28.sp,
                                  fontWeight: fontWeight ?? FontWeight.w400,
                                  color: fontColor ?? blackColor,
                                ),
                              ),
                              if (iconPosition == IconPosition.right)
                                16.horizontalSpace,
                              if (iconPosition == IconPosition.right)
                                SvgPicture.asset(
                                  assetName!,
                                  width: widthIcon?.w ?? 16.w,
                                  height: heightIcon?.h ?? 16.h,
                                  color: fontColor ?? blackColor,
                                ),
                            ],
                          )
                    : Text(
                        title ?? '',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: fontSize?.sp ?? 28.sp,
                          fontWeight: fontWeight ?? FontWeight.w400,
                          color: isSelected ?? false
                              ? blackColor
                              : fontColor ?? blackColor,
                        ),
                      ),
          ),
        ),
      ),
    );
  }
}
