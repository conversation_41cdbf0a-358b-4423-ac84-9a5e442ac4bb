import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomButtonFilter extends StatelessWidget {
  final String title;
  final double? fontSize;
  final double? heightButton;
  final double? widthButton;
  final double? radius;
  final Color? textColor;
  final Color? bgColor;
  final bool? isSelected;
  final EdgeInsetsGeometry? padding;
  final FontWeight? fontWeight;
  final void Function()? onPressed;
  const CustomButtonFilter(
      {super.key,
      required this.title,
      this.fontSize,
      this.heightButton,
      this.widthButton,
      this.radius,
      this.textColor,
      this.bgColor,
      this.padding,
      this.fontWeight,
      this.onPressed,
      this.isSelected});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widthButton?.w,
      height: heightButton?.h ?? 50.h,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          splashFactory: NoSplash.splashFactory,
          backgroundColor:
              isSelected ?? false ? bgColor : secondBlueColor.withOpacity(0.2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius?.r ?? 12.r),
          ),
        ),
        child: CustomTextWigdet(
          title: title,
          fontSize: fontSize ?? 28.sp,
          textColor: isSelected ?? false ? darkGreyColor : textColor,
          fontWeight: fontWeight ?? FontWeight.w600,
        ),
      ),
    );
  }
}
