import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomUserInfo extends StatelessWidget {
  final String? photoUserUrl;
  final String? title;
  final String? subTitle;
  final double? fontSizeTitle;
  final double? fontSizeSubtitle;
  final double? width;
  final double? height;
  final double? spacing;
  final double? maxWidth;
  final Color? titleColor;
  final Color? subTitleColor;
  const CustomUserInfo({
    super.key,
    this.fontSizeTitle,
    this.fontSizeSubtitle,
    this.photoUserUrl,
    this.title,
    this.subTitle,
    this.width,
    this.height,
    this.maxWidth,
    this.titleColor,
    this.subTitleColor,
    this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
              color: darkBlueColor.withValues(alpha: .4),
              borderRadius: BorderRadiusDirectional.circular(12.r)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: (photoUserUrl == null || photoUserUrl!.isEmpty)
                ? Image.asset(
                    'assets/images/profile_pict.png',
                    width: width?.w ?? 70.w,
                    height: height?.h ?? 150.h,
                    fit: BoxFit.cover,
                  )
                : CachedNetworkImage(
                    imageUrl: photoUserUrl!,
                    width: width?.w ?? 70.w,
                    height: height?.h ?? 150.h,
                    placeholder: (context, url) => const CustomLoadingWidget(),
                    errorWidget: (context, url, error) {
                      return const Icon(Icons.error);
                    },
                  ),
          ),
        ),
        spacing?.horizontalSpace ?? 24.horizontalSpace,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ConstrainedBox(
              constraints:
                  BoxConstraints(maxWidth: maxWidth ?? double.maxFinite),
              child: CustomTextWigdet(
                title: title ?? 'Danyl Stephan Kok',
                fontSize: fontSizeTitle?.sp ?? 28.sp,
                fontWeight: FontWeight.w700,
                textColor: titleColor ?? whiteColor,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            ConstrainedBox(
              constraints:
                  BoxConstraints(maxWidth: maxWidth ?? double.maxFinite),
              child: CustomTextWigdet(
                title: subTitle == null ? '@$subTitle' : '@$subTitle',
                fontSize: fontSizeSubtitle?.sp ?? 22.sp,
                textColor: subTitleColor ?? secondWhiteColor,
                overflow: TextOverflow.ellipsis,
              ),
            )
          ],
        )
      ],
    );
  }
}
