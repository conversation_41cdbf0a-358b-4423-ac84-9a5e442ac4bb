import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_user_info.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';

class CustomHeader extends StatelessWidget {
  final String? userName;
  final String? nameTag;
  final String? userImageUrl;
  final double? titleSize;
  final double? subTitleSize;
  final double? widthUserInfo;
  final double? heightUserInfo;
  final double? maxWidth;
  final Color? titleColor;
  final Color? subTitleColor;
  final bool? isLandscape;
  final void Function()? onDownloadPress;
  final void Function()? onMyVideoPress;
  final void Function()? onUploadPress;
  final void Function()? onBroadcastPress;
  final void Function()? viewOtherButton;
  const CustomHeader({
    super.key,
    this.widthUserInfo,
    this.heightUserInfo,
    this.onDownloadPress,
    this.onMyVideoPress,
    this.onUploadPress,
    this.onBroadcastPress,
    this.viewOtherButton,
    this.titleColor,
    this.subTitleColor,
    this.titleSize,
    this.subTitleSize,
    this.isLandscape,
    this.userName,
    this.nameTag,
    this.maxWidth,
    this.userImageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      bgColor: whiteColor.withOpacity(0.1),
      radius: isLandscape ?? false ? 12 : 0,
      widget: Stack(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
                horizontal: isLandscape ?? false ? 20.w : 40.w,
                vertical: isLandscape ?? false ? 12.h : 32.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomUserInfo(
                  spacing: 24,
                  maxWidth: isLandscape ?? false
                      ? maxWidth?.w ?? 220.w
                      : double.maxFinite,
                  title: userName ?? 'User Name',
                  photoUserUrl: userImageUrl ?? '',
                  fontSizeTitle: titleSize,
                  fontSizeSubtitle: subTitleSize,
                  titleColor: titleColor,
                  subTitleColor: subTitleColor,
                  width: widthUserInfo,
                  height: heightUserInfo,
                ),
                Row(
                  children: [
                    isLandscape ?? false
                        ? 16.horizontalSpace
                        : 24.horizontalSpace,
                    CustomFilledButtonWidget(
                      onPressed: onMyVideoPress,
                      withIcon: true,
                      assetName: 'assets/icons/icon_videos.svg',
                      title: 'My Videos',
                      fontSize: 18,
                      widthIcon: 32,
                      heightIcon: 32,
                      radius: 8,
                      bgColor: darkGreyColor,
                      fontColor: secondWhiteColor,
                      padding: EdgeInsets.symmetric(horizontal: 18.w),
                    ),
                    isLandscape ?? false
                        ? 16.horizontalSpace
                        : 24.horizontalSpace,
                    CustomFilledButtonWidget(
                      onPressed: onUploadPress,
                      withIcon: true,
                      assetName: 'assets/icons/plus.svg',
                      title: 'Upload Video',
                      fontSize: 18,
                      widthIcon: 24,
                      heightIcon: 24,
                      radius: 8,
                      bgColor: blueColor,
                      fontColor: secondWhiteColor,
                      padding: EdgeInsets.symmetric(horizontal: 18.w),
                    ),
                    // isLandscape ?? false
                    //     ? 1.horizontalSpace
                    //     : 24.horizontalSpace,
                    // isLandscape ?? false
                    //     ? CustomFilledButtonWidget(
                    //         onPressed: viewOtherButton,
                    //         withIcon: true,
                    //         onlyIcon: true,
                    //         assetName: 'assets/icons/icon_chevron.svg',
                    //         widthButton: 32,
                    //         heightIcon: 35,
                    //         radius: 4,
                    //         bgColor: blueColor,
                    //       )
                    //     : CustomFilledButtonWidget(
                    //         onPressed: onBroadcastPress,
                    //         withIcon: true,
                    //         assetName: 'assets/icons/broadcast.svg',
                    //         title: 'Broadcast',
                    //         fontSize: 18,
                    //         widthIcon: 32,
                    //         heightIcon: 32,
                    //         radius: 8,
                    //         bgColor: null,
                    //         gradient: LinearGradient(
                    //           colors: [redColor, darkFuchsia],
                    //           stops: const [0.5, 2.0],
                    //           transform: const GradientRotation(0.5),
                    //         ),
                    //         fontColor: secondWhiteColor,
                    //         padding: EdgeInsets.symmetric(horizontal: 18.w),
                    //       )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
