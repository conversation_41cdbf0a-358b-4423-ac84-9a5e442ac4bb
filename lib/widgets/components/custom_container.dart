import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomContainer extends StatelessWidget {
  final Widget? widget;
  final Color? bgColor;
  final double? radius;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Border? border;
  final EdgeInsetsGeometry? padding;
  final Clip? clipBehavior;
  final BoxShape shape;
  final Gradient? gradient;
  final List<BoxShadow>? boxShadow;
  final AlignmentGeometry? alignment;
  final EdgeInsets? margin;

  const CustomContainer({
    super.key,
    this.widget,
    this.bgColor,
    this.radius,
    this.height,
    this.width,
    this.borderRadius,
    this.border,
    this.padding,
    this.clipBehavior,
    this.shape = BoxShape.rectangle,
    this.gradient,
    this.boxShadow,
    this.alignment,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width?.w,
      height: height?.h,
      padding: padding,
      clipBehavior: clipBehavior ?? Clip.antiAliasWithSaveLayer,
      alignment: alignment,
      margin: margin,
      decoration: BoxDecoration(
        color: bgColor,
        shape: shape,
        gradient: gradient,
        boxShadow: boxShadow,
        borderRadius: shape == BoxShape.rectangle ? (borderRadius ?? BorderRadius.circular(radius?.r ?? 0)) : null,
        border: border,
      ),
      child: widget ?? const SizedBox.shrink(),
    );
  }
}
