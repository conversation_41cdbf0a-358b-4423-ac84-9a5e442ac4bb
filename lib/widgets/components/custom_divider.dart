import 'package:flutter/material.dart';

class CustomDivider extends StatelessWidget {
  final double? width;
  final double? height;
  final bool? isVertical;
  const CustomDivider({super.key, this.width, this.height, this.isVertical});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? double.infinity,
      height: height ?? 1,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin:
              isVertical ?? true ? Alignment.centerLeft : Alignment.topCenter,
          end: isVertical ?? true
              ? Alignment.centerRight
              : Alignment.bottomCenter,
          colors: [
            const Color(0xFF12181F).withOpacity(0.75),
            const Color(0xFF6D839C).withOpacity(0.75),
            const Color(0xFF12181F).withOpacity(0.75)
          ],
          stops: isVertical ?? true ? const [0, 0.5, 1] : const [0.1, 0.5, 1],
        ),
      ),
    );
  }
}
