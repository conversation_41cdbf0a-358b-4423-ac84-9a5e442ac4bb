import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomSection extends StatelessWidget {
  final String sectionTitle;
  final String? sectionTitleButton;
  final void Function()? onPressed;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? paddingChild;
  final Color? bgColor;
  final double? height;
  final double? width;

  const CustomSection({
    super.key,
    required this.sectionTitle,
    this.sectionTitleButton = '',
    this.onPressed,
    this.padding,
    this.paddingChild,
    this.bgColor,
    this.height,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          padding ?? EdgeInsets.symmetric(horizontal: 40.w, vertical: 32.h),
      child: CustomContainer(
        height: height,
        width: width,
        bgColor: bgColor,
        widget: Padding(
          padding: paddingChild ?? EdgeInsets.zero,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomTextWigdet(
                title: sectionTitle,
                fontSize: 32,
                fontWeight: FontWeight.w600,
              ),
              sectionTitleButton == null || sectionTitleButton!.isEmpty
                  ? const SizedBox.shrink()
                  : CustomFilledButtonWidget(
                      onPressed: onPressed,
                      title: sectionTitleButton,
                      fontSize: 18,
                      fontColor: whiteColor,
                      bgColor: darkGreyColor,
                      radius: 8,
                      padding: EdgeInsets.symmetric(horizontal: 18.w),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
