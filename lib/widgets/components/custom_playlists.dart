import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_model.dart';
import 'package:mides_skadik/app/data/utils/date_format.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_shimmer.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomPlaylists extends StatelessWidget {
  final PlaylistModel? playlist;
  final void Function()? onTap;
  final bool? isLoading;
  const CustomPlaylists({
    super.key,
    this.onTap,
    this.isLoading,
    this.playlist,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading == true) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomShimmer(
            height: 480.h,
            width: Get.width,
          ),
          16.verticalSpace,
          CustomShimmer(
            height: 80.h,
            width: Get.width,
          ),
        ],
      );
    }
    return GestureDetector(
      onTap: onTap ?? () {},
      child: LayoutBuilder(builder: (context, constraints) {
        var width = constraints.maxWidth;

        var firstVideo =
            playlist?.vods?.isNotEmpty == true ? playlist?.vods?.first : null;

        var uploadDate = firstVideo?.uploadDate != null
            ? DateFormatUtil.formatDateTimeFromString(
                firstVideo?.uploadDate ?? '')
            : '-';
        return CustomContainer(
          bgColor: greyColor.withOpacity(0.5),
          radius: 12,
          width: width,
          widget: Column(
            children: [
              Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomContainer(
                      radius: 8,
                      widget: Container(
                        color: greyColor.withAlpha(140),
                        height: 300.h,
                        width: width * 0.9,
                      ),
                    ),
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomContainer(
                      radius: 8,
                      widget: Container(
                        color: greyColor.withOpacity(0.9),
                        height: 290.h,
                        width: width * 0.95,
                      ),
                    ),
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomContainer(
                      radius: 8,
                      bgColor: greyColor.withOpacity(1),
                      widget: SizedBox(
                        height: 280.h,
                        width: width,
                        child: firstVideo != null
                            ? Image.network(
                                firstVideo.thumbnailUrl ?? '',
                                width: width,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return const Icon(Icons.error);
                                },
                              )
                            : const Icon(Icons.error),
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomTextWigdet(
                          title: playlist?.name ?? '',
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          textColor: whiteColor,
                        ),
                        CustomTextWigdet(
                          title:
                              '${(playlist?.visibility ?? '').toLowerCase().capitalizeFirst} • Diupload $uploadDate',
                          fontSize: 18,
                          fontWeight: FontWeight.w300,
                        )
                      ],
                    ),
                    const Spacer(),
                    Icon(
                      Icons.more_vert_rounded,
                      color: whiteColor,
                      size: 48.r,
                    )
                  ],
                ),
              )
            ],
          ),
        );
      }),
    );
  }
}
