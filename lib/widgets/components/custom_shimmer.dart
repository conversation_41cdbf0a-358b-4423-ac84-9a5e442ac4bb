import 'package:flutter/material.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:shimmer/shimmer.dart';

class CustomShimmer extends StatelessWidget {
  final double? width;
  final double? height;
  final double? radius;
  const CustomShimmer({
    Key? key,
    this.width,
    this.height,
    this.radius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: width ?? 200.0,
      height: height ?? 100.0,
      radius: radius,
      widget: Shimmer.fromColors(
        baseColor: greyColor,
        highlightColor: darkGreyColor,
        child:  CustomContainer(
          bgColor: whiteColor,
        ),
      ),
    );
  }
}
