import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/constant/constants.dart';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';

class GroupedTimeline extends StatefulWidget {
  final double duration;
  final double currentPosition;
  final List<MediaItem> mediaItems;
  final Function(double) onPositionChanged;
  final Function(MediaItem) onMediaItemTap;
  final Function(MediaItem) onMediaItemDelete;
  final Function(MediaItem, double, double)? onMediaItemResize;
  final Function(MediaItem, double)? onMediaItemMove;

  const GroupedTimeline({
    super.key,
    required this.duration,
    required this.currentPosition,
    required this.mediaItems,
    required this.onPositionChanged,
    required this.onMediaItemTap,
    required this.onMediaItemDelete,
    this.onMediaItemResize,
    this.onMediaItemMove,
  });

  @override
  State<GroupedTimeline> createState() => _GroupedTimelineState();
}

class _GroupedTimelineState extends State<GroupedTimeline> {
  bool _isDragging = false;
  double _dragPosition = 0;

  // Zoom state
  double _zoomLevel =
      0.5; // 0.5 = 50% (50px per second), 1.0 = 100% (100px per second)
  final double _minZoom = 0.01; // 1% (1px per second)
  final double _maxZoom = 1.0; // 100% (100px per second)

  // Drag and resize state
  MediaItem? _draggingItem;
  bool _isDraggingItem = false;
  bool _isResizingStart = false;
  bool _isResizingEnd = false;
  double _dragStartX = 0;
  double _originalStartTime = 0;
  double _originalEndTime = 0;

  @override
  Widget build(BuildContext context) {
    // Group media items by type
    final groupedItems = _groupItemsByType();

    return Container(
      height: 240, // Increased height for zoom controls
      color: Color(AppConstants.surfaceColor),
      child: Column(
        children: [
          // Zoom controls
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Row(
              children: [
                Text(
                  'Zoom (1%-100%):',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _zoomOut,
                  icon: const Icon(Icons.zoom_out,
                      color: Colors.white70, size: 20),
                  constraints:
                      const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                Expanded(
                  child: Slider(
                    value: _zoomLevel,
                    min: _minZoom,
                    max: _maxZoom,
                    divisions: 99, // 1% to 100% = 99 divisions
                    activeColor: Color(AppConstants.accentColor),
                    inactiveColor: Colors.white30,
                    onChanged: (value) {
                      setState(() {
                        _zoomLevel = value;
                      });
                    },
                  ),
                ),
                IconButton(
                  onPressed: _zoomIn,
                  icon: const Icon(Icons.zoom_in,
                      color: Colors.white70, size: 20),
                  constraints:
                      const BoxConstraints(minWidth: 32, minHeight: 32),
                ),
                Text(
                  '${(_zoomLevel * 100).round()}%',
                  style: TextStyle(color: Colors.white70, fontSize: 12),
                ),
              ],
            ),
          ),
          // Time ruler
          Container(
            height: 30,
            child: CustomPaint(
              painter: TimeRulerPainter(
                duration: widget.duration,
                currentPosition:
                    _isDragging ? _dragPosition : widget.currentPosition,
                zoomLevel: _zoomLevel,
              ),
              size: Size.infinite,
            ),
          ),
          // Grouped tracks
          Expanded(
            child: Row(
              children: [
                // Track labels
                Container(
                  width: 80,
                  child: Column(
                    children: [
                      _buildTrackLabel('Video', Colors.blue, Icons.videocam),
                      _buildTrackLabel('Audio', Colors.green, Icons.audiotrack),
                      _buildTrackLabel('Image', Colors.orange, Icons.image),
                      _buildTrackLabel(
                          'Text', Colors.purple, Icons.text_fields),
                    ],
                  ),
                ),
                // Timeline content with horizontal scroll
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: SizedBox(
                      width: _getTimelineWidth(),
                      child: GestureDetector(
                        onTapDown: (details) => _onTimelineClick(details),
                        child: Stack(
                          children: [
                            // Background grid
                            CustomPaint(
                              painter: GroupedGridPainter(
                                duration: widget.duration,
                                zoomLevel: _zoomLevel,
                              ),
                              size: Size(_getTimelineWidth(), 170),
                            ),
                            // Media items by type
                            ...groupedItems.entries.expand((entry) {
                              final type = entry.key;
                              final items = entry.value;
                              return items.map(
                                  (item) => _buildMediaItemWidget(item, type));
                            }),
                            // Playhead
                            Positioned(
                              left: _getPositionX(_isDragging
                                  ? _dragPosition
                                  : widget.currentPosition),
                              top: 0,
                              bottom: 0,
                              child: Container(
                                width: 2,
                                color: Color(AppConstants.accentColor),
                                child: Transform.translate(
                                  offset: const Offset(-3, 5),
                                  child: Container(
                                    width: 8,
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: Color(AppConstants.accentColor),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Map<MediaType, List<MediaItem>> _groupItemsByType() {
    final grouped = <MediaType, List<MediaItem>>{
      MediaType.video: [],
      MediaType.audio: [],
      MediaType.image: [],
      MediaType.text: [],
    };

    for (final item in widget.mediaItems) {
      grouped[item.type]?.add(item);
    }

    // Sort items within each group by layer
    for (final items in grouped.values) {
      items.sort((a, b) => a.layer.compareTo(b.layer));
    }

    return grouped;
  }

  Widget _buildTrackLabel(String label, Color color, IconData icon) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Color(AppConstants.backgroundColor),
          border: Border(
            bottom: BorderSide(color: Colors.white10, width: 0.5),
            right: BorderSide(color: Colors.white10, width: 0.5),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaItemWidget(MediaItem item, MediaType type) {
    final left = _getPositionX(item.startTime);
    final width = _getPositionX(item.endTime) - left;
    final trackY = _getTrackY(type);

    Color itemColor;
    switch (type) {
      case MediaType.video:
        itemColor = Colors.blue;
        break;
      case MediaType.audio:
        itemColor = Colors.green;
        break;
      case MediaType.image:
        itemColor = Colors.orange;
        break;
      case MediaType.text:
        itemColor = Colors.purple;
        break;
    }

    return Positioned(
      left: left,
      top: trackY,
      width: width.clamp(20.0, double.infinity), // Minimum width
      height: 35, // Track height
      child: Stack(
        children: [
          // Main item container
          GestureDetector(
            onTap: () => widget.onMediaItemTap(item),
            onLongPress: () => _showMediaItemMenu(item),
            onPanStart: (details) => _onItemPanStart(item, details),
            onPanUpdate: (details) => _onItemPanUpdate(item, details),
            onPanEnd: (details) => _onItemPanEnd(item),
            child: Container(
              width: width,
              height: 25,
              margin: const EdgeInsets.symmetric(vertical: 5),
              decoration: BoxDecoration(
                color: itemColor.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: itemColor, width: 1),
              ),
              child: Row(
                children: [
                  // Layer indicator
                  Container(
                    width: 20,
                    height: 20,
                    margin: const EdgeInsets.only(left: 2, right: 4),
                    decoration: BoxDecoration(
                      color: itemColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(
                      child: Text(
                        '${item.layer}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  if (width > 60)
                    Expanded(
                      child: Text(
                        _getItemDisplayText(item),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Left resize handle
          Positioned(
            left: 0,
            top: 5,
            bottom: 5,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, true, details),
              onPanUpdate: (details) => _onResizeUpdate(item, true, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 6,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    bottomLeft: Radius.circular(4),
                  ),
                ),
              ),
            ),
          ),

          // Right resize handle
          Positioned(
            right: 0,
            top: 5,
            bottom: 5,
            child: GestureDetector(
              onPanStart: (details) => _onResizeStart(item, false, details),
              onPanUpdate: (details) => _onResizeUpdate(item, false, details),
              onPanEnd: (details) => _onResizeEnd(item),
              child: Container(
                width: 6,
                decoration: BoxDecoration(
                  color: itemColor,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(4),
                    bottomRight: Radius.circular(4),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getTrackY(MediaType type) {
    switch (type) {
      case MediaType.video:
        return 5;
      case MediaType.audio:
        return 45;
      case MediaType.image:
        return 85;
      case MediaType.text:
        return 125;
    }
  }

  double _getTimelineWidth() {
    // Calculate timeline width based on duration and zoom level
    // Base: 100px per second at 100% zoom (1.0), scaled by zoom level
    // At 1% zoom (0.01): 1px per second
    // At 100% zoom (1.0): 100px per second
    final basePixelsPerSecond = 100.0;
    final pixelsPerSecond = basePixelsPerSecond * _zoomLevel;
    final calculatedWidth = widget.duration * pixelsPerSecond;

    // Minimum width should also scale with zoom
    final minWidth = 500.0 * _zoomLevel;

    return calculatedWidth > minWidth ? calculatedWidth : minWidth;
  }

  void _zoomIn() {
    setState(() {
      // Increase by 10% (0.1) or multiply by 1.5, whichever is smaller
      final increment = (_zoomLevel * 0.5).clamp(0.05, 0.1);
      _zoomLevel = (_zoomLevel + increment).clamp(_minZoom, _maxZoom);
    });
  }

  void _zoomOut() {
    setState(() {
      // Decrease by 10% (0.1) or divide by 1.5, whichever is smaller
      final decrement = (_zoomLevel * 0.3).clamp(0.05, 0.1);
      _zoomLevel = (_zoomLevel - decrement).clamp(_minZoom, _maxZoom);
    });
  }

  double _getPositionX(double time) {
    // Calculate position based on zoom level
    // At 100% zoom (1.0): 100px per second
    // At 1% zoom (0.01): 1px per second
    final pixelsPerSecond = 100.0 * _zoomLevel;
    return time * pixelsPerSecond;
  }

  String _getItemDisplayText(MediaItem item) {
    switch (item.type) {
      case MediaType.text:
        return item.text ?? 'Text';
      case MediaType.video:
        return 'Video ${item.layer}';
      case MediaType.audio:
        return 'Audio ${item.layer}';
      case MediaType.image:
        return 'Image ${item.layer}';
    }
  }

  void _showMediaItemMenu(MediaItem item) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit'),
              onTap: () {
                Navigator.pop(context);
                widget.onMediaItemTap(item);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                widget.onMediaItemDelete(item);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Drag and resize methods
  void _onItemPanStart(MediaItem item, DragStartDetails details) {
    setState(() {
      _draggingItem = item;
      _isDraggingItem = true;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onItemPanUpdate(MediaItem item, DragUpdateDetails details) {
    if (!_isDraggingItem || _draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    final newStartTime =
        (_originalStartTime + deltaTime).clamp(0.0, widget.duration);

    if (widget.onMediaItemMove != null) {
      widget.onMediaItemMove!(item, newStartTime);
    }
  }

  void _onItemPanEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isDraggingItem = false;
    });
  }

  void _onResizeStart(
      MediaItem item, bool isLeftHandle, DragStartDetails details) {
    setState(() {
      _draggingItem = item;
      _isResizingStart = isLeftHandle;
      _isResizingEnd = !isLeftHandle;
      _dragStartX = details.localPosition.dx;
      _originalStartTime = item.startTime;
      _originalEndTime = item.endTime;
    });
  }

  void _onResizeUpdate(
      MediaItem item, bool isLeftHandle, DragUpdateDetails details) {
    if (_draggingItem?.id != item.id) return;

    final deltaX = details.localPosition.dx - _dragStartX;
    final deltaTime = _getTimeFromDeltaX(deltaX);

    double newStartTime = _originalStartTime;
    double newEndTime = _originalEndTime;

    if (isLeftHandle) {
      newStartTime =
          (_originalStartTime + deltaTime).clamp(0.0, _originalEndTime - 0.1);
    } else {
      newEndTime = (_originalEndTime + deltaTime)
          .clamp(_originalStartTime + 0.1, widget.duration);
    }

    if (widget.onMediaItemResize != null) {
      widget.onMediaItemResize!(item, newStartTime, newEndTime);
    }
  }

  void _onResizeEnd(MediaItem item) {
    setState(() {
      _draggingItem = null;
      _isResizingStart = false;
      _isResizingEnd = false;
    });
  }

  double _getTimeFromDeltaX(double deltaX) {
    // Calculate time from pixel delta based on zoom level
    final pixelsPerSecond = 100.0 * _zoomLevel;
    if (pixelsPerSecond <= 0) return 0.0;
    return deltaX / pixelsPerSecond;
  }

  void _onTimelineClick(TapDownDetails details) {
    final pixelsPerSecond = 100.0 * _zoomLevel;
    if (pixelsPerSecond <= 0) return;

    final clickX = details.localPosition.dx;
    final clickTime = clickX / pixelsPerSecond;
    final clampedTime = clickTime.clamp(0.0, widget.duration);

    widget.onPositionChanged(clampedTime);
  }
}

class TimeRulerPainter extends CustomPainter {
  final double duration;
  final double currentPosition;
  final double zoomLevel;

  TimeRulerPainter({
    required this.duration,
    required this.currentPosition,
    this.zoomLevel = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0 || size.height <= 0) return;

    final paint = Paint()
      ..color = Colors.white70
      ..strokeWidth = 1;

    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Calculate appropriate interval based on zoom level
    double interval;
    if (zoomLevel >= 0.5) {
      // High zoom: show every second
      interval = 1.0;
    } else if (zoomLevel >= 0.2) {
      // Medium zoom: show every 5 seconds
      interval = 5.0;
    } else if (zoomLevel >= 0.1) {
      // Low zoom: show every 10 seconds
      interval = 10.0;
    } else {
      // Very low zoom: show every 30 seconds
      interval = 30.0;
    }

    // Draw time markers
    final pixelsPerSecond = 100.0 * zoomLevel;
    final numMarkers = (duration / interval).ceil();

    for (int i = 0; i <= numMarkers; i++) {
      final time = i * interval;
      if (time > duration) break;

      final x = time * pixelsPerSecond;

      if (!x.isFinite) continue;

      // Draw tick
      canvas.drawLine(
        Offset(x, size.height - 10),
        Offset(x, size.height),
        paint,
      );

      // Draw time label (only if there's enough space)
      if (zoomLevel >= 0.1) {
        final timeText = _formatTime(time);
        textPainter.text = TextSpan(
          text: timeText,
          style: const TextStyle(color: Colors.white70, fontSize: 10),
        );
        textPainter.layout();

        final textX = x - textPainter.width / 2;
        if (textX.isFinite &&
            textX >= 0 &&
            textX + textPainter.width <= size.width) {
          textPainter.paint(canvas, Offset(textX, 0));
        }
      }
    }

    // Draw playhead
    final playheadX = currentPosition * pixelsPerSecond;
    if (playheadX.isFinite && playheadX >= 0 && playheadX <= size.width) {
      final playheadPaint = Paint()
        ..color = Colors.red
        ..strokeWidth = 2;

      canvas.drawLine(
        Offset(playheadX, 0),
        Offset(playheadX, size.height),
        playheadPaint,
      );
    }
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final secs = (seconds % 60).floor();
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class GroupedGridPainter extends CustomPainter {
  final double duration;
  final double zoomLevel;

  GroupedGridPainter({
    required this.duration,
    this.zoomLevel = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (duration <= 0 || size.width <= 0 || size.height <= 0) return;

    final paint = Paint()
      ..color = Colors.white10
      ..strokeWidth = 0.5;

    // Draw horizontal lines for tracks
    for (int i = 1; i < 4; i++) {
      final y = i * (size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // Draw vertical grid lines based on zoom level
    final pixelsPerSecond = 100.0 * zoomLevel;

    // Calculate appropriate grid interval based on zoom level
    double gridInterval;
    if (zoomLevel >= 0.5) {
      gridInterval = 1.0; // Every second
    } else if (zoomLevel >= 0.2) {
      gridInterval = 5.0; // Every 5 seconds
    } else if (zoomLevel >= 0.1) {
      gridInterval = 10.0; // Every 10 seconds
    } else {
      gridInterval = 30.0; // Every 30 seconds
    }

    final numGridLines = (duration / gridInterval).ceil();
    for (int i = 1; i <= numGridLines; i++) {
      final time = i * gridInterval;
      if (time > duration) break;

      final x = time * pixelsPerSecond;
      if (x.isFinite && x <= size.width) {
        canvas.drawLine(
          Offset(x, 0),
          Offset(x, size.height),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
