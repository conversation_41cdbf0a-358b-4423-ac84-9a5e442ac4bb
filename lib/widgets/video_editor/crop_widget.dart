import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/constant/constants.dart';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';

class CropWidget extends StatefulWidget {
  final MediaItem mediaItem;
  final Function(MediaItem) onCropChanged;
  final VoidCallback onCancel;

  const CropWidget({
    super.key,
    required this.mediaItem,
    required this.onCropChanged,
    required this.onCancel,
  });

  @override
  State<CropWidget> createState() => _CropWidgetState();
}

class _CropWidgetState extends State<CropWidget> {
  late double _cropLeft;
  late double _cropTop;
  late double _cropRight;
  late double _cropBottom;
  late double _x;
  late double _y;
  late double _width;
  late double _height;

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    final transform = widget.mediaItem.transformSettings;
    _cropLeft = transform?.cropLeft ?? 0.0;
    _cropTop = transform?.cropTop ?? 0.0;
    _cropRight = transform?.cropRight ?? 0.0;
    _cropBottom = transform?.cropBottom ?? 0.0;

    _x = widget.mediaItem.x ?? 50;
    _y = widget.mediaItem.y ?? 50;
    _width = widget.mediaItem.width ?? 200;
    _height = widget.mediaItem.height ?? 150;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Crop & Resize',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Preview area
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white30),
                ),
                child: Stack(
                  children: [
                    // Preview of the item
                    Center(
                      child: Container(
                        width: 200,
                        height: 150,
                        decoration: BoxDecoration(
                          color: _getItemColor(),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Stack(
                          children: [
                            // Item content
                            _buildItemPreview(),

                            // Crop overlay
                            _buildCropOverlay(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Controls
            Expanded(
              flex: 2,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Position controls
                    const Text(
                      'Position',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),

                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'X: ${_x.round()}',
                                style: const TextStyle(color: Colors.white),
                              ),
                              Slider(
                                value: _x,
                                min: 0,
                                max: 400,
                                activeColor: Color(AppConstants.accentColor),
                                onChanged: (value) {
                                  setState(() {
                                    _x = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Y: ${_y.round()}',
                                style: const TextStyle(color: Colors.white),
                              ),
                              Slider(
                                value: _y,
                                min: 0,
                                max: 400,
                                activeColor: Color(AppConstants.accentColor),
                                onChanged: (value) {
                                  setState(() {
                                    _y = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Size controls
                    const Text(
                      'Size',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),

                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Width: ${_width.round()}',
                                style: const TextStyle(color: Colors.white),
                              ),
                              Slider(
                                value: _width,
                                min: 50,
                                max: 400,
                                activeColor: Color(AppConstants.accentColor),
                                onChanged: (value) {
                                  setState(() {
                                    _width = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Height: ${_height.round()}',
                                style: const TextStyle(color: Colors.white),
                              ),
                              Slider(
                                value: _height,
                                min: 50,
                                max: 400,
                                activeColor: Color(AppConstants.accentColor),
                                onChanged: (value) {
                                  setState(() {
                                    _height = value;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Crop controls
                    const Text(
                      'Crop',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),

                    // Crop sliders
                    _buildCropSlider('Left', _cropLeft, (value) {
                      setState(() {
                        _cropLeft = value;
                      });
                    }),

                    _buildCropSlider('Top', _cropTop, (value) {
                      setState(() {
                        _cropTop = value;
                      });
                    }),

                    _buildCropSlider('Right', _cropRight, (value) {
                      setState(() {
                        _cropRight = value;
                      });
                    }),

                    _buildCropSlider('Bottom', _cropBottom, (value) {
                      setState(() {
                        _cropBottom = value;
                      });
                    }),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _resetValues,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('Reset'),
                ),
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _applyCrop,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCropSlider(
      String label, double value, Function(double) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.round()}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: value,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: onChanged,
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  Widget _buildItemPreview() {
    switch (widget.mediaItem.type) {
      case MediaType.text:
        return Center(
          child: Text(
            widget.mediaItem.text ?? 'Sample Text',
            style: TextStyle(
              color: Color(widget.mediaItem.textColor ?? 0xFFFFFFFF),
              fontSize: (widget.mediaItem.fontSize ?? 24) / 2, // Scaled down
            ),
          ),
        );
      case MediaType.image:
        return Container(
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Center(
            child: Icon(Icons.image, color: Colors.white, size: 40),
          ),
        );
      case MediaType.video:
        return Container(
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Center(
            child: Icon(Icons.videocam, color: Colors.white, size: 40),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildCropOverlay() {
    return Stack(
      children: [
        // Left crop
        if (_cropLeft > 0)
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            width: _cropLeft * 2, // Scale for preview
            child: Container(
              color: Colors.black.withValues(alpha: 0.7),
            ),
          ),

        // Top crop
        if (_cropTop > 0)
          Positioned(
            left: 0,
            top: 0,
            right: 0,
            height: _cropTop * 1.5, // Scale for preview
            child: Container(
              color: Colors.black.withValues(alpha: 0.7),
            ),
          ),

        // Right crop
        if (_cropRight > 0)
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: _cropRight * 2, // Scale for preview
            child: Container(
              color: Colors.black.withValues(alpha: 0.7),
            ),
          ),

        // Bottom crop
        if (_cropBottom > 0)
          Positioned(
            left: 0,
            bottom: 0,
            right: 0,
            height: _cropBottom * 1.5, // Scale for preview
            child: Container(
              color: Colors.black.withValues(alpha: 0.7),
            ),
          ),
      ],
    );
  }

  Color _getItemColor() {
    switch (widget.mediaItem.type) {
      case MediaType.video:
        return Colors.blue;
      case MediaType.audio:
        return Colors.green;
      case MediaType.image:
        return Colors.orange;
      case MediaType.text:
        return Colors.purple;
    }
  }

  void _resetValues() {
    setState(() {
      _cropLeft = 0.0;
      _cropTop = 0.0;
      _cropRight = 0.0;
      _cropBottom = 0.0;
      _x = 50;
      _y = 50;
      _width = 200;
      _height = 150;
    });
  }

  void _applyCrop() {
    final updatedTransform =
        (widget.mediaItem.transformSettings ?? TransformSettings()).copyWith(
      cropLeft: _cropLeft,
      cropTop: _cropTop,
      cropRight: _cropRight,
      cropBottom: _cropBottom,
    );

    final updatedItem = widget.mediaItem.copyWith(
      x: _x,
      y: _y,
      width: _width,
      height: _height,
      transformSettings: updatedTransform,
    );

    widget.onCropChanged(updatedItem);
    widget.onCancel();
  }
}
