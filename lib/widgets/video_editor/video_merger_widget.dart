import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import 'package:mides_skadik/app/data/constant/constants.dart';

class VideoMergerWidget extends StatefulWidget {
  final Function(List<String>) onVideosMerged;
  final VoidCallback onCancel;

  const VideoMergerWidget({
    super.key,
    required this.onVideosMerged,
    required this.onCancel,
  });

  @override
  State<VideoMergerWidget> createState() => _VideoMergerWidgetState();
}

class _VideoMergerWidgetState extends State<VideoMergerWidget> {
  final ImagePicker _imagePicker = ImagePicker();
  List<String> _selectedVideos = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Merge Videos',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Color(AppConstants.backgroundColor),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'How to merge videos:',
                        style: TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. Add 2 or more videos using the + button\n'
                    '2. Reorder videos by dragging\n'
                    '3. Tap "Merge Videos" to combine them\n'
                    '4. Videos will be joined in the order shown',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Add video button
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _addVideo,
              icon: const Icon(Icons.add),
              label: const Text('Add Video'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(AppConstants.accentColor),
                foregroundColor: Colors.black,
              ),
            ),
            const SizedBox(height: 20),

            // Video list
            Expanded(
              child: _selectedVideos.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.video_library_outlined,
                            size: 64,
                            color: Colors.white30,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'No videos selected',
                            style: TextStyle(
                              color: Colors.white30,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Tap "Add Video" to start',
                            style: TextStyle(
                              color: Colors.white30,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ReorderableListView.builder(
                      itemCount: _selectedVideos.length,
                      onReorder: _reorderVideos,
                      itemBuilder: (context, index) {
                        final videoPath = _selectedVideos[index];
                        final fileName = videoPath.split('/').last;

                        return Card(
                          key: ValueKey(videoPath),
                          color: Color(AppConstants.backgroundColor),
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: Container(
                              width: 60,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: const Icon(
                                Icons.videocam,
                                color: Colors.blue,
                                size: 24,
                              ),
                            ),
                            title: Text(
                              fileName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Text(
                              'Video ${index + 1}',
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.drag_handle,
                                  color: Colors.white54,
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () => _removeVideo(index),
                                  icon: const Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _clearAll,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('Clear All'),
                ),
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _selectedVideos.length >= 2 && !_isLoading
                      ? _mergeVideos
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                    foregroundColor: Colors.black,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.black),
                          ),
                        )
                      : const Text('Merge Videos'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addVideo() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final XFile? video = await _imagePicker.pickVideo(
        source: ImageSource.gallery,
      );

      if (video != null) {
        // Check if video is already added
        if (!_selectedVideos.contains(video.path)) {
          setState(() {
            _selectedVideos.add(video.path);
          });
        } else {
          _showSnackBar('Video already added', Colors.orange);
        }
      }
    } catch (e) {
      _showSnackBar('Error selecting video: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _removeVideo(int index) {
    setState(() {
      _selectedVideos.removeAt(index);
    });
  }

  void _reorderVideos(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _selectedVideos.removeAt(oldIndex);
      _selectedVideos.insert(newIndex, item);
    });
  }

  void _clearAll() {
    setState(() {
      _selectedVideos.clear();
    });
  }

  void _mergeVideos() {
    if (_selectedVideos.length < 2) {
      _showSnackBar('Please select at least 2 videos to merge', Colors.red);
      return;
    }

    widget.onVideosMerged(_selectedVideos);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
