import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/constant/constants.dart';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';

class FilterWidget extends StatefulWidget {
  final FilterSettings? currentFilter;
  final Function(FilterSettings?) onFilterChanged;
  final VoidCallback onCancel;

  const FilterWidget({
    super.key,
    this.currentFilter,
    required this.onFilterChanged,
    required this.onCancel,
  });

  @override
  State<FilterWidget> createState() => _FilterWidgetState();
}

class _FilterWidgetState extends State<FilterWidget> {
  late FilterType _selectedFilter;
  late double _intensity;
  late Map<String, double> _parameters;

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.currentFilter?.type ?? FilterType.none;
    _intensity = widget.currentFilter?.intensity ?? 1.0;
    _parameters = Map.from(widget.currentFilter?.parameters ?? {});
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Apply Filter',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Filter selection grid
            const Text(
              'Choose Filter',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),

            Expanded(
              flex: 2,
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 10,
                  childAspectRatio: 1.2,
                ),
                itemCount: FilterType.values.length,
                itemBuilder: (context, index) {
                  final filter = FilterType.values[index];
                  final isSelected = _selectedFilter == filter;

                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedFilter = filter;
                        _initializeFilterParameters(filter);
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Color(AppConstants.accentColor)
                            : Color(AppConstants.backgroundColor),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? Color(AppConstants.accentColor)
                              : Colors.white30,
                          width: 2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getFilterIcon(filter),
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getFilterName(filter),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Filter intensity
            if (_selectedFilter != FilterType.none) ...[
              Text(
                'Intensity: ${(_intensity * 100).round()}%',
                style: const TextStyle(color: Colors.white),
              ),
              Slider(
                value: _intensity,
                min: 0.0,
                max: 2.0,
                divisions: 20,
                activeColor: Color(AppConstants.accentColor),
                onChanged: (value) {
                  setState(() {
                    _intensity = value;
                  });
                },
              ),
              const SizedBox(height: 10),
            ],

            // Additional parameters based on filter type
            if (_selectedFilter != FilterType.none)
              Expanded(
                flex: 1,
                child: _buildFilterParameters(),
              ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    widget.onFilterChanged(null);
                    widget.onCancel();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Remove Filter'),
                ),
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _applyFilter,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterParameters() {
    switch (_selectedFilter) {
      case FilterType.brightness:
        return _buildSliderParameter('Brightness', 'brightness', -1.0, 1.0);
      case FilterType.contrast:
        return _buildSliderParameter('Contrast', 'contrast', -1.0, 1.0);
      case FilterType.saturation:
        return _buildSliderParameter('Saturation', 'saturation', -1.0, 1.0);
      case FilterType.hue:
        return _buildSliderParameter('Hue', 'hue', -180.0, 180.0);
      case FilterType.blur:
        return _buildSliderParameter('Blur Radius', 'radius', 0.0, 10.0);
      case FilterType.sharpen:
        return _buildSliderParameter('Sharpen Amount', 'amount', 0.0, 2.0);
      case FilterType.vignette:
        return Column(
          children: [
            _buildSliderParameter('Vignette Size', 'size', 0.0, 1.0),
            _buildSliderParameter('Vignette Intensity', 'intensity', 0.0, 1.0),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSliderParameter(
      String label, String key, double min, double max) {
    final value = _parameters[key] ?? 0.0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ${value.toStringAsFixed(2)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: value.clamp(min, max),
          min: min,
          max: max,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (newValue) {
            setState(() {
              _parameters[key] = newValue;
            });
          },
        ),
      ],
    );
  }

  void _initializeFilterParameters(FilterType filter) {
    _parameters.clear();
    switch (filter) {
      case FilterType.brightness:
        _parameters['brightness'] = 0.0;
        break;
      case FilterType.contrast:
        _parameters['contrast'] = 0.0;
        break;
      case FilterType.saturation:
        _parameters['saturation'] = 0.0;
        break;
      case FilterType.hue:
        _parameters['hue'] = 0.0;
        break;
      case FilterType.blur:
        _parameters['radius'] = 2.0;
        break;
      case FilterType.sharpen:
        _parameters['amount'] = 1.0;
        break;
      case FilterType.vignette:
        _parameters['size'] = 0.5;
        _parameters['intensity'] = 0.5;
        break;
      default:
        break;
    }
  }

  IconData _getFilterIcon(FilterType filter) {
    switch (filter) {
      case FilterType.none:
        return Icons.filter_none;
      case FilterType.vintage:
        return Icons.filter_vintage;
      case FilterType.blackWhite:
        return Icons.filter_b_and_w;
      case FilterType.sepia:
        return Icons.filter_1;
      case FilterType.blur:
        return Icons.blur_on;
      case FilterType.sharpen:
        return Icons.tune;
      case FilterType.brightness:
        return Icons.brightness_6;
      case FilterType.contrast:
        return Icons.contrast;
      case FilterType.saturation:
        return Icons.palette;
      case FilterType.hue:
        return Icons.color_lens;
      case FilterType.vignette:
        return Icons.vignette;
      case FilterType.warm:
        return Icons.wb_sunny;
      case FilterType.cool:
        return Icons.ac_unit;
      case FilterType.dramatic:
        return Icons.auto_awesome;
    }
  }

  String _getFilterName(FilterType filter) {
    switch (filter) {
      case FilterType.none:
        return 'None';
      case FilterType.vintage:
        return 'Vintage';
      case FilterType.blackWhite:
        return 'B&W';
      case FilterType.sepia:
        return 'Sepia';
      case FilterType.blur:
        return 'Blur';
      case FilterType.sharpen:
        return 'Sharpen';
      case FilterType.brightness:
        return 'Brightness';
      case FilterType.contrast:
        return 'Contrast';
      case FilterType.saturation:
        return 'Saturation';
      case FilterType.hue:
        return 'Hue';
      case FilterType.vignette:
        return 'Vignette';
      case FilterType.warm:
        return 'Warm';
      case FilterType.cool:
        return 'Cool';
      case FilterType.dramatic:
        return 'Dramatic';
    }
  }

  void _applyFilter() {
    if (_selectedFilter == FilterType.none) {
      widget.onFilterChanged(null);
    } else {
      final filterSettings = FilterSettings(
        type: _selectedFilter,
        intensity: _intensity,
        parameters: _parameters,
      );
      widget.onFilterChanged(filterSettings);
    }
    widget.onCancel();
  }
}
