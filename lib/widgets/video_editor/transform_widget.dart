import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/constant/constants.dart';
import 'package:mides_skadik/app/data/models/response/video_editor/video_project.dart';

class TransformWidget extends StatefulWidget {
  final TransformSettings? currentTransform;
  final Function(TransformSettings?) onTransformChanged;
  final VoidCallback onCancel;

  const TransformWidget({
    super.key,
    this.currentTransform,
    required this.onTransformChanged,
    required this.onCancel,
  });

  @override
  State<TransformWidget> createState() => _TransformWidgetState();
}

class _TransformWidgetState extends State<TransformWidget> {
  late double _scaleX;
  late double _scaleY;
  late double _rotation;
  late double _translateX;
  late double _translateY;
  late bool _flipHorizontal;
  late bool _flipVertical;
  late double _cropLeft;
  late double _cropTop;
  late double _cropRight;
  late double _cropBottom;

  int _selectedTab = 0; // 0: Scale/Rotate, 1: Position, 2: Flip, 3: Crop

  @override
  void initState() {
    super.initState();
    _initializeValues();
  }

  void _initializeValues() {
    final transform = widget.currentTransform ?? TransformSettings();
    _scaleX = transform.scaleX;
    _scaleY = transform.scaleY;
    _rotation = transform.rotation;
    _translateX = transform.translateX;
    _translateY = transform.translateY;
    _flipHorizontal = transform.flipHorizontal;
    _flipVertical = transform.flipVertical;
    _cropLeft = transform.cropLeft;
    _cropTop = transform.cropTop;
    _cropRight = transform.cropRight;
    _cropBottom = transform.cropBottom;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Color(AppConstants.surfaceColor),
      child: Container(
        padding: const EdgeInsets.all(20),
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Transform',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: widget.onCancel,
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Tab bar
            Container(
              decoration: BoxDecoration(
                color: Color(AppConstants.backgroundColor),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  _buildTabButton('Scale', 0),
                  _buildTabButton('Position', 1),
                  _buildTabButton('Flip', 2),
                  _buildTabButton('Crop', 3),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Tab content with scroll
            Expanded(
              child: SingleChildScrollView(
                child: _buildTabContent(),
              ),
            ),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _resetTransform,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                  child: const Text('Reset'),
                ),
                ElevatedButton(
                  onPressed: widget.onCancel,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: _applyTransform,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(AppConstants.accentColor),
                  ),
                  child: const Text('Apply'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabButton(String title, int index) {
    final isSelected = _selectedTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = index;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? Color(AppConstants.accentColor)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTab) {
      case 0:
        return _buildScaleRotateTab();
      case 1:
        return _buildPositionTab();
      case 2:
        return _buildFlipTab();
      case 3:
        return _buildCropTab();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildScaleRotateTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Scale X
        Text(
          'Scale X: ${_scaleX.toStringAsFixed(2)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _scaleX,
          min: 0.1,
          max: 3.0,
          divisions: 29,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _scaleX = value;
            });
          },
        ),
        const SizedBox(height: 10),

        // Scale Y
        Text(
          'Scale Y: ${_scaleY.toStringAsFixed(2)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _scaleY,
          min: 0.1,
          max: 3.0,
          divisions: 29,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _scaleY = value;
            });
          },
        ),
        const SizedBox(height: 10),

        // Lock aspect ratio
        Row(
          children: [
            Checkbox(
              value: _scaleX == _scaleY,
              activeColor: Color(AppConstants.accentColor),
              onChanged: (value) {
                if (value == true) {
                  setState(() {
                    _scaleY = _scaleX;
                  });
                }
              },
            ),
            const Text(
              'Lock aspect ratio',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        const SizedBox(height: 20),

        // Rotation
        Text(
          'Rotation: ${_rotation.toStringAsFixed(0)}°',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _rotation,
          min: -180,
          max: 180,
          divisions: 72,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _rotation = value;
            });
          },
        ),

        // Quick rotation buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildQuickRotateButton('-90°', -90),
            _buildQuickRotateButton('0°', 0),
            _buildQuickRotateButton('90°', 90),
            _buildQuickRotateButton('180°', 180),
          ],
        ),
      ],
    );
  }

  Widget _buildPositionTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Translate X
        Text(
          'Position X: ${_translateX.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _translateX,
          min: -200,
          max: 200,
          divisions: 40,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _translateX = value;
            });
          },
        ),
        const SizedBox(height: 20),

        // Translate Y
        Text(
          'Position Y: ${_translateY.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _translateY,
          min: -200,
          max: 200,
          divisions: 40,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _translateY = value;
            });
          },
        ),
        const SizedBox(height: 20),

        // Center button
        Center(
          child: ElevatedButton(
            onPressed: () {
              setState(() {
                _translateX = 0;
                _translateY = 0;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(AppConstants.accentColor),
            ),
            child: const Text('Center'),
          ),
        ),
      ],
    );
  }

  Widget _buildFlipTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Flip Horizontal
        SwitchListTile(
          title: const Text(
            'Flip Horizontal',
            style: TextStyle(color: Colors.white),
          ),
          value: _flipHorizontal,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _flipHorizontal = value;
            });
          },
        ),
        const SizedBox(height: 10),

        // Flip Vertical
        SwitchListTile(
          title: const Text(
            'Flip Vertical',
            style: TextStyle(color: Colors.white),
          ),
          value: _flipVertical,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _flipVertical = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildCropTab() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Crop Left
        Text(
          'Crop Left: ${_cropLeft.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _cropLeft,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _cropLeft = value;
            });
          },
        ),

        // Crop Top
        Text(
          'Crop Top: ${_cropTop.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _cropTop,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _cropTop = value;
            });
          },
        ),

        // Crop Right
        Text(
          'Crop Right: ${_cropRight.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _cropRight,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _cropRight = value;
            });
          },
        ),

        // Crop Bottom
        Text(
          'Crop Bottom: ${_cropBottom.toStringAsFixed(0)}',
          style: const TextStyle(color: Colors.white),
        ),
        Slider(
          value: _cropBottom,
          min: 0,
          max: 100,
          divisions: 20,
          activeColor: Color(AppConstants.accentColor),
          onChanged: (value) {
            setState(() {
              _cropBottom = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildQuickRotateButton(String label, double rotation) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _rotation = rotation;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: _rotation == rotation
            ? Color(AppConstants.accentColor)
            : Colors.grey,
        minimumSize: const Size(60, 36),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  void _resetTransform() {
    setState(() {
      _scaleX = 1.0;
      _scaleY = 1.0;
      _rotation = 0.0;
      _translateX = 0.0;
      _translateY = 0.0;
      _flipHorizontal = false;
      _flipVertical = false;
      _cropLeft = 0.0;
      _cropTop = 0.0;
      _cropRight = 0.0;
      _cropBottom = 0.0;
    });
  }

  void _applyTransform() {
    final transformSettings = TransformSettings(
      scaleX: _scaleX,
      scaleY: _scaleY,
      rotation: _rotation,
      translateX: _translateX,
      translateY: _translateY,
      flipHorizontal: _flipHorizontal,
      flipVertical: _flipVertical,
      cropLeft: _cropLeft,
      cropTop: _cropTop,
      cropRight: _cropRight,
      cropBottom: _cropBottom,
    );

    widget.onTransformChanged(transformSettings);
    widget.onCancel();
  }
}
