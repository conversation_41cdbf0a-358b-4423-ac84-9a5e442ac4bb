import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';

class CustomTextWigdet extends StatelessWidget {
  final String title;
  final TextDecoration? textDecoration;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final TextAlign? textAlign;
  final double? fontSize;
  final Color? textColor;
  final TextOverflow? overflow;
  final int? maxLines;
  final bool? softWrap;

  const CustomTextWigdet({
    super.key,
    required this.title,
    this.textDecoration,
    this.fontWeight,
    this.fontStyle,
    this.textAlign,
    this.fontSize = 24,
    this.textColor,
    this.overflow = TextOverflow.visible,
    this.maxLines,
    this.softWrap = true,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
      style: TextStyle(
        fontFamily: "Inter",
        color: textColor ?? secondWhiteColor,
        fontSize: fontSize?.sp,
        fontStyle: fontStyle ?? FontStyle.normal,
        fontWeight: fontWeight ?? FontWeight.w400,
        decoration: textDecoration ?? TextDecoration.none,
        decorationColor: whiteColor,
      ),
      textAlign: textAlign ?? TextAlign.start,
    );
  }

  const CustomTextWigdet.title(
    String text, {
    Key? key,
    Color? textColor = titleColor,
  }) : this(
          key: key,
          title: text,
          fontSize: 18,
          fontWeight: FontWeight.bold,
          textColor: textColor,
        );

  const CustomTextWigdet.subtitle(
    String text, {
    Key? key,
    Color? textColor = subtitleColor,
  }) : this(
          key: key,
          title: text,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          textColor: textColor,
        );
}
