import 'dart:developer';

import 'package:get/get.dart';

class CourseNotificationController extends GetxController {
  final RxString status = 'closed'.obs;

  void open() {
    status.value = 'open';
  }

  void expand() {
    if (isExpand()) {
      open();
    } else {
      status.value = 'expand';
    }
  }

  void close() {
    status.value = 'closed';
  }

  void toggle() {
    if (status.value == 'closed') {
      open();
    } else {
      close();
    }
  }

  bool isOpen() {
    if (status.value != 'closed') {
      return true;
    }

    return false;
  }

  bool isExpand() {
    return status.value == 'expand';
  }
}
