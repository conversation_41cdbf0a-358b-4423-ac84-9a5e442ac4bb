import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/device_admin_service.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class DeviceAdminWidget extends StatefulWidget {
  const DeviceAdminWidget({super.key});

  @override
  State<DeviceAdminWidget> createState() => _DeviceAdminWidgetState();
}

class _DeviceAdminWidgetState extends State<DeviceAdminWidget> {
  bool isDeviceAdmin = false;
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkDeviceAdminStatus();
  }

  Future<void> _checkDeviceAdminStatus() async {
    setState(() => isLoading = true);
    final status = await DeviceAdminService.isDeviceAdmin();
    setState(() {
      isDeviceAdmin = status;
      isLoading = false;
    });
  }

  Future<void> _requestDeviceAdmin() async {
    setState(() => isLoading = true);
    final success = await DeviceAdminService.requestDeviceAdmin();
    
    if (success) {
      Get.snackbar(
        'Device Administrator',
        'Permintaan aktivasi Device Administrator telah dikirim. Silakan aktifkan di pengaturan.',
        backgroundColor: blueColor,
        colorText: whiteColor,
        snackPosition: SnackPosition.TOP,
      );
      
      // Check status again after a delay
      await Future.delayed(const Duration(seconds: 2));
      await _checkDeviceAdminStatus();
    } else {
      Get.snackbar(
        'Error',
        'Gagal meminta aktivasi Device Administrator',
        backgroundColor: Colors.red,
        colorText: whiteColor,
        snackPosition: SnackPosition.TOP,
      );
    }
    
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      bgColor: baseBlueColor.withOpacity(0.2),
      radius: 12,
      padding: EdgeInsets.all(20.w),
      widget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isDeviceAdmin ? Icons.admin_panel_settings : Icons.security,
                color: isDeviceAdmin ? Colors.green : Colors.orange,
                size: 24.w,
              ),
              12.horizontalSpace,
              Expanded(
                child: CustomTextWigdet(
                  title: 'Device Administrator',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
              ),
              if (isLoading)
                SizedBox(
                  width: 20.w,
                  height: 20.h,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(whiteColor),
                  ),
                ),
            ],
          ),
          16.verticalSpace,
          CustomTextWigdet(
            title: isDeviceAdmin 
                ? 'Aplikasi sudah terdaftar sebagai Device Administrator. Fitur monitoring dan kontrol perangkat aktif.'
                : 'Aplikasi belum terdaftar sebagai Device Administrator. Diperlukan untuk fitur monitoring ujian.',
            fontSize: 14,
            fontWeight: FontWeight.w400,
            textColor: whiteColor.withOpacity(0.7),
            maxLines: 3,
          ),
          16.verticalSpace,
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: isDeviceAdmin ? Colors.green : Colors.orange,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: CustomTextWigdet(
                  title: isDeviceAdmin ? 'AKTIF' : 'TIDAK AKTIF',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
              ),
              const Spacer(),
              if (!isDeviceAdmin)
                CustomFilledButtonWidget(
                  onPressed: isLoading ? null : _requestDeviceAdmin,
                  bgColor: blueColor,
                  title: 'Aktifkan',
                  fontSize: 14,
                  fontColor: whiteColor,
                  widthButton: 100,
                  heightButton: 35,
                  radius: 8,
                ),
              if (isDeviceAdmin)
                CustomFilledButtonWidget(
                  onPressed: _checkDeviceAdminStatus,
                  bgColor: Colors.green,
                  title: 'Refresh',
                  fontSize: 14,
                  fontColor: whiteColor,
                  widthButton: 100,
                  heightButton: 35,
                  radius: 8,
                ),
            ],
          ),
        ],
      ),
    );
  }
}
