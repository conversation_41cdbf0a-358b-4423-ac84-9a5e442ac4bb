import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playback_model.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/history/controllers/history_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';

import '../videos/custom_item_dialog_more.dart';

class HistoryItemWidget extends StatelessWidget {
  final PlaybackModel item;
  final int index;
  final HistoryController controller;

  const HistoryItemWidget(
      {super.key,
      required this.item,
      required this.index,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return CustomVideoItemLarge(
            thumbnailWidth: 480,
            thumbnailHeight: 280,
            thumbnailUrl: item.vod?.thumbnailUrl,
            duration: formatDuration(item.vod?.duration),
            title: item.vod?.title,
            totalViews: item.vod?.totalView,
            uploadDate: timeRange(controller.timeNow.value,
                item.vod?.uploadDate ?? controller.timeNow.value),
            tags: item.vod?.tagNames,
            description: item.vod?.desc,
            imageUrl: item.vod?.uploader?.imageProfile,
            uploaderName: item.vod?.uploader?.pasisName,
            userName: item.vod?.uploader?.username,
            lastDuration: item.lastWatchedTime,
            lastWatch: (convertDurationToSeconds(item.lastWatchedTime!) /
                    convertDurationToSeconds(item.vod!.duration!))
                .clamp(0.0, 1.0),
            onPressMore: () {
              _showMoreDialog();
            });
      },
    );
  }

  void _showMoreDialog() {
    Get.dialog(CustomDialogMore(
      widgets: [
        const CustomItemDialogMore(
          assetName: 'assets/icons/time.svg',
          title: CustomTextWigdet(
            title: 'Tambah ke Tonton Nanti',
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        Divider(color: blackColor),
        const CustomItemDialogMore(
          assetName: 'assets/icons/add_to_playlist.svg',
          title: CustomTextWigdet(
            title: 'Tambah atau Hapus dari Playlist',
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        Divider(color: blackColor),
        const CustomItemDialogMore(
          assetName: 'assets/icons/share.svg',
          title: CustomTextWigdet(
            title: 'Share',
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    ));
  }
}
