import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/upload_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_delete_pop_up.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_comment.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_privacy.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_form.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_item_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/my_video_item.dart';

class MyVodItemGridWidget extends StatelessWidget {
  final VodModel item;
  final int index;
  final MyVideoController controller;

  const MyVodItemGridWidget({
    super.key,
    required this.item,
    required this.index,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        final isOpen = controller.openDetailStatus[index] ?? false;
        return MyVideoItem(
          onTap: () => controller.openContainerDetail(index),
          isOpen: isOpen,
          widthContainer: double.maxFinite,
          statusApprovement: item.status,
          heightAnimatedContainer: context.isPortrait ? 381 : 392,
          statusNote: item.statusNote,
          thumbnailUrl: item.thumbnailUrl,
          duration: formatDuration(item.duration),
          title: item.title,
          totalViews: item.totalView,
          uploadDate: timeRange(
            controller.timeNow.value,
            item.uploadDate ?? controller.timeNow.value,
          ),
          tags: item.tagNames,
          onPressEdit: () {
            Get.to(
                () => UploadVideoView(
                      title: 'Edit Detail Video',
                      idVod: item.id,
                      titleVideo: item.title,
                      descriptionVideo: item.desc,
                      thumbnailUrl: item.thumbnailUrl,
                      privasi: item.visibility,
                      isCommentEnable: item.isCommentEnabled,
                      categories: item.tagId,
                      isEdit: true,
                    ),
                binding: UploadVideoBinding(),
                arguments: {
                  "videoUrl": item.videoUrl,
                  "videoId": item.id,
                  "videoSourceUrl": item.videoSourceUrl,
                  "videoTitle": item.title,
                  "videoTags": item.tagNames,
                });
          },
          onPressMore: () {
            _showMoreDialog(item);
          },
        );
      },
    );
  }

  void _showMoreDialog(VodModel item) {
    final uploadController = Get.put(UploadVideoController());

    Get.dialog(
      CustomDialogMore(
        widgets: [
          const CustomItemDialogMore(
            assetName: 'assets/icons/add_to_playlist.svg',
            title: CustomTextWigdet(
              title: 'Tambah atau Hapus dari Playlist',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(color: blackColor),
          CustomItemDialogMore(
            onTap: () {
              Get.back();
              Future.delayed(const Duration(milliseconds: 90), () {
                uploadController.privacy.value = item.visibility ?? '';
                uploadController.comment.value =
                    (item.isCommentEnabled ?? false) ? 'Aktif' : 'Tidak Aktif';

                Get.dialog(
                  barrierDismissible: false,
                  barrierColor: blackColor.withOpacity(0.3),
                  CustomDialogForm(
                    title: 'Privasi & Komen',
                    widgets: [
                      const Divider(),
                      const CustomUploadPrivacyWidget(),
                      const CustomUploadComment(),
                      Row(
                        children: [
                          Expanded(
                            child: Material(
                              color: Colors.transparent,
                              child: Obx(() => CustomFilledButtonWidget(
                                    onPressed: controller.isLoadingUpdate.value
                                        ? null
                                        : () {
                                            Get.back();
                                          },
                                    title: 'Batal',
                                    radius: 8,
                                    bgColor: secondWhiteColor,
                                  )),
                            ),
                          ),
                          16.horizontalSpace,
                          Obx(
                            () => Expanded(
                              child: controller.isLoadingUpdate.value
                                  ? const CustomLoadingWidget()
                                  : Material(
                                      color: Colors.transparent,
                                      child: CustomFilledButtonWidget(
                                        onPressed: () {
                                          final isCommentEnabled =
                                              uploadController.comment.value ==
                                                  'Aktif';
                                          controller.updatePrivacyAndComment(
                                            item.id!,
                                            uploadController.privacy.value,
                                            isCommentEnabled,
                                          );
                                        },
                                        title: 'Simpan',
                                        radius: 8,
                                        bgColor: blueColor,
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                );
              });
            },
            assetName: 'assets/icons/settings.svg',
            title: const CustomTextWigdet(
              title: 'Privasi & Komen',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(color: blackColor),
          CustomItemDialogMore(
            assetName: 'assets/icons/delete_outline.svg',
            title: CustomTextWigdet(
              title: 'Hapus Video',
              fontSize: 20,
              fontWeight: FontWeight.w500,
              textColor: redColor,
            ),
            onTap: () {
              Get.back();
              Get.dialog(
                MyVideoDeletePopUp(
                  thumbnailUrl: item.thumbnailUrl,
                  duration: item.duration,
                  title: item.title,
                  uploaderName: item.uploader!.username,
                  uploadDate: timeRange(controller.timeNow.value,
                      item.uploadDate ?? controller.timeNow.value),
                  totalViews: item.totalView,
                  tags: item.tagNames,
                  widget: Material(
                    color: Colors.transparent,
                    child: Row(
                      spacing: 16.w,
                      children: [
                        Obx(() => Expanded(
                              child: CustomFilledButtonWidget(
                                bgColor: whiteColor,
                                radius: 8,
                                heightButton: 64,
                                fontSize: 28,
                                onPressed: controller.isLoadingUpdate.value
                                    ? null
                                    : () {
                                        Get.back();
                                      },
                                title: 'Cancel',
                              ),
                            )),
                        Obx(
                          () => Expanded(
                            child: controller.isLoadingDelete.value
                                ? const CustomLoadingWidget()
                                : CustomFilledButtonWidget(
                                    bgColor: redColor,
                                    radius: 8,
                                    heightButton: 64,
                                    fontSize: 28,
                                    fontColor: whiteColor,
                                    fontWeight: FontWeight.w700,
                                    onPressed: () {
                                      controller
                                          .deleteMyVideoByID(item.id ?? '');
                                    },
                                    title: 'Delete',
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () {
                    Get.to(VideoPlayerView(),
                        binding: VideoPlayerBinding(),
                        arguments: {
                          "videoUrl": item.videoUrl,
                          "videoId": item.id,
                          "totalLike": item.totalLike,
                          "totalDislike": item.totalDislike,
                          "totalRating": item.totalRating,
                          "totalView": item.totalView,
                          "videoSourceUrl": item.videoSourceUrl,
                          "videoTitle": item.title,
                          "videoTags": item.tagNames,
                        });
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
