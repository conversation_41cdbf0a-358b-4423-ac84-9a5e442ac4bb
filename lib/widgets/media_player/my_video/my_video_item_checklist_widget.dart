import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/my_video/controllers/my_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/add_video_playlist_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/bindings/upload_video_binding.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/views/upload_video_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_delete_pop_up.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_comment.dart';
import 'package:mides_skadik/widgets/media_player/upload_video/custom_upload_privacy.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_form.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_item_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';

class MyVodItemChecklistWidget extends StatelessWidget {
  final VodModel item;
  final int index;
  final AddVideoPlaylistController controller;

  const MyVodItemChecklistWidget({
    super.key,
    required this.item,
    required this.index,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return GestureDetector(
          onTap: () {
            controller.toggleSelection(item.id!);
          },
          child: CustomContainer(
            radius: 16.r,
            bgColor:
                controller.isSelected(item.id!) ? greyColor : darkGreyColor,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            widget: Row(
              children: [
                CustomContainer(
                  bgColor: blackColor,
                  radius: 16.r,
                  widget: Image.network(
                    item.thumbnailUrl ?? '',
                    width: 100,
                    height: 9 / 16 * 100,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.error);
                    },
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: item.title ?? '',
                      fontSize: 40.sp,
                    ),
                    CustomTextWigdet(
                      title: item.desc ?? '',
                      fontSize: 24.sp,
                    ),
                  ],
                )),
                Checkbox(
                  value: controller.isSelected(item.id!),
                  onChanged: (value) {
                    controller.toggleSelection(item.id!);
                  },
                  fillColor: MaterialStateProperty.all(darkBlueColor),
                  checkColor: Colors.white,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
