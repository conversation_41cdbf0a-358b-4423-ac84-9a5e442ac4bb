import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';

class MyVideoDeleteItemDetail extends StatelessWidget {
  final String? thumbnailUrl;
  final String? duration;
  final String? title;
  final String? uploaderName;
  final String? uploadDate;
  final int? totalViews;
  final List<String>? tags;
  final void Function()? onTap;
  const MyVideoDeleteItemDetail({
    super.key,
    this.thumbnailUrl = '',
    this.duration,
    this.title,
    this.uploaderName,
    this.uploadDate,
    this.totalViews,
    this.tags,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 144.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: 12,
        children: [
          CustomContainer(
            bgColor: baseBlueColor,
            width: 256,
            radius: 8,
            widget: Stack(
              children: [
                ClipRRect(
                  child: thumbnailUrl!.isEmpty
                      ? Image.asset(
                          'assets/images/airplane.png',
                          fit: BoxFit.cover,
                          height: 144.h,
                          width: double.maxFinite,
                        )
                      : CachedNetworkImage(
                          imageUrl: thumbnailUrl!,
                          height: 144.h,
                          fit: BoxFit.cover,
                          width: double.maxFinite,
                          placeholder: (context, url) {
                            return const CustomLoadingWidget();
                          },
                          errorWidget: (context, url, error) {
                            return const Icon(Icons.error);
                          },
                        ),
                ),
                Positioned(
                  bottom: 10,
                  right: 10,
                  child: CustomCountDuration(duration: duration ?? '00:00'),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 8.h,
            children: [
              CustomTextWigdet(
                title: title ?? 'Judul Video',
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              CustomTextWigdet(
                title: uploaderName ?? 'Uploader Name',
                fontSize: 16,
              ),
              CustomTextWigdet(
                title:
                    '${totalViews ?? '0'} views • ${uploadDate ?? 'Hari ini'}',
                fontSize: 16,
              ),
              const Spacer(),
              Row(
                spacing: 4.w,
                children: [
                  for (var tag in tags ?? ['No Tag']) ...[
                    CustomTag(
                      tag: tag,
                      fontSize: 14,
                    )
                  ]
                ],
              )
            ],
          ),
          const Spacer(),
          GestureDetector(
            onTap: onTap,
            child: CustomContainer(
              width: 80,
              bgColor: blackColor,
              widget: Center(
                child: SvgPicture.asset('assets/icons/chevron.svg'),
              ),
            ),
          )
        ],
      ),
    );
  }
}
