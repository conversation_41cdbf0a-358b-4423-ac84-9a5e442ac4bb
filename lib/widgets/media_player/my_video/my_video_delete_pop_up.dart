import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/my_video/my_video_delete_item_detail.dart';

class MyVideoDeletePopUp extends StatelessWidget {
  final String? thumbnailUrl;
  final String? duration;
  final String? title;
  final String? uploaderName;
  final String? uploadDate;
  final int? totalViews;
  final Widget? widget;
  final List<String>? tags;
  final void Function()? onTap;
  const MyVideoDeletePopUp({
    super.key,
    this.thumbnailUrl = '',
    this.duration,
    this.title,
    this.uploaderName,
    this.uploadDate,
    this.totalViews,
    this.widget,
    this.tags,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CustomContainer(
        width: 960,
        height: 500,
        radius: 10,
        gradient: const LinearGradient(
            colors: [Color(0xFF112941), Color(0xFF0D1319)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            stops: [.1, .5]),
        widget: Padding(
          padding: EdgeInsets.all(48.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                spacing: 12.w,
                children: [
                  SvgPicture.asset(
                    'assets/icons/delete_card.svg',
                    height: 96.h,
                  ),
                  Column(
                    spacing: 12.h,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      CustomTextWigdet(
                        title: 'Menghapus Video Secara Permanen?',
                        fontSize: 32,
                        fontWeight: FontWeight.w600,
                      ),
                      CustomTextWigdet(
                        title:
                            'Video akan diarsipkan dan dihapus dari pemutar media',
                        fontSize: 24,
                      ),
                    ],
                  ),
                ],
              ),
              MyVideoDeleteItemDetail(
                thumbnailUrl: thumbnailUrl,
                duration: formatDuration(duration),
                title: title,
                uploaderName: uploaderName,
                uploadDate: uploadDate,
                totalViews: totalViews,
                tags: tags,
                onTap: onTap,
              ),
              widget ?? const SizedBox.shrink(),
            ],
          ),
        ),
      ),
    );
  }
}
