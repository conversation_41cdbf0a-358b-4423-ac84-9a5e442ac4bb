import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomHistorySearch extends StatelessWidget {
  final String? item;
  final String? assetName;
  final double? widthIconSuffix;
  final double? heightIconSuffix;
  final void Function()? onTap;
  const CustomHistorySearch({
    super.key,
    this.item,
    this.assetName,
    this.widthIconSuffix,
    this.heightIconSuffix,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        width: double.maxFinite,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              SvgPicture.asset(
                assetName ?? 'assets/icons/history.svg',
                width: widthIconSuffix?.w ?? 41.w,
                height: heightIconSuffix?.h ?? 41.h,
              ),
              28.horizontalSpace,
              CustomTextWigdet(
                title: item ?? '-',
                fontSize: 24,
                fontWeight: FontWeight.w400,
              ),
              const Spacer(),
              // SvgPicture.asset(
              //   'assets/icons/arrow_autofill.svg',
              //   width: 37.w,
              //   height: 37.h,
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
