import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/bindings/playlist_binding.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/views/playlist_view.dart';
import 'package:mides_skadik/widgets/components/custom_playlists.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/custom_data_empty.dart';

class CustomPlaylistGrid extends StatelessWidget {
  final bool? isScrollable;
  const CustomPlaylistGrid({super.key, this.isScrollable});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PlaylistController());
    final orientation = MediaQuery.of(Get.context!).orientation;

    return Obx(
      () {
        if (controller.playlists.isEmpty) {
          return const Center(
              child: CustomDataEmpty(
                  title: 'Tidak ada data riwayat.',
                  assetName: 'assets/icons/no_data_video.svg'));
        }

        if (controller.isLoading.value) {
          return const Center(
            child: CustomLoadingWidget(),
          );
        }

        return GridView.builder(
          shrinkWrap: isScrollable == true ? false : true,
          physics: isScrollable ?? false
              ? const NeverScrollableScrollPhysics()
              : null,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio:
                orientation == Orientation.portrait ? 14 / 9 : 18 / 14,
            crossAxisSpacing: 24.w,
            mainAxisSpacing: 24.h,
          ),
          itemCount:
              controller.isLoading.value ? 6 : controller.playlists.length,
          itemBuilder: (it, idx) {
            var row = controller.playlists.isNotEmpty
                ? controller.playlists[idx]
                : null;
            return CustomPlaylists(
              playlist: row,
              isLoading: controller.isLoading.value,
              onTap: () {
                // Get.toNamed('/playlist');

                if (row == null) return;

                Get.to(PlaylistView(playlist: row), binding: PlaylistBinding());
              },
            );
          },
        );
      },
    );
  }
}
