import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_text.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_switch_button.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';

class CustomUploadPrivacyWidget extends StatefulWidget {
  final String? privasi;
  const CustomUploadPrivacyWidget({Key? key, this.privasi}) : super(key: key);

  @override
  State<CustomUploadPrivacyWidget> createState() =>
      _CustomUploadPrivacyWidgetState();
}

class _CustomUploadPrivacyWidgetState extends State<CustomUploadPrivacyWidget> {
  final controller = Get.put(UploadVideoController());

  @override
  Widget build(BuildContext context) {
    if (widget.privasi != null && widget.privasi!.isNotEmpty) {
      controller.privacy.value = widget.privasi!;
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const CustomTextWigdet(
          title: 'Privasi',
          fontSize: 24,
          fontWeight: FontWeight.w500,
        ),
        CustomContainer(
          bgColor: secondBlueColor.withOpacity(0.1),
          radius: 8,
          height: 65,
          widget: Obx(
            () => CustomSwitchButton(
              children: [
                CustomButtonText(
                  title: 'Publik',
                  fontSize: 18,
                  isSelected: controller.privacy.value == 'public',
                  onTap: () {
                    controller.privacy.value = 'public';
                  },
                ),
                4.horizontalSpace,
                CustomButtonText(
                  title: 'Pribadi',
                  fontSize: 18,
                  isSelected: controller.privacy.value == 'private',
                  onTap: () {
                    controller.privacy.value = "private";
                  },
                ),
                4.horizontalSpace,
                CustomButtonText(
                  title: 'Terbatas',
                  fontSize: 18,
                  isSelected: controller.privacy.value == "restricted",
                  onTap: () {
                    controller.privacy.value = "restricted";
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
