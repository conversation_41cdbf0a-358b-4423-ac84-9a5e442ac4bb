import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_list.dart';

class CustomUploadCategoryWidget extends StatefulWidget {
  final List<String>? selectedTags;
  const CustomUploadCategoryWidget({Key? key, this.selectedTags})
      : super(key: key);

  @override
  State<CustomUploadCategoryWidget> createState() =>
      _CustomUploadCategoryWidgetState();
}

class _CustomUploadCategoryWidgetState
    extends State<CustomUploadCategoryWidget> {
  final controller = Get.find<UploadVideoController>();

  @override
  void initState() {
    super.initState();

    if (!controller.hasInitialize.value) {
      controller.selectedTags.clear();
      if (widget.selectedTags != null && widget.selectedTags!.isNotEmpty) {
        controller.selectedTags.addAll(widget.selectedTags!.map((e) => e));
      }
      controller.hasInitialize.value = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        final loading = controller.isLoadingCategory.value;
        final data = controller.categories;

        return CustomContainerList(
          title: 'Kategori',
          withButton: false,
          height: context.isPortrait ? 300 : 310,
          fontSize: 24,
          widget: SizedBox(
            height: 200.h,
            width: Get.width,
            child: loading
                ? const CustomLoadingWidget()
                : (!loading && data.isEmpty)
                    ? const CustomTextWigdet(title: 'Category not Found')
                    : SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16.w, vertical: 16.h),
                          child: Wrap(
                            alignment: WrapAlignment.start,
                            spacing: 10.w,
                            runSpacing: 15.h,
                            children: [
                              ...controller.categories.map(
                                (e) {
                                  return GestureDetector(
                                    onTap: () =>
                                        controller.selectTags(e.id ?? ""),
                                    child: CustomContainer(
                                      bgColor:
                                          controller.selectedTags.contains(e.id)
                                              ? blueColor
                                              : greyColor,
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.w, vertical: 8.h),
                                      radius: 8.r,
                                      widget: CustomTextWigdet(
                                        title: e.name ?? "",
                                        fontSize: 20,
                                      ),
                                    ),
                                  );
                                },
                              ).toList(),
                            ],
                          ),
                        ),
                      ),
          ),
        );
      },
    );
  }
}
