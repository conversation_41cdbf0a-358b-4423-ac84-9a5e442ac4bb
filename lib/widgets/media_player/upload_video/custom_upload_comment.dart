import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_text.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_switch_button.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomUploadComment extends StatelessWidget {
  const CustomUploadComment({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(UploadVideoController());
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const CustomTextWigdet(
          title: 'Komentar',
          fontSize: 24,
          fontWeight: FontWeight.w500,
        ),
        CustomContainer(
          radius: 8,
          bgColor: secondBlueColor.withOpacity(0.1),
          height: 65,
          widget: Obx(
            () => CustomSwitchButton(
              children: [
                CustomButtonText(
                  title: 'Aktif',
                  fontSize: 18,
                  isSelected: controller.comment.value == "Aktif",
                  onTap: () {
                    controller.comment.value = 'Aktif';
                  },
                ),
                4.horizontalSpace,
                CustomButtonText(
                  title: 'Tidak Aktif',
                  fontSize: 18,
                  isSelected: controller.comment.value == "Tidak Aktif",
                  onTap: () {
                    controller.comment.value = "Tidak Aktif";
                  },
                ),
              ],
            ),
          ),
        )
      ],
    );
  }
}
