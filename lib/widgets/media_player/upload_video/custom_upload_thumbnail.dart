import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomUploadThumbnailWidget extends StatefulWidget {
  final String? thumbnailUrl;
  const CustomUploadThumbnailWidget({Key? key, this.thumbnailUrl = ''})
      : super(key: key);

  @override
  State<CustomUploadThumbnailWidget> createState() =>
      _CustomUploadThumbnailWidgetState();
}

class _CustomUploadThumbnailWidgetState
    extends State<CustomUploadThumbnailWidget> {
  final controller = Get.find<UploadVideoController>();

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 16 / 9,
      child: Obx(
        () {
          if (controller.selectedThumbnailPath.value.isNotEmpty) {
            return CustomContainer(
              width: double.infinity,
              height: 335,
              bgColor: secondBlueColor.withOpacity(0.15),
              radius: 10,
              widget: Image.file(
                File(controller.selectedThumbnailPath.value),
                errorBuilder: (context, error, stackTrace) {
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/cloud_upload.svg',
                        width: 72.w,
                        height: 72.h,
                        fit: BoxFit.cover,
                      ),
                      8.verticalSpace,
                      CustomTextWigdet(
                        title: 'Unggah gambar mini khusus',
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      8.verticalSpace,
                      const CustomTextWigdet(
                        title: 'Format gambar: ... (2MB)',
                        fontSize: 24,
                        fontWeight: FontWeight.w300,
                      ),
                    ],
                  );
                },
              ),
            );
          }
          return widget.thumbnailUrl == null || widget.thumbnailUrl!.isEmpty
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/cloud_upload.svg',
                      width: 72.w,
                      height: 72.h,
                      fit: BoxFit.cover,
                    ),
                    8.verticalSpace,
                    CustomTextWigdet(
                      title: 'Unggah gambar mini khusus',
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w500,
                    ),
                    8.verticalSpace,
                    const CustomTextWigdet(
                      title: 'Format gambar: ... (2MB)',
                      fontSize: 24,
                      fontWeight: FontWeight.w300,
                    ),
                  ],
                )
              : CachedNetworkImage(
                  imageUrl: widget.thumbnailUrl ?? '',
                  width: double.maxFinite,
                  height: double.maxFinite,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => const CustomLoadingWidget(),
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                );
        },
      ),
    );
  }
}
