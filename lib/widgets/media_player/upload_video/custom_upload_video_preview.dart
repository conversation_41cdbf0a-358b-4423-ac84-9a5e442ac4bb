import 'package:chewie/chewie.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/video_util.dart';
import 'package:mides_skadik/app/modules/media_player/upload_video/controllers/upload_video_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/controllers/video_player_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:video_player/video_player.dart';

class CustomUploadVideoPreviewWidget extends GetView<UploadVideoController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var isInit = controller.videoPlayerController.value != null &&
          controller.videoPlayerController.value!.value.isInitialized;
      final isEdit = controller.isEdit;

      var isPlaying = controller.isPlaying.value == true;

      return isEdit
          ? AspectRatio(
              aspectRatio: 16 / 9,
              child: Obx(
                () {
                  final videoPlayerController = Get.put(VideoController());

                  if (!videoPlayerController.isInitialized.value) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  return Chewie(
                    controller: videoPlayerController.chewieController,
                  );
                },
              ),
            )
          : Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 10,
                  ),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: CustomContainer(
                      width: double.infinity,
                      radius: isInit ? 0 : 12,
                      bgColor: secondBlueColor.withOpacity(0.15),
                      widget: isInit
                          ? Stack(
                              children: [
                                VideoPlayer(
                                    controller.videoPlayerController.value!),
                                Center(
                                  child: InkWell(
                                    onTap: controller.playPause,
                                    child: CustomContainer(
                                      radius: 8,
                                      bgColor: darkGreyColor.withOpacity(0.4),
                                      padding:
                                          const EdgeInsets.fromLTRB(8, 8, 8, 8),
                                      widget: isPlaying
                                          ? Icon(
                                              Icons.pause,
                                              color: whiteColor,
                                            )
                                          : Icon(
                                              Icons.play_arrow,
                                              color: whiteColor,
                                            ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/cloud_upload.svg',
                                  width: 72.w,
                                  height: 72.h,
                                  fit: BoxFit.cover,
                                ),
                                8.verticalSpace,
                                CustomTextWigdet(
                                  title: 'Unggah Video',
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                                8.verticalSpace,
                                const CustomTextWigdet(
                                  title: 'Format video: ... (500MB)',
                                  fontSize: 24,
                                  fontWeight: FontWeight.w300,
                                ),
                              ],
                            ),
                    ),
                  ),
                ),
                if (isInit)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _duration(controller.currentPosition.value),
                              _duration(controller
                                  .videoPlayerController.value!.value.duration),
                            ],
                          ),
                        ),
                        MaterialApp(
                          debugShowCheckedModeBanner: false,
                          theme: ThemeData(
                            sliderTheme: SliderThemeData(
                              trackHeight: 5,
                              thumbColor: blueColor,
                              activeTrackColor: blueColor,
                              inactiveTrackColor: secondGreyColor,
                              thumbShape: const RoundSliderThumbShape(
                                  enabledThumbRadius: 8),
                              trackShape: const RectangularSliderTrackShape(),
                              overlayShape: const RoundSliderOverlayShape(
                                  overlayRadius: 0),
                            ),
                          ),
                          home: Slider(
                            thumbColor: blueColor,
                            activeColor: blueColor,
                            value: controller.videoPlayerController.value!.value
                                .position.inSeconds
                                .toDouble()
                                .clamp(
                                    0,
                                    controller.videoPlayerController.value!
                                        .value.duration.inSeconds
                                        .toDouble()),
                            max: controller.videoPlayerController.value!.value
                                .duration.inSeconds
                                .toDouble(),
                            onChangeStart: (value) {
                              controller.wasPlayingBeforeDrag.value = controller
                                  .videoPlayerController.value!.value.isPlaying;
                              controller.videoPlayerController.value!.pause();
                              controller.isDragging.value = true;
                            },
                            onChanged: (value) {
                              controller.currentPosition.value =
                                  Duration(seconds: value.toInt());

                              final newDuration =
                                  Duration(seconds: value.toInt());
                              controller.videoPlayerController.value!
                                  .seekTo(newDuration);
                            },
                            onChangeEnd: (value) {
                              final newDuration =
                                  Duration(seconds: value.toInt());
                              controller.videoPlayerController.value!
                                  .seekTo(newDuration)
                                  .then((_) {
                                if (controller.wasPlayingBeforeDrag.value) {
                                  controller.videoPlayerController.value!
                                      .play();
                                }
                                controller.isDragging.value = false;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            );
    });
  }

  Widget _duration(Duration duration) {
    var dur = VideoUtil.formatDuration(duration);
    return CustomContainer(
      radius: 8,
      bgColor: darkBlueColor.withOpacity(0.2),
      padding: const EdgeInsets.fromLTRB(4, 4, 4, 4),
      widget: CustomTextWigdet(
        title: dur,
        fontSize: 20.sp,
      ),
    );
  }
}
