import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomDataEmpty extends StatelessWidget {
  final String title;
  final String assetName;
  const CustomDataEmpty(
      {super.key, required this.title, required this.assetName});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 8,
      children: [
        SvgPicture.asset(
          assetName,
          width: 48.w,
          height: 48.h,
          color: secondWhiteColor.withValues(alpha: 0.5),
        ),
        CustomTextWigdet(
          title: title,
          textColor: secondWhiteColor.withValues(alpha: 0.5),
          fontSize: 20,
          fontWeight: FontWeight.w600,
        )
      ],
    ));
  }
}
