import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_user_info.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomVideoItemLarge extends StatelessWidget {
  final String? thumbnailUrl;
  final String? imageUrl;
  final String? duration;
  final String? title;
  final Widget? customtextwigdet;
  final String? uploaderName;
  final String? userName;
  final String? viewsAndDate;
  final String? description;
  final String? statusNote;
  final int? totalViews;
  final String? uploadDate;
  final List<String>? tags;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final double? thumbnailWidth;
  final double? thumbnailHeight;
  final double? heightAnimatedContainer;
  final bool? isApprovement;
  final String? statusApprovement;
  final String? lastDuration;
  final double? lastWatch;
  final bool? isOpen;
  final bool? showMenuIcon;
  final void Function()? onTapRejectedDetail;
  final void Function()? onTapPlay;
  final void Function()? onPressMore;
  final void Function()? onPressEdit;

  const CustomVideoItemLarge({
    super.key,
    this.thumbnailUrl = '',
    this.imageUrl,
    this.duration,
    this.title,
    this.customtextwigdet,
    this.uploaderName,
    this.userName,
    this.description,
    this.viewsAndDate,
    this.totalViews,
    this.uploadDate,
    this.tags,
    this.padding,
    this.width,
    this.height,
    this.thumbnailWidth,
    this.thumbnailHeight,
    this.isApprovement,
    this.statusApprovement,
    this.isOpen,
    this.showMenuIcon = false,
    this.onTapRejectedDetail,
    this.onTapPlay,
    this.heightAnimatedContainer,
    this.lastDuration = '',
    this.lastWatch,
    this.statusNote,
    this.onPressMore,
    this.onPressEdit,
  });

  Widget _statusApprove(String status) {
    final statusText = status.toLowerCase();

    switch (statusText) {
      case 'approved':
        return CustomContainer(
          bgColor: blueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/circle_check.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      case 'waiting':
        return CustomContainer(
          bgColor: baseBlueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/time.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      case 'rejected':
        return CustomContainer(
          bgColor: redColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/warning.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      default:
        return CustomContainer(
          bgColor: baseBlueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/warning.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                const CustomTextWigdet(
                  title: 'UNKNOWN',
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapPlay,
      child: CustomContainer(
        height: height ?? thumbnailHeight,
        bgColor: secondBlueColor.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12.r),
        widget: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (showMenuIcon ?? false)
              Align(
                alignment: Alignment.center,
                child: GestureDetector(
                  onTap: () {},
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: SvgPicture.asset(
                      "assets/icons/icon_drag_handle.svg",
                      width: 32.w,
                      height: 48.h,
                    ),
                  ),
                ),
              ),
            CustomContainer(
              width: thumbnailWidth ?? 255,
              height: thumbnailHeight ?? height,
              bgColor: baseBlueColor,
              widget: Stack(
                alignment: Alignment.centerLeft,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10.r),
                      bottomLeft: Radius.circular(10.r),
                    ),
                    child: thumbnailUrl!.isEmpty
                        ? Image.asset(
                            'assets/images/airplane.png',
                            fit: BoxFit.cover,
                            width: thumbnailWidth ?? 255,
                            height: thumbnailHeight ?? height,
                          )
                        : CachedNetworkImage(
                            imageUrl: thumbnailUrl!,
                            width: thumbnailWidth ?? 255,
                            height: thumbnailHeight ?? height,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CustomLoadingWidget(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                  ),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: CustomCountDuration(
                      duration: duration ?? '00:00',
                      padding: EdgeInsetsDirectional.symmetric(
                          horizontal: 12.w, vertical: 12.h),
                    ),
                  ),
                  lastDuration!.isNotEmpty
                      ? Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            color: darkBlueColor.withValues(alpha: .4),
                            width: double.maxFinite,
                            height: 6.h,
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: lastWatch,
                              child: Container(
                                height: 6.h,
                                color: blueColor,
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
            Flexible(
              child: SizedBox(
                height: height,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    isApprovement ?? false
                        ? CustomContainer(
                            height: 80,
                            bgColor: secondBlueColor.withOpacity(0.15),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 16.w, vertical: 16.h),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    onTap: statusApprovement!.toLowerCase() ==
                                            'rejected'
                                        ? onTapRejectedDetail
                                        : null,
                                    child: Row(
                                      children: [
                                        isOpen ?? false
                                            ? Transform.rotate(
                                                angle: 90 * pi / 180,
                                                child: SvgPicture.asset(
                                                  'assets/icons/chevron.svg',
                                                  width: 24,
                                                ),
                                              )
                                            : SvgPicture.asset(
                                                'assets/icons/chevron.svg',
                                                width: 24,
                                              ),
                                        _statusApprove(statusApprovement ?? '')
                                      ],
                                    ),
                                  ),
                                  statusApprovement!.toLowerCase() == 'approved'
                                      ? const SizedBox.shrink()
                                      : CustomFilledButtonWidget(
                                          onPressed: onPressEdit,
                                          withIcon: true,
                                          assetName: 'assets/icons/pencil.svg',
                                          title: 'Edit',
                                          radius: 10,
                                          fontSize: 18,
                                          fontWeight: FontWeight.w700,
                                          fontColor: whiteColor,
                                          bgColor:
                                              baseBlueColor.withOpacity(0.15),
                                          widthIcon: 35,
                                          heightIcon: 35,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 16.w),
                                        ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                    Stack(
                      children: [
                        CustomContainer(
                          widget: Padding(
                            padding: EdgeInsets.only(
                                left: 16.w, top: 16.h, right: 16.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        ConstrainedBox(
                                          constraints: const BoxConstraints(
                                              maxWidth: 450),
                                          child: CustomTextWigdet(
                                            title: title ?? "Judul Video",
                                            fontSize: 24,
                                            fontWeight: FontWeight.w500,
                                            textColor: whiteColor,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                        CustomTextWigdet(
                                          title:
                                              "${totalViews ?? 0} penonton • $uploadDate",
                                          fontSize: 16,
                                          fontWeight: FontWeight.w300,
                                          textColor: secondWhiteColor,
                                        ),
                                      ],
                                    ),
                                    GestureDetector(
                                      onTap: onPressMore,
                                      child: Icon(
                                        Icons.more_vert_rounded,
                                        color: whiteColor,
                                      ),
                                    )
                                  ],
                                ),
                                16.verticalSpace,
                                Row(
                                  children: [
                                    for (var item in tags ?? ['No Tags']) ...[
                                      CustomTag(
                                          tag: item, fontSize: 14, vertical: 4),
                                      8.horizontalSpace,
                                    ]
                                  ],
                                ),
                                16.verticalSpace,
                                CustomTextWigdet(
                                  title: description ??
                                      "Dalam video kali ini, kami akan membahas tentang teknologi terbaru dalam dunia penerbangan militer. Dari inovasi pesawat tempur hingga sistem pertahanan canggih, kami akan mengeksplorasi berbagai aspek yang membuat Angkatan Udara semakin modern dan efisien.",
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                  textColor: secondWhiteColor,
                                  maxLines: 2,
                                  softWrap: true,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.left,
                                ),
                              ],
                            ),
                          ),
                        ),
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          height: isOpen ?? false
                              ? (heightAnimatedContainer?.h ?? 220.h)
                              : 0,
                          clipBehavior: Clip.hardEdge,
                          curve: Curves.easeIn,
                          decoration: BoxDecoration(
                            color: baseBlueColor,
                            borderRadius: BorderRadius.only(
                              bottomRight: Radius.circular(12.r),
                            ),
                          ),
                          child: SizedBox(
                            width: double.infinity,
                            child: Padding(
                              padding: EdgeInsets.only(
                                  left: 16.w, right: 16.w, bottom: 16.h),
                              child: SingleChildScrollView(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Admin: ',
                                      overflow: isOpen ?? false
                                          ? TextOverflow.fade
                                          : TextOverflow.visible,
                                    ),
                                    10.verticalSpace,
                                    CustomTextWigdet(
                                      title: statusNote ?? '',
                                      overflow: isOpen ?? false
                                          ? TextOverflow.visible
                                          : TextOverflow.fade,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    isApprovement ?? false
                        ? const SizedBox.shrink()
                        : CustomContainer(
                            bgColor: secondBlueColor.withOpacity(0.1),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 16.5.w, vertical: 16.5.h),
                              child: CustomUserInfo(
                                title: uploaderName,
                                subTitle: userName,
                                photoUserUrl: imageUrl,
                                width: 48,
                                height: 48,
                              ),
                            ),
                          ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
