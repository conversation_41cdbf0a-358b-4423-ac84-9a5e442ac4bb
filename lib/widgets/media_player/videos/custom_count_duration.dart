import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomCountDuration extends StatelessWidget {
  final String duration;
  final double? fontSize;
  final EdgeInsetsDirectional? padding;
  const CustomCountDuration(
      {super.key, required this.duration, this.padding, this.fontSize});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(0),
      child: CustomContainer(
        radius: 4,
        widget: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
          child: CustomTextWigdet(
            title: duration,
            fontSize: fontSize ?? 18,
          ),
        ),
        bgColor: blackColor.withOpacity(0.4),
      ),
    );
  }
}
