import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';
import 'package:mides_skadik/widgets/components/custom_user_info.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomVideoContainer extends StatelessWidget {
  final String thumbnailUrl;
  final String? photoUserUrl;
  final String? titleVideo;
  final int? totalView;
  final String? uploadDate;
  final String? duration;
  final String? uploaderName;
  final String? userName;
  final List<String>? tags;
  final double? width;
  final double? heightVideo;
  final double? heightContainerVideo;
  final double? widthUserInfo;
  final double? heightUserInfo;
  final double? heightButton;
  final double? widthIcon;
  final double? heightIcon;
  final double? titleSize;
  final double? subTitleSize;
  final bool? isLandscape;
  final bool? isLive;
  final void Function()? onTapMore;
  const CustomVideoContainer({
    super.key,
    required this.thumbnailUrl,
    this.photoUserUrl,
    this.titleVideo,
    this.totalView,
    this.uploadDate,
    this.duration,
    this.tags,
    this.width,
    this.heightVideo,
    this.heightContainerVideo,
    this.widthUserInfo,
    this.heightUserInfo,
    this.heightButton,
    this.titleSize,
    this.subTitleSize,
    this.widthIcon,
    this.heightIcon,
    this.isLandscape,
    this.uploaderName,
    this.userName,
    this.isLive,
    this.onTapMore,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
        borderRadius: isLandscape ?? false
            ? BorderRadius.only(
                topLeft: Radius.circular(12.r), topRight: Radius.circular(12.r))
            : BorderRadius.circular(12.r),
        width: width ?? 560,
        bgColor: secondBlueColor.withOpacity(0.2),
        widget: Stack(
          children: [
            Column(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(12.r),
                  child: Stack(alignment: Alignment.center, children: [
                    thumbnailUrl.isEmpty
                        ? Image.asset(
                            'assets/images/airplane.png',
                            height: heightVideo ?? 395.h,
                            width: width?.w ?? 560.w,
                            fit: BoxFit.cover,
                          )
                        : CachedNetworkImage(
                            imageUrl: thumbnailUrl,
                            fit: BoxFit.cover,
                            height: heightVideo ?? 395.h,
                            width: width?.w ?? 560.w,
                            placeholder: (context, url) => Center(
                              child: SizedBox(
                                width: 50.w,
                                height: 50.h,
                                child: const CustomLoadingWidget(),
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                    Container(
                      height: heightContainerVideo ?? 395.h,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            blackColor.withOpacity(0.5),
                            blackColor.withOpacity(0.1),
                            blackColor.withOpacity(0.5),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                    Positioned(
                      top: 16.h,
                      left: 16.w,
                      right: 16.w,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: 400.w,
                                child: CustomTextWigdet(
                                  title: titleVideo ?? 'Judul Video',
                                  fontWeight: FontWeight.w500,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Row(
                                children: [
                                  CustomTextWigdet(
                                    title:
                                        '${totalView ?? 0} penonton • ${uploadDate ?? 'Hari ini'}',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w300,
                                  ),
                                ],
                              ),
                            ],
                          ),
                          GestureDetector(
                            onTap: onTapMore,
                            child: Icon(
                              Icons.more_vert_rounded,
                              color: whiteColor,
                            ),
                          )
                        ],
                      ),
                    ),
                    Positioned(
                      bottom: 16.h,
                      left: 16.w,
                      right: 16.w,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ConstrainedBox(
                            constraints: const BoxConstraints(
                                minWidth: 135, maxWidth: 250),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(children: [
                                for (var item in tags ?? ['Tag']) ...[
                                  CustomTag(
                                      bgColor: greyColor.withOpacity(0.8),
                                      tag: item),
                                  8.horizontalSpace,
                                ]
                              ]),
                            ),
                          ),
                          if (isLive ?? false)
                            CustomContainer(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.w, vertical: 4.h),
                              bgColor: redColor,
                              borderRadius: BorderRadius.circular(8.r),
                              widget: CustomTextWigdet(
                                title: 'LIVE',
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                textColor: whiteColor,
                              ),
                            )
                          else
                            CustomCountDuration(duration: duration ?? '00:00'),
                        ],
                      ),
                    ),
                  ]),
                ),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomUserInfo(
                        spacing: 16,
                        fontSizeTitle: titleSize,
                        fontSizeSubtitle: subTitleSize,
                        width: widthUserInfo ?? 48,
                        height: heightUserInfo ?? 48,
                        titleColor: whiteColor,
                        subTitleColor: whiteColor,
                        title: uploaderName,
                        subTitle: userName,
                        photoUserUrl: photoUserUrl,
                      ),
                      if (isLive ?? false)
                        CustomFilledButtonWidget(
                          onPressed: () {},
                          bgColor: Colors.black.withOpacity(0.5),
                          withIcon: true,
                          assetName: 'assets/icons/mi_user.svg',
                          title: '99',
                          widthIcon: widthIcon,
                          heightIcon: heightIcon,
                          fontSize: 18,
                          fontColor: whiteColor,
                          heightButton: heightButton,
                          padding: EdgeInsets.symmetric(horizontal: 18.w),
                          radius: 8,
                        )
                      else
                        const SizedBox.shrink()
                      // CustomFilledButtonWidget(
                      //   onPressed: () {},
                      //   bgColor: darkGreyColor,
                      //   withIcon: true,
                      //   assetName: 'assets/icons/time.svg',
                      //   title: 'Tonton Nanti',
                      //   widthIcon: widthIcon,
                      //   heightIcon: heightIcon,
                      //   fontSize: 18,
                      //   fontColor: whiteColor,
                      //   heightButton: heightButton,
                      //   padding: EdgeInsets.symmetric(horizontal: 18.w),
                      //   radius: 8,
                      // )
                    ],
                  ),
                )
              ],
            )
          ],
        ));
  }
}
