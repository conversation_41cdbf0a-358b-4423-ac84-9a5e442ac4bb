import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomContainerList extends StatelessWidget {
  final String title;
  final double? width;
  final double? height;
  final double? fontSize;
  final bool? withButton;
  final bool? isLandscape;
  final BorderRadius? borderRadius;
  final double? widthIcon;
  final double? heightIcon;
  final double? heightButton;
  final Widget? widget;
  final void Function()? onViewClicked;
  const CustomContainerList({
    super.key,
    required this.title,
    this.onViewClicked,
    this.withButton,
    this.widget,
    this.width,
    this.height,
    this.fontSize,
    this.widthIcon,
    this.heightIcon,
    this.heightButton,
    this.isLandscape,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      borderRadius: isLandscape ?? false ? borderRadius : BorderRadius.circular(12.r),
      height: height ?? 475,
      width: width,
      bgColor: secondBlueColor.withOpacity(0.2),
      widget: Column(
        children: [
          CustomContainer(
            radius: 12,
            bgColor: secondBlueColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(0),
            widget: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 26.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomTextWigdet(
                    title: title,
                    fontSize: fontSize ?? 32,
                    fontWeight: FontWeight.w600,
                  ),
                  withButton ?? true
                      ? CustomFilledButtonWidget(
                          onPressed: onViewClicked,
                          title: 'Lihat Semua',
                          fontColor: whiteColor,
                          bgColor: darkGreyColor,
                          fontSize: 18,
                          radius: 8,
                          heightButton: heightButton,
                          heightIcon: 100,
                          padding: EdgeInsets.symmetric(horizontal: 18.w),
                        )
                      : const SizedBox.shrink()
                ],
              ),
            ),
          ),
          widget ?? const SizedBox.shrink(),
        ],
      ),
    );
  }
}
