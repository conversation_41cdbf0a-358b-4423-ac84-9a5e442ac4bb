import 'package:flutter/widgets.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';

class CustomDialogMore extends StatelessWidget {
  final List<Widget>? widgets;
  const CustomDialogMore({super.key, this.widgets});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: CustomContainer(
        width: 720,
        radius: 10,
        bgColor: secondDarkBlueColor,
        widget: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: widgets?.isNotEmpty == true
              ? [
                  CustomContainer(
                    bgColor: darkBlueColor,
                    height: 53,
                    widget: Center(
                      child: CustomContainer(
                        height: 8,
                        width: 128,
                        bgColor: whiteColor,
                        radius: 10,
                      ),
                    ),
                  ),
                  ...widgets!
                ]
              : [const SizedBox.shrink()],
        ),
      ),
    );
  }
}
