import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:flutter/material.dart';

class CustomVideoItemPlaylist extends StatelessWidget {
  final String? thumbnail;
  final int? numberofvideos;
  final String? title;
  final String? fileVisibility;
  final DateTime? createdAt;
  final double? height;
  final double? width;
  final double? thumbnailWidth;
  final double? thumbnailHeight;
  final bool? isSelected;
  final void Function()? onTap;

  const CustomVideoItemPlaylist({
    super.key,
    this.thumbnail,
    this.numberofvideos,
    this.title,
    this.fileVisibility,
    this.createdAt,
    this.height,
    this.width,
    this.thumbnailWidth,
    this.thumbnailHeight,
    this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () {},
      child: CustomContainer(
        bgColor: isSelected == true ? secondBlueColor.withOpacity(0.10) : Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
        widget: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomContainer(
              width: thumbnailWidth ?? 350,
              height: thumbnailHeight ?? 196,
              widget: Stack(
                alignment: Alignment.centerLeft,
                children: [
                  Container(
                    width: thumbnailWidth ?? 350,
                    height: thumbnailHeight ?? 196,
                    decoration: BoxDecoration(
                      color: darkGreyColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.r),
                        bottomLeft: Radius.circular(10.r),
                      ),
                    ),
                    child: Image.network(
                      thumbnail ?? 'assets/images/airplane.png',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(Icons.error);
                      },
                    ),
                  ),
                  Positioned(
                    bottom: 10.h,
                    right: 10.w,
                    child: Align(
                      alignment: Alignment.bottomRight,
                      child: CustomContainer(
                        bgColor: blackColor.withOpacity(0.4),
                        borderRadius: BorderRadius.circular(4.r),
                        widget: Padding(
                          padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 8.w),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SvgPicture.asset(
                                "assets/icons/icon_playlist.svg",
                                width: 24.w,
                                height: 24.h,
                              ),
                              CustomTextWigdet(
                                title: '${numberofvideos ?? 9} Videos',
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                textColor: whiteColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            16.horizontalSpace,
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 8.h, right: 16.w),
                child: CustomContainer(
                  widget: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: title ?? 'Playlist Title',
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            textColor: whiteColor,
                          ),
                          Row(
                            children: [
                              CustomTextWigdet(
                                title: fileVisibility ?? 'Public •',
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                textColor: whiteColor,
                              ),
                              8.horizontalSpace,
                              CustomTextWigdet(
                                title: createdAt != null ? '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}' : 'Date',
                                fontSize: 12,
                                fontWeight: FontWeight.w300,
                                textColor: whiteColor,
                              ),
                            ],
                          ),
                        ],
                      ),
                      GestureDetector(
                        onTap: () {
                          // Handle more options tap
                        },
                        child: SvgPicture.asset(
                          "assets/icons/more.svg",
                          width: 24.w,
                          height: 24.h,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
