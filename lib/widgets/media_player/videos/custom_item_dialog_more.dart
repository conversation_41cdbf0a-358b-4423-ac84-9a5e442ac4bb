import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';

class CustomItemDialogMore extends StatelessWidget {
  final String assetName;
  final Widget? title;
  final void Function()? onTap;
  const CustomItemDialogMore(
      {super.key, required this.assetName, this.title, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        width: double.maxFinite,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 20.h),
          child: Row(
            children: [
              SvgPicture.asset(
                assetName,
                width: 40.w,
                height: 40.h,
              ),
              12.horizontalSpace,
              title ?? const SizedBox.shrink(),
            ],
          ),
        ),
      ),
    );
  }
}
