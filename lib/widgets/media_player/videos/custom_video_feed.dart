import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_user_info.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomVideoFeed extends StatelessWidget {
  final String? titleVideo;
  final int? totalView;
  final String? uploadDate;
  final String? photoUserUrl;
  final String? thumbnailUrl;
  final List<String>? tags;
  final String? uploaderName;
  final String? userName;
  final String? duration;
  final double? width;
  final double? height;
  final String? lastDuration;
  final double? lastWatch;
  final void Function()? onTap;
  final void Function()? onTapMore;
  const CustomVideoFeed({
    super.key,
    this.titleVideo,
    this.totalView,
    this.uploadDate,
    this.uploaderName,
    this.userName,
    this.duration,
    this.photoUserUrl,
    this.thumbnailUrl,
    this.tags,
    this.width,
    this.height,
    this.lastDuration = '',
    this.lastWatch,
    this.onTap,
    this.onTapMore,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: width?.w ?? 385.w,
        height: height?.h,
        child: CustomContainer(
          radius: 12,
          width: width ?? 385,
          bgColor: darkGreyColor.withOpacity(0.5),
          widget: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomContainer(
                height: height ?? 200, // Reduced height to prevent overflow
                width: width ?? 385,
                radius: 12,
                bgColor: baseBlueColor,
                widget: ClipRRect(
                  borderRadius: BorderRadius.circular(12.r),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      (thumbnailUrl == null || thumbnailUrl!.isNotEmpty)
                          ? CachedNetworkImage(
                              imageUrl: thumbnailUrl ?? '',
                              width: width ?? 385.w,
                              height: height ?? 215,
                              fit: BoxFit.cover,
                              placeholder: (context, url) =>
                                  const CustomLoadingWidget(),
                              errorWidget: (context, url, error) =>
                                  const Icon(Icons.error),
                            )
                          : Image.asset(
                              'assets/images/airplane.png',
                              width: width ?? 385.w,
                              height: height ?? 215,
                              fit: BoxFit.fitWidth,
                            ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 12.h, right: 12.w),
                          child: CustomCountDuration(
                              duration: duration ?? '99:99'),
                        ),
                      ),
                      lastDuration!.isNotEmpty
                          ? Align(
                              alignment: Alignment.bottomLeft,
                              child: Container(
                                color: darkBlueColor.withValues(alpha: .4),
                                height: 6.h,
                                width: double.maxFinite,
                                child: FractionallySizedBox(
                                  alignment: Alignment.centerLeft,
                                  widthFactor: lastWatch,
                                  child: Container(
                                    color: blueColor,
                                    height: 6.h,
                                  ),
                                ),
                              ),
                            )
                          : const SizedBox.shrink()
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: titleVideo ?? 'Judul Video',
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          8.verticalSpace,
                          CustomTextWigdet(
                            title:
                                '${totalView ?? 10} penonton • ${uploadDate ?? 'Hari ini'}',
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                          )
                        ],
                      ),
                    ),
                    GestureDetector(
                      onTap: onTapMore,
                      child: const Icon(
                        Icons.more_vert_rounded,
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
              10.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  children: [
                    ConstrainedBox(
                      constraints:
                          const BoxConstraints(minWidth: 135, maxWidth: 200),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            for (var item in tags ?? ['No Tag'])
                              CustomTag(tag: item, fontSize: 14, vertical: 4),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              CustomContainer(
                height: 70, // Reduced height to prevent overflow
                width: width ?? 385,
                bgColor: secondBlueColor.withOpacity(0.2),
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12.r),
                    bottomRight: Radius.circular(12.r)),
                widget: Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h), // Reduced vertical padding
                  child: CustomUserInfo(
                    title: uploaderName ?? 'Pasis Name',
                    subTitle: userName ?? 'User Name',
                    photoUserUrl: photoUserUrl ?? '',
                    spacing: 16,
                    fontSizeTitle: 20,
                    fontSizeSubtitle: 18,
                    width: 48,
                    height: 48,
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
