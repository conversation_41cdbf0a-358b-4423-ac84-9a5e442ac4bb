import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomTag extends StatelessWidget {
  final String tag;
  final double? fontSize;
  final double? vertical;
  final Color? bgColor;
  const CustomTag(
      {super.key,
      required this.tag,
      this.fontSize,
      this.vertical,
      this.bgColor});

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      margin: EdgeInsets.only(right: 8.w),
      radius: 4,
      bgColor: bgColor ?? whiteColor.withOpacity(0.25),
      widget: Padding(
        padding:
            EdgeInsets.symmetric(horizontal: 12.w, vertical: vertical ?? 8.h),
        child: CustomTextWigdet(
          title: tag,
          fontSize: fontSize ?? 18,
          // overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}
