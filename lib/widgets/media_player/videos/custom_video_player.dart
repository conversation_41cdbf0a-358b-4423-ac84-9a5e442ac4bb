import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating/flutter_rating.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/bindings/clipping_binding.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/playlist_collection_view.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/controllers/video_player_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/comment.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/components/custom_divider.dart';
import 'package:mides_skadik/widgets/course/chat/message_bar_chat.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CustomVideoPlayer extends StatelessWidget {
  final EdgeInsetsGeometry? paddingColumn;
  final EdgeInsetsGeometry? paddingCustomContainer;
  final EdgeInsetsGeometry? paddingComment;
  final String title;
  final String uploaderName;
  final String uploaderHandle;
  final String thumbnail;
  final int views;
  final int comments;
  final List<String> tags;
  final VideoController videoPlayerController;

  const CustomVideoPlayer({
    super.key,
    this.paddingColumn,
    this.paddingCustomContainer,
    this.paddingComment,
    required this.title,
    required this.uploaderName,
    required this.uploaderHandle,
    required this.thumbnail,
    required this.views,
    required this.comments,
    required this.tags,
    required this.videoPlayerController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AspectRatio(
            aspectRatio: 16 / 9,
            child: Obx(
              () => !videoPlayerController.isInitialized.value
                  ? const Center(child: CircularProgressIndicator())
                  : videoPlayerController.videoUrl.value.contains('youtube.com')
                      ? WebViewWidget(
                          controller: videoPlayerController.webViewController)
                      : Chewie(
                          controller: videoPlayerController.chewieController,
                        ),
            )),
        Padding(
          padding: paddingColumn ?? EdgeInsets.symmetric(horizontal: 40.w),
          child: SizedBox(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                24.verticalSpace,
                CustomTextWigdet(
                  title: title,
                  fontSize: 24,
                  textColor: whiteColor,
                  fontWeight: FontWeight.w700,
                ),
                CustomTextWigdet(
                  title: '$views views • 9 days ago',
                  fontSize: 16,
                  textColor: whiteColor,
                  fontWeight: FontWeight.w300,
                ),
                8.verticalSpace,
                Wrap(
                  spacing: 8.w,
                  runSpacing: 4.h,
                  children: tags.map((tag) => CustomTag(tag: tag)).toList(),
                ),
                16.verticalSpace,
                if (!videoPlayerController.videoUrl.value
                    .contains('youtube.com'))
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomContainer(
                        height: 60,
                        widget: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          child: Row(
                            children: [
                              Obx(
                                () => StarRating(
                                    size: 25.0,
                                    rating:
                                        videoPlayerController.rattings.value,
                                    color: Colors.orange,
                                    borderColor: Colors.grey,
                                    allowHalfRating: true,
                                    starCount: 5,
                                    onRatingChanged: (rating) {
                                      videoPlayerController.rattings.value =
                                          rating;
                                      videoPlayerController.tambahRatting();
                                      LogService.log
                                          .i('Rating changed: $rating');
                                    }),
                              )
                            ],
                          ),
                        ),
                      ),
                      16.horizontalSpace,
                      Row(
                        children: [
                          CustomContainer(
                            height: 60,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            borderRadius: BorderRadius.circular(8.r),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/share.svg",
                                      width: 20.w,
                                      height: 20.h,
                                    ),
                                    8.horizontalSpace,
                                    CustomTextWigdet(
                                      title: 'Share',
                                      fontSize: 18,
                                      textColor: whiteColor,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          16.horizontalSpace,
                          GestureDetector(
                            onTap: () async {
                              downloadVideo(
                                  videoPlayerController.videoSourceUrl.value,
                                  videoPlayerController.videoId.value);
                              videoPlayerController.chewieController.pause();
                            },
                            child: CustomContainer(
                              height: 60,
                              bgColor: secondBlueColor.withOpacity(0.10),
                              borderRadius: BorderRadius.circular(8.r),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 20.w),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        "assets/icons/save.svg",
                                        width: 20.w,
                                        height: 20.h,
                                      ),
                                      8.horizontalSpace,
                                      CustomTextWigdet(
                                        title: 'Clip',
                                        fontSize: 18,
                                        textColor: whiteColor,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          16.horizontalSpace,
                          GestureDetector(
                            onTap: () async {
                              videoPlayerController.chewieController.pause();
                              Get.to(
                                  PlaylistCollectionView(
                                    vodId: videoPlayerController.videoId.value,
                                  ),
                                  binding: ClippingBinding());
                            },
                            child: CustomContainer(
                              height: 60,
                              bgColor: secondBlueColor.withOpacity(0.10),
                              borderRadius: BorderRadius.circular(8.r),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 20.w),
                                child: Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        "assets/icons/save.svg",
                                        width: 20.w,
                                        height: 20.h,
                                      ),
                                      8.horizontalSpace,
                                      CustomTextWigdet(
                                        title: 'Save',
                                        fontSize: 18,
                                        textColor: whiteColor,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                          16.horizontalSpace,
                          CustomContainer(
                            height: 60,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            borderRadius: BorderRadius.circular(8.r),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SvgPicture.asset(
                                      "assets/icons/flag.svg",
                                      width: 20.w,
                                      height: 20.h,
                                    ),
                                    8.horizontalSpace,
                                    CustomTextWigdet(
                                      title: 'Flag',
                                      fontSize: 18,
                                      textColor: whiteColor,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                16.verticalSpace,
                // CustomContainer(
                //   width: double.infinity,
                //   bgColor: secondBlueColor.withOpacity(0.10),
                //   borderRadius: BorderRadius.circular(10.r),
                //   widget: Padding(
                //     padding: paddingCustomContainer ??
                //         EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
                //     child: Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //       children: [
                //         const CustomUserInfo(
                //           width: 48,
                //           height: 48,
                //         ),
                //         CustomContainer(
                //           height: 60,
                //           bgColor: baseBlueColor,
                //           borderRadius: BorderRadius.circular(8.r),
                //           widget: Padding(
                //             padding: EdgeInsets.symmetric(horizontal: 20.w),
                //             child: Center(
                //               child: Row(
                //                 mainAxisAlignment: MainAxisAlignment.center,
                //                 children: [
                //                   CustomTextWigdet(
                //                     title: 'Visit Channel',
                //                     fontSize: 18,
                //                     textColor: whiteColor,
                //                     fontWeight: FontWeight.w400,
                //                   ),
                //                   8.horizontalSpace,
                //                   SvgPicture.asset(
                //                     "assets/icons/icon_chevron_right.svg",
                //                     width: 20.w,
                //                     height: 20.h,
                //                   ),
                //                 ],
                //               ),
                //             ),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
                16.verticalSpace,
                Column(
                  children: [
                    CustomContainer(
                      width: double.infinity,
                      height: 60,
                      bgColor: secondBlueColor.withOpacity(0.15),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.r),
                        topRight: Radius.circular(10.r),
                      ),
                      widget: Padding(
                        padding: paddingComment ??
                            EdgeInsets.symmetric(horizontal: 16.w),
                        child: Row(
                          children: [
                            CustomTextWigdet(
                              title: "Comments",
                              fontSize: 22,
                              textColor: whiteColor,
                              fontWeight: FontWeight.w500,
                            ),
                            16.horizontalSpace,
                            // CustomContainer(
                            //   width: 36,
                            //   height: 28,
                            //   bgColor: Colors.grey.withOpacity(0.3),
                            //   borderRadius: BorderRadius.circular(2),
                            //   widget: Center(
                            //     child: CustomTextWigdet(
                            //       title: "$comments",
                            //       fontSize: 16,
                            //       textColor: whiteColor,
                            //       fontWeight: FontWeight.w400,
                            //     ),
                            //   ),
                            // ),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        _showAllComments(context, comments);
                      },
                      child: CustomContainer(
                        width: double.infinity,
                        height: 80,
                        bgColor: secondBlueColor.withOpacity(0.10),
                        widget: Padding(
                          padding: paddingComment ??
                              EdgeInsets.symmetric(horizontal: 16.w),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 60.w,
                                height: 60.h,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12.r),
                                  child: Image.asset(
                                    'assets/images/profile_pict.png',
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              8.horizontalSpace,
                              Expanded(
                                child: CustomTextWigdet(
                                  title:
                                      "Tulis komentar... (Tap to open comments)",
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.w700,
                                  textColor: whiteColor,
                                  textAlign: TextAlign.start,
                                  maxLines: 3,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showAllComments(BuildContext context, int totalComments) {
    showModalBottomSheet(
      context: context,
      constraints: const BoxConstraints(maxWidth: double.infinity),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return GetBuilder<VideoController>(
                builder: (controller) => AnimatedPadding(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: DraggableScrollableSheet(
                        initialChildSize: 0.55,
                        minChildSize: 0.3,
                        maxChildSize: 0.95,
                        builder: (context, scrollController) {
                          return Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(16.r),
                              ),
                            ),
                            child: Stack(
                              children: [
                                SvgPicture.asset(
                                  "assets/images/background.svg",
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                ),
                                Padding(
                                  padding: paddingComment ??
                                      EdgeInsets.symmetric(horizontal: 24.w),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      CustomContainer(
                                        width: 100,
                                        height: 5,
                                        bgColor: whiteColor,
                                        borderRadius:
                                            BorderRadius.circular(5.r),
                                      ),
                                      16.verticalSpace,
                                      CustomContainer(
                                        width: double.infinity,
                                        height: 88,
                                        bgColor:
                                            secondBlueColor.withOpacity(0.10),
                                        borderRadius:
                                            BorderRadius.circular(5.r),
                                        widget: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            GestureDetector(
                                              onTap: () {
                                                Navigator.pop(context);
                                              },
                                              child: SvgPicture.asset(
                                                "assets/icons/icon_X.svg",
                                                width: 72.w,
                                                height: 72.h,
                                              ),
                                            ),
                                            5.horizontalSpace,
                                            CustomTextWigdet(
                                              title: "Comments",
                                              fontSize: 22,
                                              textColor: whiteColor,
                                              fontWeight: FontWeight.w700,
                                            ),
                                            16.horizontalSpace,
                                            // CustomContainer(
                                            //   width: 36,
                                            //   height: 28,
                                            //   bgColor:
                                            //       Colors.grey.withOpacity(0.3),
                                            //   borderRadius:
                                            //       BorderRadius.circular(2),
                                            //   widget: Center(
                                            //     child: CustomTextWigdet(
                                            //       title: "$comments",
                                            //       fontSize: 16,
                                            //       textColor: whiteColor,
                                            //       fontWeight: FontWeight.w400,
                                            //     ),
                                            //   ),
                                            // ),
                                          ],
                                        ),
                                      ),
                                      16.verticalSpace,
                                      const CustomDivider(),
                                      Expanded(
                                        child: ListView(
                                          controller: scrollController,
                                          keyboardDismissBehavior:
                                              ScrollViewKeyboardDismissBehavior
                                                  .onDrag,
                                          children: [
                                            ...controller.comments.reversed
                                                .map(
                                                  (comment) => CommentSection(
                                                    commentModel: comment,
                                                    onSubmitReply:
                                                        (parentComment,
                                                            replyText) {
                                                      LogService.log.i(
                                                          'Submit reply to comment ${parentComment.id}: $replyText');

                                                      // Post reply dengan parentId - akan otomatis refresh comments
                                                      controller.postComment(
                                                          comment: replyText,
                                                          parentId:
                                                              parentComment.id);

                                                      // Auto-expand parent comment untuk menampilkan reply baru
                                                      controller.setReplyState(
                                                          parentComment.id ??
                                                              '',
                                                          true);
                                                    },
                                                    onToggleReply:
                                                        (commentModel) {
                                                      // Toggle isOpenReply menggunakan VideoController method
                                                      controller
                                                          .toggleReplyState(
                                                              commentModel.id ??
                                                                  '');
                                                    },
                                                  ),
                                                )
                                                .toList(),
                                            SizedBox(
                                              height: 150.h,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Comment input section menggunakan MessageBarChat
                                Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Padding(
                                    padding: paddingComment ??
                                        EdgeInsets.symmetric(vertical: 16.h),
                                    child: MessageBarChat(
                                      messageBarHintText: "Tulis komentar...",
                                      messageBarColor: darkBlueColor,
                                      textFieldColor:
                                          baseBlueColor.withOpacity(0.1),
                                      borderEnabledFieldColor:
                                          secondWhiteColor.withOpacity(0.3),
                                      borderFocusedFieldColor: secondBlueColor,
                                      sendButtonColor: secondBlueColor,
                                      textFieldTextStyle: TextStyle(
                                        color: whiteColor,
                                        fontSize: 14.sp,
                                        fontFamily: 'Inter',
                                      ),
                                      messageBarHintStyle: TextStyle(
                                        color:
                                            secondWhiteColor.withOpacity(0.6),
                                        fontSize: 14.sp,
                                        fontFamily: 'Inter',
                                      ),
                                      radiusTextField: 20.r,
                                      userBorder: true,
                                      onSend: (commentText) {
                                        LogService.log
                                            .i('Posting comment: $commentText');

                                        // Post comment menggunakan VideoController
                                        videoPlayerController.postComment(
                                          comment: commentText,
                                        );
                                      },
                                      actions: [
                                        // Avatar user
                                        Padding(
                                          padding: EdgeInsets.only(right: 12.w),
                                          child: CircleAvatar(
                                            radius: 20.r,
                                            backgroundImage: const AssetImage(
                                              'assets/images/profile_pict.png',
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ));
          },
        );
      },
    );
  }
}
