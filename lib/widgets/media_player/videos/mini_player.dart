import 'dart:ui';

import 'package:animate_do/animate_do.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/media_player/dashboard/controllers/dashboard_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/controllers/video_player_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';

class MiniPlayer extends StatelessWidget {
  final String videourl;
  MiniPlayer({
    super.key,
    required this.videourl,
  });

  final DashboardController dashboardController =
      Get.put(DashboardController());

  @override
  Widget build(BuildContext context) {
    Get.put(VideoController()).videoUrl.value = videourl;
    final videoPlayerController = Get.put(VideoController());

    return Obx(
      () => FadeInUp(
        animate: videoPlayerController.isPlaying.value,
        child: GestureDetector(
          onTap: () {
            // Get.toNamed(Routes.VIDEO_PLAYER);
          },
          child: Column(
            children: [
              CustomContainer(
                width: 550,
                height: 330,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
                widget: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Obx(
                        () => !videoPlayerController.isInitialized.value
                            ? const Center(child: CircularProgressIndicator())
                            : Chewie(
                                controller:
                                    videoPlayerController.chewieController,
                              ),
                      )),
                ),
              ),
              ClipRRect(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12.r),
                  bottomRight: Radius.circular(12.r),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: CustomContainer(
                    width: 550,
                    height: 100,
                    bgColor: darkGreyColor.withOpacity(0.2),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12.r),
                      bottomRight: Radius.circular(12.r),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
