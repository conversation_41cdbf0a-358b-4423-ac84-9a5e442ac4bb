import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

import '../../components/custom_user_info.dart';

class CustomVideoItem extends StatelessWidget {
  final String? thumbnailUrl;
  final String? duration;
  final String? titleVideo;
  final String? uploaderName;
  final int? totalView;
  final String? uploadDate;
  final String? lastDuration;
  final double? lastWatchTime;
  final List<String>? tags;
  final double? height;
  final double? thumbnailHeight;
  final double? thumbnailWidth;
  final double? bottomRightSpace;
  final double? topSpace;
  final bool? withDesc;
  final bool? isLandscape;
  final bool? isLibrary;
  final void Function()? onTapMore;
  const CustomVideoItem({
    super.key,
    this.thumbnailHeight,
    this.thumbnailWidth,
    this.withDesc,
    this.height,
    this.isLandscape,
    this.isLibrary,
    this.bottomRightSpace,
    this.thumbnailUrl,
    this.duration,
    this.titleVideo,
    this.uploaderName,
    this.totalView,
    this.uploadDate,
    this.topSpace,
    this.tags,
    this.lastDuration = '',
    this.lastWatchTime,
    this.onTapMore,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: CustomContainer(
        width: double.infinity,
        height: height,
        bgColor: Colors.transparent,
        radius: 12,
        widget: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomContainer(
              width: thumbnailWidth ?? 255,
              height: thumbnailHeight ?? height,
              bgColor: baseBlueColor,
              radius: 12,
              widget: Stack(
                children: [
                  ClipRRect(
                    borderRadius: isLibrary ?? false
                        ? BorderRadius.only(
                            topLeft: Radius.circular(12.r),
                            bottomLeft: Radius.circular(12.r))
                        : BorderRadius.all(Radius.circular(12.r)),
                    child: (thumbnailUrl == null || thumbnailUrl!.isEmpty)
                        ? Image.asset(
                            'assets/images/airplane.png',
                            width: thumbnailWidth ?? 255.w,
                            height: thumbnailHeight,
                            fit: BoxFit.cover,
                          )
                        : CachedNetworkImage(
                            imageUrl: thumbnailUrl!,
                            width: thumbnailWidth ?? 255.w,
                            height: thumbnailHeight,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CustomLoadingWidget(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                  ),
                  Positioned(
                    bottom: 0.h,
                    left: bottomRightSpace?.w,
                    child: CustomCountDuration(
                      duration: duration ?? '99:99',
                      fontSize: isLandscape ?? false ? 14 : 18,
                      padding: EdgeInsetsDirectional.symmetric(
                          horizontal: 12.w, vertical: 12.h),
                    ),
                  ),
                  lastDuration!.isNotEmpty
                      ? Align(
                          alignment: Alignment.bottomLeft,
                          child: Container(
                            color: darkBlueColor.withValues(alpha: .4),
                            width: double.maxFinite,
                            height: 6.h,
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: lastWatchTime,
                              child: Container(
                                color: blueColor,
                                height: 6.h,
                              ),
                            ),
                          ))
                      : const SizedBox.shrink()
                ],
              ),
            ),
            Flexible(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(left: 12.w, top: topSpace?.h ?? 16.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomTextWigdet(
                                title: titleVideo ?? 'Judul Video',
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                textColor: whiteColor,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              CustomTextWigdet(
                                title: uploaderName ?? 'Nama Pengunggah',
                                fontSize: isLandscape ?? false ? 16 : 18,
                                fontWeight: FontWeight.w300,
                                textColor: whiteColor,
                              ),
                              CustomTextWigdet(
                                title:
                                    '${totalView ?? 0} penonton • ${uploadDate ?? 'Hari ini'}',
                                fontSize: isLandscape ?? false ? 16 : 18,
                                fontWeight: FontWeight.w300,
                                textColor: whiteColor,
                              ),
                            ],
                          ),
                        ),
                        GestureDetector(
                          onTap: onTapMore,
                          child: Icon(
                            Icons.more_vert_rounded,
                            color: whiteColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  40.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          for (var item in tags ?? ['No Tag']) ...[
                            CustomTag(
                              bgColor: greyColor,
                              tag: item,
                              fontSize: 14,
                              vertical: 4,
                            ),
                            8.horizontalSpace,
                          ],
                        ],
                      ),
                    ),
                  ),
                  withDesc ?? false ? 16.verticalSpace : 0.verticalSpace,
                  withDesc ?? false
                      ? Column(
                          children: [
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 12.w),
                              child: CustomTextWigdet(
                                title:
                                    "Dalam video kali ini, kami akan membahas tentang teknologi terbaru dalam dunia penerbangan militer. Dari inovasi pesawat tempur hingga sistem pertahanan canggih, kami akan mengeksplorasi berbagai aspek yang membuat Angkatan Udara semakin modern dan efisien.",
                                fontSize: 16,
                                fontWeight: FontWeight.w300,
                                textColor: secondWhiteColor,
                                maxLines: 2,
                                softWrap: true,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(height: 10.h),
                            CustomContainer(
                              bgColor: secondBlueColor.withOpacity(0.1),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.w, vertical: 16.h),
                                child: const CustomUserInfo(),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
