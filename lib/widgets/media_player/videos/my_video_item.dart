import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_count_duration.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_tag.dart';

class MyVideoItem extends StatelessWidget {
  final String? thumbnailUrl;
  final String? title;
  final String? uploadDate;
  final String? duration;
  final String? statusApprovement;
  final String? statusNote;
  final int? totalViews;
  final bool? isOpen;
  final double? heightAnimatedContainer;
  final double? widthContainer;
  final double? maxWidthConstraint;
  final List<String>? tags;
  final void Function()? onTap;
  final void Function()? onPressMore;
  final void Function()? onPressEdit;

  const MyVideoItem({
    super.key,
    this.thumbnailUrl = '',
    this.title,
    this.totalViews,
    this.uploadDate,
    this.duration,
    this.statusApprovement,
    this.statusNote,
    this.tags,
    this.onTap,
    this.onPressMore,
    this.isOpen,
    this.heightAnimatedContainer,
    this.widthContainer,
    this.maxWidthConstraint,
    this.onPressEdit,
  });

  Widget _statusApprove(String status) {
    final statusText = status.toLowerCase();

    switch (statusText) {
      case 'approved':
        return CustomContainer(
          bgColor: blueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/circle_check.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      case 'waiting':
        return CustomContainer(
          bgColor: baseBlueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/time.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      case 'rejected':
        return CustomContainer(
          bgColor: redColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/warning.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                CustomTextWigdet(
                  title: statusText.toUpperCase(),
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
      default:
        return CustomContainer(
          bgColor: baseBlueColor,
          radius: 18,
          height: 50,
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  'assets/icons/warning.svg',
                  width: 18,
                ),
                8.horizontalSpace,
                const CustomTextWigdet(
                  title: 'UNKNOWN',
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ],
            ),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      bgColor: baseBlueColor.withValues(alpha: .4),
      width: widthContainer ?? 437,
      radius: 12,
      widget: Column(
        children: [
          CustomContainer(
            bgColor: baseBlueColor.withValues(alpha: .6),
            width: widthContainer ?? 437,
            height: 80,
            widget: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: statusApprovement?.toLowerCase() == 'rejected'
                        ? onTap
                        : null,
                    child: Row(
                      children: [
                        isOpen ?? false
                            ? Transform.rotate(
                                angle: 90 * pi / 180,
                                child: SvgPicture.asset(
                                  'assets/icons/chevron.svg',
                                  width: 24,
                                ),
                              )
                            : SvgPicture.asset(
                                'assets/icons/chevron.svg',
                                width: 24,
                              ),
                        12.horizontalSpace,
                        _statusApprove(statusApprovement ?? ''),
                      ],
                    ),
                  ),
                  const Spacer(),
                  statusApprovement?.toLowerCase() == 'approved'
                      ? const SizedBox.shrink()
                      : CustomFilledButtonWidget(
                          onPressed: onPressEdit,
                          withIcon: true,
                          onlyIcon: true,
                          assetName: 'assets/icons/pencil.svg',
                          radius: 10,
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          fontColor: whiteColor,
                          bgColor: secondBlueColor.withOpacity(0.1),
                          widthIcon: 35,
                          heightIcon: 35,
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                        ),
                ],
              ),
            ),
          ),
          Stack(
            children: [
              Column(
                children: [
                  CustomContainer(
                    height: 246,
                    width: widthContainer ?? 437,
                    bgColor: baseBlueColor.withValues(alpha: .5),
                    widget: Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: thumbnailUrl!.isEmpty
                              ? Image.asset(
                                  'assets/images/airplane.png',
                                  fit: BoxFit.cover,
                                  width: widthContainer ?? 437,
                                )
                              : CachedNetworkImage(
                                  imageUrl: thumbnailUrl!,
                                  width: widthContainer ?? 437,
                                  height: 246,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) =>
                                      const CustomLoadingWidget(),
                                  errorWidget: (context, url, error) =>
                                      const Icon(Icons.error),
                                ),
                        ),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: CustomCountDuration(
                            duration: duration ?? '00:00',
                            padding: EdgeInsetsDirectional.symmetric(
                                horizontal: 12.w, vertical: 12.h),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(16.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ConstrainedBox(
                                  constraints: BoxConstraints(
                                      maxWidth: maxWidthConstraint?.w ?? 300.w),
                                  child: CustomTextWigdet(
                                    title: title ?? "Judul Video",
                                    fontSize: 24,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                CustomTextWigdet(
                                  title:
                                      "${totalViews ?? 0} penonton • ${uploadDate ?? 'Hari Ini'}",
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                  textColor: secondWhiteColor,
                                ),
                              ],
                            ),
                            GestureDetector(
                              onTap: onPressMore,
                              child: Icon(
                                Icons.more_vert_rounded,
                                color: whiteColor,
                              ),
                            )
                          ],
                        ),
                        16.verticalSpace,
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              for (var item in tags ?? ['No Tags']) ...[
                                CustomTag(tag: item, fontSize: 14, vertical: 4),
                                8.horizontalSpace,
                              ]
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                height:
                    isOpen ?? false ? (heightAnimatedContainer?.h ?? 220.h) : 0,
                clipBehavior: Clip.hardEdge,
                curve: Curves.easeIn,
                decoration: BoxDecoration(
                  color: baseBlueColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12.r),
                    bottomRight: Radius.circular(12.r),
                  ),
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: Padding(
                    padding:
                        EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Admin: ',
                            fontSize: 22,
                            overflow: isOpen ?? false
                                ? TextOverflow.fade
                                : TextOverflow.visible,
                          ),
                          10.verticalSpace,
                          CustomTextWigdet(
                            title: statusNote ?? '-',
                            fontSize: 18,
                            overflow: isOpen ?? false
                                ? TextOverflow.visible
                                : TextOverflow.fade,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
