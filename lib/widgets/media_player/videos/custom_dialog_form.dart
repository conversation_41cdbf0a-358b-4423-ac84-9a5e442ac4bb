import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomDialogForm extends StatelessWidget {
  final String? title;
  final List<Widget>? widgets;
  const CustomDialogForm({
    super.key,
    this.title,
    this.widgets,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 700.w),
        child: CustomContainer(
          bgColor: blackColor,
          widget: Padding(
            padding: EdgeInsets.all(48.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              spacing: 10.h,
              children: widgets?.isNotEmpty == true
                  ? [
                      CustomTextWigdet(
                        title: title ?? '',
                        fontSize: 24,
                        fontWeight: FontWeight.w400,
                      ),
                      ...widgets!
                    ]
                  : [const SizedBox.shrink()],
            ),
          ),
        ),
      ),
    );
  }
}
