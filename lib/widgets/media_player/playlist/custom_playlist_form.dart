import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/playlist/playlist_model.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_text.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_switch_button.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomPlaylistFormWidget extends StatefulWidget {
  final PlaylistModel? playlist;
  const CustomPlaylistFormWidget({
    super.key,
    this.playlist,
  });

  @override
  State<CustomPlaylistFormWidget> createState() =>
      _CustomPlaylistFormWidgetState();
}

class _CustomPlaylistFormWidgetState extends State<CustomPlaylistFormWidget>
    with SingleTickerProviderStateMixin {
  String privacy = "public";

  final titleController = TextEditingController();
  final descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((v) {
      if (widget.playlist != null) {
        titleController.text = widget.playlist?.name ?? '';
        descriptionController.text = widget.playlist?.description ?? '';
        privacy = widget.playlist?.visibility?.toLowerCase().capitalizeFirst ??
            'public';
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PlaylistController>();
    return CustomContainer(
      bgColor: blackColor,
      width: 800,
      radius: 16,
      padding: const EdgeInsets.all(32),
      widget: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextWigdet(
              title: widget.playlist == null ? 'New Playlist' : 'Edit Playlist',
              fontSize: 48.sp,
              fontWeight: FontWeight.w500,
            ),
            12.verticalSpace,
            Divider(
              height: 1,
              color: greyColor,
            ),
            40.verticalSpace,
            CustomTextFieldWidget(
              controller: titleController,
              colorText: whiteColor,
              hintText: 'Judul Playlist',
              colorTextHint: secondWhiteColor.withOpacity(0.5),
              colorField: secondBlueColor.withOpacity(0.1),
              radius: 10,
            ),
            24.verticalSpace,
            CustomTextFieldWidget(
              controller: descriptionController,
              colorText: whiteColor,
              hintText: 'Deskripsi Playlist',
              colorTextHint: secondWhiteColor.withOpacity(0.5),
              colorField: secondBlueColor.withOpacity(0.1),
              radius: 10,
              maxLines: 5,
            ),
            24.verticalSpace,
            const CustomTextWigdet(
              title: 'Privasi',
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
            12.verticalSpace,
            CustomContainer(
              bgColor: secondBlueColor.withOpacity(0.1),
              radius: 8,
              height: 65,
              widget: CustomSwitchButton(
                children: [
                  Expanded(
                    child: CustomButtonText(
                      title: 'Publik',
                      fontSize: 18,
                      isSelected: privacy == 'public',
                      onTap: () {
                        setState(() {
                          privacy = 'public';
                        });
                      },
                    ),
                  ),
                  4.horizontalSpace,
                  Expanded(
                    child: CustomButtonText(
                      title: 'Pribadi',
                      fontSize: 18,
                      isSelected: privacy == 'private',
                      onTap: () {
                        setState(() {
                          privacy = "private";
                        });
                      },
                    ),
                  ),
                  4.horizontalSpace,
                  Expanded(
                    child: CustomButtonText(
                      title: 'Terbatas',
                      fontSize: 18,
                      isSelected: privacy == "restricted",
                      onTap: () {
                        setState(() {
                          privacy = "restricted";
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            42.verticalSpace,
            Row(
              children: [
                Expanded(
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.back();
                    },
                    title: 'Cancel',
                    fontColor: blackColor,
                    bgColor: whiteColor,
                    heightButton: 120.h,
                    radius: 16,
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                  child: CustomFilledButtonWidget(
                    onPressed: () {
                      Get.back(
                          result: PlaylistModel(
                        id: widget.playlist?.id ?? '',
                        name: titleController.text,
                        description: descriptionController.text,
                        visibility: privacy,
                      ));
                    },
                    title: widget.playlist == null ? 'Create' : 'Save',
                    fontColor: whiteColor,
                    bgColor: blueColor,
                    heightButton: 120.h,
                    radius: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
