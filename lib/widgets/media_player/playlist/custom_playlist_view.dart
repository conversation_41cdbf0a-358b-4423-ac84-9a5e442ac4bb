import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/modules/media_player/playlist/controllers/playlist_controller.dart';

import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_playlist.dart';

class CustomPlaylistView extends StatefulWidget {
  const CustomPlaylistView({super.key});

  @override
  State<CustomPlaylistView> createState() => _CustomPlaylistViewState();
}

class _CustomPlaylistViewState extends State<CustomPlaylistView> {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PlaylistController>();
    return Obx(() {
      return ListView.builder(
        itemCount: controller.playlists.length,
        itemBuilder: (context, idx) {
          var row = controller.playlists[idx];
          var thumbnail =
              row.vods?.isNotEmpty == true ? row.vods?.first.thumbnailUrl : '';
          var firstVideo =
              row.vods?.isNotEmpty == true ? row.vods?.first : null;

          DateTime uploadedDate = DateTime.parse(
              firstVideo?.uploadDate ?? DateTime.now().toIso8601String());
          var visibility = row.visibility;

          return Padding(
            padding: EdgeInsets.only(bottom: 16.h, left: 24.w, right: 34.w),
            child: CustomVideoItemPlaylist(
              width: 350,
              height: 300,
              title: row.name ?? '',
              createdAt: uploadedDate,
              numberofvideos: (row.vods ?? []).length,
              thumbnail: thumbnail,
              fileVisibility: visibility,
              isSelected: controller.selectedPlaylist.value?.id == row.id,
              onTap: () {
                controller.setSelectedPlaylist(row);
                setState(() {});
              },
            ),
          );
        },
      );
    });
  }
}
