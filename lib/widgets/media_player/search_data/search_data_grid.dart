import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/playlist_collection_view.dart';
import 'package:mides_skadik/app/modules/media_player/searchdata/controllers/searchdata_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_item_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_feed.dart';

class SearchDataGrid extends StatelessWidget {
  final VodModel item;
  final int index;
  final SearchdataController controller;

  const SearchDataGrid(
      {super.key,
      required this.item,
      required this.index,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return CustomVideoFeed(
      width: double.maxFinite,
      height: 293,
      thumbnailUrl: item.thumbnailUrl,
      duration: formatDuration(item.duration),
      titleVideo: item.title,
      totalView: item.totalView,
      uploadDate: timeRange(controller.timeNow.value,
          item.uploadDate ?? controller.timeNow.value),
      tags: item.tagNames,
      uploaderName: item.uploader?.pasisName,
      onTap: () {
        controller.sentAnaliticVodById(
            vodId: item.id, tagId: controller.tagID.value);
        Get.to(VideoPlayerView(), binding: VideoPlayerBinding(), arguments: {
          "videoUrl": item.videoUrl,
          "videoId": item.id,
          "totalLike": item.totalLike,
          "totalDislike": item.totalDislike,
          "totalRating": item.totalRating,
          "totalView": item.totalView,
          "videoSourceUrl": item.videoSourceUrl,
          "videoTitle": item.title,
          "videoTags": item.tagNames,
        });
      },
      onTapMore: () {
        _showMoreDialog(item);
      },
    );
  }

  void _showMoreDialog(VodModel item) {
    Get.dialog(
      CustomDialogMore(
        widgets: [
          const CustomItemDialogMore(
            assetName: 'assets/icons/time.svg',
            title: CustomTextWigdet(
              title: 'Tambah ke Tonton Nanti',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(color: blackColor),
          CustomItemDialogMore(
            assetName: 'assets/icons/add_to_playlist.svg',
            title: const CustomTextWigdet(
              title: 'Tambah atau Hapus dari Playlist',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
            onTap: () {
              Get.to(
                () => PlaylistCollectionView(
                  vodId: item.id,
                  needBackNavigate: true,
                ),
              );
            },
          ),
          Divider(color: blackColor),
          const CustomItemDialogMore(
            assetName: 'assets/icons/share.svg',
            title: CustomTextWigdet(
              title: 'Bagikan',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
