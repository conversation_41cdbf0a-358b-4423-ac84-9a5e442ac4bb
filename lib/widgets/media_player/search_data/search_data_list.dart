import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/media_player/dashboard/vod_model.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/media_player/clipping/views/playlist_collection_view.dart';
import 'package:mides_skadik/app/modules/media_player/searchdata/controllers/searchdata_controller.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/bindings/video_player_binding.dart';
import 'package:mides_skadik/app/modules/media_player/video_player/views/video_player_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_item_dialog_more.dart';
import 'package:mides_skadik/widgets/media_player/videos/custom_video_item_large.dart';

class SearchDataList extends StatelessWidget {
  final VodModel item;
  final int index;
  final SearchdataController controller;

  const SearchDataList(
      {super.key,
      required this.item,
      required this.index,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return CustomVideoItemLarge(
      thumbnailUrl: item.thumbnailUrl ?? '',
      imageUrl: item.uploader?.imageProfile,
      title: item.title,
      duration: formatDuration(item.duration),
      totalViews: item.totalView,
      uploadDate: timeRange(DateTime.now(), item.uploadDate ?? DateTime.now()),
      tags: item.tagNames,
      description: item.desc,
      uploaderName: item.uploader?.pasisName,
      userName: item.uploader?.username,
      thumbnailWidth: 600,
      thumbnailHeight: 280,
      onTapPlay: () {
        controller.sentAnaliticVodById(
            vodId: item.id, tagId: controller.tagID.value);
        Get.to(VideoPlayerView(), binding: VideoPlayerBinding(), arguments: {
          "videoUrl": item.videoUrl,
          "videoId": item.id,
          "totalLike": item.totalLike,
          "totalDislike": item.totalDislike,
          "totalRating": item.totalRating,
          "totalView": item.totalView,
          "videoTitle": item.title,
          "videoTags": item.tagNames,
        });
      },
      onPressMore: () => _showMoreDialog(item),
    );
  }

  void _showMoreDialog(VodModel item) {
    Get.dialog(
      CustomDialogMore(
        widgets: [
          const CustomItemDialogMore(
            assetName: 'assets/icons/time.svg',
            title: CustomTextWigdet(
              title: 'Tambah ke Tonton Nanti',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(color: blackColor),
          CustomItemDialogMore(
            assetName: 'assets/icons/add_to_playlist.svg',
            title: const CustomTextWigdet(
              title: 'Tambah atau Hapus dari Playlist',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
            onTap: () {
              Get.to(
                () => PlaylistCollectionView(
                  vodId: item.id,
                  needBackNavigate: true,
                ),
              );
            },
          ),
          Divider(color: blackColor),
          const CustomItemDialogMore(
            assetName: 'assets/icons/share.svg',
            title: CustomTextWigdet(
              title: 'Bagikan',
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
