import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';

class CustomLoadingWidget extends StatelessWidget {
  final double? width;
  final double? height;
  final double? imageWidth;
  final double? imageHeight;
  final Color? color;
  final Color? bgColor;
  final double strokeWidth;

  const CustomLoadingWidget(
      {super.key,
      this.color,
      this.bgColor,
      this.width,
      this.height,
      this.imageWidth,
      this.imageHeight,
      this.strokeWidth = 2});

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Stack(
      alignment: Alignment.center,
      children: [
        Positioned(
          top: 0.5,
          child: Image.asset(
            'assets/images/loading_image.png',
            width: imageWidth?.w,
            height: imageHeight?.h,
          ),
        ),
        Container(
          width: width?.w ?? 80.w,
          height: height?.h ?? 80.h,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
          ),
          child: Padding(
            padding: const EdgeInsets.all(2),
            child: CircularProgressIndicator(
              color: color ?? blueColor,
              strokeWidth: strokeWidth,
              strokeCap: StrokeCap.round,
            ),
          ),
        ),
      ],
    ));
  }
}
