// calender_schedule_landscape.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/schedule_course/controllers/schedule_course_controller.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CalenderScheduleLandscape extends StatelessWidget {
  const CalenderScheduleLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ScheduleCourseController>();

    return Obx(() {
      return CustomContainer(
        radius: 8,
        height: 960,
        bgColor: baseBlueColor.withOpacity(0.3),
        widget: Column(
          children: [
            Expanded(
              child: SfCalendar(
                view: CalendarView.week,
                showDatePickerButton: false,
                showNavigationArrow: true,
                backgroundColor: Colors.transparent,
                todayHighlightColor: blueColor,
                selectionDecoration: BoxDecoration(
                  color: Colors.transparent,
                  border: Border.all(color: blueColor),
                ),
                dataSource: controller.calendarDataSource.value,
                onTap: (details) {
                  if (details.appointments != null &&
                      details.appointments!.isNotEmpty) {
                    controller
                        .setSelectedAppointment(details.appointments!.first);
                  }
                },
                onViewChanged: (details) {
                  if (details.visibleDates.isNotEmpty) {
                    final midDate =
                        details.visibleDates[details.visibleDates.length ~/ 2];
                    controller.updateMonthYearAndFetch(
                        midDate.month, midDate.year);
                  }
                },
                headerStyle: CalendarHeaderStyle(
                  backgroundColor: baseBlueColor.withOpacity(0.3),
                  textAlign: TextAlign.left,
                  textStyle: TextStyle(
                    color: whiteColor,
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                headerHeight: 50,
                viewHeaderStyle: ViewHeaderStyle(
                  backgroundColor: baseBlueColor.withOpacity(0.3),
                  dayTextStyle: TextStyle(
                    color: whiteColor,
                    fontWeight: FontWeight.w500,
                  ),
                  dateTextStyle: TextStyle(
                    color: whiteColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 24.sp,
                  ),
                ),
                timeSlotViewSettings: TimeSlotViewSettings(
                  timeInterval: const Duration(hours: 1),
                  timeFormat: 'HH:mm',
                  timeTextStyle: TextStyle(
                    color: whiteColor,
                    fontSize: 24.sp,
                  ),
                ),
                appointmentBuilder: (context, details) {
                  final Appointment appointment = details.appointments.first;
                  return CustomContainer(
                    padding: const EdgeInsets.all(4), // ✅ Reduced padding
                    radius: 4,
                    bgColor: appointment.color,
                    widget: Column(
                      mainAxisSize: MainAxisSize.min, // ✅ Prevent overflow
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Flexible(
                          // ✅ Make title flexible
                          child: CustomTextWigdet(
                            title: appointment.subject.length > 15
                                ? "...."
                                : appointment.subject,
                            fontSize: 24, // ✅ Slightly smaller font
                            fontWeight: FontWeight.bold,
                            maxLines: 1, // ✅ Limit to 1 line
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        2.verticalSpace, // ✅ Reduced spacing
                        Flexible(
                          // ✅ Make time row flexible
                          child: Row(
                            mainAxisSize:
                                MainAxisSize.min, // ✅ Prevent overflow
                            children: [
                              Icon(
                                Icons.access_time,
                                color: whiteColor,
                                size: 18.r, // ✅ Even smaller icon
                              ),
                              SizedBox(width: 2.w), // ✅ Fixed spacing
                              Flexible(
                                // ✅ Changed from Expanded to Flexible
                                child: CustomTextWigdet(
                                  title:
                                      "${appointment.startTime.hour.toString().padLeft(2, '0')}:${appointment.startTime.minute.toString().padLeft(2, '0')}-${appointment.endTime.hour.toString().padLeft(2, '0')}:${appointment.endTime.minute.toString().padLeft(2, '0')}", // ✅ Simplified format
                                  fontSize: 18, // ✅ Even smaller font
                                  textColor: whiteColor,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }
}
