import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/schedule_model.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/pdf_viewer_page.dart';
import 'package:mides_skadik/app/modules/course/history_attendance_course/controllers/history_attendance_course_controller.dart';
import 'package:mides_skadik/widgets/components/audio_player_widget.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/app/modules/course/schedule_course/controllers/schedule_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

class DetailMapelLandscape extends StatelessWidget {
  final Appointment appointment;
  const DetailMapelLandscape({super.key, required this.appointment});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ScheduleCourseController>();
    final historyController = Get.find<HistoryAttendanceCourseController>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      historyController.getAttendanceHistoryViewAll();
    });

    String mapelName = '';
    String lecturerName = '';

    if (appointment.subject.contains(" - ")) {
      final parts = appointment.subject.split(" - ");
      mapelName = parts[0];
      lecturerName = parts[1];
    } else {
      mapelName = appointment.subject;
    }

    final MonthlySchedule? schedule = appointment.id is MonthlySchedule
        ? appointment.id as MonthlySchedule
        : null;

    final hanjarFiles =
        schedule?.mapel.subMapels.expand((sub) => sub.hanjars).toList();

    return CustomContainer(
      width: 600,
      radius: 8,
      padding: const EdgeInsets.all(20),
      bgColor: baseBlueColor.withOpacity(0.3),
      widget: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomTextWigdet(
                  title: 'Detail Mapel',
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
                CustomFilledButtonWidget(
                  onPressed: controller.closeDetailMapel,
                  assetName: 'assets/icons/close_course.svg',
                  withIcon: true,
                  onlyIcon: true,
                  iconColor: whiteColor,
                  widthIcon: 32,
                  heightIcon: 32,
                  radius: 4,
                  bgColor: Colors.transparent,
                ),
              ],
            ),
            const Divider(),
            CustomContainer(
              width: double.infinity,
              bgColor: baseBlueColor.withOpacity(0.6),
              radius: 8,
              padding: const EdgeInsets.symmetric(vertical: 8),
              widget: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: mapelName,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                    5.verticalSpace,
                    Row(
                      children: [
                        ClipOval(
                          child: (appointment.resourceIds != null &&
                                  appointment.resourceIds!.isNotEmpty &&
                                  (appointment.resourceIds!.first as String)
                                      .isNotEmpty)
                              ? Image.network(
                                  appointment.resourceIds!.first as String,
                                  width: 20,
                                  height: 20,
                                  fit: BoxFit.cover,
                                )
                              : Image.asset(
                                  "assets/images/Person.jpg",
                                  width: 20,
                                  height: 20,
                                  fit: BoxFit.cover,
                                ),
                        ),
                        5.horizontalSpace,
                        Expanded(
                          child: CustomTextWigdet(
                            title: lecturerName.isNotEmpty
                                ? lecturerName
                                : 'Lecturer',
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                            textColor: secondWhiteColor,
                          ),
                        ),
                      ],
                    ),
                    10.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/time.svg',
                              color: secondWhiteColor.withValues(alpha: 0.8),
                              width: 18,
                              height: 18,
                            ),
                            5.horizontalSpace,
                            CustomTextWigdet(
                              title:
                                  "${appointment.startTime.hour.toString().padLeft(2, '0')}:${appointment.startTime.minute.toString().padLeft(2, '0')} - "
                                  "${appointment.endTime.hour.toString().padLeft(2, '0')}:${appointment.endTime.minute.toString().padLeft(2, '0')}",
                              fontSize: 16,
                              fontWeight: FontWeight.w300,
                              textColor: secondWhiteColor,
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/location_on.svg',
                              color: secondWhiteColor.withValues(alpha: 0.8),
                              width: 18,
                              height: 18,
                            ),
                            CustomTextWigdet(
                              title: '${appointment.notes}',
                              fontSize: 16,
                              fontWeight: FontWeight.w300,
                              textColor: secondWhiteColor,
                            ),
                            5.horizontalSpace,
                          ],
                        )
                      ],
                    ),
                    10.verticalSpace,
                    CustomContainer(
                      width: double.infinity,
                      bgColor: baseBlueColor.withOpacity(0.6),
                      radius: 8,
                      border: Border.all(
                        color: secondWhiteColor.withOpacity(0.5),
                        width: 0.4,
                      ),
                      widget: Padding(
                        padding: EdgeInsets.all(10.r),
                        child: Center(
                          child: hanjarFiles == null || hanjarFiles.isEmpty
                              ? CustomTextWigdet(
                                  title: 'Tidak ada file tersedia',
                                  fontSize: 16,
                                  fontWeight: FontWeight.w300,
                                  textColor: secondWhiteColor,
                                )
                              : Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    ...hanjarFiles.map(
                                      (file) => Padding(
                                        padding: EdgeInsets.symmetric(
                                            vertical: 5.w, horizontal: 10.w),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            /// ICON + TITLE
                                            Row(
                                              children: [
                                                CustomContainer(
                                                  radius: 8,
                                                  bgColor: secondBlueColor
                                                      .withOpacity(0.3),
                                                  widget: Padding(
                                                    padding:
                                                        EdgeInsets.all(5.r),
                                                    child: SvgPicture.asset(
                                                      file.type.toLowerCase() ==
                                                              'audio'
                                                          ? 'assets/icons/audiotrack.svg'
                                                          : 'assets/icons/pdf.svg',
                                                      width: 16,
                                                      height: 16,
                                                      color: whiteColor,
                                                    ),
                                                  ),
                                                ),
                                                8.horizontalSpace,
                                                Expanded(
                                                  child: CustomTextWigdet(
                                                    title: file.title,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                    textColor: secondWhiteColor,
                                                  ),
                                                ),
                                              ],
                                            ),

                                            10.verticalSpace,

                                            /// BUTTONS
                                            if (file.type.toLowerCase() ==
                                                'audio') ...[
                                              // Audio Player Widget for audio files
                                              AudioPlayerWidget(
                                                audioUrl: file.file,
                                                title: file.title,
                                                showFullControls: true,
                                              ),
                                              10.verticalSpace,
                                              // Download button for audio
                                              CustomFilledButtonWidget(
                                                onPressed: () {
                                                  FileDownloadUtil.downloadFile(
                                                    file.file,
                                                    fileName: file.title,
                                                  );
                                                },
                                                withIcon: true,
                                                assetName:
                                                    'assets/icons/download_2.svg',
                                                title: 'Download',
                                                fontColor: whiteColor,
                                                fontSize: 14,
                                                widthIcon: 20,
                                                heightIcon: 20,
                                                widthButton: double.infinity,
                                                heightButton: 38,
                                                radius: 6,
                                                bgColor: Colors.transparent,
                                                isOutlined: true,
                                                borderColor: secondWhiteColor
                                                    .withOpacity(0.3),
                                              ),
                                            ] else ...[
                                              // Regular buttons for non-audio files
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CustomFilledButtonWidget(
                                                    onPressed: () {
                                                      FileDownloadUtil
                                                          .downloadFile(
                                                        file.file,
                                                        fileName: file.title,
                                                      );
                                                    },
                                                    withIcon: true,
                                                    assetName:
                                                        'assets/icons/download_2.svg',
                                                    title: 'Download',
                                                    fontColor: whiteColor,
                                                    fontSize: 14,
                                                    widthIcon: 20,
                                                    heightIcon: 20,
                                                    widthButton: 135,
                                                    heightButton: 38,
                                                    radius: 6,
                                                    bgColor: Colors.transparent,
                                                    isOutlined: true,
                                                    borderColor:
                                                        secondWhiteColor
                                                            .withOpacity(0.3),
                                                  ),
                                                  12.horizontalSpace,
                                                  CustomFilledButtonWidget(
                                                    onPressed: () {
                                                      Get.to(() => PDFViwerPage(
                                                            path: file.file,
                                                            title: file.title,
                                                            subtitle: mapelName,
                                                          ));
                                                    },
                                                    withIcon: true,
                                                    assetName:
                                                        'assets/icons/eye.svg',
                                                    title: 'Read',
                                                    fontColor: whiteColor,
                                                    fontSize: 14,
                                                    widthIcon: 20,
                                                    heightIcon: 20,
                                                    widthButton: 120,
                                                    heightButton: 38,
                                                    radius: 6,
                                                    bgColor: Colors.transparent,
                                                    isOutlined: true,
                                                    borderColor:
                                                        secondWhiteColor
                                                            .withOpacity(0.3),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            20.verticalSpace,
            CustomTextWigdet(
              title: "Presence History",
              fontSize: 16,
              fontWeight: FontWeight.w500,
              textColor: secondWhiteColor.withValues(
                alpha: 0.8,
              ),
            ),
            10.verticalSpace,
            Obx(() => SizedBox(
                  height: 1050.h,
                  child: SingleChildScrollView(
                    child: Column(
                      children:
                          historyController.historyAttendance.map((attendance) {
                        final isExpanded = false.obs;
                        final dateFormatted = DateFormat('dd MMM yyyy')
                            .format(DateTime.parse(attendance.date.toString()));
                        final clockInTime = attendance.clockIn != null
                            ? DateFormat('HH:mm').format(attendance.clockIn!)
                            : '-';
                        final clockOutTime = attendance.clockOut != null
                            ? DateFormat('HH:mm').format(attendance.clockOut!)
                            : '-';
                        final clockInPhoto = attendance.clockInPhoto;
                        final clockOutPhoto = attendance.clockOutPhoto;

                        return Obx(
                          () => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5),
                            child: CustomContainer(
                              radius: 8,
                              bgColor: baseBlueColor.withOpacity(0.6),
                              widget: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GestureDetector(
                                    onTap: () => isExpanded.toggle(),
                                    child: CustomContainer(
                                      bgColor: baseBlueColor.withOpacity(0.6),
                                      radius: 8,
                                      widget: Padding(
                                        padding: EdgeInsets.all(10.r),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomTextWigdet(
                                              title: dateFormatted,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              textColor: whiteColor,
                                            ),
                                            Row(
                                              children: [
                                                CustomContainer(
                                                  radius: 4,
                                                  bgColor: (attendance
                                                                  .clockIn !=
                                                              null &&
                                                          attendance.clockOut !=
                                                              null)
                                                      ? greenColor
                                                      : (attendance.clockIn !=
                                                                  null &&
                                                              attendance
                                                                      .clockOut ==
                                                                  null)
                                                          ? goldenYellow
                                                              .withValues(
                                                                  alpha: 0.7)
                                                          : redColor,
                                                  widget: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 10,
                                                        vertical: 4),
                                                    child: CustomTextWigdet(
                                                      title: (attendance
                                                                      .clockIn !=
                                                                  null &&
                                                              attendance
                                                                      .clockOut !=
                                                                  null)
                                                          ? 'Hadir Penuh'
                                                          : (attendance.clockIn !=
                                                                      null &&
                                                                  attendance
                                                                          .clockOut ==
                                                                      null)
                                                              ? 'Hadir Sebagian'
                                                              : 'Tidak Hadir',
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      textColor: whiteColor,
                                                    ),
                                                  ),
                                                ),
                                                5.horizontalSpace,
                                                Icon(
                                                  isExpanded.value
                                                      ? Icons
                                                          .keyboard_arrow_up_rounded
                                                      : Icons
                                                          .keyboard_arrow_down_rounded,
                                                  color: secondWhiteColor
                                                      .withOpacity(0.8),
                                                  size: 30.r,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  if (isExpanded.value) ...[
                                    //TIME
                                    20.verticalSpace,
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.w),
                                      child: CustomContainer(
                                        bgColor: baseBlueColor.withOpacity(0.6),
                                        radius: 8,
                                        widget: Padding(
                                          padding: EdgeInsets.all(10.r),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    CustomTextWigdet(
                                                      title: 'Clock In:',
                                                      fontSize: 14,
                                                      textColor: whiteColor,
                                                    ),
                                                    CustomTextWigdet(
                                                      title: "$clockInTime",
                                                      fontSize: 14,
                                                      textColor: whiteColor,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              10.horizontalSpace,
                                              Expanded(
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    CustomTextWigdet(
                                                      title: 'Clock Out:',
                                                      fontSize: 14,
                                                      textColor: whiteColor,
                                                    ),
                                                    CustomTextWigdet(
                                                      title: "$clockOutTime",
                                                      fontSize: 14,
                                                      textColor: whiteColor,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    //FOTO
                                    10.verticalSpace,
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.w),
                                      child: CustomContainer(
                                        bgColor: baseBlueColor.withOpacity(0.6),
                                        radius: 8,
                                        widget: Padding(
                                          padding: EdgeInsets.all(10.r),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    CustomTextWigdet(
                                                      title: 'Clock In Photo',
                                                      fontSize: 14,
                                                      textColor: whiteColor
                                                          .withOpacity(0.7),
                                                    ),
                                                    6.verticalSpace,
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      child: Image.network(
                                                        clockInPhoto,
                                                        height: 180,
                                                        width: double.infinity,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (context,
                                                                error,
                                                                stackTrace) =>
                                                            Container(
                                                          height: 180,
                                                          color: baseBlueColor,
                                                          alignment:
                                                              Alignment.center,
                                                          child: Icon(
                                                              Icons
                                                                  .broken_image,
                                                              color:
                                                                  whiteColor),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              10.horizontalSpace,
                                              Expanded(
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    CustomTextWigdet(
                                                      title: 'Clock Out Photo',
                                                      fontSize: 14,
                                                      textColor: whiteColor
                                                          .withOpacity(0.7),
                                                    ),
                                                    6.verticalSpace,
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      child: Image.network(
                                                        clockOutPhoto,
                                                        height: 180,
                                                        width: double.infinity,
                                                        fit: BoxFit.cover,
                                                        errorBuilder: (context,
                                                                error,
                                                                stackTrace) =>
                                                            Container(
                                                          height: 180,
                                                          color: baseBlueColor,
                                                          alignment:
                                                              Alignment.center,
                                                          child: Icon(
                                                              Icons
                                                                  .broken_image,
                                                              color:
                                                                  whiteColor),
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ]
                                ],
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                )),
          ],
        ),
      ),
    );
  }
}
