import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/event/event_model.dart';
import 'package:mides_skadik/widgets/course/schedule/event/all_event/all_landscape.dart';
import 'package:mides_skadik/widgets/course/schedule/event/card_event/card_event.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event_controller.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class All extends StatelessWidget {
  const All({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EventController>();

    return Obx(() {
      if (controller.isLoading.value && controller.events.isEmpty) {
        return const Center(child: CustomLoadingWidget());
      }

      if (!controller.isLoading.value && controller.events.isEmpty) {
        return const Center(
          child: CustomTextWigdet(
            title: "Tidak ada data pada ALL.",
            fontSize: 16,
            fontWeight: FontWeight.w500,
            textColor: Colors.white,
          ),
        );
      }

      final isLandscape =
          MediaQuery.of(context).orientation == Orientation.landscape;

      if (isLandscape) return const AllLandscape();

      return GridView.builder(
        controller: controller.scrollController,
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
        physics: const AlwaysScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 20.h,
          crossAxisSpacing: 20.w,
          childAspectRatio: 0.8,
        ),
        itemCount: controller.events.length + 1,
        itemBuilder: (context, index) {
          if (index == controller.events.length) {
            return controller.isLoadMore.value
                ? const Center(child: CustomLoadingWidget())
                : const SizedBox();
          }

          CalendarEvent event = controller.events[index];

          return CardEvent(
            title: event.title,
            thumbnail: event.thumbnail,
            description: event.note ?? '-',
            date: DateFormat('EEEE, d MMMM yyyy').format(event.date),
            time: "${event.startTime} – ${event.endTime}",
            participant: event.participantType,
            status: event.eventStatus,
            onPressed: () => controller.openDetailEvent(event),
          );
        },
      );
    });
  }
}
