import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/event/event_model.dart';
import 'package:mides_skadik/app/data/services/course/schedule/event/event_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:http/http.dart' as http;

class EventController extends GetxController {
  var selectMenuEvent = 0.obs;
  var selectEventTab = 0.obs;
  RxList<CalendarEvent> events = <CalendarEvent>[].obs;
  RxBool isLoading = false.obs;
  final EventService _service = EventService();
  final isOpenDetailCardEvent = false.obs;
  final selectedEvent = Rxn<CalendarEvent>();
  RxInt currentPage = 1.obs;
  RxBool isLoadMore = false.obs;
  ScrollController scrollController = ScrollController();

  void selectedMenuEvent(int index) {
    selectMenuEvent.value = index;
    fetchEventList(); // fetch data sesuai tab
  }

  void openDetailEvent(CalendarEvent event) {
    selectedEvent.value = event;
    isOpenDetailCardEvent.value = true;
  }

  void closeDetailEvent() {
    selectedEvent.value = null;
    isOpenDetailCardEvent.value = false;
  }

  EventStatus get eventStatusParam {
    switch (selectMenuEvent.value) {
      case 0:
        return EventStatus.All;
      case 1:
        return EventStatus.Upcoming;
      case 2:
        return EventStatus.Complete;
      default:
        return EventStatus.All;
    }
  }

  Future<String> getFileSizeFormatted(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      if (response.statusCode == 200 &&
          response.headers['content-length'] != null) {
        final bytes = int.tryParse(response.headers['content-length']!) ?? 0;
        return _formatFileSize(bytes);
      }
    } catch (e) {
      LogService.log.e("Error fetching file size: $e");
    }
    return "-";
  }

  String _formatFileSize(int sizeInBytes) {
    if (sizeInBytes < 1024) {
      return "$sizeInBytes B";
    } else if (sizeInBytes < 1024 * 1024) {
      return "${(sizeInBytes / 1024).toStringAsFixed(2)} KB";
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return "${(sizeInBytes / (1024 * 1024)).toStringAsFixed(2)} MB";
    } else {
      return "${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB";
    }
  }

  Widget getFileIcon(String fileUrl) {
    final extension = fileUrl.split('.').last.toLowerCase();
    if (extension == 'pdf') {
      return Icon(
        Icons.picture_as_pdf_outlined,
        color: whiteColor,
        size: 25.r,
      );
    } else if (["jpg", "jpeg", "png", "webp"].contains(extension)) {
      return Icon(
        Icons.image_outlined,
        color: whiteColor,
        size: 25.r,
      );
    } else if (["doc", "docx"].contains(extension)) {
      return Icon(
        Icons.description_outlined,
        color: whiteColor,
        size: 25.r,
      );
    } else if (["ppt", "pptx"].contains(extension)) {
      return Icon(
        Icons.slideshow_outlined,
        color: whiteColor,
        size: 25.r,
      );
    } else if (["xls", "xlsx"].contains(extension)) {
      return Icon(
        Icons.grid_on_outlined,
        color: whiteColor,
        size: 25.r,
      );
    } else {
      return Icon(
        Icons.insert_drive_file_outlined,
        color: whiteColor,
        size: 25.r,
      );
    }
  }

  // Event Data
  void fetchEventList({bool isLoadMoreRequest = false}) async {
    if (!isLoadMoreRequest) {
      isLoading.value = true;
      currentPage.value = 1;
    } else {
      isLoadMore.value = true;
      currentPage.value += 1;
    }

    final result = await _service.getListEvent(
      page: currentPage.value,
      eventStatus: eventStatusParam,
    );

    if (result.isSuccess) {
      if (isLoadMoreRequest) {
        events.addAll(result.resultValue ?? []);
      } else {
        events.value = result.resultValue ?? [];
      }
    }

    isLoading.value = false;
    isLoadMore.value = false;
  }

  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        fetchEventList(isLoadMoreRequest: true);
      }
    });
  }

  @override
  void onReady() {
    super.onReady();
    fetchEventList();
  }
}
