import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/schedule/event/card_event/card_event_Landscape.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CardEvent extends StatelessWidget {
  final String? title;
  final String? description;
  final String? date;
  final String? time;
  final String? participant;
  final String? status;
  final String? thumbnail;
  final Function()? onPressed;

  const CardEvent({
    super.key,
    this.title,
    this.description,
    this.date,
    this.time,
    this.participant,
    this.status,
    this.onPressed,
    this.thumbnail,
  });

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    if (isLandscape) {
      return CardEventLandscape(
        title: title,
        description: description,
        date: date,
        time: time,
        participant: participant,
        status: status,
        thumbnail: thumbnail,
        onPressed: onPressed,
      );
    }

    return CustomContainer(
      radius: 8,
      width: 421,
      bgColor: baseBlueColor.withValues(alpha: 0.3),
      widget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              CustomContainer(
                height: 260,
                width: double.infinity,
                widget: thumbnail != null && thumbnail!.isNotEmpty
                    ? Image.network(
                        thumbnail!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return CustomContainer(
                            bgColor: baseBlueColor.withValues(alpha: 0.3),
                            widget: Center(
                              child: Icon(
                                Icons.broken_image,
                                color: whiteColor,
                                size: 40.r,
                              ),
                            ),
                          );
                        },
                      )
                    : CustomContainer(
                        bgColor: baseBlueColor.withValues(alpha: 0.3),
                        widget: Center(
                          child: Icon(
                            Icons.broken_image,
                            color: whiteColor,
                            size: 40.r,
                          ),
                        ),
                      ),
              ),
              Positioned(
                top: 12,
                right: 12,
                child: CustomContainer(
                  bgColor: status?.toLowerCase() == "upcoming"
                      ? goldenYellow
                      : greyColor,
                  radius: 4,
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                  widget: CustomTextWigdet(
                    title: (status ?? 'Unknown').toUpperCase(),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    textColor: whiteColor,
                  ),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: title ?? '-',
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  textColor: whiteColor,
                ),
                4.verticalSpace,
                CustomTextWigdet(
                  title: description ?? '-',
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  textColor: secondWhiteColor,
                  maxLines: 2,
                ),
                16.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset('assets/icons/calendar.svg',
                        color: secondWhiteColor, width: 16.w),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: date ?? '-',
                      fontSize: 12,
                      textColor: secondWhiteColor,
                    ),
                  ],
                ),
                8.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset('assets/icons/timer.svg',
                        color: secondWhiteColor, width: 16.w),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: time ?? '-',
                      fontSize: 12,
                      textColor: secondWhiteColor,
                    ),
                  ],
                ),
                8.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset('assets/icons/group_person.svg',
                        color: secondWhiteColor, width: 16.w),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: participant ?? '-',
                      fontSize: 12,
                      textColor: secondWhiteColor,
                    ),
                  ],
                ),
                16.verticalSpace,
                CustomFilledButtonWidget(
                  onPressed: onPressed,
                  title: 'See Detail',
                  heightButton: 40,
                  widthButton: double.infinity,
                  radius: 6,
                  fontSize: 14,
                  fontColor: whiteColor,
                  bgColor: blueColor,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
