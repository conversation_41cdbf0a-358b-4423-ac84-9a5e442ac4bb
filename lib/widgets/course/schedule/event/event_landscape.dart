import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/schedule/event/all_event/all.dart';
import 'package:mides_skadik/widgets/course/schedule/event/completed_event/completed.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event_controller.dart';
import 'package:mides_skadik/widgets/course/schedule/event/upcoming_event/upcoming.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class EventLandscape extends StatelessWidget {
  const EventLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final controllerEvent = Get.put(EventController());

    return SizedBox(
      height: 900.h,
      child: CustomContainer(
        widget: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                children: [
                  InkWell(
                    onTap: () => controllerEvent.selectedMenuEvent(0),
                    child: CustomTextWigdet(
                      title: 'All',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                  50.horizontalSpace,
                  InkWell(
                    onTap: () => controllerEvent.selectedMenuEvent(1),
                    child: CustomTextWigdet(
                      title: 'Upcoming',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                  50.horizontalSpace,
                  InkWell(
                    onTap: () => controllerEvent.selectedMenuEvent(2),
                    child: CustomTextWigdet(
                      title: 'Completed',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
            ),
            10.verticalSpace,
            Obx(
              () => Container(
                width: Get.width,
                height: 1,
                color: greyColor,
                child: Row(
                  children: [
                    CustomContainer(
                      width: 50,
                      height: 5,
                      bgColor: controllerEvent.selectMenuEvent.value == 0
                          ? blueColor
                          : greyColor.withOpacity(0),
                    ),
                    20.horizontalSpace,
                    CustomContainer(
                      width: 120,
                      height: 5,
                      bgColor: controllerEvent.selectMenuEvent.value == 1
                          ? blueColor
                          : greyColor.withOpacity(0),
                    ),
                    20.horizontalSpace,
                    CustomContainer(
                      width: 130,
                      height: 5,
                      bgColor: controllerEvent.selectMenuEvent.value == 2
                          ? blueColor
                          : greyColor.withOpacity(0),
                    ),
                  ],
                ),
              ),
            ),
            32.verticalSpace,
            Expanded(
              child: Obx(() {
                switch (controllerEvent.selectMenuEvent.value) {
                  case 0:
                    return const All();
                  case 1:
                    return const Upcoming();
                  case 2:
                    return const Completed();
                  default:
                    return const SizedBox();
                }
              }),
            ),
          ],
        ),
      ),
    );
  }
}
