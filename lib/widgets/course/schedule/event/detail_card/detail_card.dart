import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/models/response/course/schedule/event/event_model.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event_controller.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class DetailCard extends StatelessWidget {
  final CalendarEvent event;

  const DetailCard({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    final controllerEvent = Get.put(EventController());
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 9, sigmaY: 9),
        child: CustomContainer(
          width: 818,
          radius: 8,
          bgColor: baseBlueColor.withValues(alpha: 0.8),
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 34.w),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Center(
                          child: CustomTextWigdet(
                            title: 'Event Detail',
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            textColor: whiteColor,
                          ),
                        ),
                      ),
                      CustomFilledButtonWidget(
                        onPressed: () {
                          controllerEvent.closeDetailEvent();
                        },
                        withIcon: true,
                        onlyIcon: true,
                        heightIcon: 32,
                        widthIcon: 32,
                        assetName: 'assets/icons/close2.svg',
                        bgColor: Colors.transparent,
                      ),
                    ],
                  ),
                  40.verticalSpace,
                  CustomContainer(
                    width: 750,
                    height: 400,
                    radius: 7,
                    widget: event.thumbnail != null
                        ? Image.network(
                            event.thumbnail!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return CustomContainer(
                                widget: Center(
                                  child: Icon(
                                    Icons.broken_image,
                                    color: whiteColor,
                                    size: 40.r,
                                  ),
                                ),
                              );
                            },
                          )
                        : Image.asset("assets/images/quiz.png",
                            fit: BoxFit.cover),
                  ),
                  40.verticalSpace,
                  CustomTextWigdet(
                    title: event.title,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    textColor: whiteColor,
                  ),
                  4.verticalSpace,
                  CustomTextWigdet(
                    title: event.note ?? '-',
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    textColor: secondWhiteColor,
                  ),
                  40.verticalSpace,
                  CustomTextWigdet(
                    title: "Event Detail",
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textColor: secondWhiteColor,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SvgPicture.asset('assets/icons/calendar.svg',
                                    width: 24.h,
                                    height: 24.w,
                                    color: whiteColor),
                                10.horizontalSpace,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Date',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      textColor: secondWhiteColor,
                                    ),
                                    CustomTextWigdet(
                                      title: DateFormat('MMM dd yyyy')
                                          .format(event.date),
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      textColor: whiteColor,
                                    ),
                                  ],
                                )
                              ],
                            ),
                            20.verticalSpace,
                            Row(
                              children: [
                                SvgPicture.asset('assets/icons/location_on.svg',
                                    width: 24.h,
                                    height: 24.w,
                                    color: whiteColor),
                                10.horizontalSpace,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Location',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      textColor: secondWhiteColor,
                                    ),
                                    CustomTextWigdet(
                                      title: event.location ?? '-',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      textColor: whiteColor,
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                      16.horizontalSpace,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SvgPicture.asset('assets/icons/time.svg',
                                    width: 24.h,
                                    height: 24.w,
                                    color: whiteColor),
                                10.horizontalSpace,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Time',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      textColor: secondWhiteColor,
                                    ),
                                    CustomTextWigdet(
                                      title:
                                          '${event.startTime} - ${event.endTime}',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      textColor: whiteColor,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            20.verticalSpace,
                            Row(
                              children: [
                                SvgPicture.asset(
                                    'assets/icons/group_person.svg',
                                    width: 24.h,
                                    height: 24.w,
                                    color: whiteColor),
                                10.horizontalSpace,
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Participant',
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      textColor: secondWhiteColor,
                                    ),
                                    CustomTextWigdet(
                                      title: event.participantType,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      textColor: whiteColor,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  32.verticalSpace,
                  CustomTextWigdet(
                    title: 'ABOUT EVENT',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textColor: secondWhiteColor,
                  ),
                  10.verticalSpace,
                  CustomTextWigdet(
                    title: event.description ?? '-',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textColor: secondWhiteColor,
                  ),
                  if (event.users.isNotEmpty) ...[
                    10.verticalSpace,
                    CustomTextWigdet(
                      title: 'Narasumber: ${event.users.first.name}',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: secondWhiteColor,
                    )
                  ],
                  if (event.file != null) ...[
                    32.verticalSpace,
                    CustomTextWigdet(
                      title: 'DOWNLOAD MATERIAL',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: secondWhiteColor,
                    ),
                    10.verticalSpace,
                    CustomContainer(
                      width: double.infinity,
                      radius: 8,
                      border: Border.all(color: whiteColor, width: 0.1),
                      widget: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: 14.h, horizontal: 12.w),
                        child: Row(
                          children: [
                            CustomContainer(
                              height: 40,
                              width: 40,
                              radius: 8,
                              bgColor: greyColor,
                              widget: controllerEvent.getFileIcon(event.file!),
                            ),
                            10.horizontalSpace,
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomTextWigdet(
                                    title: event.title,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor,
                                  ),
                                  FutureBuilder<String>(
                                    future: controllerEvent
                                        .getFileSizeFormatted(event.file!),
                                    builder: (context, snapshot) {
                                      final fileSizeText =
                                          snapshot.connectionState ==
                                                      ConnectionState.done &&
                                                  snapshot.hasData
                                              ? snapshot.data!
                                              : "Loading size...";

                                      return CustomTextWigdet(
                                        title: fileSizeText,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        textColor: secondWhiteColor,
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                            CustomFilledButtonWidget(
                              onPressed: () {
                                FileDownloadUtil.downloadFile(
                                  event.file!,
                                  fileName: event.title,
                                );
                              },
                              withIcon: true,
                              assetName: "assets/icons/download_2.svg",
                              widthIcon: 20,
                              heightIcon: 20,
                              title: "Download",
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontColor: whiteColor,
                              bgColor: Colors.transparent,
                              isOutlined: true,
                              outlineWidth: 0.3,
                              borderColor: secondWhiteColor,
                              radius: 8,
                              widthButton: 141,
                              heightButton: 36,
                            )
                          ],
                        ),
                      ),
                    ),
                  ]
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
