import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/widgets/course/schedule/event/card_event/card_event.dart';
import 'package:mides_skadik/widgets/course/schedule/event/event_controller.dart';
import 'package:mides_skadik/widgets/course/schedule/event/upcoming_event/upcoming_landscape.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class Upcoming extends StatelessWidget {
  const Upcoming({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EventController>();

    return Obx(() {
      final events = controller.events
          .where((e) => e.eventStatus?.toLowerCase() == 'upcoming')
          .toList();

      if (controller.isLoading.value && events.isEmpty) {
        return const Center(child: CustomLoadingWidget());
      }

      if (!controller.isLoading.value && controller.events.isEmpty) {
        return const Center(
          child: CustomTextWigdet(
            title: "Tidak ada data pada UPCOMING.",
            fontSize: 16,
            fontWeight: FontWeight.w500,
            textColor: Colors.white,
          ),
        );
      }

      final isLandscape =
          MediaQuery.of(context).orientation == Orientation.landscape;

      if (isLandscape) return const UpcomingLandscape();

      return GridView.builder(
        controller: controller.scrollController,
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          mainAxisSpacing: 20.h,
          crossAxisSpacing: 20.w,
          childAspectRatio: 0.8,
        ),
        itemCount: events.length + 1,
        itemBuilder: (context, index) {
          if (index == events.length) {
            return controller.isLoadMore.value
                ? const Center(child: CustomLoadingWidget())
                : const SizedBox();
          }

          final event = events[index];
          return CardEvent(
            title: event.title,
            thumbnail: event.thumbnail,
            description: event.note ?? '-',
            date: DateFormat('EEEE, d MMMM yyyy').format(event.date),
            time: "${event.startTime} – ${event.endTime}",
            participant: event.participantType,
            status: event.eventStatus,
            onPressed: () => controller.openDetailEvent(event),
          );
        },
      );
    });
  }
}
