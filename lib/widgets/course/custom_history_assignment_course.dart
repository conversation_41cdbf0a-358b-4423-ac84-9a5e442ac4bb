import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/detail_assignment_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomHistoryAssignmentCourse extends StatelessWidget {
  int index;
  CustomHistoryAssignmentCourse({super.key, this.index = 0});

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
        // width: Get.width,
        // height: Get.height,
        radius: 12,
        // bgColor: secondBlueColor.withOpacity(0.10),
        widget: Container(
          margin: const EdgeInsets.symmetric(vertical: 12),
          padding: EdgeInsets.symmetric(vertical: 28.h, horizontal: 24.w),
          width: Get.width,
          // height: 92.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: secondBlueColor.withOpacity(0.19),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 32,
                height: 32,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: secondBlueColor.withOpacity(0.1),
                ),
                child: SvgPicture.asset(
                  'assets/icons/calendar.svg',
                  width: 10,
                  height: 10,
                  color: secondWhiteColor,
                ),
              ),
              24.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("ASSIGNEMT ${index + 1}",
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: whiteColor,
                      )),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const CircleAvatar(
                        radius: 10,
                        backgroundImage: AssetImage("assets/images/Person.jpg"),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      CustomTextWigdet(
                        title: "Yoshua Kaesang",
                        fontSize: 18,
                        fontWeight: FontWeight.w300,
                        textColor: whiteColor,
                      )
                    ],
                  ),
                ],
              ),
              // const Spacer(),
              10.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Time Remaining",
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: whiteColor,
                      )),
                  10.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            'assets/icons/calendar.svg',
                            width: 26.w,
                            height: 26.h,
                            color: secondWhiteColor,
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          CustomTextWigdet(
                            title: "3 Days, 2 Hours",
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                            textColor: whiteColor,
                          )
                        ],
                      )
                    ],
                  ),
                ],
              ),
              100.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Mapel",
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: whiteColor,
                      )),
                  10.horizontalSpace,
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: whiteColor.withOpacity(0.1),
                    ),
                    child: CustomTextWigdet(
                      title: "Matematika",
                      fontSize: 18,
                      fontWeight: FontWeight.w300,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              100.horizontalSpace,
              CustomFilledButtonWidget(
                  onPressed: () {
                    Get.to(DetailAssignmentView());
                  },
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  heightButton: 44,
                  fontSize: 16,
                  radius: 10,
                  title: "Submited",
                  bgColor: blueColor,
                  fontColor: whiteColor),
            ],
          ),
        ));
  }
}
