import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class CustomCalender extends StatefulWidget {
  final List<DateTime>? selected;
  final Function(int month)? onMonthChanged;
  final Function(int year)? onYearChanged;

  const CustomCalender({
    super.key,
    this.selected,
    this.onMonthChanged,
    this.onYearChanged,
  });

  @override
  State<CustomCalender> createState() => _CustomCalenderState();
}

class _CustomCalenderState extends State<CustomCalender> {
  final DateRangePickerController dateRangePickerController =
      DateRangePickerController();
  late int selectedMonth;
  late int selectedYear;

  @override
  void initState() {
    super.initState();
    selectedMonth = DateTime.now().month;
    selectedYear = DateTime.now().year;
    WidgetsBinding.instance.addPostFrameCallback((e) {
      dateRangePickerController.displayDate =
          DateTime(selectedYear, selectedMonth);
      dateRangePickerController.selectedDates = widget.selected ?? [];
    });
  }

  void updateCalendar() {
    setState(() {
      dateRangePickerController.displayDate =
          DateTime(selectedYear, selectedMonth);
    });
  }

  @override
  Widget build(BuildContext context) {
    dateRangePickerController.displayDate =
        DateTime(selectedYear, selectedMonth);
    dateRangePickerController.selectedDates = widget.selected ?? [];
    return CustomContainer(
      // width: 400,
      bgColor: bgCalendar,
      radius: 16,
      widget: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 8.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                //
                Row(
                  children: [
                    4.horizontalSpace,
                    // Month Dropdown
                    Expanded(
                      child: CustomContainer(
                        bgColor: secondBlueColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        widget: DropdownButton<int>(
                          value: selectedMonth,
                          dropdownColor: baseBlueColor.withOpacity(0.6),
                          isExpanded: true,
                          isDense: true,
                          elevation: 0,
                          borderRadius: BorderRadius.circular(4),
                          padding: const EdgeInsets.fromLTRB(8, 0, 2, 0),
                          underline: const SizedBox.shrink(),
                          items: List.generate(12, (index) {
                            return DropdownMenuItem(
                              value: index + 1,
                              child: CustomTextWigdet(
                                title: DateFormat.MMMM()
                                    .format(DateTime(0, index + 1)),
                                textColor: whiteColor,
                                fontSize: 28.sp,
                              ),
                            );
                          }),
                          onChanged: (value) {
                            if (value != null) {
                              selectedMonth = value;
                              updateCalendar();

                              if (widget.onMonthChanged != null) {
                                widget.onMonthChanged!(value);
                              }
                            }
                          },
                        ),
                      ),
                    ),
                    8.horizontalSpace,
                    // Year Dropdown
                    Expanded(
                      child: CustomContainer(
                        bgColor: secondBlueColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        widget: DropdownButton<int>(
                          value: selectedYear,
                          dropdownColor: baseBlueColor.withOpacity(0.8),
                          isExpanded: true,
                          isDense: true,
                          elevation: 0,
                          borderRadius: BorderRadius.circular(4),
                          padding: const EdgeInsets.fromLTRB(8, 0, 2, 0),
                          underline: const SizedBox.shrink(),
                          items: List.generate(10, (index) {
                            int year = DateTime.now().year - 5 + index;
                            return DropdownMenuItem(
                              value: year,
                              child: CustomTextWigdet(
                                title: year.toString(),
                                textColor: whiteColor,
                                fontSize: 28.sp,
                              ),
                            );
                          }),
                          onChanged: (value) {
                            if (value != null) {
                              selectedYear = value;
                              updateCalendar();

                              if (widget.onYearChanged != null) {
                                widget.onYearChanged!(value);
                              }
                            }
                          },
                        ),
                      ),
                    ),

                    4.horizontalSpace,
                  ],
                ),

                SizedBox(
                  height: 200,
                  child: SfDateRangePicker(
                    controller: dateRangePickerController,
                    selectionMode: DateRangePickerSelectionMode.multiple,
                    selectionShape: DateRangePickerSelectionShape.rectangle,
                    navigationMode: DateRangePickerNavigationMode.none,
                    rangeSelectionColor: secondBlueColor.withOpacity(0.2),
                    startRangeSelectionColor: blueColor,
                    endRangeSelectionColor: blueColor,
                    backgroundColor: Colors.transparent,
                    rangeTextStyle: TextStyle(color: whiteColor),
                    initialSelectedRange: PickerDateRange(
                      DateTime.now(),
                      DateTime.now().add(
                        const Duration(days: 2),
                      ),
                    ),
                    onSelectionChanged: (val) {},
                    selectionColor: Colors.transparent,
                    selectionRadius: 4,
                    yearCellStyle: DateRangePickerYearCellStyle(
                      textStyle: TextStyle(
                        color: whiteColor,
                        fontWeight: FontWeight.w300,
                      ),
                      todayTextStyle: TextStyle(
                        color: whiteColor,
                      ),
                      todayCellDecoration: BoxDecoration(
                        shape: BoxShape.rectangle,
                        color: blueColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      cellDecoration: BoxDecoration(
                        shape: BoxShape.rectangle,
                        color: Colors.black.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    headerStyle: DateRangePickerHeaderStyle(
                      textAlign: TextAlign.center,
                      textStyle: TextStyle(
                        color: whiteColor,
                      ),
                      backgroundColor: Colors.transparent,
                    ),
                    headerHeight: 0,
                    monthViewSettings: DateRangePickerMonthViewSettings(
                      viewHeaderStyle: DateRangePickerViewHeaderStyle(
                        textStyle: TextStyle(
                          color: whiteColor,
                          fontWeight: FontWeight.w300,
                          fontSize: 16.sp,
                        ),
                      ),
                      firstDayOfWeek: 1,
                      dayFormat: 'EEE',
                      showTrailingAndLeadingDates: true,
                    ),
                    monthCellStyle: DateRangePickerMonthCellStyle(
                      textStyle: TextStyle(
                          color: whiteColor, fontWeight: FontWeight.w300),
                      todayTextStyle: TextStyle(
                        color: whiteColor,
                      ),
                      todayCellDecoration: BoxDecoration(
                        shape: BoxShape.rectangle,
                        color: blueColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      cellDecoration: BoxDecoration(
                        shape: BoxShape.rectangle,
                        color: Colors.black.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    cellBuilder: (context, cellDetails) {
                      return CustomContainer(
                        bgColor: cellDetails.date.month == selectedMonth
                            ? (((widget.selected ?? []).map(
                                    (e) =>
                                        DateFormat.yMMMd().format(e))).contains(
                                    DateFormat.yMMMd().format(cellDetails.date))
                                ? blueColor
                                : secondBlueColor.withOpacity(0.1))
                            : null,
                        borderRadius: BorderRadius.circular(4),
                        alignment: Alignment.center,
                        widget: CustomTextWigdet(
                          title: cellDetails.date.day.toString(),
                          fontSize: 28.sp,
                          textColor: cellDetails.date.month == selectedMonth
                              ? whiteColor
                              : secondWhiteColor.withOpacity(0.6),
                          fontWeight: cellDetails.date.month == selectedMonth
                              ? FontWeight.w500
                              : FontWeight.w400,
                        ),
                        margin: const EdgeInsets.all(1),
                      );
                    },
                    initialSelectedDates: widget.selected,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
