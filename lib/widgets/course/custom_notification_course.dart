import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/annoucement_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/controllers/course_notification_controller.dart';
import 'package:mides_skadik/widgets/course/dashboard/academic_notification/academic_notification_item.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomNotificationCourse extends StatefulWidget {
  const CustomNotificationCourse({Key? key}) : super(key: key);

  @override
  State<CustomNotificationCourse> createState() =>
      _CustomNotificationCourseState();
}

class _CustomNotificationCourseState extends State<CustomNotificationCourse> {
  final notificationController = Get.find<CourseNotificationController>();
  late final AnnouncementController annoucementController;

  @override
  void initState() {
    super.initState();
    // Try to find existing controller first, if not found then create one
    if (Get.isRegistered<AnnouncementController>()) {
      annoucementController = Get.find<AnnouncementController>();
    } else {
      annoucementController = Get.put(AnnouncementController());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (notificationController.isOpen() == false) {
        return Container();
      }
      return Stack(
        children: [
          Center(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.0),
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: 4,
                  sigmaY: 4,
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 4,
                    sigmaY: 4,
                  ),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 4,
                      sigmaY: 4,
                    ),
                    child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: notificationController.isExpand()
                            ? Get.width - 32
                            : Get.width / 2,
                        padding: const EdgeInsets.fromLTRB(16, 8, 8, 8),
                        decoration: BoxDecoration(
                          color: darkGreyColor.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Expanded(
                                  child: CustomTextWigdet(
                                    title: "Notification",
                                    fontSize: 24,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    notificationController.expand();
                                  },
                                  icon: SvgPicture.asset(
                                    "assets/icons/scale.svg",
                                    width: 18,
                                  ),
                                ),
                                IconButton(
                                  onPressed: () {
                                    notificationController.close();
                                  },
                                  icon: SvgPicture.asset(
                                    "assets/icons/close2.svg",
                                    width: 18,
                                  ),
                                ),
                              ],
                            ),
                            Obx(() => annoucementController.list.isEmpty
                                ? const CustomContainer(
                                    widget: CustomTextWigdet(
                                        title: "No Notification"),
                                  )
                                : Expanded(
                                    child: ListView.separated(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemBuilder: (context, index) {
                                          return AcademicNotificationItemWidget(
                                            title: annoucementController
                                                .list[index].title,
                                            subtitle: annoucementController
                                                .list[index].description,
                                            dateTime: annoucementController
                                                .list[index].createdAt,
                                          );
                                        },
                                        separatorBuilder: (context, index) {
                                          return 8.verticalSpace;
                                        },
                                        itemCount:
                                            annoucementController.list.length),
                                  )),
                            16.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                CustomButtonOutlined(
                                  icon: "assets/icons/check_course.svg",
                                  onTap: () {},
                                  label: "Mark As Read",
                                  height: 32,
                                ),
                              ],
                            ),
                            16.verticalSpace,
                          ],
                        )),
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }
}
