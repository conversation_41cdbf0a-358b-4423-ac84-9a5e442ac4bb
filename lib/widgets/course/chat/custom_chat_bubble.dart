import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/chat/custom_bubble_normal.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomChatBubble extends StatelessWidget {
  final String message;
  final String? query;
  final String? replyTo;
  final String? name;
  final bool isMe;
  final bool sent;
  final bool delivered;
  final bool seen;
  final bool isReply;
  final String chatId;
  final String time;
  const CustomChatBubble({
    super.key,
    required this.message,
    this.replyTo,
    this.name,
    this.query,
    required this.isMe,
    required this.sent,
    required this.delivered,
    required this.seen,
    required this.chatId,
    required this.time,
    required this.isReply,
  });

  @override
  Widget build(BuildContext context) {
    bool stateTick = false;
    Icon? stateIcon;

    if (sent) {
      stateTick = true;
      stateIcon = const Icon(
        Icons.done,
        size: 18,
        color: Color(0xFF97AD8E),
      );
    }
    if (delivered) {
      stateTick = true;
      stateIcon = const Icon(
        Icons.done_all,
        size: 18,
        color: Color(0xFF97AD8E),
      );
    }
    if (seen) {
      stateTick = true;
      stateIcon = const Icon(
        Icons.done_all,
        size: 18,
        color: Color(0xFF92DEDA),
      );
    }
    return isReply
        ? Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
            child: Row(
              children: [
                isMe
                    ? const Expanded(
                        child: SizedBox(
                          width: 5,
                        ),
                      )
                    : Container(),
                Stack(
                  children: [
                    Container(
                      constraints: BoxConstraints(maxWidth: 600.w),
                      margin: EdgeInsets.zero,
                      decoration: BoxDecoration(
                        color: isMe
                            ? secondBlueColor.withOpacity(0.1)
                            : blueColor.withOpacity(0.5),
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12.r),
                            topRight: Radius.circular(12.r),
                            bottomLeft: Radius.circular(isMe ? 12.r : 0),
                            bottomRight: Radius.circular(isMe ? 0 : 12.r)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                left: 8.w, top: 8.h, right: 8.w),
                            child: Stack(
                              alignment: Alignment.centerLeft,
                              children: [
                                CustomContainer(
                                  bgColor: whiteColor,
                                  width: 8,
                                  height: 73,
                                  borderRadius: BorderRadius.only(
                                    bottomLeft: Radius.circular(12.r),
                                    topLeft: Radius.circular(12.r),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 3),
                                  child: CustomContainer(
                                    radius: 12.r,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 18.h),
                                    widget: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomTextWigdet(
                                          title: name ?? '',
                                          fontSize: 18,
                                          fontWeight: FontWeight.w500,
                                          textColor:
                                              secondWhiteColor.withOpacity(0.5),
                                        ),
                                        4.verticalSpace,
                                        CustomTextWigdet(
                                          title: replyTo ?? '',
                                          fontSize: 20,
                                          fontWeight: FontWeight.w400,
                                          textColor: secondWhiteColor,
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: stateTick
                                ? const EdgeInsets.fromLTRB(12, 6, 30, 22)
                                : const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 12),
                            child: CustomTextWigdet(
                              title: message,
                              fontSize: 26,
                              textColor: secondWhiteColor,
                              fontWeight: FontWeight.w400,
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ],
                      ),
                    ),
                    stateIcon != null && stateTick
                        ? Positioned(
                            bottom: 4,
                            right: 6,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CustomTextWigdet(
                                  title: time,
                                  fontSize: 14,
                                  textColor: secondWhiteColor,
                                ),
                                10.horizontalSpace,
                                stateIcon,
                              ],
                            ),
                          )
                        : const SizedBox(
                            width: 1,
                          ),
                  ],
                ),
              ],
            ),
          )
        : BubbleNormal(
            text: message,
            query: query,
            tail: true,
            time: time,
            bubbleRadius: 12.r,
            isSender: isMe,
            sent: sent,
            delivered: delivered,
            seen: seen,
            constraints: BoxConstraints(maxWidth: 650.w),
            color: isMe
                ? secondBlueColor.withOpacity(0.1)
                : blueColor.withOpacity(0.5),
            textStyle: TextStyle(
              fontFamily: "Inter",
              color: secondWhiteColor,
              fontSize: 18,
              fontStyle: FontStyle.normal,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.none,
            ),
          );
  }
}
