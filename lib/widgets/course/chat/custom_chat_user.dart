import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomChatUser extends StatelessWidget {
  final String? idStudent;
  final String? nameStudent;
  final String? nrpStudent;
  final String? imageUrl;
  final String? lastMessage;
  final String? lastMessageTime;
  final int? unreadCount;
  final bool? isMe;
  final bool? isRead;
  final bool? isHeaderChat;
  final VoidCallback? onTap;

  const CustomChatUser({
    super.key,
    this.idStudent,
    this.nameStudent,
    this.imageUrl,
    this.lastMessage,
    this.lastMessageTime,
    this.unreadCount,
    this.isRead,
    this.isMe,
    this.onTap,
    this.isHeaderChat,
    this.nrpStudent,
  });

  String formatText(String text) {
    if (text.length > 30) {
      return '${text.substring(0, 23)}...';
    } else {
      return text;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isHeaderChat ?? false ? onTap : onTap,
      child: CustomContainer(
        widget: Row(
          children: [
            ClipOval(
              child: imageUrl != null && imageUrl!.isNotEmpty
                  ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: imageUrl ?? '',
                        fit: BoxFit.cover,
                        width: 48.w,
                        height: 48.h,
                        placeholder: (context, url) =>
                            const CustomLoadingWidget(
                          width: 15,
                          height: 15,
                        ),
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                      ),
                    )
                  : Image.asset(
                      'assets/images/profile_chat.png',
                      width: 48.w,
                      height: 48.h,
                      fit: BoxFit.cover,
                    ),
            ),
            12.horizontalSpace,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: isHeaderChat ?? false
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: (nameStudent != null && nameStudent!.isNotEmpty)
                      ? nameStudent!
                      : 'Nama Siswa',
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
                isHeaderChat ?? false ? 0.verticalSpace : 4.verticalSpace,
                isHeaderChat ?? false
                    ? CustomTextWigdet(
                        title: nrpStudent ?? '111010010100',
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: secondWhiteColor.withOpacity(0.5),
                      )
                    : Row(
                        children: [
                          isMe ?? false
                              ? isRead ?? false
                                  ? SvgPicture.asset(
                                      'assets/icons/read_message.svg',
                                      width: 16.w,
                                      height: 16.h,
                                    )
                                  : const Icon(
                                      Icons.done_all,
                                      size: 18,
                                      color: Color(0xFF97AD8E),
                                    )
                              : const SizedBox.shrink(),
                          3.horizontalSpace,
                          CustomTextWigdet(
                            title: formatText(
                                (lastMessage != null && lastMessage!.isNotEmpty)
                                    ? lastMessage!
                                    : '---'),
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.7),
                          )
                        ],
                      ),
              ],
            ),
            isHeaderChat ?? false ? const SizedBox.shrink() : const Spacer(),
            isHeaderChat ?? false
                ? const SizedBox.shrink()
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CustomTextWigdet(
                        title: (lastMessageTime != null ||
                                lastMessageTime!.isEmpty)
                            ? lastMessageTime!
                            : '00:00',
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: unreadCount != null && unreadCount! > 0
                            ? blueColor
                            : secondWhiteColor,
                      ),
                      4.verticalSpace,
                      unreadCount != null && unreadCount! > 0
                          ? CustomContainer(
                              width: 30,
                              height: 30,
                              bgColor: blueColor,
                              shape: BoxShape.circle,
                              widget: Center(
                                child: CustomTextWigdet(
                                  title: unreadCount.toString(),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: Colors.white,
                                ),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ],
                  )
          ],
        ),
      ),
    );
  }
}
