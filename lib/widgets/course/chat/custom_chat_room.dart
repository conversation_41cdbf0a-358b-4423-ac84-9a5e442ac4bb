import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomChatRoom extends StatelessWidget {
  final String? nameStudent;
  final String? imageUrl;
  final String? nrp;

  const CustomChatRoom({
    super.key,
    this.nameStudent,
    this.imageUrl,
    this.nrp,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomContainer(
          width: double.infinity,
          height: 87,
          bgColor: secondBlueColor.withOpacity(0.10),
          widget: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              children: [
                ClipOval(
                  child: imageUrl != null && imageUrl!.isNotEmpty
                      ? Image.network(
                          imageUrl!,
                          width: 48.w,
                          height: 48.h,
                          fit: BoxFit.cover,
                        )
                      : Image.asset(
                          'assets/images/profile_chat.png',
                          width: 48.w,
                          height: 48.h,
                          fit: BoxFit.cover,
                        ),
                ),
                12.horizontalSpace,
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: nameStudent ?? 'Nama Siswa',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      textColor: whiteColor,
                    ),
                    4.verticalSpace,
                    CustomTextWigdet(
                      title: nrp ?? '172617627167',
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      textColor: secondWhiteColor.withOpacity(0.70),
                    ),
                  ],
                ),
                const Spacer(),
                CustomFilledButtonWidget(
                  onPressed: () {},
                  onlyIcon: true,
                  withIcon: true,
                  assetName: 'assets/icons/search.svg',
                  bgColor: Colors.transparent,
                  widthIcon: 24,
                  heightButton: 24,
                  heightIcon: 24,
                )
              ],
            ),
          ),
        ),

        /// Bubble Chat
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(12.r),
            child: ListView(
              children: [
                _bubbleChat('Halo, selamat pagi!', true),
                _bubbleChat('Pagi juga! Ada yang bisa saya bantu?', false),
                _bubbleChat('Saya ingin bertanya tentang tugas kemarin.', true),
                _bubbleChat('Tentu, silakan.', false),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Bubble Chat Widget
  Widget _bubbleChat(String message, bool isSender) {
    return Align(
      alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 6.h),
        padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 10.h),
        constraints: BoxConstraints(maxWidth: 250.w),
        decoration: BoxDecoration(
          color: isSender ? blueColor : secondWhiteColor.withOpacity(0.1),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
            bottomLeft: Radius.circular(isSender ? 16.r : 0),
            bottomRight: Radius.circular(isSender ? 0 : 16.r),
          ),
        ),
        child: CustomTextWigdet(
          title: message,
          fontSize: 16,
          fontWeight: FontWeight.w400,
          textColor: whiteColor,
        ),
      ),
    );
  }
}
