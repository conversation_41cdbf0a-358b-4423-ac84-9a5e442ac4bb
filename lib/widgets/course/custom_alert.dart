import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAlert extends StatefulWidget {
  final String? title;
  final String? subtitle;

  const CustomAlert({
    Key? key,
    required this.title,
    required this.subtitle,
  }) : super(key: key);

  @override
  State<CustomAlert> createState() => _CustomAlertState();
}

class _CustomAlertState extends State<CustomAlert> {
  bool isShow = true;

  @override
  Widget build(BuildContext context) {
    return AnimatedCrossFade(
      firstChild: Container(
        margin: EdgeInsets.fromLTRB(0, 8, 0, 8),
        decoration: BoxDecoration(
          color: const Color(0xFFE8B931).withOpacity(0.16), // Yellow background
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.08),
              child: SvgPicture.asset(
                'assets/icons/volume-down-line.svg',
                width: 22,
                height: 22,
              ),
            ),
            20.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: widget.title ?? '',
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.white,
                  ),
                  4.verticalSpace,
                  CustomTextWigdet(
                    title: widget.subtitle ?? '',
                    fontSize: 20,
                  ),
                ],
              ),
            ),
            12.horizontalSpace,
            IconButton(
              onPressed: () {
                isShow = false;
                setState(() {});
              },
              icon: const Icon(Icons.close, color: Colors.white70),
            ),
          ],
        ),
      ),
      secondChild: const SizedBox(),
      crossFadeState: isShow ? CrossFadeState.showFirst : CrossFadeState.showSecond,
      duration: const Duration(milliseconds: 300),
    );
  }
}
