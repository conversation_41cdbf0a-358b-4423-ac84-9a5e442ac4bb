import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class CustomCalender extends StatefulWidget {
  final void Function(DateTime) onApply;
  final VoidCallback onCancel;

  const CustomCalender({
    super.key,
    required this.onApply,
    required this.onCancel,
  });

  @override
  State<CustomCalender> createState() => _CustomCalenderState();
}

class _CustomCalenderState extends State<CustomCalender> {
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: 400,
      bgColor: secondBlueColor.withOpacity(0.10),
      radius: 5,
      widget: ClipRRect(
        borderRadius: BorderRadius.circular(5.r),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 8.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SfDateRangePicker(
                  selectionMode: DateRangePickerSelectionMode.single,
                  showActionButtons: false,
                  selectionShape: DateRangePickerSelectionShape.rectangle,
                  navigationMode: DateRangePickerNavigationMode.snap,
                  showNavigationArrow: true,
                  backgroundColor: Colors.transparent,
                  initialSelectedDate: _selectedDate,
                  selectionColor: blueColor,
                  todayHighlightColor: whiteColor,
                  onSelectionChanged: (args) {
                    if (args.value is DateTime) {
                      _selectedDate = args.value;
                    }
                  },
                  headerStyle: DateRangePickerHeaderStyle(
                    textAlign: TextAlign.center,
                    textStyle: TextStyle(
                      color: whiteColor,
                      fontSize: 18.sp,
                    ),
                    backgroundColor: Colors.transparent,
                  ),
                  yearCellStyle: DateRangePickerYearCellStyle(
                    textStyle: TextStyle(
                      color: whiteColor,
                      fontWeight: FontWeight.w300,
                      fontSize: 16.sp,
                    ),
                    todayTextStyle: TextStyle(
                      color: whiteColor,
                      fontSize: 16.sp,
                    ),
                    todayCellDecoration: null,
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  monthViewSettings: DateRangePickerMonthViewSettings(
                    dayFormat: 'EEE',
                    showTrailingAndLeadingDates: true,
                    viewHeaderStyle: DateRangePickerViewHeaderStyle(
                      textStyle: TextStyle(
                        color: whiteColor,
                        fontWeight: FontWeight.w300,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  monthCellStyle: DateRangePickerMonthCellStyle(
                    textStyle: TextStyle(
                      color: whiteColor,
                      fontWeight: FontWeight.w300,
                      fontSize: 16.sp,
                    ),
                    todayTextStyle: TextStyle(
                      color: whiteColor,
                      fontSize: 16.sp,
                    ),
                    todayCellDecoration: null,
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                16.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {
                        widget.onCancel();
                      },
                      title: 'Cancel',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 179,
                      heightButton: 44,
                      isOutlined: true,
                      borderColor: secondWhiteColor.withOpacity(0.20),
                      radius: 4,
                    ),
                    16.horizontalSpace,
                    CustomFilledButtonWidget(
                      onPressed: () {
                        widget.onApply(_selectedDate);
                      },
                      title: 'Apply',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 179,
                      heightButton: 44,
                      bgColor: blueColor,
                      radius: 4,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
