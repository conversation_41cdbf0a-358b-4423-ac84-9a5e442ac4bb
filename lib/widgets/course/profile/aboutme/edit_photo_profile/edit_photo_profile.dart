import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/aboutme_controller.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class EditPhotoProfile extends StatelessWidget {
  const EditPhotoProfile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AboutmeController());

    return ClipRRect(
      borderRadius: BorderRadius.circular(0.5),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 9, sigmaY: 9),
        child: CustomContainer(
          width: 764,
          radius: 4,
          bgColor: baseBlueColor.withOpacity(0.8),
          widget: Padding(
            padding: EdgeInsets.all(32.r),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: CustomTextWigdet(
                          title: 'Preview',
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          textColor: whiteColor,
                        ),
                      ),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        final parentController =
                            Get.find<ProfileCourseController>();
                        final aboutmeController = Get.find<AboutmeController>();
                        parentController.openEditPhoto();
                        aboutmeController.clearForm();
                      },
                      withIcon: true,
                      onlyIcon: true,
                      heightIcon: 32,
                      widthIcon: 32,
                      assetName: 'assets/icons/close2.svg',
                      bgColor: Colors.transparent,
                    ),
                  ],
                ),
                32.verticalSpace,
                Obx(() {
                  final aboutmeController = Get.find<AboutmeController>();
                  final profileController = Get.find<ProfileCourseController>();
                  final photoFile = aboutmeController.selectedPhoto.value;
                  final photoUrl =
                      profileController.profileData.value?.imageProfile;

                  return CustomContainer(
                    width: 700,
                    height: 890,
                    radius: 8,
                    widget: photoFile != null
                        ? Image.file(photoFile, fit: BoxFit.cover)
                        : (photoUrl != null && photoUrl.isNotEmpty
                            ? Image.network(photoUrl, fit: BoxFit.cover)
                            : const SizedBox.shrink()),
                  );
                }),
                32.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () => controller.selectPhotoFromGallery(),
                      title: 'Select Another Photo',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 344,
                      heightButton: 52,
                      isOutlined: true,
                      radius: 4,
                      borderColor: secondWhiteColor.withOpacity(0.2),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () async {
                        final aboutmeController = Get.find<AboutmeController>();
                        final profileController =
                            Get.find<ProfileCourseController>();
                        aboutmeController.confirmedPhoto.value =
                            aboutmeController.selectedPhoto.value;
                        await aboutmeController.updateProfile();
                        profileController.openEditPhoto();
                      },
                      title: 'Confirm',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 344,
                      heightButton: 52,
                      radius: 4,
                      bgColor: blueColor,
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
