import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/aboutme_controller.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AboutmeProfileLandscape extends StatelessWidget {
  const AboutmeProfileLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileCourseController>();
    Get.put(AboutmeController());
    return CustomContainer(
      width: double.infinity,
      radius: 8,
      bgColor: baseBlueColor.withOpacity(0.3),
      widget: Padding(
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 28.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const CustomTextWigdet(
                  title: 'Account Information',
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                ),
                CustomFilledButtonWidget(
                  onPressed: () {
                    final controller = Get.find<ProfileCourseController>();
                    controller.openEditProfile();
                  },
                  onlyIcon: true,
                  withIcon: true,
                  assetName: 'assets/icons/edit.svg',
                  widthIcon: 32,
                  heightIcon: 32,
                  bgColor: Colors.transparent,
                )
              ],
            ),
            const Divider(),
            20.verticalSpace,
            Obx(() {
              final profile = controller.profileData.value;
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ClipOval(
                          child: SizedBox(
                            width: 120.h,
                            height: 120.h,
                            child: Image.network(
                              profile?.imageProfile ?? '',
                              fit: BoxFit.cover,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return const CustomLoadingWidget(
                                  width: 60,
                                  height: 60,
                                  imageWidth: 60,
                                  imageHeight: 60,
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  color: greyColor,
                                  child: const Icon(
                                    Icons.person,
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        20.verticalSpace,
                        CustomFilledButtonWidget(
                          onPressed: () {
                            final controller =
                                Get.find<ProfileCourseController>();
                            controller.openEditPhoto();
                          },
                          title: 'Edit Photo',
                          fontSize: 24,
                          fontWeight: FontWeight.w500,
                          widthButton: 140,
                          heightButton: 50,
                          radius: 4,
                          fontColor: whiteColor,
                          bgColor: Colors.transparent,
                          isOutlined: true,
                          borderColor: secondWhiteColor.withOpacity(0.3),
                        )
                      ],
                    ),
                  ),
                  Flexible(
                    child: CustomContainer(
                      width: 570,
                      widget: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Name',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.name ?? "-",
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                          20.verticalSpace,
                          CustomTextWigdet(
                            title: 'Student Number',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.nosis ?? "-",
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Flexible(
                    child: CustomContainer(
                      width: 570,
                      widget: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'NRP',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.nrp ?? '-',
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                          20.verticalSpace,
                          CustomTextWigdet(
                            title: 'Username',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.username ?? '-',
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Flexible(
                    child: CustomContainer(
                      width: 570,
                      widget: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Email',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.email ?? '-',
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                          20.verticalSpace,
                          CustomTextWigdet(
                            title: 'No. Telp',
                            fontSize: 24,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                          CustomTextWigdet(
                            title: profile?.phoneNumber != null
                                ? '+62${profile?.phoneNumber}'
                                : '-',
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.95),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            })
          ],
        ),
      ),
    );
  }
}
