import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/aboutme/aboutme_controller.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class EditAccountProfile extends StatelessWidget {
  const EditAccountProfile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileCourseController>();
    final aboutmeController = Get.find<AboutmeController>();

    return ClipRRect(
      borderRadius: BorderRadius.circular(0.5),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 9, sigmaY: 9),
        child: CustomContainer(
          width: 1000,
          bgColor: baseBlueColor.withOpacity(0.5),
          widget: Padding(
            padding: EdgeInsets.all(32.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: Column(
                          children: [
                            CustomTextWigdet(
                              title: 'Edit Account Information',
                              fontSize: 24,
                              fontWeight: FontWeight.w600,
                              textColor: whiteColor,
                            ),
                            CustomTextWigdet(
                              title:
                                  'Update your personal details to keep your account accurate and secure.',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                          ],
                        ),
                      ),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        final controller = Get.find<ProfileCourseController>();
                        aboutmeController.clearForm();
                        controller.openEditProfile();
                      },
                      withIcon: true,
                      onlyIcon: true,
                      heightIcon: 32,
                      widthIcon: 32,
                      assetName: 'assets/icons/close2.svg',
                      bgColor: Colors.transparent,
                    ),
                  ],
                ),
                32.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'Name',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'Enter your name',
                          controller: aboutmeController.nameController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondWhiteColor.withOpacity(0.7),
                          radius: 4,
                          widthField: 456,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          colorText: whiteColor,
                        ),
                        15.verticalSpace,
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'NRP',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        Obx(() {
                          final profile = controller.profileData.value;

                          return CustomTextFieldWidget(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            colorTextHint: secondWhiteColor.withOpacity(0.7),
                            radius: 4,
                            widthField: 456,
                            heightField: 52,
                            contentPadding: EdgeInsetsDirectional.symmetric(
                                vertical: 10.h, horizontal: 14.w),
                            colorField: baseBlueColor.withOpacity(0.5),
                            controller: TextEditingController(
                                text: profile?.nrp ?? "-"),
                            readOnly: true,
                            colorText: whiteColor,
                          );
                        }),
                        15.verticalSpace,
                      ],
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'Email',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'Enter your email',
                          controller: aboutmeController.emailController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondWhiteColor.withOpacity(0.7),
                          radius: 4,
                          widthField: 456,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          colorText: whiteColor,
                        ),
                        15.verticalSpace,
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'Username',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'Enter your username',
                          controller: aboutmeController.userNameController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondWhiteColor.withOpacity(0.7),
                          radius: 4,
                          widthField: 456,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          colorText: whiteColor,
                        ),
                        15.verticalSpace,
                      ],
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'Student Number',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        Obx(() {
                          final profile = controller.profileData.value;

                          return CustomTextFieldWidget(
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            colorTextHint: secondWhiteColor.withOpacity(0.7),
                            radius: 4,
                            widthField: 456,
                            heightField: 52,
                            contentPadding: EdgeInsetsDirectional.symmetric(
                                vertical: 10.h, horizontal: 14.w),
                            colorField: baseBlueColor.withOpacity(0.5),
                            controller: TextEditingController(
                                text: profile?.nosis ?? "-"),
                            readOnly: true,
                            colorText: whiteColor,
                          );
                        }),
                        15.verticalSpace,
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CustomTextWigdet(
                              title: 'Phone Number',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.7),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomContainer(
                          width: 456,
                          height: 52,
                          radius: 4,
                          bgColor: baseBlueColor.withOpacity(0.5),
                          widget: Row(
                            children: [
                              14.horizontalSpace,
                              Image.asset(
                                'assets/images/Indonesia.png',
                                width: 24,
                                height: 24,
                              ),
                              8.horizontalSpace,
                              Text(
                                '+62',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Inter',
                                ),
                              ),
                              VerticalDivider(
                                color: Colors.white.withOpacity(0.3),
                                thickness: 1,
                                width: 20,
                                indent: 0,
                                endIndent: 0,
                              ),
                              16.horizontalSpace,
                              Expanded(
                                child: CustomTextFieldWidget(
                                  controller:
                                      aboutmeController.phoneNumberController,
                                  hintText: 'Enter your phone number',
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: 456,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h),
                                  colorField: Colors.transparent,
                                  colorText: whiteColor,
                                  isPhoneNumber: true,
                                ),
                              ),
                            ],
                          ),
                        ),
                        15.verticalSpace,
                      ],
                    ),
                  ],
                ),
                40.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {
                        aboutmeController.clearForm();
                        controller.openEditProfile();
                      },
                      title: 'Cancel',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 450,
                      heightButton: 52,
                      radius: 4,
                      isOutlined: true,
                      borderColor: secondWhiteColor.withOpacity(0.2),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () async {
                        await aboutmeController.updateProfile();
                        controller.openEditProfile();
                      },
                      title: 'Save',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 450,
                      heightButton: 52,
                      radius: 4,
                      bgColor: blueColor,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
