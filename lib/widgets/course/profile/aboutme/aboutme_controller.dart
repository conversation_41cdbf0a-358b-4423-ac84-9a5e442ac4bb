import 'dart:io';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mides_skadik/app/data/services/course/profile/profile_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class AboutmeController extends GetxController {
  /// ================================
  /// VAR
  final Rx<File?> selectedPhoto = Rx<File?>(null);
  final Rx<File?> confirmedPhoto = Rx<File?>(null);

  /// ================================
  /// TEXTEDITING
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController userNameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();

  /// ================================
  /// FUNCTION SELECT PHOTO
  Future<void> selectPhotoFromGallery() async {
    final picker = ImagePicker();
    final pickedImage = await picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      selectedPhoto.value = File(pickedImage.path);
    }
  }

  /// ================================
  /// FUNCTION EDIT PROFILE
  Future<void> updateProfile() async {
    final service = ProfileService();
    final success = await service.updateProfile(
      name: nameController.text,
      email: emailController.text,
      username: userNameController.text,
      phoneNumber: phoneNumberController.text,
      image: confirmedPhoto.value,
    );

    if (success.isSuccess && success.resultValue == true) {
      LogService.log.i("🎉 Berhasil update profile");

      // Panggil getProfile() dari ProfileCourseController
      final profileController = Get.find<ProfileCourseController>();
      profileController.getProfile();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Berhasil update profile');
      clearForm();
    } else {
      LogService.log.w("Gagal update profile");
    }
  }

  void clearForm() {
    nameController.clear();
    emailController.clear();
    userNameController.clear();
    phoneNumberController.clear();
    selectedPhoto.value = null;
    confirmedPhoto.value = null;
  }
}
