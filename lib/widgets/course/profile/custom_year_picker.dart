import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomYearPickerDialog extends StatefulWidget {
  final int initialYear;
  final int startYear;
  final int endYear;
  final ValueChanged<int> onYearSelected;

  const CustomYearPickerDialog({
    super.key,
    required this.initialYear,
    required this.onYearSelected,
    this.startYear = 1950,
    required this.endYear,
  });

  @override
  State<CustomYearPickerDialog> createState() => _CustomYearPickerDialogState();
}

class _CustomYearPickerDialogState extends State<CustomYearPickerDialog> {
  late int selectedYear;
  late int decadeStart;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialYear;
    decadeStart = (selectedYear ~/ 10) * 10;
  }

  void _changeDecade(int offset) {
    final newStart = decadeStart + offset * 10;
    if (newStart >= widget.startYear && newStart <= widget.endYear) {
      setState(() {
        decadeStart = newStart;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final years = List<int>.generate(10, (index) => decadeStart + index);

    return CustomContainer(
      width: 400,
      bgColor: baseBlueColor.withOpacity(0.9),
      radius: 8,
      widget: Padding(
        padding: EdgeInsets.all(8.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            CustomContainer(
              bgColor: baseBlueColor,
              widget: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    onPressed: () => _changeDecade(-1),
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: whiteColor,
                      size: 24.r,
                    ),
                  ),
                  CustomTextWigdet(
                    title: '${decadeStart} - ${decadeStart + 9}',
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    textColor: whiteColor,
                  ),
                  IconButton(
                    onPressed: () => _changeDecade(1),
                    icon: Icon(
                      Icons.arrow_forward_ios,
                      color: whiteColor,
                      size: 24.r,
                    ),
                  ),
                ],
              ),
            ),

            Divider(color: whiteColor),
            5.verticalSpace,
            // Year grid
            GridView.builder(
              shrinkWrap: true,
              itemCount: years.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisExtent: 25,
                crossAxisSpacing: 5,
                mainAxisSpacing: 12,
              ),
              itemBuilder: (context, index) {
                final year = years[index];
                final isSelected = year == selectedYear;
                return GestureDetector(
                  onTap: () => setState(() => selectedYear = year),
                  child: CustomContainer(
                    alignment: Alignment.center,
                    radius: 8,
                    bgColor: isSelected
                        ? secondBlueColor.withOpacity(0.3)
                        : Colors.transparent,
                    widget: CustomTextWigdet(
                      title: year.toString(),
                      fontSize: 18,
                      fontWeight:
                          isSelected ? FontWeight.w500 : FontWeight.w400,
                      textColor: isSelected
                          ? whiteColor
                          : secondWhiteColor.withOpacity(0.8),
                    ),
                  ),
                );
              },
            ),

            16.verticalSpace,
            CustomFilledButtonWidget(
              onPressed: () => widget.onYearSelected(selectedYear),
              title: 'Apply',
              fontSize: 18,
              fontWeight: FontWeight.w500,
              fontColor: whiteColor,
              bgColor: blueColor,
              radius: 4,
              widthButton: 274,
              heightButton: 44,
            )
          ],
        ),
      ),
    );
  }
}
