import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AdaptiveFormDialog extends StatelessWidget {
  final String? title;
  final String? description;
  final List<Widget>? formFields;
  final Widget? uploadSection;
  final VoidCallback? onSave;
  final VoidCallback? onCancel;
  final String? cancelText;
  final String? saveText;
  final bool? isSaveEnabled;

  const AdaptiveFormDialog({
    super.key,
    this.title,
    this.description,
    this.formFields,
    this.uploadSection,
    this.onSave,
    this.onCancel,
    this.cancelText,
    this.saveText,
    this.isSaveEnabled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent, // agar bg blur/tidak keras
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: CustomContainer(
        width: 619,
        bgColor: const Color(0xFF101B2A), // fallback bg (dark)
        widget: Padding(
          padding: EdgeInsets.all(32.r),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Center(
                      child: CustomTextWigdet(
                        title: title ?? '',
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        textColor: whiteColor,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: onCancel ?? () => Navigator.of(context).pop(),
                    child: Icon(
                      Icons.close,
                      color: whiteColor,
                    ),
                  ),
                ],
              ),
              if (description != null) ...[
                4.verticalSpace,
                CustomTextWigdet(
                  title: description ?? '',
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                  textColor: greyColor,
                ),
                16.verticalSpace,
              ],
              // Form fields
              if (formFields != null) ...formFields!,
              // Upload section
              if (uploadSection != null) ...[
                24.verticalSpace,
                uploadSection!,
              ],
              24.verticalSpace,
              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: CustomFilledButtonWidget(
                      onPressed: onCancel ?? () => Navigator.of(context).pop(),
                      title: cancelText ?? 'Cancel',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 271,
                      heightButton: 52,
                      radius: 4,
                      isOutlined: true,
                      borderColor: secondWhiteColor.withOpacity(0.2),
                    ),
                  ),
                  12.horizontalSpace,
                  Expanded(
                    child: CustomFilledButtonWidget(
                      onPressed: (isSaveEnabled ?? false) ? onSave : null,
                      title: saveText ?? 'Save',
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      widthButton: 271,
                      heightButton: 52,
                      radius: 4,
                      fontColor: whiteColor,
                      bgColor: blueColor,
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
