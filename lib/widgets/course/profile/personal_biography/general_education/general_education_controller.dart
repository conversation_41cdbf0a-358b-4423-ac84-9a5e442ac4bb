import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/general_education/general_education_model.dart';
import 'package:mides_skadik/app/data/services/course/profile/general_education/general_education_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class GeneralEducationController extends GetxController {
  /// ================================
  //* VAR
  var isOpenYearCompleted = false.obs;
  final selectedFiles = <PlatformFile>[].obs;
  final GeneralEducationService service = GeneralEducationService();
  final generalEducationList = <GeneralEducationModel>[].obs;
  final filteredEducationList = <GeneralEducationModel>[].obs;
  final searchQuery = ''.obs;
  String? existingDiplomaUrl;
  var isLoading = false.obs;

  /// ================================
  //* ADD GENNERAL EDUCATION
  final TextEditingController schoolNameController = TextEditingController();
  final TextEditingController yearCompletedController = TextEditingController();

  /// ================================
  //* FUNCTION
  void openYear() {
    isOpenYearCompleted.toggle();
  }

  void addFiles(List<PlatformFile> files) {
    // Cek dan hapus file lama yang berasal dari URL
    if (selectedFiles.isNotEmpty &&
        selectedFiles.first.path?.startsWith('http') == true) {
      selectedFiles.clear();
    }

    final filtered = files.where((file) {
      final isAllowedExtension =
          ['pdf', 'png', 'jpg', 'jpeg'].contains(file.extension?.toLowerCase());
      final isUnder10MB = (file.size / (1024 * 1024)) <= 10;

      if (!isAllowedExtension) {
        SnackbarUtil.showOnce(
          title: 'Format Tidak Didukung',
          message: 'Hanya file PDF, PNG, JPG, dan JPEG yang diperbolehkan.',
        );
      } else if (!isUnder10MB) {
        SnackbarUtil.showOnce(
          title: 'Ukuran File Terlalu Besar',
          message: 'Ukuran maksimum file adalah 10MB.',
        );
      }

      return isAllowedExtension && isUnder10MB;
    }).toList();

    if (filtered.isNotEmpty) {
      selectedFiles.addAll(filtered);
    }
  }

  void replaceWithNewFile(PlatformFile newFile) {
    if (!(newFile.path?.startsWith('http') ?? false)) {
      selectedFiles.value = [newFile]; // Replace with clean list
    }
  }

  void removeFile(int index) {
    selectedFiles.removeAt(index);
  }

  /// ================================
  /// FUCTION SUBMIT
  Future<void> submitGeneralEducation() async {
    final schoolName = schoolNameController.text.trim();
    final yearText = yearCompletedController.text.trim();

    if (schoolName.isEmpty || yearText.isEmpty || selectedFiles.isEmpty) {
      SnackbarUtil.showOnce(
          title: 'Data Tidak Lengkap',
          message: 'Harap isi semua kolom dan unggah file');
      return;
    }

    final file = selectedFiles.first;

    final result = await service.postGeneralEducation(
      schoolName: schoolName,
      yearCompleted: int.tryParse(yearText) ?? 0,
      diplomaFilePath: file.path!,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil dikirim');
      clearForm();
      getGeneralEducationList();
    } else {
      LogService.log.e(
          'Error POST General Education: ${result.errorMessage ?? 'Failed to create'}');
    }
  }

  /// ================================
  /// FUCTION UPDATE
  /// ================================
  /// FUCTION UPDATE
  Future<void> updateGeneralEducation({required String id}) async {
    final schoolName = schoolNameController.text.trim();
    final yearText = yearCompletedController.text.trim();

    final currentData =
        generalEducationList.firstWhereOrNull((e) => e.id == id);
    if (currentData == null) {
      SnackbarUtil.showOnce(title: 'Error', message: 'Data tidak ditemukan.');
      return;
    }

    final updatedSchoolName =
        schoolName.isEmpty ? currentData.schoolName! : schoolName;
    final updatedYear = yearText.isEmpty
        ? currentData.yearCompleted!
        : int.tryParse(yearText) ?? currentData.yearCompleted!;

    String? filePathToSend;

    final localFile = selectedFiles.firstWhereOrNull(
      (file) => !(file.path?.startsWith('http') ?? true),
    );

    if (localFile != null) {
      filePathToSend = localFile.path!;
    }

    // Validasi tambahan untuk logging jika file tidak berubah
    if (selectedFiles.isNotEmpty &&
        selectedFiles.first.path == currentData.diploma) {
      LogService.log.w('File tidak berubah dari sebelumnya');
    }

    // [LANGKAH 1] Debug log detail file yang akan dikirim
    LogService.log.d('''
🧾 File Debug Info:
-  File yang AKAN dikirim ke server: $filePathToSend
- File name: ${selectedFiles.isNotEmpty ? selectedFiles.first.name : 'No file selected'}
- Is from URL: ${selectedFiles.isNotEmpty && selectedFiles.first.path != null ? selectedFiles.first.path!.startsWith('http') : false}
''');

    final result = await service.editGeneralEducation(
      id: id,
      schoolName: updatedSchoolName,
      yearCompleted: updatedYear,
      diplomaFilePath: filePathToSend,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil diperbarui');
      clearForm();
      getGeneralEducationList();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Terjadi kesalahan saat update.');
    }
  }

  /// ================================
  /// FUCTION CLEAR
  void clearForm() {
    schoolNameController.clear();
    yearCompletedController.clear();
    selectedFiles.clear();
    isOpenYearCompleted.value = false;
  }

  /// ================================
  /// FUCTION GET
  Future<void> getGeneralEducationList() async {
    isLoading.value = true;
    final result = await service.getGeneralEducation();
    if (result.isSuccess) {
      generalEducationList.assignAll(result.resultValue ?? []);

      LogService.log.i(
          'Berhasil load general education (${result.resultValue?.length ?? 0})');
    } else {
      LogService.log.e('Gagal load data: ${result.errorMessage}');
    }
    isLoading.value = false;
  }

  /// ================================
  /// FUCTION DELETE
  Future<void> deleteGeneralEducation(String id) async {
    final result = await service.deleteGeneralEducation(id: id);
    if (result.isSuccess) {
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil dihapus');
      getGeneralEducationList();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Gagal menghapus data');
    }
  }

  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
    _applySearch();
  }

  void _applySearch() {
    final query = searchQuery.value;
    if (query.isEmpty) {
      filteredEducationList.assignAll(generalEducationList);
    } else {
      filteredEducationList.assignAll(
        generalEducationList.where((item) =>
            (item.schoolName?.toLowerCase().contains(query) ?? false) ||
            (item.yearCompleted?.toString().contains(query) ?? false)),
      );
    }
  }

  @override
  void onInit() {
    super.onInit();
    getGeneralEducationList();
  }
}
