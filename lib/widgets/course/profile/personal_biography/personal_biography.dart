import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/attachments/attachments.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/courses/courses.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/family_information/family_information.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/military_education/military_education.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/personal_biography_landscape.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/personal_profile/personal_profile.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/side_tab_personal_biography.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/general_education/general_education.dart';

class PersonalBiography extends StatelessWidget {
  const PersonalBiography({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileCourseController>();

    final orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      return const PersonalBiographyLnad();
    }

    return CustomContainer(
      width: double.infinity,
      radius: 8,
      widget: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // SIDE TAB
          CustomContainer(
            width: 270,
            radius: 8,
            bgColor: baseBlueColor.withOpacity(0.3),
            widget: Padding(
              padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 18.w),
              child: const BiographySideTab(),
            ),
          ),
          10.horizontalSpace,

          // KONTEN KANAN
          Expanded(
            child: SingleChildScrollView(
              child: CustomContainer(
                radius: 8,
                bgColor: baseBlueColor.withOpacity(0.3),
                widget: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 18.h, horizontal: 28.w),
                  child: Obx(() {
                    switch (controller.selectedBiographyTabIndex.value) {
                      case 0:
                        return const PersonalProfile();
                      case 1:
                        return const GeneralEducation();
                      case 2:
                        return const MilitaryEducation();
                      case 3:
                        return const Courses();
                      case 4:
                        return const FamilyInformation();
                      case 5:
                        return const Attachments();
                      default:
                        return const Center(child: Text("Belum ada konten"));
                    }
                  }),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
