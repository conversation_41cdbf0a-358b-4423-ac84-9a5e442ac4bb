import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/services/course/profile/family_information/family_information_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class FamilyInformationController extends GetxController {
//* VAR
  var isOpenCalenderDateOfBirth = false.obs;
  var isOpenCalenderDateOfMarriage = false.obs;
  final selectedDateOfBirth = Rxn<DateTime>();
  final selectedDateOfMarriage = Rxn<DateTime>();
  final FamilyInformationService service = FamilyInformationService();

  //* PARENTS
  final TextEditingController fathersNameController = TextEditingController();
  final TextEditingController motherNameController = TextEditingController();
  final TextEditingController fatherOccuptionNameController =
      TextEditingController();
  final TextEditingController mobileNumberParentsController =
      TextEditingController();
  final TextEditingController addressParentsController =
      TextEditingController();

//* SPOUSE
  final TextEditingController spouseNameController = TextEditingController();
  final TextEditingController placeOfBirthController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();
  final TextEditingController placeOfMarriageController =
      TextEditingController();
  final TextEditingController dateOfMarriageController =
      TextEditingController();
  final TextEditingController occupationController = TextEditingController();
  final TextEditingController educationAcademicTitleCntroller =
      TextEditingController();
  final TextEditingController phoneNumberSpouseCntroller =
      TextEditingController();
  final TextEditingController addressSpouseController = TextEditingController();

//* FUNCTION
  void openCalenderDateOfBirth() {
    isOpenCalenderDateOfBirth.toggle();
  }

  void openCalenderDateOfMarriage() {
    isOpenCalenderDateOfMarriage.toggle();
  }

  Future<void> submitFamilyInformation() async {
    if (fathersNameController.text.trim().isEmpty ||
        motherNameController.text.trim().isEmpty ||
        fatherOccuptionNameController.text.trim().isEmpty ||
        mobileNumberParentsController.text.trim().isEmpty ||
        addressParentsController.text.trim().isEmpty ||
        spouseNameController.text.trim().isEmpty ||
        placeOfBirthController.text.trim().isEmpty ||
        selectedDateOfBirth.value == null ||
        placeOfMarriageController.text.trim().isEmpty ||
        selectedDateOfMarriage.value == null ||
        occupationController.text.trim().isEmpty ||
        educationAcademicTitleCntroller.text.trim().isEmpty ||
        phoneNumberSpouseCntroller.text.trim().isEmpty ||
        addressSpouseController.text.trim().isEmpty) {
      SnackbarUtil.showOnce(
        title: 'Validasi Gagal',
        message: 'Semua field wajib diisi.',
      );
      return;
    }

    final result = await service.postFamilyInformation(
      fatherName: fathersNameController.text.trim(),
      fatherOccupation: fatherOccuptionNameController.text.trim(),
      motherName: motherNameController.text.trim(),
      motherMobileNumber: mobileNumberParentsController.text.trim(),
      parentsAddress: addressParentsController.text.trim(),
      spouseName: spouseNameController.text.trim(),
      spousePlaceOfBirth: placeOfBirthController.text.trim(),
      spouseDateOfBirth: selectedDateOfBirth.value!.toIso8601String(),
      spousePlaceOfMarriage: placeOfMarriageController.text.trim(),
      spouseDateOfMarriage: selectedDateOfMarriage.value!.toIso8601String(),
      spouseOccupation: occupationController.text.trim(),
      spouseEducationTitle: educationAcademicTitleCntroller.text.trim(),
      spousePhoneNumber: phoneNumberSpouseCntroller.text.trim(),
      spouseHomeAddress: addressSpouseController.text.trim(),
    );

    if (result.isSuccess) {
      SnackbarUtil.showOnce(
        title: 'Sukses',
        message: 'Data keluarga berhasil disimpan.',
      );
      // optionally call clearForm() if ingin dikosongkan
    } else {
      SnackbarUtil.showOnce(
        title: 'Sukses',
        message: result.errorMessage ?? 'Gagal menyimpan data.',
      );
    }
  }

  Future<void> getAndFillFamilyInformation() async {
    final result = await service.getFamilyInformation();
    if (result.isSuccess && result.resultValue != null) {
      final data = result.resultValue!;
      fathersNameController.text = data.fatherName ?? '';
      motherNameController.text = data.motherName ?? '';
      fatherOccuptionNameController.text = data.fatherOccupation ?? '';
      mobileNumberParentsController.text = data.motherMobileNumber ?? '';
      addressParentsController.text = data.parentsAddress ?? '';

      spouseNameController.text = data.spouseName ?? '';
      placeOfBirthController.text = data.spousePlaceOfBirth ?? '';
      placeOfMarriageController.text = data.spousePlaceOfMarriage ?? '';
      occupationController.text = data.spouseOccupation ?? '';
      educationAcademicTitleCntroller.text = data.spouseEducationTitle ?? '';
      phoneNumberSpouseCntroller.text = data.spousePhoneNumber ?? '';
      addressSpouseController.text = data.spouseHomeAddress ?? '';

      if (data.spouseDateOfBirth != null) {
        selectedDateOfBirth.value = data.spouseDateOfBirth;
        dateOfBirthController.text =
            DateFormat('MMMM d, yyyy').format(data.spouseDateOfBirth!);
      }

      if (data.spouseDateOfMarriage != null) {
        selectedDateOfMarriage.value = data.spouseDateOfMarriage;
        dateOfMarriageController.text =
            DateFormat('MMMM d, yyyy').format(data.spouseDateOfMarriage!);
      }
    }
  }

  @override
  void onInit() {
    super.onInit();
    getAndFillFamilyInformation();
  }
}
