import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/custom_calender.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/family_information/family_information_controller.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class FamilyInformationLandscape extends StatelessWidget {
  const FamilyInformationLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FamilyInformationController());

    return CustomContainer(
      radius: 8,
      widget: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: 'Family Information',
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
                const Divider(),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // LEFT PERSONAL INFORMATION
                    CustomContainer(
                      width: 260,
                      radius: 8,
                      widget: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Parents',
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Flexible(
                      child: CustomContainer(
                        radius: 8,
                        widget: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: "Father's Name",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.fathersNameController,
                                        hintText: "Enter your father's name",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Mother's Name",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.motherNameController,
                                        hintText: "Enter your mother's name",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: "Father’s Occupation",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller
                                            .fatherOccuptionNameController,
                                        hintText:
                                            "Enter your Father’s Occupation",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Mobile Number',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomContainer(
                                        width: double.infinity,
                                        height: 52,
                                        radius: 4,
                                        bgColor: baseBlueColor.withOpacity(0.5),
                                        widget: Row(
                                          children: [
                                            14.horizontalSpace,
                                            Image.asset(
                                              'assets/images/Indonesia.png',
                                              width: 24,
                                              height: 24,
                                            ),
                                            8.horizontalSpace,
                                            Text(
                                              '+62',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 18.sp,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Inter',
                                              ),
                                            ),
                                            VerticalDivider(
                                              color:
                                                  Colors.white.withOpacity(0.3),
                                              thickness: 1,
                                              width: 20,
                                              indent: 0,
                                              endIndent: 0,
                                            ),
                                            16.horizontalSpace,
                                            Expanded(
                                              child: CustomTextFieldWidget(
                                                controller: controller
                                                    .mobileNumberParentsController,
                                                hintText: '(0333) 886-8769',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                colorTextHint: secondWhiteColor
                                                    .withOpacity(0.7),
                                                colorText: whiteColor,
                                                radius: 4,
                                                widthField: double.infinity,
                                                heightField: 52,
                                                contentPadding:
                                                    EdgeInsetsDirectional
                                                        .symmetric(
                                                            vertical: 10.h,
                                                            horizontal: 14.w),
                                                colorField: Colors.transparent,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Address',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller:
                                      controller.addressParentsController,
                                  hintText: "Enter your parent's address",
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  colorText: whiteColor,
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
                // SPOUSE
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // LEFT SPOUSE
                    CustomContainer(
                      width: 260,
                      radius: 8,
                      widget: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Spouse',
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Flexible(
                      child: CustomContainer(
                        radius: 8,
                        widget: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Spouse’s Name",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.spouseNameController,
                                        hintText: "Enter your Spouse’s Name",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Place of Birth",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.placeOfBirthController,
                                        hintText: "Enter your Place of birth",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Date of Birth",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      GestureDetector(
                                        onTap: () {
                                          controller.openCalenderDateOfBirth();
                                        },
                                        child: AbsorbPointer(
                                          child: CustomTextFieldWidget(
                                            controller: controller
                                                .dateOfBirthController,
                                            hintText:
                                                "Enter your date of Birth",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            colorTextHint: secondWhiteColor
                                                .withOpacity(0.7),
                                            colorText: whiteColor,
                                            radius: 4,
                                            widthField: double.infinity,
                                            heightField: 52,
                                            contentPadding:
                                                EdgeInsetsDirectional.symmetric(
                                                    vertical: 10.h,
                                                    horizontal: 14.w),
                                            colorField:
                                                baseBlueColor.withOpacity(0.5),
                                            suffixAssetNameIcon:
                                                'assets/icons/calendar_today.svg',
                                            suffixIconConstraints:
                                                const BoxConstraints(
                                                    maxHeight: 60,
                                                    maxWidth: 60),
                                          ),
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Place of Marriage",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller
                                            .placeOfMarriageController,
                                        hintText:
                                            "Enter your Place of Marriege",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Date of Marriage ",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      GestureDetector(
                                        onTap: () {
                                          controller
                                              .openCalenderDateOfMarriage();
                                        },
                                        child: AbsorbPointer(
                                          child: CustomTextFieldWidget(
                                            controller: controller
                                                .dateOfMarriageController,
                                            hintText:
                                                "Enter your Place of Marriage",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            colorTextHint: secondWhiteColor
                                                .withOpacity(0.7),
                                            colorText: whiteColor,
                                            radius: 4,
                                            widthField: double.infinity,
                                            heightField: 52,
                                            contentPadding:
                                                EdgeInsetsDirectional.symmetric(
                                                    vertical: 10.h,
                                                    horizontal: 14.w),
                                            colorField:
                                                baseBlueColor.withOpacity(0.5),
                                            suffixAssetNameIcon:
                                                'assets/icons/calendar_today.svg',
                                            suffixIconConstraints:
                                                const BoxConstraints(
                                                    maxHeight: 60,
                                                    maxWidth: 60),
                                          ),
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Occupation",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.occupationController,
                                        hintText: "Enter your Occupation",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            15.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: "Education / Academic Title",
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller
                                            .educationAcademicTitleCntroller,
                                        hintText: "Enter your Academic Title",
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        colorText: whiteColor,
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Phone Number',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomContainer(
                                        width: double.infinity,
                                        height: 52,
                                        radius: 4,
                                        bgColor: baseBlueColor.withOpacity(0.5),
                                        widget: Row(
                                          children: [
                                            14.horizontalSpace,
                                            Image.asset(
                                              'assets/images/Indonesia.png',
                                              width: 24,
                                              height: 24,
                                            ),
                                            8.horizontalSpace,
                                            Text(
                                              '+62',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 18.sp,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Inter',
                                              ),
                                            ),
                                            VerticalDivider(
                                              color:
                                                  Colors.white.withOpacity(0.3),
                                              thickness: 1,
                                              width: 20,
                                              indent: 0,
                                              endIndent: 0,
                                            ),
                                            16.horizontalSpace,
                                            Expanded(
                                              child: CustomTextFieldWidget(
                                                controller: controller
                                                    .phoneNumberSpouseCntroller,
                                                hintText:
                                                    'Enter your mobile number',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                colorTextHint: secondWhiteColor
                                                    .withOpacity(0.7),
                                                colorText: whiteColor,
                                                radius: 4,
                                                widthField: double.infinity,
                                                heightField: 52,
                                                contentPadding:
                                                    EdgeInsetsDirectional
                                                        .symmetric(
                                                            vertical: 10.h,
                                                            horizontal: 14.w),
                                                colorField: Colors.transparent,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Address',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller:
                                      controller.addressSpouseController,
                                  hintText: "Enter your parent's address",
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  colorText: whiteColor,
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                                CustomFilledButtonWidget(
                                  onPressed: () {
                                    controller.submitFamilyInformation();
                                  },
                                  title: 'Save',
                                  fontSize: 24,
                                  fontWeight: FontWeight.w500,
                                  radius: 4,
                                  fontColor: whiteColor,
                                  bgColor: blueColor,
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Obx(() {
              if (controller.isOpenCalenderDateOfBirth.value) {
                return Positioned(
                  top: 50,
                  right: 0,
                  child: Material(
                    color: baseBlueColor,
                    borderRadius: BorderRadius.circular(8),
                    child: CustomCalender(
                      onApply: (date) {
                        controller.selectedDateOfBirth.value = date;
                        controller.dateOfBirthController.text =
                            DateFormat('MMMM d, yyyy').format(date);
                        controller.openCalenderDateOfBirth();
                      },
                      onCancel: () {
                        controller.openCalenderDateOfBirth();
                      },
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
            Obx(() {
              if (controller.isOpenCalenderDateOfMarriage.value) {
                return Positioned(
                  top: 200,
                  right: 0,
                  child: Material(
                    color: baseBlueColor,
                    borderRadius: BorderRadius.circular(8),
                    child: CustomCalender(
                      onApply: (date) {
                        controller.selectedDateOfMarriage.value = date;
                        controller.dateOfMarriageController.text =
                            DateFormat('MMMM d, yyyy').format(date);
                        controller.openCalenderDateOfMarriage();
                      },
                      onCancel: () {
                        controller.openCalenderDateOfMarriage();
                      },
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }
}
