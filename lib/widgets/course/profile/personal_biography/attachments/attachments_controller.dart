import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/attachment/attachment_model.dart';
import 'package:mides_skadik/app/data/services/course/profile/attachment/attachment_service.dart';

import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class AttachmentsController extends GetxController {
  final selectedFiles = <PlatformFile>[].obs;
  final TextEditingController attachmentNameController =
      TextEditingController();
  final attachmentList = <AttachmentModel>[].obs;
  final service = AttachmentService();
  final searchQuery = ''.obs;
  final filteredAttachmentList = <AttachmentModel>[].obs;
  final isLoading = false.obs;

  String? attachmentFile;

  void addFiles(List<PlatformFile> files) {
    if (selectedFiles.isNotEmpty &&
        selectedFiles.first.path?.startsWith('http') == true) {
      selectedFiles.clear();
    }

    final filtered = files.where((file) {
      final isAllowedExtension =
          ['pdf', 'png', 'jpg', 'jpeg'].contains(file.extension?.toLowerCase());
      final isUnder10MB = (file.size / (1024 * 1024)) <= 10;

      if (!isAllowedExtension) {
        SnackbarUtil.showOnce(
          title: 'Format Tidak Didukung',
          message: 'Hanya file PDF, PNG, JPG, dan JPEG yang diperbolehkan.',
        );
      } else if (!isUnder10MB) {
        SnackbarUtil.showOnce(
          title: 'Ukuran File Terlalu Besar',
          message: 'Ukuran maksimum file adalah 10MB.',
        );
      }

      return isAllowedExtension && isUnder10MB;
    }).toList();

    if (filtered.isNotEmpty) {
      selectedFiles.addAll(filtered);
    }
  }

  void replaceWithNewFile(PlatformFile newFile) {
    if (!(newFile.path?.startsWith('http') ?? false)) {
      selectedFiles.value = [newFile];
    }
  }

  void removeFile(int index) {
    selectedFiles.removeAt(index);
  }

  void clearForm() {
    attachmentNameController.clear();
    selectedFiles.clear();
  }

  Future<void> getAttachmentsList() async {
    isLoading.value = true;
    final result = await service.getAttachments();
    if (result.isSuccess) {
      attachmentList.assignAll(result.resultValue ?? []);
      filteredAttachmentList.assignAll(result.resultValue ?? []);
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
    isLoading.value = false;
  }

  Future<void> submitAttachment() async {
    final name = attachmentNameController.text.trim();
    if (name.isEmpty || selectedFiles.isEmpty) {
      SnackbarUtil.showOnce(
          title: 'Error', message: 'Nama dan file wajib diisi.');
      return;
    }

    final result = await service.postAttachment(
      name: name,
      filePath: selectedFiles.first.path!,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil dikirim');
      clearForm();
      getAttachmentsList();
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
  }

  Future<void> updateAttachment(String id) async {
    final current = attachmentList.firstWhereOrNull((e) => e.id == id);
    if (current == null) return;

    final name = attachmentNameController.text.trim().isNotEmpty
        ? attachmentNameController.text.trim()
        : current.name ?? '';

    // Hanya gunakan file lokal
    final filePath = selectedFiles
        .firstWhereOrNull(
          (file) => file.path != null && !file.path!.startsWith('http'),
        )
        ?.path;

    final result = await service.editAttachment(
      id: id,
      name: name,
      filePath: filePath,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil diperbarui');
      clearForm();
      getAttachmentsList();
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
  }

  Future<void> deleteAttachment(String id) async {
    final result = await service.deleteAttachment(id);
    if (result.isSuccess) {
      SnackbarUtil.showOnce(title: 'Berhasil', message: 'Attachment dihapus.');
      getAttachmentsList();
    } else {
      SnackbarUtil.showOnce(title: 'Gagal', message: result.errorMessage ?? '');
    }
  }

  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
    _applySearch();
  }

  void _applySearch() {
    final query = searchQuery.value;
    if (query.isEmpty) {
      filteredAttachmentList.assignAll(attachmentList);
    } else {
      filteredAttachmentList.assignAll(
        attachmentList.where((item) =>
            (item.name?.toLowerCase().contains(query) ?? false) ||
            (item.createdAt?.toString().toLowerCase().contains(query) ??
                false)),
      );
    }
  }

  @override
  void onInit() {
    super.onInit();
    getAttachmentsList();
  }
}
