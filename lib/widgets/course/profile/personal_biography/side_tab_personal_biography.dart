import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';

class BiographySideTab extends StatelessWidget {
  final double? fontSize;
  final double? heightButton;
  final double? widthButton;

  const BiographySideTab({
    super.key,
    this.fontSize,
    this.heightButton,
    this.widthButton,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileCourseController>();

    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTabButton("Personal Profile", 0, controller),
            10.verticalSpace,
            _buildTabButton("General Education", 1, controller),
            10.verticalSpace,
            _buildTabButton("Military Education", 2, controller),
            10.verticalSpace,
            _buildTabButton("Courses", 3, controller),
            10.verticalSpace,
            _buildTabButton("Family Information", 4, controller),
            10.verticalSpace,
            _buildTabButton("Attachments", 5, controller),
          ],
        ));
  }

  Widget _buildTabButton(
      String title, int index, ProfileCourseController controller) {
    final isActive = controller.selectedBiographyTabIndex.value == index;
    return CustomFilledButtonWidget(
      onPressed: () => controller.changeBiographyTab(index),
      title: title,
      fontSize: fontSize ?? 18,
      heightButton: heightButton,
      widthButton: widthButton,
      fontWeight: isActive ? FontWeight.w500 : FontWeight.w400,
      fontColor: isActive ? Colors.white : Colors.white,
      bgColor: isActive ? baseBlueColor : whiteColor.withOpacity(0.05),
      radius: 8,
    );
  }
}
