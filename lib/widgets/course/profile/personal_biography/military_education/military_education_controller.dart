import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/military_education/military_education_model.dart';
import 'package:mides_skadik/app/data/services/course/profile/military_education/military_education_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class MilitaryEducationController extends GetxController {
  /// ================================
  //* VAR
  var isOpenYearCompleted = false.obs;
  final selectedFiles = <PlatformFile>[].obs;
  final MilitaryEducationService service = MilitaryEducationService();
  final militaryEducationList = <MilitaryEducationModel>[].obs;
  String? existingDiplomaUrl;
  final searchQuery = ''.obs;
  final filteredMillitaryEducation = <MilitaryEducationModel>[].obs;
  var isLoading = false.obs;

  /// ================================
  //* ADD MILITARY EDUCATION
  final TextEditingController militaryEducationController =
      TextEditingController();
  final TextEditingController generationController = TextEditingController();
  final TextEditingController yearCompletedController = TextEditingController();

  /// ================================
  //* FUNCTION
  void openYear() {
    isOpenYearCompleted.toggle();
  }

  void addFiles(List<PlatformFile> files) {
    // Cek dan hapus file lama yang berasal dari URL
    if (selectedFiles.isNotEmpty &&
        selectedFiles.first.path?.startsWith('http') == true) {
      selectedFiles.clear();
    }

    final filtered = files.where((file) {
      final isAllowedExtension =
          ['pdf', 'png', 'jpg', 'jpeg'].contains(file.extension?.toLowerCase());
      final isUnder10MB = (file.size / (1024 * 1024)) <= 10;

      if (!isAllowedExtension) {
        SnackbarUtil.showOnce(
          title: 'Format Tidak Didukung',
          message: 'Hanya file PDF, PNG, JPG, dan JPEG yang diperbolehkan.',
        );
      } else if (!isUnder10MB) {
        SnackbarUtil.showOnce(
          title: 'Ukuran File Terlalu Besar',
          message: 'Ukuran maksimum file adalah 10MB.',
        );
      }

      return isAllowedExtension && isUnder10MB;
    }).toList();

    if (filtered.isNotEmpty) {
      selectedFiles.addAll(filtered);
    }
  }

  void replaceWithNewFile(PlatformFile newFile) {
    if (!(newFile.path?.startsWith('http') ?? false)) {
      selectedFiles.value = [newFile];
    }
  }

  void removeFile(int index) {
    selectedFiles.removeAt(index);
  }

  /// ================================
  /// FUCTION SUBMIT
  Future<void> submitMilitaryEducation() async {
    final militaryEducation = militaryEducationController.text.trim();
    final yearText = yearCompletedController.text.trim();
    final generation = generationController.text.trim();

    if (militaryEducation.isEmpty ||
        yearText.isEmpty ||
        selectedFiles.isEmpty ||
        generation.isEmpty) {
      SnackbarUtil.showOnce(
          title: 'Data Tidak Lengkap',
          message: 'Harap isi semua kolom dan unggah file');
      return;
    }

    final file = selectedFiles.first;

    final result = await service.postMilitaryEducation(
      militaryEducation: militaryEducation,
      generation: generation,
      yearCompleted: int.tryParse(yearText) ?? 0,
      diplomaFilePath: file.path!,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil dikirim');
      clearForm();
      getMilitaryEducationList();
    } else {
      LogService.log.e(
          'Error POST General Education: ${result.errorMessage ?? 'Failed to create'}');
    }
  }

  /// ================================
  /// FUCTION SUBMIT
  Future<void> updateMilitaryEducation({required String id}) async {
    final militaryEducation = militaryEducationController.text.trim();
    final generation = generationController.text.trim();
    final yearText = yearCompletedController.text.trim();

    final currentData =
        militaryEducationList.firstWhereOrNull((e) => e.id == id);
    if (currentData == null) {
      SnackbarUtil.showOnce(title: 'Error', message: 'Data tidak ditemukan.');
      return;
    }

    final updatedMilitaryEducation = militaryEducation.isEmpty
        ? currentData.militaryEducation!
        : militaryEducation;
    final updatedGeneration =
        generation.isEmpty ? currentData.generation! : generation;
    final updatedYear = yearText.isEmpty
        ? currentData.yearCompleted!
        : int.tryParse(yearText) ?? currentData.yearCompleted!;

    String? filePathToSend;
    final localFile = selectedFiles.firstWhereOrNull(
      (file) => !(file.path?.startsWith('http') ?? true),
    );
    if (localFile != null) {
      filePathToSend = localFile.path!;
    }

    final result = await service.editMilitaryEducation(
      id: id,
      militaryEducation: updatedMilitaryEducation,
      generation: updatedGeneration,
      yearCompleted: updatedYear,
      diplomaFilePath: filePathToSend,
    );

    if (result.isSuccess) {
      Get.back();
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil diperbarui');
      clearForm();
      getMilitaryEducationList();
    } else {
      SnackbarUtil.showOnce(
        title: 'Gagal',
        message: result.errorMessage ?? 'Terjadi kesalahan saat update.',
      );
    }
  }

  /// ================================
  /// FUCTION CLEAR
  void clearForm() {
    militaryEducationController.clear();
    generationController.clear();
    yearCompletedController.clear();
    selectedFiles.clear();
    isOpenYearCompleted.value = false;
  }

  /// ================================
  /// FUCTION GET
  Future<void> getMilitaryEducationList() async {
    isLoading.value = true;
    final result = await service.getMilitaryEducation();

    if (result.isSuccess) {
      militaryEducationList.assignAll(result.resultValue ?? []);

      LogService.log.i(
          'Berhasil load general education (${result.resultValue?.length ?? 0})');
    } else {
      LogService.log.e('Gagal load data: ${result.errorMessage}');
    }
    isLoading.value = false;
  }

  /// ================================
  /// FUNCTION DELETE
  Future<void> deleteMilitaryEducation(String id) async {
    final result = await service.deleteMilitaryEducation(id: id);
    if (result.isSuccess) {
      SnackbarUtil.showOnce(
          title: 'Berhasil', message: 'Data berhasil dihapus');
      getMilitaryEducationList(); // Refresh list setelah delete
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Gagal menghapus data');
    }
  }

  /// ================================
  /// FUNCTION FILTER
  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
    _applySearch();
  }

  void _applySearch() {
    final query = searchQuery.value;
    if (query.isEmpty) {
      filteredMillitaryEducation.assignAll(militaryEducationList);
    } else {
      filteredMillitaryEducation.assignAll(
        militaryEducationList.where((item) =>
            (item.militaryEducation?.toLowerCase().contains(query) ?? false) ||
            (item.yearCompleted?.toString().contains(query) ?? false)),
      );
    }
  }

  @override
  void onInit() {
    super.onInit();
    getMilitaryEducationList();
  }
}
