import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/app/data/services/course/profile/personal_profile/personal_profile_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class PersonalProfileControllers extends GetxController {
  /// ================================
  //* VAR
  var isOpenCalenderDateOfBirth = false.obs;
  var isOpenCalenderTMT = false.obs;
  final selectedDateOfBirth = Rxn<DateTime>();
  final selectedTMT = Rxn<DateTime>();
  final PersonalProfileService service = PersonalProfileService();

  /// Dropdown support
  final selectedRank = ''.obs;
  final selectedBloodType = ''.obs;
  final rankOptions = ['Letkol', 'Ka<PERSON>en', 'Mayor', '<PERSON><PERSON>', '<PERSON>lonel'];
  final bloodTypeOptions = ['A', 'B', 'AB', 'O'];

  /// ================================
  //* PERSONAL PROFILE
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController corpsController = TextEditingController();
  final TextEditingController nrpController = TextEditingController();
  final TextEditingController rankController = TextEditingController();
  final TextEditingController placeOfBirthController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();
  final TextEditingController religionController = TextEditingController();
  final TextEditingController ethnicityController = TextEditingController();
  final TextEditingController bloodtypeController = TextEditingController();
  final TextEditingController nationalityController = TextEditingController();
  final TextEditingController heightController = TextEditingController();
  final TextEditingController weightController = TextEditingController();
  final TextEditingController hobbiesController = TextEditingController();

  /// ================================
  //* CONACT INFOMRATION
  final TextEditingController homeAddressController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController emailAddressController = TextEditingController();

  /// ================================
  //* MILITARY INFOMRATION
  final TextEditingController lastPositionHeldController =
      TextEditingController();
  final TextEditingController commissioningSourceController =
      TextEditingController();
  final TextEditingController graduationYearController =
      TextEditingController();
  final TextEditingController tmtController = TextEditingController();
  final TextEditingController currentUnitController = TextEditingController();
  final TextEditingController unitAddressController = TextEditingController();

  //* FUNCTION
  void openCalenderDateOfBirth() {
    isOpenCalenderDateOfBirth.toggle();
  }

  void openCalenderTMT() {
    isOpenCalenderTMT.toggle();
  }

  Future<void> submitPersonalProfile() async {
    // Validasi semua field wajib
    if (
        //PERSONAL PROFILE
        fullNameController.text.trim().isEmpty ||
            corpsController.text.trim().isEmpty ||
            nrpController.text.trim().isEmpty ||
            rankController.text.trim().isEmpty ||
            placeOfBirthController.text.trim().isEmpty ||
            selectedDateOfBirth.value == null ||
            religionController.text.trim().isEmpty ||
            bloodtypeController.text.trim().isEmpty ||
            nationalityController.text.trim().isEmpty ||
            heightController.text.trim().isEmpty ||
            weightController.text.trim().isEmpty ||
            hobbiesController.text.trim().isEmpty ||

            //CONTACT INFOMATION
            homeAddressController.text.trim().isEmpty ||
            mobileNumberController.text.trim().isEmpty ||
            emailAddressController.text.trim().isEmpty ||

            //MILITARY INFOMRATION
            lastPositionHeldController.text.trim().isEmpty ||
            commissioningSourceController.text.trim().isEmpty ||
            graduationYearController.text.trim().isEmpty ||
            selectedTMT.value == null ||
            currentUnitController.text.trim().isEmpty ||
            unitAddressController.text.trim().isEmpty) {
      SnackbarUtil.showOnce(
        title: 'Peringatan',
        message: 'Harap lengkapi semua data yang wajib diisi.',
      );
      return;
    }

    try {
      final result = await service.postPersonalProfile(
        // PERSONAL PROFILE
        fullName: fullNameController.text.trim(),
        corps: corpsController.text.trim(),
        nrp: nrpController.text.trim(),
        rank: rankController.text.trim(),
        placeOfBirth: placeOfBirthController.text.trim(),
        dateOfBirth: selectedDateOfBirth.value!.toIso8601String(),
        religion: religionController.text.trim(),
        ethnicity: ethnicityController.text.trim(),
        bloodType: bloodtypeController.text.trim(),
        nationality: nationalityController.text.trim(),
        height: int.tryParse(heightController.text.trim()) ?? 0,
        weight: int.tryParse(weightController.text.trim()) ?? 0,
        hobbies: hobbiesController.text.trim(),

        // CONTACT INFOMATION
        homeAddress: homeAddressController.text.trim(),
        mobileNumber: mobileNumberController.text.trim(),
        emailAddress: emailAddressController.text.trim(),

        // MILITARY INFOMRATION
        lastPosition: lastPositionHeldController.text.trim(),
        commissioningSource: commissioningSourceController.text.trim(),
        graduationYear: graduationYearController.text.trim(),
        effectiveStartDate: selectedTMT.value!.toIso8601String(),
        currentUnit: currentUnitController.text.trim(),
        unitAddress: unitAddressController.text.trim(),
      );

      if (result.isSuccess) {
        SnackbarUtil.showOnce(
            title: 'Sukses', message: 'Profil berhasil disimpan');
      } else {
        SnackbarUtil.showOnce(
          title: 'Sukses',
          message: result.errorMessage ?? 'Gagal menyimpan data',
        );
      }
    } catch (e) {
      LogService.log.e('submitPersonalProfile Exception: $e');
      SnackbarUtil.showOnce(
        title: 'Error',
        message: 'Terjadi kesalahan saat mengirim data',
      );
    }
  }

  Future<void> loadProfile() async {
    final result = await service.getPersonalProfile();

    //Jika gagal mengambil data dari server
    if (!result.isSuccess) {
      SnackbarUtil.showOnce(
        title: 'Gagal ini?',
        message: result.errorMessage ?? 'Gagal memuat data profil',
      );
      return;
    }

    //Jika data null (belum dilengkapi oleh user)
    if (result.resultValue == null) {
      SnackbarUtil.showOnce(
        title: 'Lengkapi Profil',
        message: 'Silakan lengkapi data personal profile Anda.',
      );
      return;
    }

    // Jika data tersedia, assign ke form controller
    final data = result.resultValue!;

    // PERSONAL PROFILE
    fullNameController.text = data['fullName'] ?? '';
    corpsController.text = data['corps'] ?? '';
    nrpController.text = data['NRP'] ?? '';
    rankController.text = data['rank'] ?? '';
    placeOfBirthController.text = data['placeOfBirth'] ?? '';
    dateOfBirthController.text = data['dateOfBirth'] ?? '';
    religionController.text = data['religion'] ?? '';
    ethnicityController.text = data['ethnicity'] ?? '';
    bloodtypeController.text = data['bloodType'] ?? '';
    nationalityController.text = data['nationality'] ?? '';
    heightController.text = (data['height'] ?? '').toString();
    weightController.text = (data['weight'] ?? '').toString();
    hobbiesController.text = data['hobbies'] ?? '';

    // CONTACT INFORMATION
    homeAddressController.text = data['homeAddress'] ?? '';
    mobileNumberController.text = data['mobileNumber'] ?? '';
    emailAddressController.text = data['emailAddress'] ?? '';

    // MILITARY INFORMATION
    lastPositionHeldController.text = data['lastPosition'] ?? '';
    commissioningSourceController.text = data['commissioningSource'] ?? '';
    graduationYearController.text = data['graduationYear'] ?? '';
    tmtController.text = data['effectiveStartDate'] ?? '';
    currentUnitController.text = data['currentUnit'] ?? '';
    unitAddressController.text = data['unitAddress'] ?? '';

    // Tanggal Lahir
    if (data['dateOfBirth'] != null) {
      final dob = DateTime.tryParse(data['dateOfBirth']);
      if (dob != null) {
        selectedDateOfBirth.value = dob;
        dateOfBirthController.text = DateFormat('MMMM d, yyyy').format(dob);
      }
    }

    // TMT
    if (data['effectiveStartDate'] != null) {
      final tmt = DateTime.tryParse(data['effectiveStartDate']);
      if (tmt != null) {
        selectedTMT.value = tmt;
        tmtController.text = DateFormat('MMMM d, yyyy').format(tmt);
      }
    }
  }

  @override
  void onInit() {
    super.onInit();
    loadProfile();
  }
}
