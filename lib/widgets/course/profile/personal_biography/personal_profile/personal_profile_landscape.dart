import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/custom_calender.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/personal_profile/personal_profile_controllers.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class PersonalProfileLandscape extends StatelessWidget {
  const PersonalProfileLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PersonalProfileControllers());

    return CustomContainer(
      radius: 8,
      widget: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: 'Personal Profile',
                  fontSize: 24,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
                const Divider(),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // LEFT PERSONAL INFORMATION
                    CustomContainer(
                      width: 260,
                      radius: 8,
                      widget: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Personal Profile',
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Flexible(
                      child: CustomContainer(
                        radius: 8,
                        widget: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Full Name',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.fullNameController,
                                        hintText: 'Enter your name',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Corps',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller.corpsController,
                                        hintText: 'Enter your Corps',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Nationality',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.nationalityController,
                                        hintText: 'Example : Indonesia',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Ethnicity',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.ethnicityController,
                                        hintText: 'Example: Javanese',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Place Of Birth',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.placeOfBirthController,
                                        hintText: 'Example : Jakarta',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Date Of Birth',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      GestureDetector(
                                        onTap: () {
                                          controller.openCalenderDateOfBirth();
                                        },
                                        child: AbsorbPointer(
                                          child: CustomTextFieldWidget(
                                            controller: controller
                                                .dateOfBirthController,
                                            hintText:
                                                'Select your date of birth',
                                            colorText: whiteColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            colorTextHint: secondWhiteColor
                                                .withOpacity(0.7),
                                            radius: 4,
                                            widthField: double.infinity,
                                            heightField: 52,
                                            contentPadding:
                                                EdgeInsetsDirectional.symmetric(
                                                    vertical: 10.h,
                                                    horizontal: 14.w),
                                            colorField:
                                                baseBlueColor.withOpacity(0.5),
                                            suffixAssetNameIcon:
                                                'assets/icons/calendar_today.svg',
                                            suffixIconConstraints:
                                                const BoxConstraints(
                                                    maxHeight: 60,
                                                    maxWidth: 60),
                                          ),
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Religion',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller: controller.religionController,
                                  hintText: 'Select your religion',
                                  colorText: whiteColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Height',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller.heightController,
                                        hintText: 'Enter your height in cm',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Weight',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller.weightController,
                                        hintText: 'Enter yout weight in cm',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Hobbies',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller: controller.hobbiesController,
                                  hintText: 'Enter your hobbies',
                                  colorText: whiteColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
                // CONTACT INFORMATION
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // LEFT PERSONAL INFORMATION
                    CustomContainer(
                      width: 260,
                      radius: 8,
                      widget: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Contact Information',
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Flexible(
                      child: CustomContainer(
                        radius: 8,
                        widget: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Home Address',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller: controller.homeAddressController,
                                  hintText: 'Enter your home address',
                                  colorText: whiteColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                            15.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Mobile Number',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomContainer(
                                        width: double.infinity,
                                        height: 52,
                                        radius: 4,
                                        bgColor: baseBlueColor.withOpacity(0.5),
                                        widget: Row(
                                          children: [
                                            14.horizontalSpace,
                                            Image.asset(
                                              'assets/images/Indonesia.png',
                                              width: 24,
                                              height: 24,
                                            ),
                                            8.horizontalSpace,
                                            Text(
                                              '+62',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 20.sp,
                                                fontWeight: FontWeight.w400,
                                                fontFamily: 'Inter',
                                              ),
                                            ),
                                            VerticalDivider(
                                              color:
                                                  Colors.white.withOpacity(0.3),
                                              thickness: 1,
                                              width: 20,
                                              indent: 0,
                                              endIndent: 0,
                                            ),
                                            16.horizontalSpace,
                                            Expanded(
                                              child: CustomTextFieldWidget(
                                                controller: controller
                                                    .mobileNumberController,
                                                hintText:
                                                    'Enter your mobile number',
                                                colorText: whiteColor,
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                colorTextHint: secondWhiteColor
                                                    .withOpacity(0.7),
                                                radius: 4,
                                                widthField: double.infinity,
                                                heightField: 52,
                                                contentPadding:
                                                    EdgeInsetsDirectional
                                                        .symmetric(
                                                            vertical: 10.h,
                                                            horizontal: 14.w),
                                                colorField: Colors.transparent,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Email Address',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.emailAddressController,
                                        hintText:
                                            'Example : <EMAIL>',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Divider(),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // MILITARY INFORMATION
                    CustomContainer(
                      width: 260,
                      radius: 8,
                      widget: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomTextWigdet(
                            title: 'Military Information',
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ),
                    10.horizontalSpace,
                    Flexible(
                      child: CustomContainer(
                        radius: 8,
                        widget: Column(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Last Position Held',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller
                                            .lastPositionHeldController,
                                        hintText:
                                            'Enter your last position held',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Commissioning Source',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller: controller
                                            .commissioningSourceController,
                                        hintText:
                                            'Enter your commissioning source',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Graduation Year',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      CustomTextFieldWidget(
                                        controller:
                                            controller.graduationYearController,
                                        hintText: 'Example : 2020',
                                        colorText: whiteColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                        colorTextHint:
                                            secondWhiteColor.withOpacity(0.7),
                                        radius: 4,
                                        widthField: double.infinity,
                                        heightField: 52,
                                        contentPadding:
                                            EdgeInsetsDirectional.symmetric(
                                                vertical: 10.h,
                                                horizontal: 14.w),
                                        colorField:
                                            baseBlueColor.withOpacity(0.5),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                                15.horizontalSpace,
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet(
                                            title: 'Effective Start Date (TMT)',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            textColor: secondWhiteColor
                                                .withOpacity(0.7),
                                          ),
                                          5.horizontalSpace,
                                          SvgPicture.asset(
                                            'assets/icons/Label_.svg',
                                            width: 6,
                                            height: 6,
                                          )
                                        ],
                                      ),
                                      6.verticalSpace,
                                      GestureDetector(
                                        onTap: () {
                                          controller.openCalenderTMT();
                                        },
                                        child: AbsorbPointer(
                                          child: CustomTextFieldWidget(
                                            controller:
                                                controller.tmtController,
                                            hintText:
                                                'Enter your effective start date',
                                            colorText: whiteColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            colorTextHint: secondWhiteColor
                                                .withOpacity(0.7),
                                            radius: 4,
                                            widthField: double.infinity,
                                            heightField: 52,
                                            contentPadding:
                                                EdgeInsetsDirectional.symmetric(
                                                    vertical: 10.h,
                                                    horizontal: 14.w),
                                            colorField:
                                                baseBlueColor.withOpacity(0.5),
                                            suffixAssetNameIcon:
                                                'assets/icons/calendar_today.svg',
                                            suffixIconConstraints:
                                                const BoxConstraints(
                                                    maxHeight: 60,
                                                    maxWidth: 60),
                                          ),
                                        ),
                                      ),
                                      15.verticalSpace,
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Current Unit',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller: controller.currentUnitController,
                                  hintText: 'Enter your current unit',
                                  colorText: whiteColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    CustomTextWigdet(
                                      title: 'Unit Address',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    5.horizontalSpace,
                                    SvgPicture.asset(
                                      'assets/icons/Label_.svg',
                                      width: 6,
                                      height: 6,
                                    )
                                  ],
                                ),
                                6.verticalSpace,
                                CustomTextFieldWidget(
                                  controller: controller.unitAddressController,
                                  hintText: 'Enter your unit address',
                                  colorText: whiteColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w400,
                                  colorTextHint:
                                      secondWhiteColor.withOpacity(0.7),
                                  radius: 4,
                                  widthField: double.infinity,
                                  heightField: 52,
                                  contentPadding:
                                      EdgeInsetsDirectional.symmetric(
                                          vertical: 10.h, horizontal: 14.w),
                                  colorField: baseBlueColor.withOpacity(0.5),
                                ),
                                15.verticalSpace,
                              ],
                            ),
                            CustomFilledButtonWidget(
                              onPressed: () {
                                controller.submitPersonalProfile();
                              },
                              title: 'Save',
                              fontSize: 24,
                              fontWeight: FontWeight.w500,
                              radius: 4,
                              fontColor: whiteColor,
                              bgColor: blueColor,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            // Kalender tanggal lahir
            Obx(() {
              if (controller.isOpenCalenderDateOfBirth.value) {
                return Positioned(
                  top: 220,
                  right: 0,
                  child: Material(
                    color: baseBlueColor,
                    borderRadius: BorderRadius.circular(8),
                    child: CustomCalender(
                      onApply: (date) {
                        controller.selectedDateOfBirth.value = date;
                        controller.dateOfBirthController.text =
                            DateFormat('MMMM d, yyyy').format(date);
                        controller.openCalenderDateOfBirth();
                      },
                      onCancel: () {
                        controller.openCalenderDateOfBirth();
                      },
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
            // Kalender TMT
            Obx(() {
              if (controller.isOpenCalenderTMT.value) {
                return Positioned(
                  top: 370,
                  right: 0,
                  child: Material(
                    color: baseBlueColor,
                    borderRadius: BorderRadius.circular(8),
                    child: CustomCalender(
                      onApply: (date) {
                        controller.selectedTMT.value = date;
                        controller.tmtController.text =
                            DateFormat('MMMM d, yyyy').format(date);
                        controller.openCalenderTMT();
                      },
                      onCancel: () {
                        controller.openCalenderTMT();
                      },
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }
}
