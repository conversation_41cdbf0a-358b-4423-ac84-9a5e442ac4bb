import 'package:dotted_border/dotted_border.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/adaptive_form_dialog.dart';
import 'package:mides_skadik/widgets/course/profile/custom_year_picker.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/courses/courses_controller.dart';
import 'package:mides_skadik/widgets/course/profile/personal_biography/courses/courses_landscape.dart';
import 'package:mides_skadik/widgets/course/profile/table_profile.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class Courses extends StatelessWidget {
  const Courses({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CoursesController());

    final orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      return const CoursesLandscape();
    }

    return TableProfile(
      title: 'Courses',
      addButtonText: 'Add New',
      searchHint: 'Search',
      columnTitle1: 'NO',
      columnTitle2: 'COURSE NAME',
      columnTitle3: 'YEAR STARTED',
      columnTitle4: 'YEAR COMPLETED',
      columnTitle5: 'ACTION',
      columnFlex1: 1,
      columnFlex2: 2,
      columnFlex3: 2,
      columnFlex4: 2,
      columnFlex5: 1,
      onAddPressed: () {
        Get.dialog(
          barrierDismissible: false,
          Stack(
            children: [
              AdaptiveFormDialog(
                title: 'Add Course',
                description: 'Add course details and attach certificate.',
                onCancel: () {
                  controller.clearForm();
                  Get.back();
                },
                onSave: () async {
                  await controller.submitCourse();
                },
                isSaveEnabled: true,
                formFields: [
                  // Course Name
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomTextWigdet(
                            title: 'Course Name',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.8),
                          ),
                          5.horizontalSpace,
                          SvgPicture.asset(
                            'assets/icons/Label_.svg',
                            width: 6,
                            height: 6,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      CustomTextFieldWidget(
                        controller: controller.coursesNameController,
                        hintText: 'Ex: University Taruna Negara',
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        colorText: secondWhiteColor.withOpacity(0.9),
                        colorTextHint: greyColor,
                        colorField: baseBlueColor.withOpacity(0.5),
                        radius: 8,
                        widthField: 555,
                        heightField: 52,
                        contentPadding: EdgeInsetsDirectional.symmetric(
                            vertical: 10.h, horizontal: 14.w),
                      )
                    ],
                  ),
                  32.verticalSpace,
                  // Year Started
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomTextWigdet(
                            title: 'Year Started',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.8),
                          ),
                          5.horizontalSpace,
                          SvgPicture.asset(
                            'assets/icons/Label_.svg',
                            width: 6,
                            height: 6,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () {
                          controller.openYearStart();
                        },
                        child: AbsorbPointer(
                          child: CustomTextFieldWidget(
                            controller: controller.yearStartedController,
                            hintText: 'Select year started',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            colorText: secondWhiteColor.withOpacity(0.9),
                            colorTextHint: greyColor,
                            colorField: baseBlueColor.withOpacity(0.5),
                            radius: 8,
                            widthField: 555,
                            heightField: 52,
                            contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h,
                              horizontal: 14.w,
                            ),
                            suffixAssetNameIcon:
                                'assets/icons/calendar_today.svg',
                            suffixIconConstraints: const BoxConstraints(
                                maxHeight: 60, maxWidth: 60),
                          ),
                        ),
                      ),
                    ],
                  ),
                  32.verticalSpace,
                  // Year Completed
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomTextWigdet(
                            title: 'Year Completed',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            textColor: secondWhiteColor.withOpacity(0.8),
                          ),
                          5.horizontalSpace,
                          SvgPicture.asset(
                            'assets/icons/Label_.svg',
                            width: 6,
                            height: 6,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () {
                          controller.openYearCompleted();
                        },
                        child: AbsorbPointer(
                          child: CustomTextFieldWidget(
                            controller: controller.yearCompletedController,
                            hintText: 'Select year completed',
                            fontSize: 18,
                            fontWeight: FontWeight.w400,
                            colorText: secondWhiteColor.withOpacity(0.9),
                            colorTextHint: greyColor,
                            colorField: baseBlueColor.withOpacity(0.5),
                            radius: 8,
                            widthField: 555,
                            heightField: 52,
                            contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h,
                              horizontal: 14.w,
                            ),
                            suffixAssetNameIcon:
                                'assets/icons/calendar_today.svg',
                            suffixIconConstraints: const BoxConstraints(
                                maxHeight: 60, maxWidth: 60),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                uploadSection: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CustomTextWigdet(
                          title: 'Certificate',
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          textColor: secondWhiteColor.withOpacity(0.8),
                        ),
                        5.horizontalSpace,
                        SvgPicture.asset(
                          'assets/icons/Label_.svg',
                          width: 6,
                          height: 6,
                        ),
                      ],
                    ),
                    8.verticalSpace,
                    Obx(() {
                      if (controller.selectedFiles.isEmpty) {
                        return GestureDetector(
                          onTap: () async {
                            FilePickerResult? result =
                                await FilePicker.platform.pickFiles(
                              allowMultiple: true,
                              type: FileType.custom,
                              allowedExtensions: [
                                'jpg',
                                'pdf',
                                'png',
                                'doc',
                                'docx'
                              ],
                            );

                            if (result != null) {
                              controller.addFiles(result.files);
                            }
                          },
                          child: DottedBorder(
                            color: blueColor,
                            strokeWidth: 1.5,
                            dashPattern: [8, 4],
                            borderType: BorderType.RRect,
                            radius: const Radius.circular(12),
                            child: Container(
                              height: 120,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Center(
                                child: CustomTextWigdet(
                                  title:
                                      'Klik untuk mengunggah Format yang didukung: PDF, PNG, JPG, JPEG · Ukuran maksimal: 10MB',
                                  fontSize: 16,
                                  textColor: whiteColor,
                                  fontWeight: FontWeight.w500,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        );
                      } else {
                        return ListView.separated(
                          shrinkWrap: true,
                          itemCount: controller.selectedFiles.length,
                          separatorBuilder: (_, __) =>
                              const SizedBox(height: 8),
                          itemBuilder: (context, index) {
                            final file = controller.selectedFiles[index];
                            return CustomContainer(
                              width: 555,
                              height: 69,
                              bgColor: baseBlueColor.withOpacity(0.7),
                              radius: 8,
                              border: Border.all(
                                color: secondWhiteColor.withOpacity(0.8),
                                width: 0.5,
                              ),
                              widget: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.w),
                                child: Row(
                                  children: [
                                    CustomContainer(
                                      width: 40,
                                      height: 40,
                                      bgColor: greyColor,
                                      radius: 8,
                                      widget: Center(
                                        child: Icon(
                                          Icons.picture_as_pdf_outlined,
                                          color: whiteColor,
                                          size: 16.r,
                                        ),
                                      ),
                                    ),
                                    10.horizontalSpace,
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          CustomTextWigdet(
                                            title: file.name,
                                            fontSize: 16,
                                            fontWeight: FontWeight.w500,
                                            textColor: whiteColor,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          CustomTextWigdet(
                                            title:
                                                '${(file.size / (1024 * 1024)).toStringAsFixed(1)} MB',
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            textColor: whiteColor,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                    CustomFilledButtonWidget(
                                      onPressed: () async {
                                        FilePickerResult? result =
                                            await FilePicker.platform.pickFiles(
                                          allowMultiple: false,
                                          type: FileType.custom,
                                          allowedExtensions: [
                                            'jpg',
                                            'pdf',
                                            'png',
                                            'doc',
                                            'docx'
                                          ],
                                        );

                                        if (result != null &&
                                            result.files.isNotEmpty) {
                                          controller.replaceWithNewFile(
                                              result.files.first);
                                        }
                                      },
                                      withIcon: true,
                                      onlyIcon: true,
                                      assetName: 'assets/icons/edit.svg',
                                      iconColor: whiteColor,
                                      widthIcon: 18,
                                      heightIcon: 18,
                                      widthButton: 34,
                                      heightButton: 36,
                                      isOutlined: true,
                                      radius: 4,
                                      borderColor:
                                          secondWhiteColor.withOpacity(0.7),
                                    ),
                                    10.horizontalSpace,
                                    CustomFilledButtonWidget(
                                      onPressed: () {
                                        controller.removeFile(index);
                                      },
                                      withIcon: true,
                                      onlyIcon: true,
                                      assetName:
                                          'assets/icons/delete_outline.svg',
                                      widthIcon: 18,
                                      heightIcon: 18,
                                      widthButton: 34,
                                      heightButton: 36,
                                      isOutlined: true,
                                      radius: 4,
                                      borderColor: redColor,
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                      }
                    })
                  ],
                ),
              ),
              Obx(() {
                if (controller.isOpenYearStart.value) {
                  return Center(
                    child: Material(
                      color: baseBlueColor,
                      borderRadius: BorderRadius.circular(8),
                      child: CustomYearPickerDialog(
                        initialYear: int.tryParse(
                                controller.yearStartedController.text) ??
                            DateTime.now().year,
                        endYear: DateTime.now().year,
                        onYearSelected: (year) {
                          controller.yearStartedController.text =
                              year.toString();
                          controller.isOpenYearStart.value = false;
                        },
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
              Obx(() {
                if (controller.isOpenYearCompleted.value) {
                  return Center(
                    child: Material(
                      color: baseBlueColor,
                      borderRadius: BorderRadius.circular(8),
                      child: CustomYearPickerDialog(
                        initialYear: int.tryParse(
                                controller.yearCompletedController.text) ??
                            DateTime.now().year,
                        endYear: DateTime.now().year,
                        onYearSelected: (year) {
                          controller.yearCompletedController.text =
                              year.toString();
                          controller.isOpenYearCompleted.value = false;
                        },
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
        );
      },

      //KONTEN DATA
      onSearchChanged: controller.updateSearchQuery,
      tableContent: Obx(() {
        controller.searchQuery.value.toLowerCase();

        if (controller.isLoading.value) {
          return SizedBox(
            height: 650.h,
            child: const Center(
              child: CustomLoadingWidget(
                width: 60,
                height: 60,
                imageWidth: 60,
                imageHeight: 60,
                strokeWidth: 3,
              ),
            ),
          );
        }
        if (controller.coursesList.isEmpty) {
          return SizedBox(
            height: 650.h,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('assets/icons/empty state.svg'),
                10.verticalSpace,
                CustomTextWigdet(
                  title: 'Data not available',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
                6.verticalSpace,
                CustomTextWigdet(
                  title: 'There are no data uploaded yet.',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  textColor: whiteColor,
                ),
              ],
            ),
          );
        }
        return Column(
          children: List.generate(controller.coursesList.length, (index) {
            final item = controller.coursesList[index];
            final query = controller.searchQuery.value.toLowerCase();
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 24.w),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: CustomTextWigdet(
                      title: '${index + 1}',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: whiteColor,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: CustomContainer(
                      radius: 4,
                      bgColor: query.isNotEmpty &&
                              (item.courseName?.toLowerCase().contains(query) ??
                                  false)
                          ? goldenYellow.withOpacity(0.4)
                          : Colors.transparent,
                      widget: Padding(
                        padding: EdgeInsets.all(5.r),
                        child: CustomTextWigdet(
                          title: item.courseName ?? '-',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          textColor: whiteColor,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: CustomTextWigdet(
                      title: item.yearStarted?.toString() ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: whiteColor,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: CustomTextWigdet(
                      title: item.yearCompleted?.toString() ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: whiteColor,
                    ),
                  ),
                  //EDIT
                  Expanded(
                    flex: 1,
                    child: Row(
                      children: [
                        CustomFilledButtonWidget(
                          onPressed: () {
                            controller.coursesNameController.text =
                                item.courseName ?? '';

                            controller.yearStartedController.text =
                                item.yearStarted?.toString() ?? '';

                            controller.yearCompletedController.text =
                                item.yearCompleted?.toString() ?? '';

                            controller.existingCertificate = item.certificate;
                            controller.selectedFiles.clear();

                            if (item.certificate != null &&
                                item.certificate!.isNotEmpty) {
                              controller.selectedFiles.add(
                                PlatformFile(
                                  name: item.certificate!.split("/").last,
                                  size: 0,
                                  path: item.certificate!,
                                ),
                              );
                            }

                            Get.dialog(
                              barrierDismissible: false,
                              Stack(
                                children: [
                                  AdaptiveFormDialog(
                                    title: 'Add Course',
                                    description:
                                        'Add course details and attach certificate.',
                                    onCancel: () {
                                      controller.clearForm();
                                      Get.back();
                                    },
                                    onSave: () async {
                                      await controller.updateCourse(item.id!);
                                    },
                                    isSaveEnabled: true,
                                    formFields: [
                                      // Course Name
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              CustomTextWigdet(
                                                title: 'Course Name',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                textColor: secondWhiteColor
                                                    .withOpacity(0.8),
                                              ),
                                              5.horizontalSpace,
                                              SvgPicture.asset(
                                                'assets/icons/Label_.svg',
                                                width: 6,
                                                height: 6,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          CustomTextFieldWidget(
                                            controller: controller
                                                .coursesNameController,
                                            hintText:
                                                'Ex: University Taruna Negara',
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400,
                                            colorText: secondWhiteColor
                                                .withOpacity(0.9),
                                            colorTextHint: greyColor,
                                            colorField:
                                                baseBlueColor.withOpacity(0.5),
                                            radius: 8,
                                            widthField: 555,
                                            heightField: 52,
                                            contentPadding:
                                                EdgeInsetsDirectional.symmetric(
                                                    vertical: 10.h,
                                                    horizontal: 14.w),
                                          )
                                        ],
                                      ),
                                      32.verticalSpace,
                                      // Year Started
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              CustomTextWigdet(
                                                title: 'Year Started',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                textColor: secondWhiteColor
                                                    .withOpacity(0.8),
                                              ),
                                              5.horizontalSpace,
                                              SvgPicture.asset(
                                                'assets/icons/Label_.svg',
                                                width: 6,
                                                height: 6,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          GestureDetector(
                                            onTap: () {
                                              controller.openYearStart();
                                            },
                                            child: AbsorbPointer(
                                              child: CustomTextFieldWidget(
                                                controller: controller
                                                    .yearStartedController,
                                                hintText: 'Select year started',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                colorText: secondWhiteColor
                                                    .withOpacity(0.9),
                                                colorTextHint: greyColor,
                                                colorField: baseBlueColor
                                                    .withOpacity(0.5),
                                                radius: 8,
                                                widthField: 555,
                                                heightField: 52,
                                                contentPadding:
                                                    EdgeInsetsDirectional
                                                        .symmetric(
                                                  vertical: 10.h,
                                                  horizontal: 14.w,
                                                ),
                                                suffixAssetNameIcon:
                                                    'assets/icons/calendar_today.svg',
                                                suffixIconConstraints:
                                                    const BoxConstraints(
                                                        maxHeight: 60,
                                                        maxWidth: 60),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      32.verticalSpace,
                                      // Year Completed
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              CustomTextWigdet(
                                                title: 'Year Completed',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                textColor: secondWhiteColor
                                                    .withOpacity(0.8),
                                              ),
                                              5.horizontalSpace,
                                              SvgPicture.asset(
                                                'assets/icons/Label_.svg',
                                                width: 6,
                                                height: 6,
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          GestureDetector(
                                            onTap: () {
                                              controller.openYearCompleted();
                                            },
                                            child: AbsorbPointer(
                                              child: CustomTextFieldWidget(
                                                controller: controller
                                                    .yearCompletedController,
                                                hintText:
                                                    'Select year completed',
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                colorText: secondWhiteColor
                                                    .withOpacity(0.9),
                                                colorTextHint: greyColor,
                                                colorField: baseBlueColor
                                                    .withOpacity(0.5),
                                                radius: 8,
                                                widthField: 555,
                                                heightField: 52,
                                                contentPadding:
                                                    EdgeInsetsDirectional
                                                        .symmetric(
                                                  vertical: 10.h,
                                                  horizontal: 14.w,
                                                ),
                                                suffixAssetNameIcon:
                                                    'assets/icons/calendar_today.svg',
                                                suffixIconConstraints:
                                                    const BoxConstraints(
                                                        maxHeight: 60,
                                                        maxWidth: 60),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                    uploadSection: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            CustomTextWigdet(
                                              title: 'Certificate',
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400,
                                              textColor: secondWhiteColor
                                                  .withOpacity(0.8),
                                            ),
                                            5.horizontalSpace,
                                            SvgPicture.asset(
                                              'assets/icons/Label_.svg',
                                              width: 6,
                                              height: 6,
                                            ),
                                          ],
                                        ),
                                        8.verticalSpace,
                                        Obx(() {
                                          if (controller
                                              .selectedFiles.isEmpty) {
                                            return GestureDetector(
                                              onTap: () async {
                                                FilePickerResult? result =
                                                    await FilePicker.platform
                                                        .pickFiles(
                                                  allowMultiple: true,
                                                  type: FileType.custom,
                                                  allowedExtensions: [
                                                    'jpg',
                                                    'pdf',
                                                    'png',
                                                    'doc',
                                                    'docx'
                                                  ],
                                                );

                                                if (result != null) {
                                                  controller
                                                      .addFiles(result.files);
                                                }
                                              },
                                              child: DottedBorder(
                                                color: blueColor,
                                                strokeWidth: 1.5,
                                                dashPattern: [8, 4],
                                                borderType: BorderType.RRect,
                                                radius:
                                                    const Radius.circular(12),
                                                child: Container(
                                                  height: 120,
                                                  width: double.infinity,
                                                  decoration: BoxDecoration(
                                                    color: Colors.white
                                                        .withOpacity(0.05),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                  child: Center(
                                                    child: CustomTextWigdet(
                                                      title:
                                                          'Klik untuk mengunggah Format yang didukung: PDF, PNG, JPG, JPEG · Ukuran maksimal: 10MB',
                                                      fontSize: 16,
                                                      textColor: whiteColor,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            );
                                          } else {
                                            return ListView.separated(
                                              shrinkWrap: true,
                                              itemCount: controller
                                                  .selectedFiles.length,
                                              separatorBuilder: (_, __) =>
                                                  const SizedBox(height: 8),
                                              itemBuilder: (context, index) {
                                                final file = controller
                                                    .selectedFiles[index];
                                                return CustomContainer(
                                                  width: 555,
                                                  height: 69,
                                                  bgColor: baseBlueColor
                                                      .withOpacity(0.7),
                                                  radius: 8,
                                                  border: Border.all(
                                                    color: secondWhiteColor
                                                        .withOpacity(0.8),
                                                    width: 0.5,
                                                  ),
                                                  widget: Padding(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    child: Row(
                                                      children: [
                                                        CustomContainer(
                                                          width: 40,
                                                          height: 40,
                                                          bgColor: greyColor,
                                                          radius: 8,
                                                          widget: Center(
                                                            child: Icon(
                                                              Icons
                                                                  .picture_as_pdf_outlined,
                                                              color: whiteColor,
                                                              size: 16.r,
                                                            ),
                                                          ),
                                                        ),
                                                        10.horizontalSpace,
                                                        Expanded(
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .center,
                                                            children: [
                                                              CustomTextWigdet(
                                                                title:
                                                                    file.name,
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                textColor:
                                                                    whiteColor,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                              ),
                                                              CustomTextWigdet(
                                                                title:
                                                                    '${(file.size / (1024 * 1024)).toStringAsFixed(1)} MB',
                                                                fontSize: 12,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                textColor:
                                                                    whiteColor,
                                                                overflow:
                                                                    TextOverflow
                                                                        .ellipsis,
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        CustomFilledButtonWidget(
                                                          onPressed: () async {
                                                            FilePickerResult?
                                                                result =
                                                                await FilePicker
                                                                    .platform
                                                                    .pickFiles(
                                                              allowMultiple:
                                                                  false,
                                                              type: FileType
                                                                  .custom,
                                                              allowedExtensions: [
                                                                'jpg',
                                                                'pdf',
                                                                'png',
                                                                'doc',
                                                                'docx'
                                                              ],
                                                            );

                                                            if (result !=
                                                                    null &&
                                                                result.files
                                                                    .isNotEmpty) {
                                                              controller
                                                                  .replaceWithNewFile(
                                                                      result
                                                                          .files
                                                                          .first);
                                                            }
                                                          },
                                                          withIcon: true,
                                                          onlyIcon: true,
                                                          assetName:
                                                              'assets/icons/edit.svg',
                                                          iconColor: whiteColor,
                                                          widthIcon: 18,
                                                          heightIcon: 18,
                                                          widthButton: 34,
                                                          heightButton: 36,
                                                          isOutlined: true,
                                                          radius: 4,
                                                          borderColor:
                                                              secondWhiteColor
                                                                  .withOpacity(
                                                                      0.7),
                                                        ),
                                                        10.horizontalSpace,
                                                        CustomFilledButtonWidget(
                                                          onPressed: () {
                                                            controller
                                                                .removeFile(
                                                                    index);
                                                          },
                                                          withIcon: true,
                                                          onlyIcon: true,
                                                          assetName:
                                                              'assets/icons/delete_outline.svg',
                                                          widthIcon: 18,
                                                          heightIcon: 18,
                                                          widthButton: 34,
                                                          heightButton: 36,
                                                          isOutlined: true,
                                                          radius: 4,
                                                          borderColor: redColor,
                                                        )
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            );
                                          }
                                        })
                                      ],
                                    ),
                                  ),
                                  Obx(() {
                                    if (controller.isOpenYearStart.value) {
                                      return Center(
                                        child: Material(
                                          color: baseBlueColor,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: CustomYearPickerDialog(
                                            initialYear: int.tryParse(controller
                                                    .yearStartedController
                                                    .text) ??
                                                DateTime.now().year,
                                            endYear: DateTime.now().year,
                                            onYearSelected: (year) {
                                              controller.yearStartedController
                                                  .text = year.toString();
                                              controller.isOpenYearStart.value =
                                                  false;
                                            },
                                          ),
                                        ),
                                      );
                                    }
                                    return const SizedBox.shrink();
                                  }),
                                  Obx(() {
                                    if (controller.isOpenYearCompleted.value) {
                                      return Center(
                                        child: Material(
                                          color: baseBlueColor,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: CustomYearPickerDialog(
                                            initialYear: int.tryParse(controller
                                                    .yearCompletedController
                                                    .text) ??
                                                DateTime.now().year,
                                            endYear: DateTime.now().year,
                                            onYearSelected: (year) {
                                              controller.yearCompletedController
                                                  .text = year.toString();
                                              controller.isOpenYearCompleted
                                                  .value = false;
                                            },
                                          ),
                                        ),
                                      );
                                    }
                                    return const SizedBox.shrink();
                                  }),
                                ],
                              ),
                            );
                          },
                          onlyIcon: true,
                          withIcon: true,
                          assetName: 'assets/icons/edit.svg',
                          iconColor: blueColor,
                          widthIcon: 34,
                          heightIcon: 34,
                          widthButton: 34,
                          heightButton: 34,
                          radius: 4,
                          bgColor: Colors.transparent,
                        ),
                        10.horizontalSpace,
                        CustomFilledButtonWidget(
                          onPressed: () {
                            controller.deleteCourse(item.id ?? '');
                          },
                          onlyIcon: true,
                          withIcon: true,
                          assetName: 'assets/icons/delete_outline.svg',
                          iconColor: redColor,
                          widthIcon: 34,
                          heightIcon: 34,
                          widthButton: 34,
                          heightButton: 34,
                          radius: 4,
                          bgColor: Colors.transparent,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        );
      }),
    );
  }
}
