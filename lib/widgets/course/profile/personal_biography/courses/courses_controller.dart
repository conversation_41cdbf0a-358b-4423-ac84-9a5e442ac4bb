import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mides_skadik/app/data/models/response/course/profile/course/course_model.dart';
import 'package:mides_skadik/app/data/services/course/profile/course/course_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class CoursesController extends GetxController {
  final selectedFiles = <PlatformFile>[].obs;
  final CoursesService service = CoursesService();
  final coursesList = <CourseModel>[].obs;
  String? existingCertificate;
  var isOpenYearStart = false.obs;
  var isOpenYearCompleted = false.obs;
  final filteredCoursesList = <CourseModel>[].obs;
  final searchQuery = ''.obs;
  var isLoading = false.obs;

  final TextEditingController coursesNameController = TextEditingController();
  final TextEditingController yearStartedController = TextEditingController();
  final TextEditingController yearCompletedController = TextEditingController();

  void openYearStart() => isOpenYearStart.toggle();
  void openYearCompleted() => isOpenYearCompleted.toggle();

  void addFiles(List<PlatformFile> files) {
    selectedFiles.addAll(files);
  }

  void removeFile(int index) {
    selectedFiles.removeAt(index);
  }

  void replaceWithNewFile(PlatformFile newFile) {
    selectedFiles.value = [newFile];
  }

  void clearForm() {
    coursesNameController.clear();
    yearStartedController.clear();
    yearCompletedController.clear();
    selectedFiles.clear();
    isOpenYearStart.value = false;
    isOpenYearCompleted.value = false;
  }

  Future<void> getCoursesList() async {
    isLoading.value = true;
    final result = await service.getCourses();
    if (result.isSuccess) {
      coursesList.assignAll(result.resultValue ?? []);
      filteredCoursesList.assignAll(result.resultValue ?? []);
    } else {
      LogService.log.e('Failed to load courses: ${result.errorMessage}');
    }
    isLoading.value = false;
  }

  Future<void> submitCourse() async {
    final name = coursesNameController.text.trim();
    final yearStart = int.tryParse(yearStartedController.text.trim()) ?? 0;
    final yearEnd = int.tryParse(yearCompletedController.text.trim()) ?? 0;

    if (name.isEmpty ||
        yearStart == 0 ||
        yearEnd == 0 ||
        selectedFiles.isEmpty) {
      SnackbarUtil.showOnce(title: 'Error', message: 'Lengkapi semua field.');
      return;
    }

    final result = await service.postCourse(
      courseName: name,
      yearStarted: yearStart,
      yearCompleted: yearEnd,
      certificateFilePath: selectedFiles.first.path!,
    );

    if (result.isSuccess) {
      clearForm();
      Get.back();
      SnackbarUtil.showOnce(title: 'Berhasil', message: 'Course ditambahkan.');
      getCoursesList();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Error saat tambah course.');
    }
  }

  Future<void> updateCourse(String id) async {
    final course = coursesList.firstWhereOrNull((e) => e.id == id);
    if (course == null) return;

    final name = coursesNameController.text.trim().isEmpty
        ? course.courseName ?? ''
        : coursesNameController.text.trim();
    final yearStart = int.tryParse(yearStartedController.text.trim()) ??
        course.yearStarted ??
        0;
    final yearEnd = int.tryParse(yearCompletedController.text.trim()) ??
        course.yearCompleted ??
        0;

    final file = selectedFiles
        .firstWhereOrNull((f) => !(f.path?.startsWith('http') ?? true));
    if (file != null) {}

    final result = await service.editCourse(
      id: id,
      courseName: name,
      yearStarted: yearStart,
      yearCompleted: yearEnd,
      certificateFilePath: selectedFiles.first.path!,
    );

    if (result.isSuccess) {
      SnackbarUtil.showOnce(title: 'Berhasil', message: 'Course diupdate.');
      clearForm();
      Get.back();
      getCoursesList();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Error saat update course.');
    }
  }

  Future<void> deleteCourse(String id) async {
    final result = await service.deleteCourse(id);
    if (result.isSuccess) {
      SnackbarUtil.showOnce(title: 'Berhasil', message: 'Course dihapus.');
      getCoursesList();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Error saat hapus course.');
    }
  }

  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
    _applySearch();
  }

  void _applySearch() {
    final query = searchQuery.value;
    if (query.isEmpty) {
      filteredCoursesList.assignAll(coursesList);
    } else {
      filteredCoursesList.assignAll(
        coursesList.where((item) =>
            (item.courseName?.toLowerCase().contains(query) ?? false) ||
            (item.yearCompleted?.toString().contains(query) ?? false) ||
            (item.yearStarted?.toString().contains(query) ?? false)),
      );
    }
  }

  @override
  void onInit() {
    super.onInit();
    getCoursesList();
  }
}
