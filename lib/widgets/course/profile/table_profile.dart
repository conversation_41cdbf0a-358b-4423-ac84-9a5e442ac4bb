import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class TableProfile extends StatelessWidget {
  final String? title;
  final double? titleFontSize;
  final double? columnTitleFontSize;
  final double? searchHintFontSize;
  final double? addButtonTextFontSize;
  final String? columnTitle1;
  final String? columnTitle2;
  final String? columnTitle3;
  final String? columnTitle4;
  final String? columnTitle5;
  final int? columnFlex1;
  final int? columnFlex2;
  final int? columnFlex3;
  final int? columnFlex4;
  final int? columnFlex5;
  final VoidCallback? onAddPressed;
  final String addButtonText;
  final bool showAddButton;
  final bool showSearchField;
  final String searchHint;
  final EdgeInsetsDirectional? contentPadding;
  final Widget? tableHeader;
  final Widget? tableContent;
  final Widget? emptyState;
  final Function(String)? onSearchChanged;

  const TableProfile({
    Key? key,
    this.title,
    this.titleFontSize,
    this.searchHintFontSize,
    this.addButtonTextFontSize,
    this.columnTitleFontSize,
    this.columnTitle1,
    this.columnTitle2,
    this.columnTitle3,
    this.columnTitle4,
    this.columnTitle5,
    this.columnFlex1,
    this.columnFlex2,
    this.columnFlex3,
    this.columnFlex4,
    this.columnFlex5,
    this.onAddPressed,
    this.addButtonText = 'Add New',
    this.showAddButton = true,
    this.showSearchField = true,
    this.searchHint = 'Search',
    this.contentPadding,
    this.tableHeader,
    this.tableContent,
    this.emptyState,
    this.onSearchChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      radius: 8,
      widget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomTextWigdet(
            title: title ?? '',
            fontSize: titleFontSize ?? 18,
            fontWeight: FontWeight.w500,
            textColor: whiteColor,
          ),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (showSearchField)
                SizedBox(
                  width: 282.w,
                  height: 48.h,
                  child: CustomTextFieldWidget(
                    colorField: secondBlueColor.withOpacity(0.2),
                    contentPadding: EdgeInsetsDirectional.symmetric(
                        horizontal: 10.w, vertical: 0),
                    radius: 8,
                    hintText: searchHint,
                    colorTextHint: secondWhiteColor.withValues(alpha: 0.8),
                    fontSize: searchHintFontSize ?? 18,
                    colorText: secondWhiteColor,
                    iconHeight: 15,
                    assetNameIcon: 'assets/icons/search.svg',
                    colorPrefixIcon: secondWhiteColor.withValues(alpha: 0.8),
                    onChanged: onSearchChanged,
                  ),
                ),
              if (showAddButton)
                CustomFilledButtonWidget(
                  onPressed: onAddPressed ?? () {},
                  title: addButtonText,
                  fontSize: addButtonTextFontSize ?? 16,
                  fontWeight: FontWeight.w500,
                  assetName: 'assets/icons/plus.svg',
                  withIcon: true,
                  fontColor: whiteColor,
                  bgColor: blueColor,
                  radius: 5,
                  heightButton: 44,
                  widthButton: 138,
                  padding: EdgeInsets.symmetric(horizontal: 5.w),
                ),
            ],
          ),
          16.verticalSpace,
          CustomContainer(
            width: double.infinity,
            radius: 8,
            widget: Column(
              children: [
                tableHeader ??
                    CustomContainer(
                      radius: 8,
                      bgColor: secondBlueColor.withOpacity(0.2),
                      widget: Padding(
                        padding: EdgeInsets.all(14.r),
                        child: IntrinsicHeight(
                          child: Row(
                            children: [
                              if (columnTitle1 != null)
                                Expanded(
                                  flex: columnFlex1 ?? 1,
                                  child: CustomTextWigdet(
                                    title: columnTitle1!,
                                    fontSize: columnTitleFontSize ?? 14,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor.withOpacity(0.7),
                                  ),
                                ),
                              if (columnTitle2 != null)
                                Expanded(
                                  flex: columnFlex2 ?? 1,
                                  child: CustomTextWigdet(
                                    title: columnTitle2!,
                                    fontSize: columnTitleFontSize ?? 14,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor.withOpacity(0.7),
                                  ),
                                ),
                              if (columnTitle3 != null)
                                Expanded(
                                  flex: columnFlex3 ?? 1,
                                  child: CustomTextWigdet(
                                    title: columnTitle3!,
                                    fontSize: columnTitleFontSize ?? 14,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor.withOpacity(0.7),
                                  ),
                                ),
                              if (columnTitle4 != null)
                                Expanded(
                                  flex: columnFlex4 ?? 1,
                                  child: CustomTextWigdet(
                                    title: columnTitle4!,
                                    fontSize: columnTitleFontSize ?? 14,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor.withOpacity(0.7),
                                  ),
                                ),
                              if (columnTitle5 != null)
                                Expanded(
                                  flex: columnFlex5 ?? 1,
                                  child: CustomTextWigdet(
                                    title: columnTitle5!,
                                    fontSize: columnTitleFontSize ?? 14,
                                    fontWeight: FontWeight.w500,
                                    textColor: whiteColor.withOpacity(0.7),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                20.verticalSpace,
                SizedBox(
                  height: 650.h,
                  child: SingleChildScrollView(
                    child: tableContent ??
                        emptyState ??
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset('assets/icons/empty state.svg'),
                            10.verticalSpace,
                            CustomTextWigdet(
                              title: 'Data not available',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              textColor: whiteColor,
                            ),
                            6.verticalSpace,
                            CustomTextWigdet(
                              title: 'There are no data uploaded yet.',
                              fontSize: columnTitleFontSize ?? 14,
                              fontWeight: FontWeight.w400,
                              textColor: whiteColor,
                            ),
                          ],
                        ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
