import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/profile_course/controllers/profile_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ProgressProfile extends StatelessWidget {
  const ProgressProfile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileCourseController>();

    return Obx(() {
      final progress =
          (controller.profileData.value?.profileCompletionPercentage ?? 0) /
              100;

      return CustomContainer(
        width: double.infinity,
        radius: 8,
        bgColor: baseBlueColor.withOpacity(0.45),
        widget: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 28.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              CustomTextWigdet(
                title: 'Some important information is still missing.',
                fontSize: 20,
                fontWeight: FontWeight.w500,
                textColor: secondWhiteColor,
              ),
              CustomTextWigdet(
                title:
                    'Your profile is not yet 100% complete. Please complete it now to proceed with further educational requirements.',
                fontSize: 18,
                fontWeight: FontWeight.w400,
                textColor: secondWhiteColor.withOpacity(0.7),
              ),
              15.verticalSpace,
              LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Stack(
                          children: [
                            Container(
                              height: 10.h,
                              decoration: BoxDecoration(
                                color: baseBlueColor,
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            Container(
                              width: constraints.maxWidth * progress,
                              height: 10.h,
                              decoration: BoxDecoration(
                                color: blueColor,
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                          ],
                        ),
                      ),
                      10.horizontalSpace,
                      CustomTextWigdet(
                        title: '${(progress * 100).toInt()}%',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        textColor: secondWhiteColor.withOpacity(0.9),
                      ),
                    ],
                  );
                },
              ),
              15.verticalSpace,
              CustomFilledButtonWidget(
                onPressed: () {
                  controller.selectedMenuProfile(2);
                },
                title: 'Complete Now',
                fontSize: 16,
                fontWeight: FontWeight.w500,
                widthButton: 153,
                heightButton: 44,
                fontColor: whiteColor,
                bgColor: blueColor,
                radius: 4,
              )
            ],
          ),
        ),
      );
    });
  }
}
