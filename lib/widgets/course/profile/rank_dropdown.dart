import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class RankDropdown extends StatefulWidget {
  final List<String> ranks;
  final String? selected;
  final void Function(String)? onSelected;

  const RankDropdown({
    super.key,
    required this.ranks,
    this.selected,
    this.onSelected,
  });

  @override
  State<RankDropdown> createState() => _RankDropdownState();
}

class _RankDropdownState extends State<RankDropdown> {
  bool isExpanded = false;
  late String? selected;

  @override
  void initState() {
    super.initState();
    selected = widget.selected;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => setState(() => isExpanded = !isExpanded),
          child: CustomContainer(
            width: 555,
            height: 52,
            radius: 8,
            bgColor: baseBlueColor.withOpacity(0.3),
            widget: Padding(
              padding: EdgeInsets.symmetric(horizontal: 14.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomTextWigdet(
                    title: (selected == null || selected!.isEmpty)
                        ? 'Select Rank'
                        : selected!,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    textColor: secondWhiteColor.withOpacity(0.9),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: whiteColor,
                  ),
                ],
              ),
            ),
          ),
        ),
        5.verticalSpace,
        if (isExpanded)
          CustomContainer(
            width: 555,
            bgColor: baseBlueColor.withOpacity(0.3),
            radius: 8,
            widget: Column(
              children: widget.ranks.map((rank) {
                return InkWell(
                  onTap: () {
                    setState(() {
                      selected = rank;
                      isExpanded = false;
                    });
                    if (widget.onSelected != null) {
                      widget.onSelected!(rank);
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 14.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Text di kiri
                        CustomTextWigdet(
                          title: rank,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          textColor: secondWhiteColor.withOpacity(0.9),
                        ),
                        // Radio bulat di kanan
                        Radio<String>(
                          value: rank,
                          groupValue: selected,
                          onChanged: (value) {
                            setState(() {
                              selected = value;
                              isExpanded = false;
                            });
                            if (widget.onSelected != null && value != null) {
                              widget.onSelected!(value);
                            }
                          },
                          activeColor: blueColor,
                          fillColor: MaterialStateProperty.resolveWith<Color>(
                              (states) {
                            if (states.contains(MaterialState.selected)) {
                              return blueColor;
                            }
                            return secondWhiteColor.withOpacity(0.6);
                          }),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
}
