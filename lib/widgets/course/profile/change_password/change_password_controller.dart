import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/course/profile/change_password/change_password_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/widgets/components/custom_snackbar.dart';

class ChangePasswordController extends GetxController {
  final oldPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final isMinCharValid = false.obs;
  final isUppercaseValid = false.obs;
  final isLowercaseValid = false.obs;
  final isSpecialCharValid = false.obs;
  final isNumberValid = false.obs;
  final isFormValid = false.obs;

  final isLoading = false.obs;

  final ChangePasswordService _service = ChangePasswordService();

  @override
  void onInit() {
    super.onInit();
    newPasswordController.addListener(_validatePassword);
  }

  void _validatePassword() {
    final password = newPasswordController.text;
    isMinCharValid.value = password.length >= 8;
    isUppercaseValid.value = password.contains(RegExp(r'[A-Z]'));
    isLowercaseValid.value = password.contains(RegExp(r'[a-z]'));
    isSpecialCharValid.value =
        password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    isNumberValid.value = password.contains(RegExp(r'\d'));

    isFormValid.value = isMinCharValid.value &&
        isUppercaseValid.value &&
        isLowercaseValid.value &&
        isSpecialCharValid.value &&
        isNumberValid.value;
  }

  Future<void> changePassword() async {
    final oldPassword = oldPasswordController.text.trim();
    final newPassword = newPasswordController.text.trim();
    final confirmPassword = confirmPasswordController.text.trim();

    if (oldPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty) {
      SnackbarUtil.showOnce(
          title: 'Peringatan', message: 'Semua inputan wajib diisi.');
      return;
    }

    if (newPassword != confirmPassword) {
      SnackbarUtil.showOnce(
          title: 'Peringatan',
          message: 'Kata sandi baru dan konfirmasi tidak cocok.');
      return;
    }

    isLoading.value = true;

    final result = await _service.changePassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
    );

    isLoading.value = false;

    if (result.isSuccess) {
      SnackbarUtil.showOnce(
          title: 'Berhasil',
          message: result.succesMessage ?? 'Kata sandi berhasil diganti.');
      oldPasswordController.clear();
      newPasswordController.clear();
      confirmPasswordController.clear();
    } else {
      SnackbarUtil.showOnce(
          title: 'Gagal',
          message: result.errorMessage ?? 'Gagal mengganti kata sandi.');
      LogService.log.w("Failed: ${result.errorMessage}");
    }
  }

  @override
  void onClose() {
    oldPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}
