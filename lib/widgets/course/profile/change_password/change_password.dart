import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/profile/change_password/change_password_controller.dart';
import 'package:mides_skadik/widgets/course/profile/change_password/change_password_landscape.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ChangePassword extends StatelessWidget {
  const ChangePassword({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ChangePasswordController());

    final orientation = MediaQuery.of(context).orientation;
    if (orientation == Orientation.landscape) {
      return const ChangePasswordLandscape();
    }
    return CustomContainer(
      width: 1320,
      radius: 8,
      bgColor: baseBlueColor.withOpacity(0.3),
      widget: Padding(
        padding: EdgeInsets.symmetric(vertical: 18.h, horizontal: 28.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextWigdet(
              title: 'Change Password',
              fontSize: 18,
              fontWeight: FontWeight.w500,
              textColor: whiteColor,
            ),
            const Divider(),
            40.verticalSpace,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomContainer(
                    width: double.infinity,
                    widget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CustomTextWigdet(
                          title: 'Change Password',
                          fontSize: 20,
                          fontWeight: FontWeight.w500,
                          textColor: whiteColor,
                        ),
                        CustomTextWigdet(
                          title:
                              'For your security, please update your password regularly and ensure its strong and confidential.',
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          textColor: secondWhiteColor.withOpacity(0.8),
                        ),
                      ],
                    ),
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                  child: CustomContainer(
                    widget: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextWigdet(
                              title: 'Old Password',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.9),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'enter your old password',
                          controller: controller.oldPasswordController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondGreyColor,
                          radius: 4,
                          widthField: double.infinity,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          suffixAssetNameIcon: "assets/icons/eye.svg",
                          suffixIconConstraints:
                              const BoxConstraints(maxWidth: 50, maxHeight: 50),
                          colorSuffixIcon: secondWhiteColor.withOpacity(0.8),
                          colorText: whiteColor,
                        ),
                        32.verticalSpace,
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextWigdet(
                              title: 'New Password',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.9),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'enter your new password',
                          controller: controller.newPasswordController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondGreyColor,
                          radius: 4,
                          widthField: double.infinity,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          suffixAssetNameIcon: "assets/icons/eye.svg",
                          suffixIconConstraints:
                              const BoxConstraints(maxWidth: 50, maxHeight: 50),
                          colorSuffixIcon: secondWhiteColor.withOpacity(0.8),
                          colorText: whiteColor,
                        ),
                        6.verticalSpace,
                        Row(
                          children: [
                            SizedBox(
                              child: Row(
                                children: [
                                  Obx(() => CustomContainer(
                                        shape: BoxShape.circle,
                                        bgColor: controller.isMinCharValid.value
                                            ? greenColor
                                            : Colors.grey,
                                        widget: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(2.r),
                                            child: SvgPicture.asset(
                                              'assets/icons/check_course.svg',
                                              width: 8,
                                              height: 8,
                                              color: whiteColor,
                                            ),
                                          ),
                                        ),
                                      )),
                                  5.horizontalSpace,
                                  CustomTextWigdet(
                                    title: 'Minimum character 8',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: greyColor,
                                  ),
                                ],
                              ),
                            ),
                            12.horizontalSpace,
                            SizedBox(
                              child: Row(
                                children: [
                                  Obx(() => CustomContainer(
                                        shape: BoxShape.circle,
                                        bgColor:
                                            controller.isUppercaseValid.value
                                                ? greenColor
                                                : Colors.grey,
                                        widget: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(2.r),
                                            child: SvgPicture.asset(
                                              'assets/icons/check_course.svg',
                                              width: 8,
                                              height: 8,
                                              color: whiteColor,
                                            ),
                                          ),
                                        ),
                                      )),
                                  5.horizontalSpace,
                                  CustomTextWigdet(
                                    title: 'One uppercase character',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: greyColor,
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                        6.verticalSpace,
                        Row(
                          children: [
                            SizedBox(
                              child: Row(
                                children: [
                                  Obx(() => CustomContainer(
                                        shape: BoxShape.circle,
                                        bgColor:
                                            controller.isLowercaseValid.value
                                                ? greenColor
                                                : Colors.grey,
                                        widget: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(2.r),
                                            child: SvgPicture.asset(
                                              'assets/icons/check_course.svg',
                                              width: 8,
                                              height: 8,
                                              color: whiteColor,
                                            ),
                                          ),
                                        ),
                                      )),
                                  5.horizontalSpace,
                                  CustomTextWigdet(
                                    title: 'One lowercase character',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: greyColor,
                                  ),
                                ],
                              ),
                            ),
                            12.horizontalSpace,
                            SizedBox(
                              child: Row(
                                children: [
                                  Obx(() => CustomContainer(
                                        shape: BoxShape.circle,
                                        bgColor:
                                            controller.isSpecialCharValid.value
                                                ? greenColor
                                                : Colors.grey,
                                        widget: Center(
                                          child: Padding(
                                            padding: EdgeInsets.all(2.r),
                                            child: SvgPicture.asset(
                                              'assets/icons/check_course.svg',
                                              width: 8,
                                              height: 8,
                                              color: whiteColor,
                                            ),
                                          ),
                                        ),
                                      )),
                                  5.horizontalSpace,
                                  CustomTextWigdet(
                                    title: 'One special character',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: greyColor,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        6.verticalSpace,
                        SizedBox(
                          child: Row(
                            children: [
                              Obx(() => CustomContainer(
                                    shape: BoxShape.circle,
                                    bgColor: controller.isNumberValid.value
                                        ? greenColor
                                        : Colors.grey,
                                    widget: Center(
                                      child: Padding(
                                        padding: EdgeInsets.all(2.r),
                                        child: SvgPicture.asset(
                                          'assets/icons/check_course.svg',
                                          width: 8,
                                          height: 8,
                                          color: whiteColor,
                                        ),
                                      ),
                                    ),
                                  )),
                              5.horizontalSpace,
                              CustomTextWigdet(
                                title: 'One number',
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                textColor: greyColor,
                              ),
                            ],
                          ),
                        ),
                        32.verticalSpace,
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextWigdet(
                              title: 'Confirm Password',
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.9),
                            ),
                            5.horizontalSpace,
                            SvgPicture.asset(
                              'assets/icons/Label_.svg',
                              width: 6,
                              height: 6,
                            )
                          ],
                        ),
                        6.verticalSpace,
                        CustomTextFieldWidget(
                          hintText: 'enter your confirm password',
                          controller: controller.confirmPasswordController,
                          fontSize: 18,
                          fontWeight: FontWeight.w400,
                          colorTextHint: secondGreyColor,
                          radius: 4,
                          widthField: double.infinity,
                          heightField: 52,
                          contentPadding: EdgeInsetsDirectional.symmetric(
                              vertical: 10.h, horizontal: 14.w),
                          colorField: baseBlueColor.withOpacity(0.5),
                          suffixAssetNameIcon: "assets/icons/eye.svg",
                          colorSuffixIcon: secondWhiteColor.withOpacity(0.8),
                          suffixIconConstraints:
                              const BoxConstraints(maxWidth: 50, maxHeight: 50),
                          colorText: whiteColor,
                        ),
                        32.verticalSpace,
                        Obx(() => CustomFilledButtonWidget(
                              onPressed: controller.isFormValid.value
                                  ? () => controller.changePassword()
                                  : null,
                              title: 'Change Password',
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              fontColor: whiteColor,
                              radius: 4,
                              bgColor: controller.isFormValid.value
                                  ? blueColor
                                  : greyColor,
                              widthButton: double.infinity,
                              heightButton: 52,
                            )),
                        24.verticalSpace,
                        // CustomFilledButtonWidget(
                        //   onPressed: () {},
                        //   title: 'Forgot Password',
                        //   fontSize: 16,
                        //   fontWeight: FontWeight.w400,
                        //   fontColor: greyColor,
                        //   radius: 4,
                        //   bgColor: Colors.transparent,
                        // ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
