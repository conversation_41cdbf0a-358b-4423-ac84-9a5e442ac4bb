import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class OpenReadHanjar extends StatelessWidget {
  const OpenReadHanjar({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SfPdfViewer.network(
        'https://cdn.syncfusion.com/content/PDFViewer/encrypted.pdf',
        password: 'syncfusion',
        onDocumentLoaded: (details) {
          print("PDF berhasil dimuat");
        },
        onDocumentLoadFailed: (error) {
          print("Gagal memuat PDF: ${error.description}");
        },
      ),
    );
  }
}
