import 'package:flutter/material.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class TabItem {
  String title;
  String key;

  TabItem({
    required this.title,
    required this.key,
  });
}

class CustomTabItemWidget extends StatelessWidget {
  final TabItem item;
  final bool? isSelected;

  final Function(TabItem key)? onTap;

  const CustomTabItemWidget({
    Key? key,
    required this.item,
    this.isSelected,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      decoration: isSelected == true
          ? const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.blue, // You can change this to any color you prefer
                  width: 1.5, // You can adjust the width of the border here
                ),
              ),
            )
          : null,
      child: GestureDetector(
        onTap: () => onTap?.call(item),
        child: CustomTextWigdet(
          title: item.title,
          fontSize: 18,
          fontWeight: isSelected == true ? FontWeight.w700 : null,
        ),
      ),
    );
  }
}
