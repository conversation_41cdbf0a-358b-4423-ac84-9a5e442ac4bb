import 'package:flutter/material.dart';
import 'package:mides_skadik/widgets/course/custom_tab_item.dart';

class CustomTabWidget extends StatefulWidget {
  final List<TabItem> list;
  final TabItem? selected;
  final Function(TabItem)? onTap;

  const CustomTabWidget({
    Key? key,
    required this.list,
    this.selected,
    this.onTap,
  }) : super(key: key);

  @override
  State<CustomTabWidget> createState() => _CustomTabWidgetState();
}

class _CustomTabWidgetState extends State<CustomTabWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(0, 8, 0, 8),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white, // You can change this to any color you prefer
            width: 0.3, // You can adjust the width of the border here
          ),
        ),
      ),
      height: 40,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          var item = widget.list[index];
          return CustomTabItemWidget(
            item: item,
            isSelected: widget.selected?.key == item.key,
            onTap: (val) => widget.onTap?.call(item),
          );
        },
        itemCount: widget.list.length,
        separatorBuilder: (context, index) {
          return const SizedBox(width: 24);
        },
      ),
    );
  }
}
