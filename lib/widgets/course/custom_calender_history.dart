import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/history_attendance_course/controllers/history_attendance_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class CustomCalenderHistory extends StatelessWidget {
  final String? date;
  final String? time;

  const CustomCalenderHistory({
    super.key,
    this.date,
    this.time,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: 400,
      bgColor: baseBlueColor,
      radius: 5,
      widget: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 8.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SfDateRangePicker(
                  selectionMode: DateRangePickerSelectionMode.range,
                  selectionShape: DateRangePickerSelectionShape.rectangle,
                  navigationMode: DateRangePickerNavigationMode.none,
                  rangeSelectionColor: secondBlueColor.withOpacity(0.2),
                  startRangeSelectionColor: blueColor,
                  endRangeSelectionColor: blueColor,
                  backgroundColor: Colors.transparent,
                  rangeTextStyle: TextStyle(color: whiteColor),
                  initialSelectedRange: PickerDateRange(
                    Get.find<HistoryAttendanceCourseController>().dateNow.value,
                    Get.find<HistoryAttendanceCourseController>()
                        .dateWeek
                        .value,
                  ),
                  onSelectionChanged:
                      Get.find<HistoryAttendanceCourseController>().changeDate,
                  yearCellStyle: DateRangePickerYearCellStyle(
                    textStyle: TextStyle(
                      color: whiteColor,
                      fontWeight: FontWeight.w300,
                    ),
                    todayTextStyle: TextStyle(
                      color: whiteColor,
                    ),
                    todayCellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: blueColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  headerStyle: DateRangePickerHeaderStyle(
                    textAlign: TextAlign.center,
                    textStyle: TextStyle(
                      color: whiteColor,
                    ),
                    backgroundColor: Colors.transparent,
                  ),
                  monthViewSettings: DateRangePickerMonthViewSettings(
                    viewHeaderStyle: DateRangePickerViewHeaderStyle(
                      textStyle: TextStyle(
                        color: whiteColor,
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                    dayFormat: 'EEE',
                    showTrailingAndLeadingDates: true,
                  ),
                  monthCellStyle: DateRangePickerMonthCellStyle(
                    textStyle: TextStyle(
                        color: whiteColor, fontWeight: FontWeight.w300),
                    todayTextStyle: TextStyle(
                      color: whiteColor,
                    ),
                    todayCellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: blueColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    cellDecoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.black.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                16.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomFilledButtonWidget(
                      onPressed: () {
                        Get.find<HistoryAttendanceCourseController>()
                            .openDialog();
                      },
                      title: 'Cancel',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 179,
                      heightButton: 44,
                      isOutlined: true,
                      borderColor: secondWhiteColor.withOpacity(0.20),
                      radius: 4,
                    ),
                    16.horizontalSpace,
                    CustomFilledButtonWidget(
                      onPressed: () {
                        Get.find<HistoryAttendanceCourseController>()
                            .getAttendanceHistoryViewAll();
                        Get.find<HistoryAttendanceCourseController>()
                            .openDialog();
                      },
                      title: 'Apply',
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      fontColor: whiteColor,
                      widthButton: 179,
                      heightButton: 44,
                      bgColor: blueColor,
                      radius: 4,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
