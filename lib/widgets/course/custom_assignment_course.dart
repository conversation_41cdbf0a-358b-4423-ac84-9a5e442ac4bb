import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAssignmentCourse extends StatelessWidget {
  String? titleAssignment;
  String? timeRemaining;
  String? mapel;
  String? lectureName;
  bool isSubmited;
  Function()? onTap;

  CustomAssignmentCourse({
    super.key,
    this.titleAssignment,
    this.mapel,
    this.timeRemaining,
    this.lectureName,
    this.isSubmited = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
        // width: Get.width,
        // height: Get.height,
        radius: 12,
        // bgColor: secondBlueColor.withOpacity(0.10),
        widget: Container(
          margin: const EdgeInsets.symmetric(vertical: 12),
          padding: EdgeInsets.symmetric(vertical: 28.h, horizontal: 24.w),
          width: Get.width,
          // height: 92.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: secondBlueColor.withOpacity(0.1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: secondBlueColor.withOpacity(0.1),
                    ),
                    child: SvgPicture.asset(
                      'assets/icons/calendar.svg',
                      width: 10,
                      height: 10,
                      color: secondWhiteColor,
                    ),
                  ),
                  24.horizontalSpace,
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 150, // Adjust width as needed
                        child: Text(
                          "${titleAssignment?.toUpperCase()}",
                          style: TextStyle(
                            fontSize: 20.sp,
                            fontWeight: FontWeight.w600,
                            color: whiteColor,
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const CircleAvatar(
                            radius: 10,
                            backgroundImage:
                                AssetImage("assets/images/Person.jpg"),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          CustomTextWigdet(
                            title: lectureName.toString(),
                            fontSize: 20,
                            fontWeight: FontWeight.w300,
                            textColor: whiteColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              // const Spacer(),

              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Time Remaining",
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: whiteColor,
                      )),
                  10.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SvgPicture.asset(
                            'assets/icons/calendar.svg',
                            width: 20.w,
                            height: 20.h,
                            color: secondWhiteColor,
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          CustomTextWigdet(
                            title: timeRemaining.toString(),
                            fontSize: 16,
                            textColor: whiteColor,
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
              100.horizontalSpace,
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Mapel",
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: whiteColor,
                      )),
                  10.horizontalSpace,
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: whiteColor.withOpacity(0.1),
                    ),
                    child: CustomTextWigdet(
                      title: mapel.toString(),
                      fontSize: 16,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              100.horizontalSpace,
              isSubmited == false
                  ? CustomFilledButtonWidget(
                      onPressed: onTap,
                      widthButton: 94,
                      heightButton: 44,
                      fontSize: 16,
                      radius: 10,
                      title: "Submit",
                      bgColor: blueColor,
                      fontColor: whiteColor)
                  : CustomFilledButtonWidget(
                      onPressed: onTap,
                      widthButton: 94,
                      heightButton: 44,
                      fontSize: 16,
                      radius: 10,
                      title: "Submited",
                      bgColor: greyColor,
                      fontColor: whiteColor),
            ],
          ),
        ));
  }
}
