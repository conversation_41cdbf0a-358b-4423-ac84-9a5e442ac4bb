import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/controllers/course_notification_controller.dart';
import 'package:mides_skadik/widgets/course/custom_notification_course.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAppBarCourse extends GetView implements PreferredSizeWidget {
  final String? title;
  final List<InlineSpan>? richTitle;
  final String? assetIcon;
  final double? height;
  final double? fontSize;
  final FontWeight? fontWeight;
  final void Function()? onBackPressed;
  final List<Widget>? actions;

  const CustomAppBarCourse({
    super.key,
    this.height,
    this.assetIcon,
    this.title,
    this.richTitle,
    this.fontSize,
    this.actions,
    this.fontWeight,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final notificationController = Get.put(CourseNotificationController());
    final loginController = Get.put(LoginController(), permanent: true);

    final height = this.height ?? 90;
    return SizedBox(
      width: double.infinity,
      height: Get.height,
      child: Stack(
        children: [
          Obx(() {
            if (notificationController.isOpen()) {
              return Positioned.fill(
                child: GestureDetector(
                  onTap: () {
                    notificationController.close();
                  },
                  behavior: HitTestBehavior.translucent,
                  child: Container(),
                ),
              );
            }
            return const SizedBox();
          }),
          SizedBox(
            height: height,
            child: AppBar(
              backgroundColor: darkBlueColor,
              automaticallyImplyLeading: false,
              actions: actions,
              scrolledUnderElevation: 0,
              toolbarHeight: height,
              title: Stack(
                children: [
                  Row(
                    children: [
                      Column(
                        children: [
                          Row(
                            children: [
                              const CustomTextWigdet(
                                title: "M-IDES",
                                fontWeight: FontWeight.w600,
                                fontSize: 24,
                              ),
                              5.horizontalSpace,
                              Image.asset(
                                "assets/images/logo.png",
                                width: 30.w,
                                height: 30.h,
                              ),
                            ],
                          ),
                          const CustomTextWigdet(
                            title: "Mobile Integrated Digital",
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                          const CustomTextWigdet(
                            title: "Education System",
                            fontWeight: FontWeight.w300,
                            fontSize: 16,
                            fontStyle: FontStyle.italic,
                          )
                        ],
                      ),
                      const Spacer(),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: GestureDetector(
                          onTap: () {
                            Get.toNamed('/feedback-course');
                          },
                          child: CustomContainer(
                            width: 52,
                            height: 52,
                            bgColor: whiteColor.withOpacity(0.20),
                            borderRadius: BorderRadius.circular(30),
                            widget: Center(
                              child: SvgPicture.asset(
                                "assets/icons/feedback_appbar_course.svg",
                                width: 30.w,
                                height: 30.h,
                              ),
                            ),
                          ),
                        ),
                      ),
                      10.horizontalSpace,
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: GestureDetector(
                          onTap: () {
                            notificationController.toggle();
                          },
                          child: CustomContainer(
                            width: 52,
                            height: 52,
                            bgColor: whiteColor.withOpacity(0.20),
                            borderRadius: BorderRadius.circular(30),
                            widget: Center(
                              child: SvgPicture.asset(
                                "assets/icons/notification.svg",
                                width: 30.w,
                                height: 30.h,
                              ),
                            ),
                          ),
                        ),
                      ),
                      10.horizontalSpace,
                      ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: GestureDetector(
                          onTap: () {
                            Get.toNamed("/chat-course");
                          },
                          child: CustomContainer(
                            width: 52,
                            height: 52,
                            bgColor: whiteColor.withOpacity(0.20),
                            borderRadius: BorderRadius.circular(30),
                            widget: Center(
                              child: SvgPicture.asset(
                                "assets/icons/chat_bubble_outline.svg",
                                width: 30.w,
                                height: 30.h,
                              ),
                            ),
                          ),
                        ),
                      ),
                      10.horizontalSpace,
                      GestureDetector(
                        onTap: () {
                          Get.toNamed('/profile-course');
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            ClipOval(
                              child: Obx(() {
                                final loading = loginController.isLoading.value;
                                final data = loginController.userProfile.value;
                                final imageUrl = data?.imageProfile ?? '';

                                // Jika sedang loading
                                if (loading) {
                                  return const CustomLoadingWidget();
                                }

                                // Jika data null atau image kosong
                                if (data == null || imageUrl.isEmpty) {
                                  return Container(
                                    width: 52.h,
                                    height: 52.h,
                                    color: greyColor,
                                    child: Center(
                                      child: Icon(
                                        Icons.person,
                                        size: 28.r,
                                        color: Colors.white,
                                      ),
                                    ),
                                  );
                                }

                                // Jika data ada dan imageUrl valid
                                return SizedBox(
                                  width: 52.h,
                                  height: 52.h,
                                  child: Image.network(
                                    imageUrl,
                                    fit: BoxFit.cover,
                                    loadingBuilder:
                                        (context, child, loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return const CustomLoadingWidget(
                                        width: 52,
                                        height: 52,
                                        imageWidth: 30,
                                        imageHeight: 30,
                                      );
                                    },
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        color: greyColor,
                                        child: const Icon(
                                          Icons.person,
                                          size: 28,
                                          color: Colors.white,
                                        ),
                                      );
                                    },
                                  ),
                                );
                              }),
                            ),
                            10.horizontalSpace,
                            ConstrainedBox(
                              constraints: const BoxConstraints(
                                maxWidth: 150,
                              ),
                              child: Obx(
                                () {
                                  final loading =
                                      loginController.isLoading.value;
                                  final data =
                                      loginController.userProfile.value;

                                  if (loading) {
                                    return const CustomLoadingWidget();
                                  }
                                  if (!loading && data == null) {
                                    return Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const CustomTextWigdet(
                                          title: "-",
                                          fontSize: 18,
                                          fontWeight: FontWeight.w400,
                                        ),
                                        CustomTextWigdet(
                                          title: "NRP • Pangkat",
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,
                                          textColor: secondWhiteColor
                                              .withValues(alpha: .75),
                                        ),
                                      ],
                                    );
                                  }
                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CustomTextWigdet(
                                        title: data?.name ?? '',
                                        fontSize: 18,
                                        fontWeight: FontWeight.w400,
                                      ),
                                      CustomTextWigdet(
                                        title:
                                            "${data?.nrp ?? 'NRP'} • ${data?.pangkat?.name ?? 'Pangkat'}",
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        textColor: secondWhiteColor.withValues(
                                            alpha: .75),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            top: height + 10,
            right: 16,
            child: SizedBox(
              width: Get.width - 32,
              height: Get.height * 0.8,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {},
                    child: const CustomNotificationCourse(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height?.h ?? kToolbarHeight);
}
