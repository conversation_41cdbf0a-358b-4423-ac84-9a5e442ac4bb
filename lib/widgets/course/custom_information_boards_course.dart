import 'package:flutter/material.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomInformationBoardsCourse extends StatelessWidget {
  const CustomInformationBoardsCourse(
      {super.key,
      this.startFrom,
      this.status,
      this.doneBy,
      this.timeSpend,
      this.score});

  final String? startFrom;
  final String? status;
  final String? doneBy;
  final String? timeSpend;
  final String? score;

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      bgColor: secondBlueColor.withOpacity(0.10),
      padding: const EdgeInsets.all(10),
      radius: 10,
      widget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Table(
            columnWidths: const {
              0: FixedColumnWidth(50), // Kolom pertama dengan lebar tetap 100
              1: FlexColumnWidth(2), // Kolom kedua dengan proporsi 2
              2: FlexColumnWidth(50), // Kolom ketiga akan mengambil sisa lebar
            },
            children: [
              TableRow(
                children: [
                  CustomTextWigdet(
                    title: 'Start From',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    textColor: whiteColor.withOpacity(0.5),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: ':',
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      textColor: whiteColor.withOpacity(0.5),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: startFrom ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              const TableRow(
                children: [
                  SizedBox(height: 10),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
              TableRow(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: CustomTextWigdet(
                        title: 'Status',
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        textColor: whiteColor.withOpacity(0.5),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: CustomTextWigdet(
                        title: ':',
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        textColor: whiteColor.withOpacity(0.5),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomContainer(
                      borderRadius:
                          const BorderRadius.all(Radius.circular(100)),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 5, vertical: 2),
                      bgColor: greenColor,
                      widget: CustomTextWigdet(
                        title: status ?? '-',
                        textColor: whiteColor,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const TableRow(
                children: [
                  SizedBox(height: 10),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
              TableRow(
                children: [
                  CustomTextWigdet(
                    title: 'Done by',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    textColor: whiteColor.withOpacity(0.5),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: ':',
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      textColor: whiteColor.withOpacity(0.5),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: doneBy ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              const TableRow(
                children: [
                  SizedBox(height: 10),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
              TableRow(
                children: [
                  CustomTextWigdet(
                    title: 'Time Spend',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    textColor: whiteColor.withOpacity(0.5),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: ':',
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      textColor: whiteColor.withOpacity(0.5),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: timeSpend ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              const TableRow(
                children: [
                  SizedBox(height: 10),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
              TableRow(
                children: [
                  CustomTextWigdet(
                    title: 'Score',
                    fontSize: 14,
                    fontWeight: FontWeight.normal,
                    textColor: whiteColor.withOpacity(0.5),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: ':',
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      textColor: whiteColor.withOpacity(0.5),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: CustomTextWigdet(
                      title: score ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ],
              ),
              const TableRow(
                children: [
                  SizedBox(height: 10),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
