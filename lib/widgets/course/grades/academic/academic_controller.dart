import 'dart:typed_data';
import 'package:flutter/services.dart' show rootBundle;
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/academic/academic_model.dart';
import 'package:mides_skadik/app/data/services/course/grades/academic/academic_service.dart';

class AcademicController extends GetxController {
  final AcademicService academicService = AcademicService();
  final selectedSubMapel = Rx<SubMapel?>(null);
  final isLoading = false.obs;
  final academicData = Rx<AcademicModel?>(null);
  final searchQuery = ''.obs;

  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
  }

  void selectSubMapel(SubMapel subMapel) {
    selectedSubMapel.value = subMapel;
  }

  Future<void> fetchAcademicData() async {
    isLoading.value = true;
    final result = await academicService.getAcademicData();
    isLoading.value = false;

    if (result.isSuccess) {
      academicData.value = result.resultValue;
    }
  }

  Future<void> printAcademicData() async {
    final data = academicData.value;
    if (data == null) return;

    final now = DateTime.now();
    final formattedDate = DateFormat('M/d/yy, h:mm a').format(now);

    final pdf = pw.Document();
    final font = await PdfGoogleFonts.openSansRegular();
    final boldFont = await PdfGoogleFonts.openSansBold();
    final italicFont = await PdfGoogleFonts.openSansItalic();

    final ByteData logoBytes = await rootBundle.load('assets/images/logo.png');
    final Uint8List logoUint8List = logoBytes.buffer.asUint8List();
    final image = pw.MemoryImage(logoUint8List);

    pdf.addPage(
      pw.MultiPage(
        pageTheme: pw.PageTheme(
          margin: const pw.EdgeInsets.all(32),
          theme: pw.ThemeData.withFont(
            base: font,
            bold: boldFont,
            italic: italicFont,
          ),
        ),
        build: (context) => [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(formattedDate, style: const pw.TextStyle(fontSize: 12)),
              pw.Text('M-IDES', style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
          pw.SizedBox(height: 24),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Image(image, width: 50),
              pw.SizedBox(width: 12),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text('Vidya Wisesa Bhakti',
                        style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    pw.Text('Skadik 502',
                        style: const pw.TextStyle(fontSize: 10)),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'Jalan Kalijati Lama, Kalijati Barat, Kecamatan Kalijati,\nKabupaten Subang, Jawa Barat 41271',
                      style: pw.TextStyle(fontSize: 8, font: italicFont),
                    ),
                  ],
                ),
              ),
            ],
          ),
          pw.Divider(color: PdfColors.grey400, thickness: 0.5, height: 32),
          pw.Center(
            child: pw.Text(
              'DAFTAR NILAI SISWA\nPENDIDIKAN JURUSAN ANGKATAN KE 12\nTAHUN ANGGARAN 2025',
              textAlign: pw.TextAlign.center,
              style: const pw.TextStyle(fontSize: 14),
            ),
          ),
          pw.SizedBox(height: 20),
          pw.Row(children: [
            pw.Container(
              width: 100,
              child: pw.Text(
                'Nama',
                textAlign: pw.TextAlign.left,
              ),
            ),
            pw.Container(
              width: 300,
              child: pw.Text(
                ': ${data.name}',
                textAlign: pw.TextAlign.left,
              ),
            ),
          ]),
          pw.Row(children: [
            pw.Container(
              width: 100,
              child: pw.Text(
                'Pangkat / NRP',
                textAlign: pw.TextAlign.left,
              ),
            ),
            pw.Container(
              width: 300,
              child: pw.Text(
                ': ${data.pangkat} / ${data.nrp}',
                textAlign: pw.TextAlign.left,
              ),
            ),
          ]),
          pw.SizedBox(height: 24),
          pw.Container(
            decoration: pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFF9FAFB),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Table(
              border: pw.TableBorder.all(width: 0.3, color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(6),
                2: const pw.FlexColumnWidth(2),
                3: const pw.FlexColumnWidth(2),
                4: const pw.FlexColumnWidth(2),
              },
              children: [
                pw.TableRow(
                  decoration:
                      pw.BoxDecoration(color: PdfColor.fromInt(0xFFEFF4FB)),
                  children: [
                    pw.Center(
                      child: pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text('NO',
                            style:
                                pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text('MATA PELAJARAN',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text('NILAI PELAJARAN',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text('HARGA NILAI',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text('NILAI PRESTASI',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                    ),
                  ],
                ),
                ...data!.academicGrades.expand((matkul) sync* {
                  for (var entry in matkul.mapels.asMap().entries) {
                    yield pw.TableRow(
                      children: [
                        pw.Text(''),
                        pw.Padding(
                          padding: const pw.EdgeInsets.only(left: 8, top: 8),
                          child: pw.Column(
                            crossAxisAlignment: pw.CrossAxisAlignment.start,
                            children: [
                              pw.Text(matkul.matkulName,
                                  style: pw.TextStyle(
                                      fontWeight: pw.FontWeight.bold)),
                              pw.Text(
                                  '${String.fromCharCode(97 + entry.key)}. ${entry.value.mapelName}',
                                  style: pw.TextStyle(
                                      fontSize: 10,
                                      fontStyle: pw.FontStyle.italic)),
                            ],
                          ),
                        ),
                        pw.Text(''),
                        pw.Text(''),
                        pw.Text(''),
                      ],
                    );
                    for (int i = 0; i < entry.value.subMapels.length; i++) {
                      final sub = entry.value.subMapels[i];
                      yield pw.TableRow(
                        children: [
                          pw.Center(
                            child: pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text('${i + 1}'),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(sub.name),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(sub.nilaiPelajaran),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(sub.hargaNilai.toString()),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(sub.nilaiPrestasi),
                          ),
                        ],
                      );
                    }
                  }
                }).toList(),
              ],
            ),
          ),
          pw.SizedBox(height: 24),
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFF9FAFB),
              borderRadius: pw.BorderRadius.circular(8),
              border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(children: [
                  pw.Expanded(child: pw.Text('Nilai Prestasi Akademik\t(NPA)')),
                  pw.Text('82.41'),
                  pw.SizedBox(width: 20),
                  pw.Text('70%'),
                  pw.SizedBox(width: 20),
                  pw.Text('57.69'),
                ]),
                pw.SizedBox(height: 6),
                pw.Row(children: [
                  pw.Expanded(
                      child: pw.Text('Nilai Prestasi Kepribadian\t(NPK)')),
                  pw.Text('85.00'),
                  pw.SizedBox(width: 20),
                  pw.Text('20%'),
                  pw.SizedBox(width: 20),
                  pw.Text('17.00'),
                ]),
                pw.SizedBox(height: 6),
                pw.Row(children: [
                  pw.Expanded(
                      child: pw.Text('Nilai Prestasi Kesegaran\t(NPJas)')),
                  pw.Text('75.50'),
                  pw.SizedBox(width: 20),
                  pw.Text('10%'),
                  pw.SizedBox(width: 20),
                  pw.Text('7.55'),
                ]),
                pw.SizedBox(height: 6),
                pw.Row(children: [
                  pw.Expanded(
                      child: pw.Text('Nilai Prestasi Pendidikan\t(NPP)')),
                  pw.Text('100%'),
                  pw.SizedBox(width: 20),
                  pw.Text('82.24'),
                ]),
                pw.SizedBox(height: 6),
                pw.Row(children: [
                  pw.Expanded(child: pw.Text('Keterangan')),
                  pw.Text('Peringkat ke-1 dari 10 Siswa'),
                ]),
              ],
            ),
          ),
          pw.SizedBox(height: 40),
          pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Text(
              'Komandan Skadron Pendidikan 502,\n\n\nDila Yuniar Tugi S.\nLetkol ADM NRP 528724',
              textAlign: pw.TextAlign.right,
              style: const pw.TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  @override
  void onInit() {
    super.onInit();
    fetchAcademicData();
  }
}
