import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/grades_course/controllers/grades_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/grades/academic/academic_controller.dart';
import 'package:mides_skadik/widgets/course/grades/academic/academic_landscape.dart';
import 'package:mides_skadik/widgets/course/grades/grades_table.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class Academic extends StatelessWidget {
  const Academic({super.key});

  @override
  Widget build(BuildContext context) {
    final gradesController = Get.put(GradesCourseController());
    final academicController = Get.put(AcademicController());

    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape) {
      return const AcademicLandscape();
    }

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 282.w,
              height: 48.h,
              child: CustomTextFieldWidget(
                colorField: baseBlueColor.withOpacity(0.4),
                radius: 8,
                hintText: 'Search Mata Pelajaran',
                colorTextHint: secondWhiteColor.withOpacity(0.3),
                fontSize: 18,
                colorText: secondWhiteColor,
                iconHeight: 15,
                assetNameIcon: 'assets/icons/search.svg',
                colorPrefixIcon: secondWhiteColor.withOpacity(0.3),
                contentPadding: EdgeInsetsDirectional.symmetric(
                    vertical: 12.h, horizontal: 10.w),
                onChanged: academicController.updateSearchQuery,
              ),
            ),
            CustomFilledButtonWidget(
              onPressed: () {
                academicController.printAcademicData();
              },
              title: 'Print',
              fontSize: 18,
              fontColor: whiteColor,
              bgColor: baseBlueColor.withOpacity(0.5),
              withIcon: true,
              assetName: 'assets/icons/printer.svg',
              padding: const EdgeInsets.symmetric(horizontal: 14),
              radius: 8,
            ),
          ],
        ),
        24.verticalSpace,
        Obx(
          () {
            final data = academicController.academicData.value;
            final query = academicController.searchQuery.value.toLowerCase();
            final isLoading = academicController.isLoading.value;

            if (isLoading) {
              return const Center(
                child: CustomLoadingWidget(
                  width: 60,
                  height: 60,
                  imageWidth: 60,
                  imageHeight: 60,
                  strokeWidth: 3,
                ),
              );
            }

            if (data == null) {
              return Center(
                child: CustomTextWigdet(
                  title: "Tidak ada data academic",
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
              );
            }

            final totalItems = data.academicGrades
                .map((e) => e.mapels
                    .map((m) => m.subMapels.length)
                    .fold(0, (a, b) => a + b))
                .fold(0, (a, b) => a + b);

            final isScrollable = totalItems > 15;
            final containerHeight = isScrollable ? 2650.h : null;

            return CustomContainer(
              radius: 8,
              bgColor: baseBlueColor.withOpacity(0.3),
              height: containerHeight,
              widget: GradesTable(
                title: 'Nilai Prestasi Akademik',
                columnTitle1: 'NO',
                columnTitle2: 'MATA PELAJARAN',
                columnTitle3: 'NILAI PELAJARAN',
                columnTitle4: 'HARGA NILAI',
                columnTitle5: 'NILAI PRESTASI',
                tableBody: Column(
                  children: [
                    CustomContainer(
                      width: double.infinity,
                      height: isScrollable ? 1500 : null,
                      radius: 8,
                      bgColor: baseBlueColor.withValues(alpha: 0.3),
                      widget: SingleChildScrollView(
                        physics: isScrollable
                            ? const AlwaysScrollableScrollPhysics()
                            : const NeverScrollableScrollPhysics(),
                        child: CustomContainer(
                          radius: 8,
                          border: Border.all(
                            color: secondWhiteColor.withOpacity(0.3),
                            width: 0.3,
                          ),
                          bgColor: baseBlueColor.withValues(alpha: 0.3),
                          widget: Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: 20.h, horizontal: 25.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                60.verticalSpace,
                                ...data.academicGrades.expand(
                                  (matkul) => [
                                    ...matkul.mapels.asMap().entries.expand(
                                          (entry) => [
                                            Padding(
                                              padding: EdgeInsets.only(
                                                  top: 12.h, bottom: 4.h),
                                              child: Row(
                                                children: [
                                                  const Expanded(
                                                      flex: 1,
                                                      child: SizedBox()),
                                                  Expanded(
                                                    flex: 3,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        CustomTextWigdet(
                                                          title:
                                                              matkul.matkulName,
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w700,
                                                          textColor: whiteColor,
                                                        ),
                                                        2.verticalSpace,
                                                        CustomTextWigdet(
                                                          title:
                                                              '${String.fromCharCode(97 + entry.key)}. ${entry.value.mapelName}',
                                                          fontSize: 13,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          textColor:
                                                              secondWhiteColor
                                                                  .withOpacity(
                                                                      0.9),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const Expanded(
                                                      flex: 3,
                                                      child: SizedBox()),
                                                  const Expanded(
                                                      flex: 3,
                                                      child: SizedBox()),
                                                  const Expanded(
                                                      flex: 4,
                                                      child: SizedBox()),
                                                ],
                                              ),
                                            ),
                                            ...List.generate(
                                              entry.value.subMapels.length,
                                              (i) {
                                                final subMapel =
                                                    entry.value.subMapels[i];
                                                return Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 6.h),
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        flex: 1,
                                                        child: CustomTextWigdet(
                                                          title: '${i + 1}',
                                                          fontSize: 14,
                                                          textColor: whiteColor,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 3,
                                                        child: CustomContainer(
                                                          radius: 4,
                                                          bgColor: query
                                                                      .isNotEmpty &&
                                                                  subMapel.name
                                                                      .toLowerCase()
                                                                      .contains(
                                                                          query)
                                                              ? goldenYellow
                                                                  .withOpacity(
                                                                      0.4)
                                                              : Colors
                                                                  .transparent,
                                                          widget: Padding(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    5.r),
                                                            child:
                                                                CustomTextWigdet(
                                                              title:
                                                                  subMapel.name,
                                                              fontSize: 14,
                                                              textColor:
                                                                  whiteColor,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 3,
                                                        child: CustomTextWigdet(
                                                          title: subMapel
                                                                  .nilaiPelajaran ??
                                                              '-',
                                                          fontSize: 14,
                                                          textColor: whiteColor,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 3,
                                                        child: CustomTextWigdet(
                                                          title: subMapel
                                                              .hargaNilai
                                                              .toString(),
                                                          fontSize: 14,
                                                          textColor: whiteColor,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 3,
                                                        child: CustomTextWigdet(
                                                          title: subMapel
                                                                  .nilaiPrestasi ??
                                                              '-',
                                                          fontSize: 14,
                                                          textColor: whiteColor,
                                                        ),
                                                      ),
                                                      Expanded(
                                                        flex: 1,
                                                        child: Align(
                                                          alignment: Alignment
                                                              .centerRight,
                                                          child:
                                                              GestureDetector(
                                                            onTap: () {
                                                              academicController
                                                                  .selectSubMapel(
                                                                      subMapel);
                                                              gradesController
                                                                  .openDetailGrades();
                                                            },
                                                            child:
                                                                CustomContainer(
                                                              width: 89,
                                                              height: 28,
                                                              bgColor: Colors
                                                                  .transparent,
                                                              radius: 6,
                                                              border:
                                                                  Border.all(
                                                                color: secondWhiteColor
                                                                    .withOpacity(
                                                                        0.3),
                                                                width: 0.3,
                                                              ),
                                                              widget: Center(
                                                                child:
                                                                    CustomTextWigdet(
                                                                  title:
                                                                      'Detail',
                                                                  fontSize: 12,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500,
                                                                  textColor:
                                                                      whiteColor,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            )
                                          ],
                                        )
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
