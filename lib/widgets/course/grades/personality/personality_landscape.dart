import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/grades/grades_table.dart';
import 'package:mides_skadik/widgets/course/grades/personality/personality_controller.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class PersonalityLandscape extends StatelessWidget {
  const PersonalityLandscape({super.key});

  @override
  Widget build(BuildContext context) {
    final personalityController = Get.put(PersonalityController());

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 282.w,
              height: 48.h,
              child: CustomTextFieldWidget(
                colorField: baseBlueColor.withOpacity(0.4),
                radius: 8,
                hintText: 'Search personality',
                colorTextHint: secondWhiteColor.withOpacity(0.3),
                fontSize: 18,
                colorText: secondWhiteColor,
                iconHeight: 15,
                assetNameIcon: 'assets/icons/search.svg',
                colorPrefixIcon: secondWhiteColor.withOpacity(0.3),
                contentPadding: EdgeInsetsDirectional.symmetric(
                  vertical: 12.h,
                  horizontal: 10.w,
                ),
                onChanged: personalityController.updateSearchQuery,
              ),
            ),
            CustomFilledButtonWidget(
              onPressed: () {
                personalityController.printPersonalityData();
              },
              title: 'Print',
              fontSize: 20,
              fontColor: whiteColor,
              bgColor: baseBlueColor.withOpacity(0.5),
              withIcon: true,
              assetName: 'assets/icons/printer.svg',
              padding: const EdgeInsets.symmetric(horizontal: 14),
              radius: 8,
            ),
          ],
        ),
        24.verticalSpace,
        Obx(() {
          final data = personalityController.personalityData.value;
          final query = personalityController.searchQuery.value.toLowerCase();
          final isLoading = personalityController.isLoading.value;

          if (isLoading) {
            return const Center(
              child: CustomLoadingWidget(
                width: 60,
                height: 60,
                imageWidth: 60,
                imageHeight: 60,
                strokeWidth: 3,
              ),
            );
          }

          if (data == null) {
            return Center(
              child: CustomTextWigdet(
                title: "Tidak ada data personality",
                fontSize: 18,
                fontWeight: FontWeight.w500,
                textColor: whiteColor,
              ),
            );
          }

          final isScrollable = data.aspects.length > 15;
          final containerHeight = isScrollable ? 1450.h : null;

          return CustomContainer(
            bgColor: baseBlueColor.withOpacity(0.3),
            radius: 8,
            height: containerHeight,
            widget: GradesTable(
              title: 'Nilai Prestasi Kepribadian',
              columnTitle1: 'NO',
              columnTitle2: 'ASPEK KEPRIBADIAN',
              columnTitle5: 'NILAI',
              showFooterBox: true,
              footerTitle1: 'Nilai Awal',
              footerTitle2: 'Nilai Akhir',
              footerTitle3: 'Keterangan',
              titleFontSize: 20,
              columnTitleFontSize: 18,
              footerTitlleFontSize: 18,
              valueTitlleFontSize: 18,
              valueTitle1: data.defaultScore.toString(),
              valueTitle2: data.finalScore.toString(),
              valueTitle3: data.scoreKualitatif,
              tableBody: CustomContainer(
                width: double.infinity,
                height: isScrollable ? 750 : null,
                radius: 8,
                bgColor: baseBlueColor.withValues(alpha: 0.3),
                widget: SingleChildScrollView(
                  physics: isScrollable
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  child: CustomContainer(
                    radius: 8,
                    border: Border.all(
                      color: secondWhiteColor.withOpacity(0.3),
                      width: 0.3,
                    ),
                    bgColor: baseBlueColor.withValues(alpha: 0.3),
                    widget: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 20.h, horizontal: 25.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          60.verticalSpace,
                          ...List.generate(
                            data.aspects.length,
                            (index) {
                              final item = data.aspects[index];
                              final isMatch = query.isNotEmpty &&
                                  item.name.toLowerCase().contains(query);

                              return Padding(
                                padding: EdgeInsets.symmetric(vertical: 6.h),
                                child: Row(
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: CustomTextWigdet(
                                        title: '${index + 1}',
                                        fontSize: 18,
                                        textColor: whiteColor,
                                      ),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: CustomContainer(
                                        radius: 4,
                                        bgColor: isMatch
                                            ? goldenYellow.withOpacity(0.4)
                                            : Colors.transparent,
                                        widget: Padding(
                                          padding: EdgeInsets.all(5.r),
                                          child: CustomTextWigdet(
                                            title: item.name,
                                            fontSize: 18,
                                            textColor: whiteColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const Expanded(flex: 3, child: SizedBox()),
                                    const Expanded(flex: 3, child: SizedBox()),
                                    Expanded(
                                      flex: 4,
                                      child: CustomTextWigdet(
                                        title: item.score.toStringAsFixed(2),
                                        fontSize: 18,
                                        textColor: whiteColor,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
}
