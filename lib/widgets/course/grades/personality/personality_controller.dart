import 'dart:typed_data';
import 'package:flutter/services.dart' show rootBundle;
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:mides_skadik/app/data/models/response/course/grades/personality/personality_model.dart';
import 'package:mides_skadik/app/data/services/course/grades/personality/personality_service.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';

class PersonalityController extends GetxController {
  final PersonalityService personalityService = PersonalityService();
  final isLoading = false.obs;
  final personalityData = Rx<PersonalityModel?>(null);
  final searchQuery = ''.obs;

  void updateSearchQuery(String value) {
    searchQuery.value = value.toLowerCase().trim();
  }

  Future<void> fetchPersonalityData() async {
    isLoading.value = true;

    final result = await personalityService.getPersonalityData();

    isLoading.value = false;

    if (result.isSuccess) {
      personalityData.value = result.resultValue;
    } else {
      LogService.log.e('Error get personality data: ${result.errorMessage}');
    }
  }

  Future<void> printPersonalityData() async {
    final data = personalityData.value;
    if (data == null) return;

    final now = DateTime.now();
    final formattedDate = DateFormat('M/d/yy, h:mm a').format(now);

    final pdf = pw.Document();
    final font = await PdfGoogleFonts.openSansRegular();
    final boldFont = await PdfGoogleFonts.openSansBold();
    final fontMedium = await PdfGoogleFonts.openSansMedium();
    final italicFont = await PdfGoogleFonts.openSansItalic();

    final ByteData logoBytes = await rootBundle.load('assets/images/logo.png');
    final Uint8List logoUint8List = logoBytes.buffer.asUint8List();
    final image = pw.MemoryImage(logoUint8List);

    pdf.addPage(
      pw.MultiPage(
        pageTheme: pw.PageTheme(
          margin: const pw.EdgeInsets.all(32),
          theme: pw.ThemeData.withFont(
            base: font,
            bold: boldFont,
            italic: italicFont,
          ),
        ),
        build: (context) => [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(formattedDate, style: const pw.TextStyle(fontSize: 12)),
              pw.Text('M-IDES', style: const pw.TextStyle(fontSize: 12)),
            ],
          ),
          pw.SizedBox(height: 24),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Image(image, width: 50),
              pw.SizedBox(width: 12),
              pw.Expanded(
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Vidya Wisesa Bhakti',
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'Skadik 502',
                      style: const pw.TextStyle(fontSize: 10),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'Jalan Kalijati Lama, Kalijati Barat, Kecamatan Kalijati,\nKabupaten Subang, Jawa Barat 41271',
                      style: pw.TextStyle(
                        fontSize: 8,
                        font: italicFont,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          pw.Divider(
            color: PdfColors.grey400,
            thickness: 0.5,
            height: 32,
          ),
          pw.Center(
            child: pw.Text(
              'TRANSKRIP NILAI KEPRIBADIAN SISWA\nSEJURSARTA MUSIK A-25 TAHUN ANGGARAN 2024',
              textAlign: pw.TextAlign.center,
              style: pw.TextStyle(
                fontSize: 14,
                font: fontMedium,
              ),
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(children: [
            pw.Container(
              width: 100,
              child: pw.Text(
                'Nama',
                textAlign: pw.TextAlign.left,
              ),
            ),
            pw.Container(
              width: 300,
              child: pw.Text(
                ': ${data.name}',
                textAlign: pw.TextAlign.left,
              ),
            ),
          ]),

          pw.Row(children: [
            pw.Container(
              width: 100,
              child: pw.Text(
                'Pangkat / NRP',
                textAlign: pw.TextAlign.left,
              ),
            ),
            pw.Container(
              width: 300,
              child: pw.Text(
                ': ${data.pangkat} / ${data.nrp}',
                textAlign: pw.TextAlign.left,
              ),
            ),
          ]),
          pw.SizedBox(height: 10),

          // Tabel utama
          pw.Container(
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFFF9FAFB),
              borderRadius: pw.BorderRadius.circular(8),
            ),
            child: pw.Table(
              border: pw.TableBorder.all(width: 0.3, color: PdfColors.grey300),
              columnWidths: {
                0: const pw.FlexColumnWidth(1),
                1: const pw.FlexColumnWidth(6),
                2: const pw.FlexColumnWidth(2),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(
                    color: PdfColor.fromInt(0xFFEFF4FB),
                  ),
                  children: [
                    pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Center(
                          child: pw.Text(
                            'NO',
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                            ),
                          ),
                        )),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'ASPEK KEPRIBADIAN',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                    pw.Padding(
                      padding: const pw.EdgeInsets.all(8),
                      child: pw.Text(
                        'NILAI',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                ...data.aspects.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return pw.TableRow(
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Center(
                          child: pw.Text('${index + 1}'),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(item.name),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(8),
                        child: pw.Text(
                          item.score.toStringAsFixed(2),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ],
            ),
          ),
          pw.SizedBox(height: 10),

          // Footer nilai
          pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              color: const PdfColor.fromInt(0xFFF9FAFB),
              borderRadius: pw.BorderRadius.circular(8),
              border: pw.Border.all(color: PdfColors.grey300, width: 0.5),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  children: [
                    pw.Expanded(child: pw.Text('Nilai Awal')),
                    pw.Container(
                      width: 100,
                      child: pw.Text(
                        data.defaultScore.toString(),
                        textAlign: pw.TextAlign.left,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 6),
                pw.Row(
                  children: [
                    pw.Expanded(child: pw.Text('Nilai Akhir')),
                    pw.Container(
                      width: 100,
                      child: pw.Text(
                        data.finalScore.toString(),
                        textAlign: pw.TextAlign.left,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 6),
                pw.Row(
                  children: [
                    pw.Expanded(child: pw.Text('Keterangan')),
                    pw.Container(
                      width: 100,
                      child: pw.Text(
                        data.scoreKualitatif.toString(),
                        textAlign: pw.TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Column(
              children: [
                pw.Text(
                  'Komandan Skadron Pendidikan 502',
                  style: const pw.TextStyle(
                    fontSize: 14,
                    color: PdfColors.black,
                  ),
                ),
                pw.SizedBox(height: 55),
                pw.Text(
                  '${data.instructorName} NRP ${data.instructorNrp}',
                  style: const pw.TextStyle(
                    fontSize: 14,
                    color: PdfColors.black,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  @override
  void onInit() {
    super.onInit();
    fetchPersonalityData();
  }
}
