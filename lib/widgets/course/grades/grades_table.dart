import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class GradesTable extends StatelessWidget {
  final String? title;
  final double? titleFontSize;
  final double? columnTitleFontSize;
  final double? footerTitlleFontSize;
  final double? valueTitlleFontSize;
  final String? columnTitle1;
  final String? columnTitle2;
  final String? columnTitle3;
  final String? columnTitle4;
  final String? columnTitle5;
  final String? footerTitle1;
  final String? footerTitle2;
  final String? footerTitle3;
  final String? footerTitle4;
  final String? valueTitle1;
  final String? valueTitle2;
  final String? valueTitle3;
  final String? valueTitle4;
  final Widget? tableHeader;
  final Widget? tableBody;
  final bool showFooterBox;

  const GradesTable({
    Key? key,
    this.title,
    this.titleFontSize,
    this.columnTitleFontSize,
    this.footerTitlleFontSize,
    this.valueTitlleFontSize,
    this.columnTitle1,
    this.columnTitle2,
    this.columnTitle3,
    this.columnTitle4,
    this.columnTitle5,
    this.footerTitle1,
    this.footerTitle2,
    this.footerTitle3,
    this.footerTitle4,
    this.valueTitle1,
    this.valueTitle2,
    this.valueTitle3,
    this.valueTitle4,
    this.tableHeader,
    this.tableBody,
    this.showFooterBox = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      radius: 8,
      widget: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (title != null)
              CustomTextWigdet(
                title: title!,
                fontSize: titleFontSize ?? 18,
                fontWeight: FontWeight.w500,
                textColor: whiteColor,
              ),
            24.verticalSpace,

            Stack(
              children: [
                // Table Body (background)
                tableBody ??
                    CustomContainer(
                      width: double.infinity,
                      bgColor: baseBlueColor.withOpacity(0.3),
                      radius: 8,
                      widget: Padding(
                        padding: EdgeInsets.only(top: 100.h, bottom: 20.h),
                        child: Column(
                          children: [
                            SvgPicture.asset('assets/icons/empty state.svg'),
                            10.verticalSpace,
                            CustomTextWigdet(
                              title: 'Data not available',
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              textColor: whiteColor,
                            ),
                            6.verticalSpace,
                            CustomTextWigdet(
                              title: 'There are no grades recorded yet.',
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              textColor: whiteColor,
                            ),
                          ],
                        ),
                      ),
                    ),

                // Table Header (foreground)
                tableHeader ??
                    CustomContainer(
                      width: double.infinity,
                      bgColor: baseBlueColor,
                      radius: 8,
                      widget: Padding(
                        padding: EdgeInsets.all(24.r),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 1,
                              child: CustomTextWigdet(
                                title: columnTitle1 ?? '',
                                fontSize: columnTitleFontSize ?? 14,
                                fontWeight: FontWeight.w500,
                                textColor: whiteColor,
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: CustomTextWigdet(
                                  title: columnTitle2 ?? '',
                                  fontSize: columnTitleFontSize ?? 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: whiteColor,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: CustomTextWigdet(
                                title: columnTitle3 ?? '',
                                fontSize: columnTitleFontSize ?? 14,
                                fontWeight: FontWeight.w500,
                                textColor: whiteColor,
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: CustomTextWigdet(
                                  title: columnTitle4 ?? '',
                                  fontSize: columnTitleFontSize ?? 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: whiteColor,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 4,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: CustomTextWigdet(
                                  title: columnTitle5 ?? '',
                                  fontSize: columnTitleFontSize ?? 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: whiteColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              ],
            ),

            20.verticalSpace,

            // FOOTER
            if (showFooterBox)
              CustomContainer(
                width: double.infinity,
                radius: 8,
                bgColor: baseBlueColor.withOpacity(0.4),
                widget: Padding(
                  padding: EdgeInsets.all(20.r),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 350.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomContainer(
                              widget: CustomTextWigdet(
                                title: footerTitle1 ?? '',
                                fontSize: footerTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            ),
                            CustomContainer(
                              width: 150,
                              widget: CustomTextWigdet(
                                title: valueTitle1 ?? '',
                                fontSize: valueTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            )
                          ],
                        ),
                      ),
                      20.verticalSpace,
                      SizedBox(
                        width: 350.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomContainer(
                              widget: CustomTextWigdet(
                                title: footerTitle2 ?? '',
                                fontSize: footerTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            ),
                            CustomContainer(
                              width: 150,
                              widget: CustomTextWigdet(
                                title: valueTitle2 ?? '',
                                fontSize: valueTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            )
                          ],
                        ),
                      ),
                      20.verticalSpace,
                      SizedBox(
                        width: 350.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomContainer(
                              widget: CustomTextWigdet(
                                title: footerTitle3 ?? '',
                                fontSize: footerTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            ),
                            CustomContainer(
                              width: 150,
                              widget: CustomTextWigdet(
                                title: valueTitle3 ?? '',
                                fontSize: valueTitlleFontSize ?? 18,
                                fontWeight: FontWeight.w500,
                                textColor: secondWhiteColor,
                              ),
                            )
                          ],
                        ),
                      ),
                      if ((footerTitle4 != null && footerTitle4!.isNotEmpty) ||
                          (valueTitle4 != null && valueTitle4!.isNotEmpty)) ...[
                        20.verticalSpace,
                        SizedBox(
                          width: 350.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomContainer(
                                widget: CustomTextWigdet(
                                  title: footerTitle4 ?? '',
                                  fontSize: footerTitlleFontSize ?? 18,
                                  fontWeight: FontWeight.w500,
                                  textColor: secondWhiteColor,
                                ),
                              ),
                              CustomContainer(
                                width: 150,
                                widget: CustomTextWigdet(
                                  title: valueTitle4 ?? '',
                                  fontSize: valueTitlleFontSize ?? 18,
                                  fontWeight: FontWeight.w500,
                                  textColor: secondWhiteColor,
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
