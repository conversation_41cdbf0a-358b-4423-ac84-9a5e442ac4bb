import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/grades/grades_table.dart';
import 'package:mides_skadik/widgets/course/grades/samapta/samapta_controller.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_field_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class SamaptaLandscape extends StatelessWidget {
  const SamaptaLandscape({super.key});

  String formatNumber(num value) {
    return value % 1 == 0 ? value.toInt().toString() : value.toStringAsFixed(1);
  }

  @override
  Widget build(BuildContext context) {
    final samaptaController = Get.put(SamaptaController());

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 282.w,
              height: 48.h,
              child: CustomTextFieldWidget(
                colorField: baseBlueColor.withOpacity(0.4),
                radius: 8,
                hintText: 'Search prestasi jasmani',
                colorTextHint: secondWhiteColor.withOpacity(0.3),
                fontSize: 18,
                colorText: secondWhiteColor,
                iconHeight: 15,
                assetNameIcon: 'assets/icons/search.svg',
                colorPrefixIcon: secondWhiteColor.withOpacity(0.3),
                contentPadding: EdgeInsetsDirectional.symmetric(
                  vertical: 12.h,
                  horizontal: 10.w,
                ),
                onChanged: samaptaController.updateSearchQuery,
              ),
            ),
            CustomFilledButtonWidget(
              onPressed: () {
                samaptaController.printSamaptaData();
              },
              title: 'Print',
              fontSize: 20,
              fontColor: whiteColor,
              bgColor: baseBlueColor.withOpacity(0.5),
              withIcon: true,
              assetName: 'assets/icons/printer.svg',
              padding: const EdgeInsets.symmetric(horizontal: 14),
              radius: 8,
            ),
          ],
        ),
        24.verticalSpace,
        Obx(() {
          final data = samaptaController.samaptaData.value;
          final isLoading = samaptaController.isLoading.value;
          final query = samaptaController.searchQuery.value.toLowerCase();

          if (isLoading) {
            return const Center(
              child: CustomLoadingWidget(
                width: 60,
                height: 60,
                imageWidth: 60,
                imageHeight: 60,
                strokeWidth: 3,
              ),
            );
          }
          if (data == null) {
            return Center(
              child: CustomTextWigdet(
                title: "Tidak ada data academic",
                fontSize: 16,
                fontWeight: FontWeight.w500,
                textColor: whiteColor,
              ),
            );
          }

          final totalItems = data.aspectsByType
              .fold<int>(0, (sum, group) => sum + group.aspects.length);
          final isScrollable = totalItems > 15;
          final containerHeight = isScrollable ? 1450.h : null;

          return CustomContainer(
            bgColor: baseBlueColor.withOpacity(0.3),
            radius: 8,
            height: containerHeight,
            widget: GradesTable(
              title: 'Nilai Prestasi Jasmani',
              columnTitle1: 'NO',
              columnTitle2: 'MATA PELAJARAN',
              columnTitle4: 'POIN',
              columnTitle5: 'NILAI',
              showFooterBox: true,
              footerTitle1: 'Rata - Rata B',
              footerTitle2: 'Rata - Rata AB',
              footerTitle3: 'Nilai Akhir',
              footerTitle4: 'Keterangan',
              titleFontSize: 20,
              columnTitleFontSize: 18,
              valueTitlleFontSize: 18,
              valueTitle1: formatNumber(data.scoreB),
              valueTitle2: formatNumber(data.scoreAb),
              valueTitle3: formatNumber(data.finalScore),
              valueTitle4: data.scoreKualitatif,
              tableBody: CustomContainer(
                width: double.infinity,
                height: isScrollable ? 750 : null,
                radius: 8,
                bgColor: baseBlueColor.withValues(alpha: 0.3),
                widget: SingleChildScrollView(
                  physics: isScrollable
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  child: CustomContainer(
                    radius: 8,
                    border: Border.all(
                      color: secondWhiteColor.withOpacity(0.3),
                      width: 0.3,
                    ),
                    bgColor: baseBlueColor.withOpacity(0.3),
                    widget: Padding(
                      padding: EdgeInsets.symmetric(
                          vertical: 20.h, horizontal: 25.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          60.verticalSpace,
                          ...data.aspectsByType.asMap().entries.map((entry) {
                            final index = entry.key;
                            final group = entry.value;
                            final isLastGroup =
                                index == data.aspectsByType.length - 1;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                10.verticalSpace,
                                ...group.aspects.asMap().entries.map((entry) {
                                  final i = entry.key;
                                  final aspect = entry.value;
                                  final isMatch = query.isNotEmpty &&
                                      (aspect.name
                                              .toLowerCase()
                                              .contains(query) ||
                                          aspect.description
                                              .toLowerCase()
                                              .contains(query));

                                  return Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      if (i == 0) ...[
                                        Row(
                                          children: [
                                            const Expanded(
                                                flex: 1, child: SizedBox()),
                                            Expanded(
                                              flex: 6,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  CustomTextWigdet(
                                                    title:
                                                        'Samapta ${group.type}',
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w700,
                                                    textColor: whiteColor,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const Expanded(
                                                flex: 3, child: SizedBox()),
                                            const Expanded(
                                                flex: 4, child: SizedBox()),
                                          ],
                                        ),
                                        10.verticalSpace,
                                      ],
                                      Row(
                                        children: [
                                          Expanded(
                                            flex: 1,
                                            child: CustomTextWigdet(
                                              title: '${i + 1}',
                                              fontSize: 18,
                                              textColor: whiteColor,
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: CustomContainer(
                                              radius: 4,
                                              bgColor: isMatch
                                                  ? goldenYellow
                                                      .withOpacity(0.4)
                                                  : Colors.transparent,
                                              widget: Padding(
                                                padding: EdgeInsets.all(5.r),
                                                child: CustomTextWigdet(
                                                  title: aspect.description,
                                                  fontSize: 18,
                                                  textColor: whiteColor,
                                                ),
                                              ),
                                            ),
                                          ),
                                          const Expanded(
                                              flex: 2, child: SizedBox()),
                                          Expanded(
                                            flex: 2,
                                            child: CustomTextWigdet(
                                              title: formatNumber(aspect.point),
                                              fontSize: 18,
                                              textColor: whiteColor,
                                              textAlign: TextAlign.right,
                                            ),
                                          ),
                                          const Expanded(
                                              flex: 3, child: SizedBox()),
                                          Expanded(
                                            flex: 4,
                                            child: CustomTextWigdet(
                                              title: formatNumber(aspect.score),
                                              fontSize: 18,
                                              textColor: whiteColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  );
                                }).toList(),
                                if (!isLastGroup) ...[
                                  16.verticalSpace,
                                  Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 8.w),
                                    child: Divider(
                                      color: secondWhiteColor.withOpacity(0.4),
                                      thickness: 0.6,
                                    ),
                                  ),
                                  16.verticalSpace,
                                ],
                              ],
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
}
