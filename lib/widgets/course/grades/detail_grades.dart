import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/grades_course/controllers/grades_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';
import 'package:mides_skadik/widgets/course/grades/academic/academic_controller.dart';

class DetailGrades extends StatelessWidget {
  const DetailGrades({super.key});

  @override
  Widget build(BuildContext context) {
    final gradesController = Get.find<GradesCourseController>();
    final academicController = Get.find<AcademicController>();
    final subMapel = academicController.selectedSubMapel.value;

    if (subMapel == null) return const SizedBox();

    return CustomContainer(
      width: 670,
      radius: 8,
      bgColor: baseBlueColor.withOpacity(0.4),
      widget: Padding(
        padding: EdgeInsets.all(20.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Center(
                    child: CustomTextWigdet(
                      title: 'Detail',
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => gradesController.openDetailGrades(),
                  child: Icon(Icons.close, color: whiteColor),
                ),
              ],
            ),
            30.verticalSpace,
            CustomTextWigdet(
              title: 'BS Pembinaan Kejuruan',
              fontSize: 18,
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            CustomTextWigdet(
              title: 'a. SBS Pengetahuan Pendukung',
              fontSize: 14,
              fontWeight: FontWeight.w400,
              textColor: secondWhiteColor.withOpacity(0.8),
            ),
            30.verticalSpace,
            CustomContainer(
              bgColor: baseBlueColor.withOpacity(0.4),
              radius: 8,
              widget: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.all(12.r),
                    child: CustomTextWigdet(
                      title: subMapel.name,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      textColor: whiteColor,
                    ),
                  ),
                  5.verticalSpace,
                  CustomContainer(
                    bgColor: baseBlueColor.withOpacity(0.3),
                    widget: Padding(
                      padding: EdgeInsets.all(12.r),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextWigdet(
                                  title: 'NILAI PELAJARAN',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: secondWhiteColor.withOpacity(0.7),
                                ),
                                6.verticalSpace,
                                CustomTextWigdet(
                                  title: subMapel.nilaiPelajaran,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  textColor: whiteColor,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12.w),
                            child: VerticalDivider(
                              color: secondWhiteColor.withOpacity(0.4),
                              thickness: 0.5,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextWigdet(
                                  title: 'HARGA NILAI',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: secondWhiteColor.withOpacity(0.7),
                                ),
                                6.verticalSpace,
                                CustomTextWigdet(
                                  title: subMapel.hargaNilai.toString(),
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  textColor: whiteColor,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12.w),
                            child: VerticalDivider(
                              color: secondWhiteColor.withOpacity(0.4),
                              thickness: 0.5,
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomTextWigdet(
                                  title: 'HARGA PRESTASI',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  textColor: secondWhiteColor.withOpacity(0.7),
                                ),
                                6.verticalSpace,
                                CustomTextWigdet(
                                  title: subMapel.nilaiPrestasi,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  textColor: whiteColor,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            20.verticalSpace,
            for (var assessment in subMapel.assessments) ...[
              Theme(
                data: ThemeData().copyWith(
                  dividerColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                ),
                child: Container(
                  margin: EdgeInsets.only(bottom: 12.h),
                  decoration: BoxDecoration(
                    color: baseBlueColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: ExpansionTile(
                    iconColor: whiteColor,
                    collapsedIconColor: whiteColor,
                    tilePadding: EdgeInsets.symmetric(horizontal: 12.w),
                    childrenPadding: EdgeInsets.only(bottom: 12.h),
                    backgroundColor: Colors.transparent,
                    collapsedBackgroundColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    title: Row(
                      children: [
                        CustomTextWigdet(
                          title: assessment.type, // misal: "Quiz", "Penugasan"
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          textColor: whiteColor,
                        ),
                        10.horizontalSpace,
                        CustomContainer(
                          bgColor: blueColor.withOpacity(0.2),
                          radius: 4,
                          widget: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 6.w, vertical: 2.h),
                            child: CustomTextWigdet(
                              title: assessment.averageScore,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              textColor: blueColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 14.w),
                        child: CustomContainer(
                          bgColor: baseBlueColor.withOpacity(0.6),
                          radius: 8,
                          widget: Padding(
                            padding: EdgeInsets.all(10.r),
                            child: Column(
                              children: [
                                for (var score in assessment.scores) ...[
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      CustomTextWigdet(
                                        title: score
                                            .name, // misal: Quiz 1, Penugasan 1
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        textColor: secondWhiteColor,
                                      ),
                                      CustomTextWigdet(
                                        title: score.score,
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        textColor: secondWhiteColor,
                                      ),
                                    ],
                                  ),
                                  8.verticalSpace,
                                ],
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomTextWigdet(
                                      title:
                                          'Rata-rata ${assessment.type.toLowerCase()}',
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      textColor: whiteColor,
                                    ),
                                    CustomTextWigdet(
                                      title: assessment.averageScore,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      textColor: whiteColor,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            20.verticalSpace,
            // CustomFilledButtonWidget(
            //   onPressed: () {},
            //   withIcon: true,
            //   assetName: 'assets/icons/print_bold.svg',
            //   title: 'Print Detail',
            //   fontSize: 18,
            //   fontWeight: FontWeight.w500,
            //   fontColor: whiteColor,
            //   bgColor: blueColor,
            //   radius: 8,
            //   iconColor: whiteColor,
            //   widthIcon: 24,
            //   heightIcon: 24,
            // )
          ],
        ),
      ),
    );
  }
}
