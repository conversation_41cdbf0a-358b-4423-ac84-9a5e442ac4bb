import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class UpcomingAssignmentItemWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final DateTime date;
  const UpcomingAssignmentItemWidget({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.date,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: title,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.white,
                  ),
                  const SizedBox(height: 4),
                  CustomTextWigdet(
                    title: subtitle,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    textColor: const Color(0xFFDEE6EC).withOpacity(0.6),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            CustomTextWigdet(
              title: DateFormat("dd MMMM").format(date),
              fontSize: 18,
              fontWeight: FontWeight.w400,
              textColor: Colors.white,
            ),
          ],
        ),
      ),
    );
  }
}
