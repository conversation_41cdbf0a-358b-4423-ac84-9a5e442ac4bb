import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/assignment_calendar_controller.dart';
import 'package:mides_skadik/app/modules/course/history_attendance_course/controllers/history_attendance_course_controller.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/custom_calender.dart';
import 'package:mides_skadik/widgets/course/dashboard/assignment_calendar/upcoming_assignment_item.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AssignmentCalendarDashboard extends StatefulWidget {
  const AssignmentCalendarDashboard({Key? key}) : super(key: key);

  @override
  State<AssignmentCalendarDashboard> createState() =>
      _AssignmentCalendarDashboardState();
}

class _AssignmentCalendarDashboardState
    extends State<AssignmentCalendarDashboard> {
  final historyAttendanceController =
      Get.put(HistoryAttendanceCourseController());
  final assignmentController = Get.put(AssignmentCalendarController());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      padding: const EdgeInsets.fromLTRB(8, 8, 8, 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        gradient: LinearGradient(
          colors: [
            const Color(0xFF0e3a56).withAlpha(70),
            const Color(0xFF0e3a56).withAlpha(30),
            const Color(0xFF1d557b).withAlpha(120),
          ],
          stops: const [
            0.0,
            0.7,
            1.0,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          16.verticalSpace,
          const CustomTextWigdet(title: "Assignment Calendar"),
          16.verticalSpace,
          Obx(
            () => CustomCalender(
              selected: assignmentController.list.map((e) => e.date!).toList(),
              onMonthChanged: (month) {
                assignmentController.loadData(
                  month: month,
                );
              },
              onYearChanged: (year) {
                assignmentController.loadData(
                  year: year,
                );
              },
            ),
          ),
          32.verticalSpace,
          const CustomTextWigdet(title: "Upcoming Assignment"),
          Obx(
            () => assignmentController.list.isEmpty
                ? CustomContainer(
                    width: Get.width,
                    padding: const EdgeInsets.fromLTRB(0, 32, 0, 16),
                    widget: const CustomTextWigdet(
                      title: "No Upcoming Assignment",
                      textAlign: TextAlign.center,
                    ),
                  )
                : ListView.separated(
                    padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var row = assignmentController.list[index];
                      return UpcomingAssignmentItemWidget(
                        title: row.title ?? "",
                        subtitle: (row.description == "undefined")
                            ? ""
                            : row.description!,
                        date: row.date!,
                      );
                    },
                    separatorBuilder: (context, index) {
                      return 16.verticalSpace;
                    },
                    itemCount: assignmentController.list.length,
                  ),
          ),
        ],
      ),
    );
  }
}
