import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/task_dashboard_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/task/task_list_item.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class TasklistDashboard extends StatefulWidget {
  const TasklistDashboard({
    super.key,
    this.onViewAll,
  });

  final VoidCallback? onViewAll;

  @override
  State<TasklistDashboard> createState() => _TasklistDashboardState();
}

class _TasklistDashboardState extends State<TasklistDashboard> {
  final controller = Get.put(TaskDashboardController());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const CustomTextWigdet(
              title: "Task List",
              fontSize: 28,
              fontWeight: FontWeight.w600,
            ),
            CustomButtonOutlined(
              label: "View All",
              onTap: widget.onViewAll,
            ),
          ],
        ),
        16.verticalSpace,
        Obx(
          () => controller.isLoading.value
              ? CustomContainer(
                  width: Get.width,
                  height: 200,
                  widget: const Stack(
                    children: [Center(child: CustomLoadingWidget())],
                  ),
                )
              : controller.list.isEmpty
                  ? CustomContainer(
                      width: double.infinity,
                      height: 100,
                      radius: 14,
                      alignment: Alignment.center,
                      bgColor: secondBlueColor.withOpacity(0.04),
                      widget: const CustomTextWigdet(title: "No Task"),
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 16),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var row = controller.list[index];
                        return TaskListItemWidget(
                          title: row.title ?? '',
                          teacherPicture: row.createdBy?.imageProfile ??
                              'assets/images/logo.png',
                          teacherName: row.createdBy?.name ?? '',
                          timeRemaining: row.endDate!,
                          mapel: row.submapel?.mapel?.name ?? "",
                          onSubmit: () {},
                        );
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(height: 8);
                      },
                      itemCount: controller.list.length),
        ),
        24.verticalSpace,
      ],
    );
  }
}
