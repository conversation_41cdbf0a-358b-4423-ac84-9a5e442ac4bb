import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/detail_assignment_view.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/custom_assignment_course.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class TaskListItemWidget extends StatelessWidget {
  final String title;
  final String teacherPicture;
  final String teacherName;
  final String mapel;
  final VoidCallback onSubmit;
  final DateTime timeRemaining;
  final bool? isSubmited;

  final String? icon;

  const TaskListItemWidget({
    Key? key,
    required this.title,
    required this.teacherPicture,
    required this.teacherName,
    required this.timeRemaining,
    required this.mapel,
    required this.onSubmit,
    this.isSubmited,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      radius: 14,
      bgColor: secondBlueColor.withOpacity(0.04),
      widget: Stack(
        children: [
          // radial gradient background
          Positioned.fill(
            child: CustomContainer(
              gradient: RadialGradient(
                center: const Alignment(-1.1, 0.0),
                radius: 4.5,
                colors: [
                  const Color(0xFF00A3FF).withOpacity(0.3),
                  const Color(0x070B1D00),
                ],
                stops: [0.0, 1.0],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                CustomContainer(
                  padding: const EdgeInsets.all(12),
                  bgColor: whiteColor.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(10),
                  widget: SvgPicture.asset(
                    icon ?? "assets/icons/file_copy.svg", // Replace with your PDF icon path
                    width: 18,
                    height: 18,
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextWigdet.title(title),
                      8.verticalSpace,
                      Row(
                        children: [
                          CustomContainer(
                            height: 24,
                            width: 24,
                            clipBehavior: Clip.antiAliasWithSaveLayer,
                            bgColor: Colors.white,
                            shape: BoxShape.circle,
                            widget: teacherPicture.contains("http")
                                ? Image.network(
                                    teacherPicture,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Image.asset(
                                        "assets/images/logo.png",
                                        fit: BoxFit.contain,
                                      );
                                    },
                                  )
                                : Image.asset(
                                    teacherPicture,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Image.asset(
                                        "assets/images/logo.png",
                                        fit: BoxFit.contain,
                                      );
                                    },
                                  ),
                          ),
                          10.horizontalSpace,
                          CustomTextWigdet.subtitle(teacherName),
                        ],
                      ),
                    ],
                  ),
                ),
                12.horizontalSpace,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: "TIME REMAINING",
                      fontSize: 12,
                      textColor: secondWhiteColor,
                    ),
                    12.verticalSpace,
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          "assets/icons/calendar_today.svg",
                          height: 10,
                        ),
                        8.horizontalSpace,
                        CustomTextWigdet(
                          title: [
                            "${timeRemaining.day} day${timeRemaining.day > 1 ? 's' : ''}",
                            "${timeRemaining.hour} hour${timeRemaining.hour > 1 ? 's' : ''}",
                          ].join(", "),
                          fontSize: 12,
                          textColor: Colors.white,
                        ),
                      ],
                    ),
                  ],
                ),
                12.horizontalSpace,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWigdet(
                      title: "MAPEL",
                      fontSize: 12,
                      textColor: secondWhiteColor,
                    ),
                    4.verticalSpace,
                    CustomContainer(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      bgColor: whiteColor.withAlpha(40),
                      borderRadius: BorderRadius.circular(4),
                      widget: CustomTextWigdet(
                        title: mapel,
                        fontSize: 12,
                        textColor: whiteColor,
                      ),
                    ),
                  ],
                ),
                24.horizontalSpace,
                CustomFilledButtonWidget(
                  onPressed: () {
                    Get.to(DetailAssignmentView());
                  },
                  title: isSubmited == true ? "Submited" : "Submit",
                  radius: 4,
                  bgColor: isSubmited == true ? darkGreyColor : blueColor,
                  fontColor: whiteColor,
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                  fontSize: 16,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
