import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/app/modules/course/history_attendance_course/controllers/history_attendance_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/custom_calender_history.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAttendanceHistory extends StatefulWidget {
  final String? iconPath;
  final String? day;
  final String? date;
  final String? time;
  final String? status;

  const CustomAttendanceHistory({
    super.key,
    this.iconPath,
    this.day,
    this.date,
    this.time,
    this.status,
  });

  @override
  State<CustomAttendanceHistory> createState() =>
      _CustomAttendanceHistoryState();
}

class _CustomAttendanceHistoryState extends State<CustomAttendanceHistory> {
  final HistoryAttendanceCourseController controller =
      Get.put(HistoryAttendanceCourseController());

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.getAttendanceHistoryViewAll();
    });
  }

  List<Widget> buttonsPagination() {
    final totalPage = controller.totalPage.value;
    final currentPage = controller.currentPage.value;

    List<Widget> buttons = [];
    int maxButtonShow = 5;

    int startPage = 1;
    int endPage = totalPage;

    if (totalPage > maxButtonShow) {
      if (currentPage <= maxButtonShow) {
        startPage = 1;
        endPage = maxButtonShow;
      } else {
        int currentBlock = ((currentPage - 1) / maxButtonShow).floor();
        startPage = currentBlock * maxButtonShow + 1;
        endPage = startPage + maxButtonShow - 1;
        if (endPage > totalPage) endPage = totalPage;
      }
    }

    for (int page = startPage; page <= endPage; page++) {
      final isActive = currentPage == page;

      buttons.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: CustomFilledButtonWidget(
            onPressed: () => controller.setPage(page),
            title: '$page',
            fontColor: whiteColor,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            widthButton: 28,
            heightButton: 28,
            bgColor: isActive ? secondBlueColor : Colors.transparent,
            radius: 8,
          ),
        ),
      );
    }

    if (endPage < totalPage) {
      buttons.add(
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: CustomFilledButtonWidget(
            onPressed: () => controller.setPage(endPage + 1),
            title: '...',
            fontColor: whiteColor,
            fontSize: 14,
            fontWeight: FontWeight.w400,
            widthButton: 28,
            heightButton: 28,
            bgColor: Colors.transparent,
            radius: 8,
          ),
        ),
      );
    }

    return buttons;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(5.r),
              topRight: Radius.circular(5.r),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
              child: CustomContainer(
                width: 1038,
                bgColor: secondDarkBlueColor.withOpacity(0.8),
                widget: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 52.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      32.verticalSpace,
                      Row(
                        children: [
                          Expanded(
                            child: Center(
                              child: CustomTextWigdet(
                                title: 'Attendance History',
                                fontSize: 32,
                                fontWeight: FontWeight.w700,
                                textColor: whiteColor,
                              ),
                            ),
                          ),
                          CustomFilledButtonWidget(
                            onPressed: () {
                              Get.back();
                            },
                            widthButton: 32,
                            heightButton: 32,
                            onlyIcon: true,
                            withIcon: true,
                            assetName: 'assets/icons/icon_X.svg',
                            widthIcon: 32,
                            heightIcon: 32,
                            bgColor: Colors.transparent,
                          )
                        ],
                      ),
                      32.verticalSpace,
                      GestureDetector(
                        onTap: () {
                          Get.find<HistoryAttendanceCourseController>()
                              .openDialog();
                        },
                        child: CustomContainer(
                          height: 44,
                          bgColor: secondBlueColor.withOpacity(0.10),
                          widget: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.w),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/calendar.svg',
                                  width: 24.w,
                                  height: 24.h,
                                ),
                                8.horizontalSpace,
                                Obx(() => CustomTextWigdet(
                                      title:
                                          '${formattedDateOnly(controller.dateNow.value)} - ${formattedDateOnly(controller.dateWeek.value)}',
                                      fontSize: 18,
                                      fontWeight: FontWeight.w400,
                                      textColor: whiteColor,
                                    )),
                                8.horizontalSpace,
                                SvgPicture.asset(
                                  'assets/icons/icon_chevron.svg',
                                  width: 24.w,
                                  height: 24.h,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      8.verticalSpace,
                    ],
                  ),
                ),
              ),
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(5.r),
              bottomRight: Radius.circular(5.r),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
              child: CustomContainer(
                width: 1038,
                height: 920,
                bgColor: secondDarkBlueColor.withOpacity(0.9),
                widget: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 52.w),
                    child: Stack(
                      children: [
                        Column(
                          children: [
                            Expanded(
                              child: SingleChildScrollView(
                                child: Obx(
                                  () {
                                    final datas = controller.historyAttendance;
                                    if (controller.isLoading.value ||
                                        controller
                                            .isLoadingMoreAttendance.value) {
                                      return const SizedBox(
                                        height: 500,
                                        child: Center(
                                            child: CustomLoadingWidget()),
                                      );
                                    }

                                    if (datas.isEmpty &&
                                        !controller.isLoading.value) {
                                      return const SizedBox(
                                        height: 500,
                                        child: Center(
                                          child: CustomTextWigdet(
                                            title: 'No Data Attendance',
                                            fontSize: 24,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      );
                                    }

                                    return Column(
                                      children: datas.map((e) {
                                        final isClockIn = (e.clockIn != null &&
                                            e.clockOut != null);
                                        final Color statusColor =
                                            !isClockIn ? darkGreen : darkRed;
                                        final attendanceTime =
                                            e.clockOut ?? e.clockIn;
                                        final String iconPath = !isClockIn
                                            ? 'assets/icons/in.svg'
                                            : 'assets/icons/out.svg';

                                        return Padding(
                                          padding:
                                              EdgeInsets.only(bottom: 24.h),
                                          child: Row(
                                            children: [
                                              ClipOval(
                                                child: CustomContainer(
                                                  width: 48,
                                                  height: 48,
                                                  bgColor: statusColor
                                                      .withOpacity(0.40),
                                                  widget: Center(
                                                    child: SvgPicture.asset(
                                                      iconPath,
                                                      width: 20.w,
                                                      height: 20.h,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              8.horizontalSpace,
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  CustomTextWigdet(
                                                    title:
                                                        getDaysInDate(e.date),
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.w400,
                                                    textColor: whiteColor,
                                                  ),
                                                  CustomTextWigdet(
                                                    title: formattedDateOnly(
                                                        e.date),
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w400,
                                                    textColor: secondWhiteColor
                                                        .withOpacity(0.60),
                                                  ),
                                                ],
                                              ),
                                              const Spacer(),
                                              CustomTextWigdet(
                                                title: formatTimeOnly(
                                                    attendanceTime!,
                                                    withSeconds: true),
                                                fontSize: 18,
                                                fontWeight: FontWeight.w400,
                                                textColor: whiteColor,
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                    );
                                  },
                                ),
                              ),
                            ),
                            24.verticalSpace,
                            Obx(() {
                              return Center(
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    GestureDetector(
                                      onTap: controller.currentPage.value == 1
                                          ? null
                                          : () {
                                              if (controller
                                                      .currentPage.value >=
                                                  controller.totalPage.value) {
                                                controller.setPage(controller
                                                        .currentPage.value -
                                                    1);
                                              }
                                            },
                                      child: SvgPicture.asset(
                                        'assets/icons/left.svg',
                                        width: 32.w,
                                        height: 32.h,
                                      ),
                                    ),
                                    8.horizontalSpace,
                                    ...buttonsPagination(),
                                    8.horizontalSpace,
                                    GestureDetector(
                                      onTap: () {
                                        if (controller.currentPage.value <
                                            controller.totalPage.value) {
                                          controller.setPage(
                                              controller.currentPage.value + 1);
                                        }
                                      },
                                      child: SvgPicture.asset(
                                        'assets/icons/right.svg',
                                        width: 32.w,
                                        height: 32.h,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                            24.verticalSpace,
                          ],
                        ),
                        Obx(
                          () => Positioned(
                            top: 0,
                            left: 0,
                            child: Get.find<HistoryAttendanceCourseController>()
                                    .isOpen
                                    .value
                                ? const CustomCalenderHistory()
                                : const SizedBox(),
                          ),
                        ),
                      ],
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
