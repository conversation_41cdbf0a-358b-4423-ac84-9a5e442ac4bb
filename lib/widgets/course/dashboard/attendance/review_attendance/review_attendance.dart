import 'dart:io';
import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/attendance_course/controllers/attendance_course_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/dashboard_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/bottom_nav_bar.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/review_attendance/review_attendance_landscape.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class ReviewImageAttendance extends StatelessWidget {
  final String imagePath;
  final Map<String, dynamic>? metadata;
  final bool isClockOut;

  const ReviewImageAttendance({
    super.key,
    required this.imagePath,
    required this.metadata,
    required this.isClockOut,
  });

  @override
  Widget build(BuildContext context) {
    final dashboardController = Get.find<DashboardCourseController>();
    final orientation = MediaQuery.of(context).orientation;

    if (orientation == Orientation.landscape) {
      return ReviewImageAttendanceLandscape(
        imagePath: imagePath,
        metadata: metadata,
        isClockOut: isClockOut,
      );
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(5),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 9, sigmaY: 9),
        child: CustomContainer(
          width: 1038,
          height: 1700,
          bgColor: baseBlueColor.withOpacity(0.5),
          widget: Padding(
            padding: EdgeInsets.symmetric(vertical: 52.h, horizontal: 52.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Center(
                        child: CustomTextWigdet(
                          title: 'Review',
                          fontSize: 32,
                          fontWeight: FontWeight.w700,
                          textColor: whiteColor,
                        ),
                      ),
                    ),
                    CustomFilledButtonWidget(
                      onPressed: () {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          dashboardController.showReview.value = false;
                          dashboardController.imagePath = '';
                          dashboardController.metadata = {};
                          Get.offAll(() =>
                              const BottomNavBar(choosenScreen: 'course'));
                        });
                      },
                      widthButton: 60,
                      heightButton: 60,
                      onlyIcon: true,
                      withIcon: true,
                      assetName: 'assets/icons/icon_X.svg',
                      widthIcon: 60,
                      heightIcon: 60,
                      bgColor: Colors.transparent,
                    )
                  ],
                ),
                32.verticalSpace,
                CustomContainer(
                  width: double.infinity,
                  height: 1201,
                  widget: Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.rotationY(math.pi),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                16.verticalSpace,
                CustomTextWigdet(
                  title:
                      'Great, You’re in Class and Ready to Begin the Lesson!',
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
                16.verticalSpace,
                CustomTextWigdet(
                  title:
                      'Your attendance has been successfully recorded. Please stay engaged, follow the instructor’s direction, and complete all tasks in accordance with the schedule.',
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  textColor: secondWhiteColor,
                  textAlign: TextAlign.center,
                ),
                16.verticalSpace,
                CustomFilledButtonWidget(
                  onPressed: () {
                    LogService.log
                        .i('🔥 Kondisi isClockOut dari argumen: $isClockOut');

                    if (isClockOut) {
                      dashboardController.submitClockOut();
                      LogService.log.i('Fungsi: submitClockOut dipanggil');
                    } else {
                      dashboardController.submitAttendance();
                      LogService.log.i('Fungsi: submitAttendance dipanggil');
                    }
                  },
                  title: 'Submit',
                  bgColor: blueColor,
                  radius: 4,
                  heightButton: 52,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  fontColor: whiteColor,
                ),
                16.verticalSpace,
                CustomFilledButtonWidget(
                  onPressed: () {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      dashboardController.showReview.value = false;
                      Get.delete<AttendanceCourseController>();
                      Get.toNamed('/attendance-course',
                          preventDuplicates: false);
                    });
                  },
                  title: 'Retake',
                  bgColor: Colors.transparent,
                  radius: 4,
                  heightButton: 52,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  fontColor: whiteColor,
                  isOutlined: true,
                  borderColor: whiteColor,
                  outlineWidth: 0.5,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
