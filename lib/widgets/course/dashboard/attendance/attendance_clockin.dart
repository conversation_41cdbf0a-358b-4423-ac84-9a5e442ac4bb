import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/services/localStorage/local_storage.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/attendance_course/controllers/attendance_course_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/dashboard_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AttendanceClockInWidget extends StatefulWidget {
  final bool? isClockOut;
  final DateTime? clock;

  const AttendanceClockInWidget({
    Key? key,
    this.isClockOut,
    this.clock,
  }) : super(key: key);

  @override
  State<AttendanceClockInWidget> createState() =>
      _AttendanceClockInWidgetState();
}

class _AttendanceClockInWidgetState extends State<AttendanceClockInWidget> {
  bool isDisabled = false;

  @override
  void initState() {
    super.initState();
    _refreshAndCheckAttendance();
  }

  /// refresh history dan cek status attendance
  Future<void> _refreshAndCheckAttendance() async {
    try {
      if (Get.isRegistered<DashboardCourseController>()) {
        final dashboardController = Get.find<DashboardCourseController>();
        await dashboardController.getAttendanceHistory();
      } else {
        LogService.log.w("DashboardCourseController belum terdaftar.");
      }

      await _checkAttendanceStatus();
    } catch (e) {
      LogService.log.e('Gagal refresh & cek attendance: $e');
    }
  }

  Future<void> _checkAttendanceStatus() async {
    final local = LocalStorage();
    dynamic todayId = await local.get("today_attendance_id");
    LogService.log.i('cek id attendance dari storage: $todayId');

    final dashboardController = Get.find<DashboardCourseController>();

    final todayData = dashboardController.historyist.isNotEmpty
        ? dashboardController.historyist.first
        : null;

    // Jika data tidak valid untuk hari ini
    if (todayData == null) {
      await local.remove("today_attendance_id");
      todayId = null;
      LogService.log
          .i('🧹 ID attendance dihapus karena tidak ada attendance hari ini');
    }

    final isIdValid = todayId != null && todayId.toString().isNotEmpty;

    final hasClockIn = todayData?.clockIn != null;
    final hasClockOut = todayData?.clockOut != null;

    if (!mounted) return;

    setState(() {
      if (hasClockIn && hasClockOut) {
        isDisabled = true;
      } else if (!hasClockIn && !widget.isClockOut!) {
        // clock in belum dilakukan, clock in aktif
        isDisabled = false;
      } else if (hasClockIn && !hasClockOut && widget.isClockOut!) {
        // clock out aktif hanya jika sudah clock in dan belum clock out
        isDisabled = false;
      } else {
        isDisabled = true;
      }
    });

    LogService.log.i(
        'Status tombol (${widget.isClockOut == true ? "Clock Out" : "Clock In"}): '
        'hasClockIn=$hasClockIn, hasClockOut=$hasClockOut, isIdValid=$isIdValid => isDisabled=$isDisabled');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.antiAliasWithSaveLayer,
      decoration: BoxDecoration(
        color: const Color(0xFF293a4f),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          Container(
            clipBehavior: Clip.antiAliasWithSaveLayer,
            padding: const EdgeInsets.fromLTRB(5, 5, 5, 5),
            decoration: const BoxDecoration(
              color: Color(0xFF1e2d3d),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  widget.isClockOut == true
                      ? "assets/icons/clockout.svg"
                      : "assets/icons/clockin.svg",
                  width: 12,
                  height: 12,
                  color: const Color(0xFFDEE6EC).withOpacity(0.6),
                ),
                const SizedBox(width: 6),
                CustomTextWigdet(
                  title:
                      widget.isClockOut == true ? "ABSENCE OUT" : "IN ABSENCE",
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  textColor: const Color(0xFFDEE6EC).withOpacity(0.6),
                ),
              ],
            ),
          ),
          Container(
            height: 70,
            alignment: Alignment.center,
            child: CustomTextWigdet(
              title: widget.clock != null
                  ? "${widget.clock!.hour.toString().padLeft(2, '0')}:${widget.clock!.minute.toString().padLeft(2, '0')}"
                  : "-",
              fontSize: 38,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(
            height: 30,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(4, 0, 4, 4),
              child: CustomFilledButtonWidget(
                title: widget.isClockOut == true ? "Clock Out" : "Clock In",
                onPressed: isDisabled
                    ? null
                    : () {
                        final dashboardController =
                            Get.find<DashboardCourseController>();
                        dashboardController.isClockOut.value =
                            widget.isClockOut ?? false;

                        Get.lazyPut(() => AttendanceCourseController());
                        Get.toNamed(
                          '/attendance-course',
                          arguments: {
                            'isClockOut': widget.isClockOut ?? false,
                          },
                        );
                      },
                fontSize: 16,
                bgColor: isDisabled ? baseBlueColor : blueColor,
                radius: 8,
                fontColor: Colors.white,
              ),
            ),
          )
        ],
      ),
    );
  }
}
