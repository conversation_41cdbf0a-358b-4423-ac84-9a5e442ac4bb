import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/attendance_history_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/custom_attendance_history.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/attendance_clockin.dart';
import 'package:mides_skadik/widgets/course/dashboard/attendance/attendance_history_item.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AttendanceDashboard extends StatefulWidget {
  const AttendanceDashboard({Key? key}) : super(key: key);

  @override
  State<AttendanceDashboard> createState() => _AttendanceDashboardState();
}

class _AttendanceDashboardState extends State<AttendanceDashboard> {
  final attendanceHistoryController = Get.find<AttendanceHistoryController>();
  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      padding: const EdgeInsets.fromLTRB(8, 16, 8, 12),
      bgColor: const Color(0x0A90ADD9),
      widget: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const CustomTextWigdet(title: "Attendance Schedule"),
          16.verticalSpace,
          Row(
            children: [
              CustomContainer(
                padding: const EdgeInsets.all(12),
                bgColor: Colors.white.withOpacity(0.1),
                shape: BoxShape.circle,
                widget: SvgPicture.asset(
                  "assets/icons/time.svg", // Replace with your PDF icon path
                  width: 16,
                  height: 16,
                ),
              ),
              16.verticalSpace,
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: "TODAY PRESENCE",
                    fontSize: 20,
                    textColor: Colors.white,
                  ),
                  CustomTextWigdet(
                    title: "07:00 - 17:00",
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.white,
                  ),
                ],
              ),
            ],
          ),
          24.verticalSpace,
          Obx(
            () => attendanceHistoryController.todayAttendance == null
                ? Column(
                    children: [
                      CustomContainer(
                        padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                        bgColor: const Color(0xFFaf233a),
                        borderRadius: BorderRadius.circular(6),
                        widget: Row(
                          children: [
                            SvgPicture.asset(
                              "assets/icons/time.svg",
                              width: 16,
                              height: 16,
                              color: Colors.white,
                            ),
                            8.horizontalSpace,
                            const Expanded(
                              child: CustomTextWigdet(
                                title:
                                    "You have not taken attendance yet. Do it now so that your attendance is recorded ",
                                fontSize: 12,
                                textAlign: TextAlign.justify,
                                textColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      24.verticalSpace,
                    ],
                  )
                : const CustomContainer(),
          ),
          Row(
            // spacing: 8,
            children: [
              Expanded(
                  child: Obx(
                () => AttendanceClockInWidget(
                  isClockOut: false,
                  clock: attendanceHistoryController.todayAttendance?.clockIn,
                ),
              )),
              16.horizontalSpace,
              Expanded(
                  child: Obx(
                () => AttendanceClockInWidget(
                  isClockOut: true,
                  clock: attendanceHistoryController.todayAttendance?.clockOut,
                ),
              )),
            ],
          ),
          24.verticalSpace,
          Divider(
            height: 1,
            thickness: 0.3,
            color: Colors.white.withOpacity(0.3),
          ),
          24.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const CustomTextWigdet(title: "Attendance"),
              CustomButtonOutlined(
                  label: "View All",
                  onTap: () {
                    Get.dialog(const CustomAttendanceHistory());
                  }),
            ],
          ),
          24.verticalSpace,
          Obx(
            () => attendanceHistoryController.isLoading.isTrue
                ? CustomContainer(
                    width: Get.width,
                    height: 200,
                    widget: const Stack(
                      children: [Center(child: CustomLoadingWidget())],
                    ),
                  )
                : attendanceHistoryController.list.isEmpty
                    ? CustomContainer(
                        padding: const EdgeInsets.all(16),
                        width: Get.width,
                        widget: const CustomTextWigdet(
                          title: "No Attendance History",
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ListView.separated(
                        shrinkWrap: true,
                        padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          var row = attendanceHistoryController.list[index];
                          return Column(
                            children: [
                              if (row.clockIn != null)
                                AttendanceHistoryItemWidget(
                                  isClockOut: false,
                                  date: row.clockIn!,
                                ),
                              if (row.clockOut != null) ...[
                                16.verticalSpace,
                                AttendanceHistoryItemWidget(
                                  isClockOut: row.clockOut != null,
                                  date: row.clockOut!,
                                ),
                              ],
                            ],
                          );
                        },
                        separatorBuilder: (context, index) {
                          return 16.verticalSpace;
                        },
                        itemCount: attendanceHistoryController.list.length,
                      ),
          ),
        ],
      ),
    );
  }
}
