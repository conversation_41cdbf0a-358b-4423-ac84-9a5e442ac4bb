import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AttendanceHistoryItemWidget extends StatefulWidget {
  final bool? isClockOut;
  final DateTime date;
  const AttendanceHistoryItemWidget({
    Key? key,
    this.isClockOut,
    required this.date,
  }) : super(key: key);

  @override
  State<AttendanceHistoryItemWidget> createState() => _AttendanceHistoryItemWidgetState();
}

class _AttendanceHistoryItemWidgetState extends State<AttendanceHistoryItemWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: widget.isClockOut == true ? const Color(0xFF51283c) : Color(0xFF17513c),
            shape: BoxShape.circle,
          ),
          child: SvgPicture.asset(
            widget.isClockOut == true ? "assets/icons/clockout.svg" : "assets/icons/clockin.svg",
            width: 12,
            height: 12,
            color: Colors.white,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTextWigdet(
              title: DateFormat("EEEE", "id").format(widget.date),
              fontSize: 14,
              textColor: Colors.white,
            ),
            const SizedBox(height: 2),
            CustomTextWigdet(
              title: DateFormat("d MMM y").format(widget.date),
              fontSize: 12,
              textColor: Color(0xFFDEE6EC),
            ),
          ],
        )),
        const SizedBox(width: 8),
        CustomTextWigdet(
          title: DateFormat("HH:mm:ss").format(widget.date),
          fontSize: 12,
          textColor: Color(0xFFDEE6EC),
          textAlign: TextAlign.end,
        ),
      ],
    );
  }
}
