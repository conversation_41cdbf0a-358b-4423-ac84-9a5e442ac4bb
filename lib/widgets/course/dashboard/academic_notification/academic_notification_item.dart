import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AcademicNotificationItemWidget extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final DateTime? dateTime;

  const AcademicNotificationItemWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.dateTime,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomContainer(
            padding: EdgeInsets.all(12),
            bgColor: whiteColor.withOpacity(0.08),
            shape: BoxShape.circle,
            widget: SvgPicture.asset(
              "assets/icons/menu_book.svg", // Replace with your PDF icon path
              width: 16,
              height: 16,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWigdet(
                  title: title ?? '',
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  textColor: whiteColor,
                ),
                4.verticalSpace,
                CustomTextWigdet(
                  title: subtitle ?? '',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  textColor: secondWhiteColor.withOpacity(0.6),
                ),
                4.verticalSpace,
                CustomTextWigdet(
                  title: dateTime != null ? DateFormat("dd MMMM yyyy • HH:mm WIB").format(dateTime!).toString() : '',
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  textColor: secondWhiteColor.withOpacity(0.6),
                ),
              ],
            ),
          ),
          CustomContainer(
            bgColor: goldenYellow,
            shape: BoxShape.circle,
            width: 8,
            height: 8,
          ),
        ],
      ),
    );
  }
}
