import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/annoucement_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/academic_notification/academic_notification_item.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class AcademicNotificationDashboard extends StatefulWidget {
  const AcademicNotificationDashboard({super.key});

  @override
  State<AcademicNotificationDashboard> createState() =>
      _AcademicNotificationDashboardState();
}

class _AcademicNotificationDashboardState
    extends State<AcademicNotificationDashboard> {
  final announcementController = Get.find<AnnouncementController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomTextWigdet(
              title: "Academic Notification",
              fontSize: 28,
              fontWeight: FontWeight.w600,
            ),
            // CustomButtonOutlined(
            //   label: "View All",
            //   onTap: () {},
            // ),
          ],
        ),
        16.verticalSpace,
        Obx(() {
          return CustomContainer(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
            radius: 14,
            gradient: LinearGradient(
              colors: [
                const Color(0xFF1d557b).withAlpha(120),
                const Color(0xFF0e3a56).withAlpha(30),
                const Color(0xFF0e3a56).withAlpha(30),
                const Color(0xFF1d557b).withAlpha(120),
              ],
              stops: const [
                0.0,
                0.6,
                0.8,
                1.0,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            widget: announcementController.list.isEmpty
                ? Center(
                    child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child:
                            const CustomTextWigdet(title: "No Notification")),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var row = announcementController.list[index];
                      return AcademicNotificationItemWidget(
                        title: row.title ?? '',
                        subtitle: row.description ?? '',
                        dateTime: row.createdAt,
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Divider(
                        height: 1,
                        color: whiteColor.withOpacity(0.1),
                      );
                    },
                    itemCount: announcementController.list.length,
                  ),
          );
        }),
        8.verticalSpace,
      ],
    );
  }
}
