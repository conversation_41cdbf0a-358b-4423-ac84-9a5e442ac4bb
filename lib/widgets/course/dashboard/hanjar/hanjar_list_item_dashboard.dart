import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class HanjarListItemDashboardWidget extends StatelessWidget {
  final String type;
  final String title;
  final String subtitle;
  final VoidCallback onDownload;
  final VoidCallback onRead;
  final VoidCallback? onCompleted;
  final bool isVisible;
  final bool isMp4;
  final bool isVideo;
  final bool isPptx;
  final bool isDownload;
  final bool? isCompleted;
  EdgeInsets? margin;

  HanjarListItemDashboardWidget(
      {super.key,
      required this.type,
      required this.title,
      required this.subtitle,
      required this.onDownload,
      required this.onRead,
      this.onCompleted,
      this.isVisible = false,
      this.margin,
      this.isVideo = false,
      this.isMp4 = false,
      this.isDownload = true,
      this.isCompleted,
      this.isPptx = false});

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      margin: margin,
      radius: 14,
      bgColor: secondBlueColor.withOpacity(0.04),
      widget: Stack(
        children: [
          // radial gradient background
          Positioned.fill(
            child: CustomContainer(
              gradient: RadialGradient(
                center: const Alignment(-1.1, 0.0),
                radius: 4.5,
                colors: [
                  const Color(0xFF00A3FF).withOpacity(0.3),
                  const Color(0x070B1D00),
                ],
                stops: [0.0, 1.0],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                CustomContainer(
                    padding: const EdgeInsets.all(12),
                    bgColor: whiteColor.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(10),
                    widget: _icon(
                      type: type,
                    )),
                16.horizontalSpace,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextWigdet.title(title),
                      2.verticalSpace,
                      CustomTextWigdet.subtitle(subtitle),
                    ],
                  ),
                ),
                16.horizontalSpace,
                Visibility(
                  visible: isDownload,
                  child: CustomButtonOutlined(
                    onTap: onDownload,
                    label: "Download",
                    icon: "assets/icons/download_2.svg",
                  ),
                ),
                8.horizontalSpace,
                CustomButtonOutlined(
                  onTap: onRead,
                  label: "Read",
                  icon: "assets/icons/eye.svg",
                ),
                8.horizontalSpace,
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _icon({required String type}) {
    final typeLower = type.toLowerCase();

    if (typeLower == 'hanjar') {
      return SvgPicture.asset(
        "assets/icons/pdf.svg",
        width: 18,
        height: 18,
      );
    } else if (typeLower == 'audio') {
      return SvgPicture.asset(
        "assets/icons/audiotrack.svg",
        width: 18,
        height: 18,
      );
    } else if (typeLower == 'video') {
      return SvgPicture.asset(
        "assets/icons/icon_videos.svg",
        width: 20,
        height: 18,
      );
    } else if (typeLower == 'slide') {
      return SvgPicture.asset(
        "assets/icons/question_answer.svg",
        width: 18,
        height: 18,
      );
    } else {
      return SvgPicture.asset(
        "assets/icons/pdf.svg",
        width: 18,
        height: 18,
      );
    }
  }
}
