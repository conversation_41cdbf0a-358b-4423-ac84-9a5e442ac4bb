import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/utils/file_download.dart';
import 'package:mides_skadik/app/data/utils/log_service.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/hanjar_dashboard_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/audio_viewer_page.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/pdf_viewer_page.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/slide_viewer_page.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/views/video_viewer_page.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/custom_tab.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/hanjar_list_item_dashboard.dart';
import 'package:mides_skadik/widgets/custom_loading_widget.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class HanjarDashboard extends StatefulWidget {
  const HanjarDashboard({super.key});

  @override
  State<HanjarDashboard> createState() => _HanjarDashboardState();
}

class _HanjarDashboardState extends State<HanjarDashboard> {
  final hanjarController = Get.put(HanjarDashboardController());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.verticalSpace,
        const CustomTextWigdet(
          title: "Hanjar",
          fontSize: 28,
          fontWeight: FontWeight.w600,
        ),
        Obx(
          () => CustomTabWidget(
            list: hanjarController.tabItems,
            selected: hanjarController.tabSelected.value,
            onTap: (val) {
              hanjarController.selectTab(val);
            },
          ),
        ),
        16.verticalSpace,
        Obx(() {
          return hanjarController.isLoading.isTrue
              ? CustomContainer(
                  width: Get.width,
                  height: 200,
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                  ),
                  widget: const Stack(
                    children: [Center(child: CustomLoadingWidget())],
                  ),
                )
              : hanjarController.list.isEmpty
                  ? CustomContainer(
                      width: double.infinity,
                      height: 100,
                      radius: 14,
                      alignment: Alignment.center,
                      bgColor: secondBlueColor.withOpacity(0.04),
                      widget: const CustomTextWigdet(title: "No Hanjar Data"),
                    )
                  : ListView.separated(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 16),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        var row = hanjarController.list[index];
                        var fileUrl = row.file;

                        return HanjarListItemDashboardWidget(
                          title: row.title,
                          type: row.type,
                          subtitle: row.subMapel.name,
                          onDownload: () {
                            var fileExtension = row.file.split('.').last;

                            FileDownloadUtil.downloadFile(
                              fileUrl,
                              fileName: "${row.title}.$fileExtension",
                            );
                          },
                          onRead: () {
                            LogService.log.i(row.type);
                            LogService.log.i(fileUrl);
                            if (row.type.toLowerCase() == "video") {
                              Get.dialog(VideoViewerPage(
                                path: fileUrl,
                                title: row.title,
                                subtitle: row.subMapel.name,
                              ));
                            } else if (row.type.toLowerCase() == 'audio') {
                              Get.dialog(AudioViewerPage(
                                path: fileUrl,
                                title: row.title,
                                subtitle: row.subMapel.name,
                              ));
                            } else if (row.type.toLowerCase() == "slide") {
                              Get.dialog(
                                SlideViewerPage(
                                  path: fileUrl,
                                  title: row.title,
                                  subtitle: row.subMapel.name,
                                ),
                              );
                            } else if (row.type.toLowerCase() == "hanjar") {
                              Get.dialog(
                                PDFViwerPage(
                                  path: fileUrl,
                                  title: row.title,
                                  subtitle: row.subMapel.name,
                                ),
                              );
                            } else {
                              LogService.log.i("Type not found");
                            }
                          },
                        );
                      },
                      separatorBuilder: (context, index) {
                        return 8.verticalSpace;
                      },
                      itemCount: hanjarController.list.length,
                    );
        }),
        8.verticalSpace
      ],
    );
  }
}
