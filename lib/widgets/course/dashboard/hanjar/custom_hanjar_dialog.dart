import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/src/extension_navigation.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/hanjar/custom_hanjar_video_player.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomHanjarDialog extends StatefulWidget {
  final String? title;
  final String? subtitle;

  final Widget child;
  final double? width;
  final double? height;

  CustomHanjarDialog({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.child,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  _CustomHanjarDialogState createState() => _CustomHanjarDialogState();
}

class _CustomHanjarDialogState extends State<CustomHanjarDialog>
    with WidgetsBindingObserver {
  @override
  Widget build(BuildContext context) {
    bool isFullscreen = false;

    var defaultWidth = Get.mediaQuery.size.width;
    var defaultHeight = Get.mediaQuery.size.height;

    return CustomContainer(
      width: widget.width ?? defaultWidth,
      height: widget.height ?? defaultHeight,
      bgColor: Colors.transparent,
      widget: CustomContainer(
        margin: const EdgeInsets.all(8),
        widget: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 4,
            sigmaY: 4,
          ),
          child: BackdropFilter(
            filter: ImageFilter.blur(
              sigmaX: 4,
              sigmaY: 4,
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 4,
                sigmaY: 4,
              ),
              child: Stack(
                children: [
                  BackdropFilter(
                    filter: ImageFilter.blur(
                      sigmaX: 4,
                      sigmaY: 4,
                    ),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(
                        sigmaX: 4,
                        sigmaY: 4,
                      ),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: 4,
                          sigmaY: 4,
                        ),
                        child: CustomContainer(
                          margin: isFullscreen
                              ? null
                              : EdgeInsets.fromLTRB(8, 8, 8, 8),
                          radius: isFullscreen ? 0 : 16,
                          clipBehavior: Clip.antiAliasWithSaveLayer,
                          bgColor: const Color(0x1A90ADD9),
                          widget: Column(
                            children: [
                              if (!isFullscreen) ...[
                                16.verticalSpace,
                                Row(
                                  children: [
                                    32.horizontalSpace,
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomTextWigdet.title(
                                              widget.title ?? ""),
                                          8.verticalSpace,
                                          CustomTextWigdet.subtitle(
                                              widget.subtitle ?? ""),
                                        ],
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        Get.back();
                                      },
                                      icon: Icon(
                                        Icons.close,
                                        color: whiteColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                              Expanded(
                                child: widget.child,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
