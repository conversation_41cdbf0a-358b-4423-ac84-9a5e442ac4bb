import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class HanjarListItemSlide extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback onDownload;
  final VoidCallback onRead;

  EdgeInsets? margin;

  HanjarListItemSlide({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onDownload,
    required this.onRead,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      margin: margin,
      radius: 14,
      bgColor: secondBlueColor.withOpacity(0.04),
      widget: Stack(
        children: [
          // radial gradient background
          Positioned.fill(
            child: CustomContainer(
              gradient: RadialGradient(
                center: const Alignment(-1.1, 0.0),
                radius: 4.5,
                colors: [
                  const Color(0xFF00A3FF).withOpacity(0.3),
                  const Color(0x070B1D00),
                ],
                stops: [0.0, 1.0],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                CustomContainer(
                  padding: const EdgeInsets.all(12),
                  bgColor: whiteColor.withOpacity(0.08),
                  borderRadius: BorderRadius.circular(10),
                  widget: SvgPicture.asset(
                    "assets/icons/question_answer.svg", // Replace with your PDF icon path
                    width: 18,
                    height: 18,
                  ),
                ),
                16.horizontalSpace,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomTextWigdet.title(title),
                      2.verticalSpace,
                      CustomTextWigdet.subtitle(subtitle),
                    ],
                  ),
                ),
                16.horizontalSpace,
                CustomButtonOutlined(
                  onTap: onRead,
                  label: "Read",
                  icon: "assets/icons/eye.svg",
                ),
                24.horizontalSpace,
                CustomButtonOutlined(
                  onTap: onDownload,
                  label: "Mark Completed",
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
