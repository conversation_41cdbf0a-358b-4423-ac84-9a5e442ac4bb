import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

enum TimelineType { start, end, center }

class TodayScheduleItemWidget extends StatefulWidget {
  final TimelineType? timelineType;
  final String title;
  final String teacherPicture;
  final String teacherName;
  final String locationName;
  final DateTimeRange time;

  const TodayScheduleItemWidget({
    Key? key,
    this.timelineType = TimelineType.center,
    required this.title,
    required this.teacherPicture,
    required this.teacherName,
    required this.locationName,
    required this.time,
  }) : super(key: key);

  @override
  State<TodayScheduleItemWidget> createState() => _TodayScheduleItemWidgetState();
}

class _TodayScheduleItemWidgetState extends State<TodayScheduleItemWidget> {
  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TimelineNode(
          showStartLine: widget.timelineType == TimelineType.start ? false : true,
          showEndLine: widget.timelineType == TimelineType.end ? false : true,
        ),
        8.horizontalSpace,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 2),
              Row(
                children: [
                  CustomContainer(
                    height: 14,
                    width: 14,
                    clipBehavior: Clip.antiAliasWithSaveLayer,
                    bgColor: Colors.white,
                    shape: BoxShape.circle,
                    widget: widget.teacherPicture.contains("http")
                        ? Image.network(
                            widget.teacherPicture,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Image.asset(
                                "assets/images/logo.png",
                                fit: BoxFit.contain,
                              );
                            },
                          )
                        : Image.asset(
                            widget.teacherPicture,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Image.asset(
                                "assets/images/logo.png",
                                fit: BoxFit.contain,
                              );
                            },
                          ),
                  ),
                  8.verticalSpace,
                  CustomTextWigdet(
                    title: widget.teacherName,
                    fontSize: 14,
                    textColor: whiteColor,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  SvgPicture.asset(
                    "assets/icons/time.svg",
                    width: 11,
                    height: 11,
                    color: secondWhiteColor.withOpacity(0.6),
                  ),
                  4.horizontalSpace,
                  CustomTextWigdet(
                    title: [
                      DateFormat("HH:mm").format(widget.time.start),
                      DateFormat("HH:mm").format(widget.time.end),
                    ].join(" - "),
                    fontSize: 14,
                    textColor: secondWhiteColor.withOpacity(0.6),
                  ),
                  6.horizontalSpace,
                  SvgPicture.asset(
                    "assets/icons/place.svg",
                    width: 11,
                    height: 11,
                    color: secondWhiteColor,
                  ),
                  4.horizontalSpace,
                  Expanded(
                    child: CustomTextWigdet(
                      title: widget.locationName,
                      fontSize: 14,
                      textColor: secondWhiteColor.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class TimelineNode extends StatelessWidget {
  final bool? showStartLine;
  final bool? showEndLine;

  const TimelineNode({
    super.key,
    required this.showStartLine,
    required this.showEndLine,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          height: 20,
          child: (showStartLine == true)
              ? CustomPaint(
                  painter: DashedLinePainter(),
                )
              : null,
        ),
        CustomContainer(
          width: 16,
          height: 16,
          shape: BoxShape.circle,
          bgColor: showStartLine == true ? Colors.transparent : Color(0xFF0A3D61),
          border: Border.all(
            color: showStartLine == true ? Colors.white.withOpacity(0.2) : Colors.transparent,
            width: showStartLine == true ? 0.5 : 0,
          ),
          widget: Center(
            child: CustomContainer(
              width: 10,
              height: 10,
              shape: BoxShape.circle,
              bgColor: showStartLine == true ? const Color(0xFF828282) : Colors.lightBlue,
            ),
          ),
        ),
        6.verticalSpace,
        if (showEndLine == true)
          SizedBox(
            height: 30,
            child: CustomPaint(
              painter: DashedLinePainter(),
            ),
          ),
      ],
    );
  }
}

class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const dashWidth = 4.0;
    const dashSpace = 8.0;
    double startY = 0;

    final paint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 2;

    while (startY < size.height) {
      canvas.drawLine(Offset(size.width / 2, startY), Offset(size.width / 2, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
