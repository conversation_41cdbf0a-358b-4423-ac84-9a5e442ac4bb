import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/today_schedule_controller.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/course/dashboard/today_schedule/today_schedule_item.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class TodayScheduleDashboard extends StatefulWidget {
  const TodayScheduleDashboard({
    Key? key,
    this.onViewAll,
  }) : super(key: key);

  final VoidCallback? onViewAll;

  @override
  State<TodayScheduleDashboard> createState() => _TodayScheduleDashboardState();
}

class _TodayScheduleDashboardState extends State<TodayScheduleDashboard> {
  final todayScheduleController = Get.find<TodayScheduleController>();

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      borderRadius: BorderRadius.circular(8),
      bgColor: const Color(0x0A90ADD9),
      widget: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const CustomTextWigdet(title: "Today Schedule", fontSize: 18),
              CustomButtonOutlined(label: "View All", onTap: widget.onViewAll),
            ],
          ),
          Obx(() {
            return todayScheduleController.list.isEmpty
                ? const CustomContainer(
                    padding: EdgeInsets.fromLTRB(16, 32, 16, 16),
                    widget: CustomTextWigdet(title: "No Schedule Today"),
                  )
                : ListView.separated(
                    padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var row = todayScheduleController.list[index];
                      return TodayScheduleItemWidget(
                        timelineType: index == 0
                            ? TimelineType.start
                            : index == 3
                                ? TimelineType.end
                                : TimelineType.center,
                        title: row.mapel?.name ?? '',
                        teacherPicture: row.lecturer?.imageProfile ?? '',
                        teacherName: row.lecturer?.name ?? '',
                        locationName: row.location ?? '',
                        time: DateTimeRange(
                          start: row.pbmStart!.startTime!,
                          end: row.pbmStart!.endTime!,
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return 2.verticalSpace;
                    },
                    itemCount: todayScheduleController.list.length,
                  );
          }),
        ],
      ),
    );
  }
}
