import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/views/detail_course_view.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class LearningDashboardItem extends StatelessWidget {
  final String teacherPicture;
  final String teacherName;
  final String title;
  final String image;
  const LearningDashboardItem({
    Key? key,
    required this.teacherName,
    required this.teacherPicture,
    required this.title,
    required this.image,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.to(DetailCourseView());
      },
      child: Container(
        clipBehavior: Clip.antiAliasWithSaveLayer,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: const Color(0xFF1a222d).withOpacity(0.6),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 405 / 260,
              child: Container(
                clipBehavior: Clip.antiAliasWithSaveLayer,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.transparent,
                ),
                child: image.contains("http")
                    ? Image.network(
                        image,
                        fit: BoxFit.cover,
                      )
                    : Image.asset(
                        image,
                        fit: BoxFit.cover,
                      ),
              ),
            ),
            Expanded(
              child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 10, 16, 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CustomTextWigdet(
                              title: title,
                              fontSize: 16,
                              textColor: const Color(0xFFDEE6EC),
                            ),
                            const SizedBox(height: 6),
                            Row(
                              children: [
                                Container(
                                  height: 18,
                                  width: 18,
                                  clipBehavior: Clip.antiAliasWithSaveLayer,
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                  child: teacherPicture.contains("http")
                                      ? Image.network(
                                          teacherPicture,
                                          fit: BoxFit.cover,
                                        )
                                      : Image.asset(
                                          teacherPicture,
                                          fit: BoxFit.cover,
                                        ),
                                ),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: CustomTextWigdet(
                                    title: teacherName,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(20),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            padding: const EdgeInsets.fromLTRB(8, 3, 8, 3),
                            child: Row(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white.withAlpha(20),
                                    shape: BoxShape.circle,
                                  ),
                                  padding: const EdgeInsets.all(3),
                                  child: const SizedBox(
                                    width: 6,
                                    height: 6,
                                    child: CircleAvatar(
                                      backgroundColor: Colors.white,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 6),
                                const CustomTextWigdet(
                                  title: "2 Available Assignment",
                                  fontSize: 14,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
