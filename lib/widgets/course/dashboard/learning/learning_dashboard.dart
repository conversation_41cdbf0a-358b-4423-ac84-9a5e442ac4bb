import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/hanjar_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/learning_item_dashboard_controller.dart';
import 'package:mides_skadik/app/modules/course/dashboard_course/controllers/task_dashboard_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_button_outlined.dart';
import 'package:mides_skadik/widgets/course/custom_materi_pendukung.dart';
import 'package:mides_skadik/widgets/course/custom_tab.dart';
import 'package:mides_skadik/widgets/course/dashboard/task/task_list_item.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class LearningDashboard extends StatefulWidget {
  const LearningDashboard({
    super.key,
    this.onViewAll,
  });

  final VoidCallback? onViewAll;

  @override
  State<LearningDashboard> createState() => _LearningDashboardState();
}

class _LearningDashboardState extends State<LearningDashboard> {
  final learningItemController = Get.put(LearningItemDashboardController());
  final classCourseController = Get.find<ClasesCourseController>();
  final taskListController = Get.find<TaskDashboardController>();
  final hanjarController = Get.put(HanjarController());

  int tabIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const CustomTextWigdet(
              title: "Learning Items",
              fontSize: 28,
              fontWeight: FontWeight.w600,
            ),
            CustomButtonOutlined(
              label: "View All",
              onTap: widget.onViewAll,
            ),
          ],
        ),
        Container(
          width: Get.width,
          margin: const EdgeInsets.fromLTRB(0, 20, 0, 16),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: hanjarController.mapel.asMap().entries.map(
                (e) {
                  final index = e.key;
                  final value = e.value;

                  if (index == 0) {
                    Future.delayed(Duration.zero, () {
                      hanjarController.fetchSubMapel(id: value.id.toString());
                    });
                  }

                  return Container(
                    width: Get.width * 0.18,
                    padding: const EdgeInsets.symmetric(horizontal: 0),
                    child: InkWell(
                      onTap: () {
                        tabIndex = index;
                        hanjarController.subMapel.clear();
                        hanjarController.filteredAssignment.clear();
                        hanjarController.fetchSubMapel(id: value.id.toString());

                        setState(() {});
                      },
                      child: Column(
                        children: [
                          Text(
                            value.name ?? "",
                            style: TextStyle(
                                fontSize: 18.sp,
                                fontWeight: FontWeight.w600,
                                color: whiteColor),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10),
                            height: 5,
                            color: tabIndex == index
                                ? blueColor
                                : Colors.transparent,
                          )
                        ],
                      ),
                    ),
                  );
                },
              ).toList(),
            ),
          ),
        ),
        // Obx(
        //   () => CustomTabWidget(
        //     list: learningItemController.tabItems,
        //     selected: learningItemController.tabSelected.value,
        //     onTap: (val) {
        //       learningItemController.selectTab(val);
        //     },
        //   ),
        // ),
        Obx(() {
          // final tabIsMateriPendukung =
          //     learningItemController.tabIsMateriPendukung.isTrue;
          // if (!tabIsMateriPendukung) {
          //   return const SizedBox.shrink();
          // }

          final datas = hanjarController.subMapel.take(1).toList();
          if (datas.isEmpty) {
            return const SizedBox.shrink();
          }
          return Wrap(
              spacing: 20.w,
              children: List.generate(
                datas.length,
                (index) {
                  final e = datas[index];
                  return CustomMateriPendukung(
                    titleQuiz: e.mapel!.matkul!.name,
                    subjectName: e.name,
                    totalQuestion: e.count!.assignments,
                    onPressed: () {
                      // controller.attemptQuiz(
                      //   id: e.id,
                      // );
                    },
                  );
                },
              ));
        }),
        Obx(
          () {
            if (learningItemController.tabIsMateriPokok.isTrue) {
              var list = taskListController.list.take(2).toList();
              return SingleChildScrollView(
                child: Wrap(
                  spacing: 20.w,
                  runSpacing: 20.h,
                  children: List.generate(list.length, (index) {
                    var row = list[index];
                    return TaskListItemWidget(
                      title: row.title ?? '',
                      teacherPicture: row.createdBy?.imageProfile ??
                          'assets/images/logo.png',
                      teacherName: row.createdBy?.name ?? '',
                      timeRemaining: row.endDate!,
                      mapel: row.submapel?.mapel?.name ?? "",
                      onSubmit: () {},
                      isSubmited: true,
                      icon: 'assets/icons/calendar.svg',
                    );
                  }),
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
        Obx(
          () {
            if (learningItemController.tabIsLatihanPraktis.isTrue) {
              var praktisList = taskListController.list.take(2).toList();
              return SingleChildScrollView(
                child: Wrap(
                  spacing: 20.w,
                  runSpacing: 20.h,
                  children: List.generate(praktisList.length, (index) {
                    var row = praktisList[index];
                    return TaskListItemWidget(
                      title: row.title ?? '',
                      teacherPicture: row.createdBy?.imageProfile ??
                          'assets/images/logo.png',
                      teacherName: row.createdBy?.name ?? '',
                      timeRemaining: row.endDate!,
                      mapel: row.submapel?.mapel?.name ?? "",
                      onSubmit: () {},
                      isSubmited: true,
                      icon: 'assets/icons/calendar.svg',
                    );
                  }),
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}
