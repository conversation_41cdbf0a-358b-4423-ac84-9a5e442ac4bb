import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mides_skadik/widgets/course/dashboard/learning/learning_dashboard_item.dart';

class CustomMateriPendukung extends StatelessWidget {
  final String? subStudies;
  final String? subjectName;
  final String? titleQuiz;
  final int? totalQuestion;
  final DateTime? date;
  final String? timeAssigned;
  final double? width;
  final double? height;
  final Function()? onPressed;

  const CustomMateriPendukung({
    super.key,
    this.subStudies,
    this.subjectName,
    this.totalQuestion = 20,
    this.date,
    this.timeAssigned = '1',
    this.titleQuiz,
    this.width,
    this.height,
    this.onPressed,
  });

  String getFormattedDate(DateTime? date) {
    if (date == null) return 'Monday, April 1, 2024 · 23:00';
    return DateFormat('EEEE, d MMMM yyyy – HH:mm', 'id_ID').format(date);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GridView.builder(
          shrinkWrap: true,
          padding: const EdgeInsets.fromLTRB(0, 16, 0, 0),
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 405 / 420,
          ),
          itemBuilder: (context, index) {
            return LearningDashboardItem(
              image: "assets/images/quiz.png",
              title: titleQuiz.toString(),
              teacherName: subjectName.toString(),
              teacherPicture: "assets/images/teacher.jpg",
            );
          },
          itemCount: 2,
        ),
      ],
    );
  }
}
