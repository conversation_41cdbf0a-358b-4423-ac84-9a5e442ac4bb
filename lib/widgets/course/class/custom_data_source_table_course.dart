import 'package:flutter/material.dart';

class CustomDataSourceTableCourse<T> extends DataTableSource {
  final List<T> data;
  final DataRow Function(T item, int index) rowBuilder;

  CustomDataSourceTableCourse({required this.data, required this.rowBuilder});

  @override
  DataRow? getRow(int index) {
    if (index >= data.length) return null;
    return rowBuilder(data[index], index);
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => data.length;

  @override
  int get selectedRowCount => 0;
}
