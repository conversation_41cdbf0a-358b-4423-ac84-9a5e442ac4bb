import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/data/models/response/course/class_course/quiz_progress_model.dart';
import 'package:mides_skadik/app/modules/course/quiz_review_course/controllers/quiz_review_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomCardQuestionsTaskCourse extends StatelessWidget {
  final int? numberQuiz;
  final int? index;
  final String? question;
  final QuizQuestion? answer;
  final UserAnswerQuizz? quizAnswers;

  CustomCardQuestionsTaskCourse({
    super.key,
    this.numberQuiz,
    this.index,
    this.question,
    this.answer,
    this.quizAnswers,
  });

  final QuizReviewCourseController controller =
      Get.put(QuizReviewCourseController()); // Initialize the controller

  final answerMe = ''.obs;
  final answerCorrectMe = false.obs;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: CustomContainer(
        bgColor: secondBlueColor.withOpacity(0.10),
        padding: const EdgeInsets.all(12),
        radius: 10,
        widget: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomContainer(
              padding: const EdgeInsets.all(16),
              bgColor: secondBlueColor.withOpacity(0.10),
              radius: 10,
              widget: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: 'Questions ${numberQuiz ?? 1}',
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    textColor: whiteColor,
                  ),
                  10.verticalSpace,
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        'assets/icons/flag.svg',
                        width: 16.w,
                        height: 16.h,
                      ),
                      6.horizontalSpace,
                      CustomTextWigdet(
                        title: 'Flag Question',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        textColor: whiteColor.withOpacity(0.5),
                      )
                    ],
                  )
                ],
              ),
            ),
            10.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: answer?.question ?? '-',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    textColor: whiteColor,
                  ),
                  10.verticalSpace,
                  CustomTextWigdet(
                    title: 'Choose one :',
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    textColor: whiteColor.withOpacity(0.5),
                  ),
                  10.verticalSpace,
                  if (answer?.quizAnswers != null)
                    ...answer!.quizAnswers!.map((entry) {
                      if (entry.isCorrect == true) {
                        answerMe.value = entry.answer ?? '';
                      }

                      return Padding(
                        padding: const EdgeInsets.only(bottom: 5),
                        child: GestureDetector(
                          onTap: () {
                            // controller.showAnswer(
                            //     numberQuiz: index ?? 0, answerKey: entry.key);
                          },
                          child: CustomContainer(
                            padding: const EdgeInsets.all(10),
                            bgColor: entry.id == quizAnswers?.answer
                                ? secondBlueColor.withOpacity(0.2)
                                : secondBlueColor.withOpacity(0.10),
                            radius: 10,
                            widget: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CustomContainer(
                                  width: 16.w,
                                  height: 16.h,
                                  radius: 100,
                                  padding: const EdgeInsets.all(2),
                                  border: Border.all(
                                    color: whiteColor,
                                    width: 1,
                                  ),
                                  widget: Center(
                                    child: CustomContainer(
                                      width: 8.w,
                                      height: 8.h,
                                      bgColor: entry.id == quizAnswers?.answer
                                          ? whiteColor
                                          : Colors.transparent,
                                      radius: 100,
                                    ),
                                  ),
                                ),
                                10.horizontalSpace,
                                Expanded(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      CustomTextWigdet(
                                        title: String.fromCharCode(65 +
                                            (answer!.quizAnswers!
                                                .indexOf(entry))),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        textColor: whiteColor,
                                      ),
                                      10.horizontalSpace,
                                      Flexible(
                                        child: CustomTextWigdet(
                                          title: entry.answer ?? '-',
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          textColor:
                                              whiteColor.withOpacity(0.5),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                10.horizontalSpace,
                                if (entry.id == quizAnswers?.answer)
                                  if (entry.isCorrect == true)
                                    SvgPicture.asset(
                                      'assets/icons/check_course.svg',
                                      width: 16.w,
                                      height: 16.h,
                                      color: greenColor,
                                    )
                                  else
                                    SvgPicture.asset(
                                      'assets/icons/icon_X.svg',
                                      width: 30.w,
                                      height: 30.h,
                                      color: redColor,
                                    ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  10.verticalSpace,
                  Obx(
                    () => CustomContainer(
                      width: Get.width * 12,
                      padding: const EdgeInsets.all(10),
                      bgColor: secondBlueColor.withOpacity(0.05),
                      radius: 10,
                      widget: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (quizAnswers?.isCorrect == true)
                            Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/check_course.svg',
                                  width: 16.w,
                                  height: 16.h,
                                  color: greenColor,
                                ),
                                CustomTextWigdet(
                                  title: 'Your answer is correct',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  textColor: greenColor,
                                ),
                              ],
                            )
                          else
                            Row(
                              children: [
                                SvgPicture.asset(
                                  'assets/icons/icon_X.svg',
                                  width: 16.w,
                                  height: 16.h,
                                  color: redColor,
                                ),
                                CustomTextWigdet(
                                  title: 'Your answer is wrong',
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  textColor: redColor,
                                ),
                              ],
                            ),
                          10.verticalSpace,
                          CustomTextWigdet(
                            title: 'The correct answer is: ${answerMe.value} ',
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            textColor: whiteColor.withOpacity(0.5),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
