import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mides_skadik/app/data/utils/utils.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomCardTasksCourse extends StatelessWidget {
  final String? subStudies;
  final String? subjectName;
  final String? title;
  final String? type;
  final String? status;
  final int? totalQuestion;
  final DateTime? date;
  final String? timeAssigned;
  final double? width;
  final double? height;
  final bool? hasStatus;
  final bool? hasType;
  final bool? isLoading;
  final Function()? onPressed;

  const CustomCardTasksCourse({
    super.key,
    this.subStudies,
    this.subjectName,
    this.totalQuestion = 20,
    this.date,
    this.timeAssigned = '1',
    this.title,
    this.width,
    this.height,
    this.onPressed,
    this.hasStatus,
    this.hasType,
    this.status,
    this.type,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final st = status?.toLowerCase();
    return CustomContainer(
      width: 421,
      radius: 12,
      bgColor: secondBlueColor.withOpacity(0.10),
      widget: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          hasStatus ?? false
              ? Stack(
                  children: [
                    Image.asset('assets/images/quiz.png'),
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: EdgeInsets.only(right: 7.w, top: 7.h),
                        child: CustomContainer(
                          bgColor: st == 'upcoming' ? goldenYellow : greenColor,
                          height: 30,
                          radius: 4,
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          widget: CustomTextWigdet(
                            title: '• ${st?.toUpperCase()}',
                            fontSize: 14,
                            textColor: whiteColor,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    )
                  ],
                )
              : Image.asset('assets/images/quiz.png'),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 28.h, horizontal: 24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/mage_book.svg',
                      width: 20.w,
                      height: 20.h,
                      color: whiteColor,
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                      title: subStudies ?? '-',
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      textColor: whiteColor,
                    )
                  ],
                ),
                8.verticalSpace,
                CustomTextWigdet(
                  title: title ?? '-',
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                8.verticalSpace,
                hasType ?? false
                    ? Row(
                        children: [
                          CustomContainer(
                            width: 177,
                            height: 29,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            widget: Center(
                              child: CustomTextWigdet(
                                title: subjectName ?? '-',
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                textColor: whiteColor,
                              ),
                            ),
                          ),
                          8.horizontalSpace,
                          CustomContainer(
                            height: 29,
                            bgColor: secondBlueColor.withOpacity(0.10),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.w),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    'assets/icons/list.svg',
                                    height: 16.h,
                                  ),
                                  6.horizontalSpace,
                                  CustomTextWigdet(
                                    title: type ?? '',
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      )
                    : CustomContainer(
                        width: 260,
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 10.h),
                        bgColor: secondBlueColor.withOpacity(0.10),
                        radius: 4,
                        widget: Center(
                          child: CustomTextWigdet(
                            title: (subjectName != null &&
                                    subjectName!.length > 20)
                                ? '${subjectName!.substring(0, 20)}...'
                                : subjectName ?? '-',
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            textColor: whiteColor,
                          ),
                        ),
                      ),
                24.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/total_question.svg',
                      width: 16.w,
                      height: 16.h,
                      color: whiteColor,
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                        title: '$totalQuestion questions total',
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: whiteColor),
                  ],
                ),
                8.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/calendar.svg',
                      width: 16.w,
                      height: 16.h,
                      color: whiteColor,
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                        title: getFormattedDate(date),
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: whiteColor),
                  ],
                ),
                8.verticalSpace,
                Row(
                  children: [
                    SvgPicture.asset(
                      'assets/icons/timer.svg',
                      width: 16.w,
                      height: 16.h,
                      color: whiteColor,
                    ),
                    6.horizontalSpace,
                    CustomTextWigdet(
                        title: 'Assigned $timeAssigned',
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        textColor: whiteColor),
                  ],
                ),
                24.verticalSpace,
                CustomFilledButtonWidget(
                  isLoading: isLoading ?? false,
                  onPressed: onPressed,
                  title: 'Attemp Now',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  fontColor: (date != null && DateTime.now().isAfter(date!))
                      ? whiteColor.withOpacity(0.5)
                      : whiteColor,
                  bgColor: (date != null && DateTime.now().isAfter(date!))
                      ? greyColor
                      : blueColor,
                  widthButton: 373.33,
                  heightButton: 44,
                  radius: 4,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
