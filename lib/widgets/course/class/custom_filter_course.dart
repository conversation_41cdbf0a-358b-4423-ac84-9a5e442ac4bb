import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomFilterCourse extends GetView<ClasesCourseController> {
  final String? title;
  final Function(String)? onPressedTime;
  String? selectedTime;
  final Function(String)? onPressedDeadline;
  String? selectedDeadline;
  final Function? onPressedReset;
  final isVisible;

  CustomFilterCourse(
      {super.key,
      this.title,
      this.onPressedTime,
      this.selectedTime,
      this.onPressedDeadline,
      this.selectedDeadline,
      this.isVisible = true,
      this.onPressedReset});

  @override
  Widget build(BuildContext context) {
    final timeList = ['Latest', 'Last 7 days', 'Last 21 days'];
    final deadline = ['Closest', 'Oldest'];

    return CustomContainer(
      width: 300,
      radius: 12,
      bgColor: Colors.black,
      widget: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 90, sigmaY: 90),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.20),
                  blueColor.withOpacity(0.60),
                ],
                transform: const GradientRotation(0.314159),
                stops: [0.4, 1.0],
              ),
            ),
            padding: EdgeInsets.symmetric(vertical: 14.h, horizontal: 14.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomContainer(
                  width: 300,
                  bgColor: secondBlueColor.withOpacity(0.05),
                  radius: 8,
                  widget: Padding(
                    padding: const EdgeInsets.all(4).h,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            SvgPicture.asset(
                              'assets/icons/timer.svg',
                              width: 20.w,
                              height: 20.h,
                              color: secondWhiteColor.withOpacity(0.50),
                            ),
                            6.horizontalSpace,
                            CustomTextWigdet(
                              title: 'Time',
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              textColor: secondWhiteColor.withOpacity(0.50),
                            ),
                            const Spacer(),
                            SvgPicture.asset(
                              'assets/icons/icon_chevron.svg',
                              width: 20.w,
                              height: 20.h,
                              color: secondWhiteColor.withOpacity(0.50),
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        Padding(
                            padding: const EdgeInsets.all(5).h,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  ...timeList.asMap().entries.map((entry) {
                                    final e = entry.value;
                                    return Padding(
                                      padding: EdgeInsets.only(bottom: 8.h),
                                      child: GestureDetector(
                                        onTap: () {
                                          onPressedTime?.call(e);

                                          selectedTime = e;
                                        },
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: CustomTextWigdet(
                                                title: e,
                                                fontSize: 16,
                                                fontWeight: FontWeight.w400,
                                                textColor: whiteColor,
                                              ),
                                            ),
                                            const Spacer(),
                                            CustomContainer(
                                              width: 20,
                                              height: 20,
                                              bgColor: e == selectedTime
                                                  ? blueColor
                                                  : Colors.transparent,
                                              radius: 4,
                                              border: Border.all(
                                                  color: e == selectedTime
                                                      ? blueColor
                                                      : secondWhiteColor),
                                              widget: e == selectedTime
                                                  ? Icon(Icons.check,
                                                      size: 8,
                                                      color: whiteColor)
                                                  : null,
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ]))
                      ],
                    ),
                  ),
                ),
                8.verticalSpace,
                Visibility(
                  visible: isVisible,
                  child: CustomContainer(
                    width: 300,
                    bgColor: secondBlueColor.withOpacity(0.05),
                    radius: 8,
                    widget: Padding(
                      padding: const EdgeInsets.all(4).h,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/icons/calendar.svg',
                                width: 20.w,
                                height: 20.h,
                                color: secondWhiteColor,
                              ),
                              6.horizontalSpace,
                              CustomTextWigdet(
                                title: 'Deadline',
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                textColor: whiteColor,
                              ),
                              const Spacer(),
                              SvgPicture.asset(
                                'assets/icons/icon_chevron.svg',
                                width: 20.w,
                                height: 20.h,
                                color: secondWhiteColor,
                              ),
                            ],
                          ),
                          10.verticalSpace,
                          Padding(
                              padding: const EdgeInsets.all(5).h,
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    ...deadline.asMap().entries.map((entry) {
                                      final e = entry.value;
                                      return Padding(
                                        padding: EdgeInsets.only(bottom: 8.h),
                                        child: GestureDetector(
                                          onTap: () {
                                            onPressedDeadline?.call(e);
                                            selectedDeadline = e;
                                          },
                                          child: Row(
                                            children: [
                                              Expanded(
                                                child: CustomTextWigdet(
                                                  title: e,
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w400,
                                                  textColor: whiteColor,
                                                ),
                                              ),
                                              const Spacer(),
                                              CustomContainer(
                                                width: 20,
                                                height: 20,
                                                bgColor: e == selectedDeadline
                                                    ? blueColor
                                                    : Colors.transparent,
                                                radius: 4,
                                                border: Border.all(
                                                    color: e == selectedDeadline
                                                        ? blueColor
                                                        : secondWhiteColor),
                                                widget: e == selectedDeadline
                                                    ? Icon(Icons.check,
                                                        size: 8,
                                                        color: whiteColor)
                                                    : null,
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    }).toList(),
                                  ]))
                        ],
                      ),
                    ),
                  ),
                ),
                8.verticalSpace,
                CustomFilledButtonWidget(
                  onPressed:
                      onPressedReset != null ? () => onPressedReset!() : null,
                  title: 'Reset',
                  fontColor: secondWhiteColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  bgColor: blueColor,
                  widthButton: 300,
                  heightButton: 48,
                  radius: 8,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
