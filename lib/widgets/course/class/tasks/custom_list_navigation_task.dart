import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../themes/colors.dart';
import '../../../components/custom_container.dart';
import '../../../custom_text_wigdet.dart';

class CustomListNavigationTask extends StatelessWidget {
  final String title;
  final String subTitle;
  final bool? isSelected;
  final Widget listWidget;
  final int total;

  const CustomListNavigationTask(
      {super.key,
      required this.title,
      required this.subTitle,
      this.isSelected,
      required this.total,
      required this.listWidget});

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      bgColor: secondBlueColor.withOpacity(0.1),
      width: 350,
      radius: 10,
      widget: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomTextWigdet(
                  title: title,
                  fontSize: 18,
                ),
                const Spacer(),
                SvgPicture.asset(
                  'assets/icons/list.svg',
                  height: 20.h,
                )
              ],
            ),
            4.verticalSpace,
            CustomTextWigdet(
              title: '$total $subTitle',
              fontSize: 16,
              textColor: baseBlueColor,
            ),
            20.verticalSpace,
            SizedBox(child: listWidget)
          ],
        ),
      ),
    );
  }
}
