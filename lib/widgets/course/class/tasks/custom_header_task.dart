import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../themes/colors.dart';
import '../../../custom_text_wigdet.dart';

class CustomHeaderTask extends StatelessWidget {
  final String titleHeader;
  final String userName;
  final String? avatarUrl;
  final String? imageUrl;
  const CustomHeaderTask(
      {super.key,
      this.imageUrl,
      required this.titleHeader,
      required this.userName,
      this.avatarUrl});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 290.h,
      width: double.infinity,
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(10.r),
            child: Stack(
              children: [
                SizedBox(
                  height: 290.h,
                  width: double.infinity,
                  child: (imageUrl == null || imageUrl!.isEmpty)
                      ? Image.asset(
                          'assets/images/image 115.png',
                          fit: BoxFit.cover,
                        )
                      : CachedNetworkImage(
                          imageUrl: imageUrl ?? '',
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              const CircularProgressIndicator(),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error),
                        ),
                ),
                Container(
                  height: 290.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [
                          baseBlueColor,
                          baseBlueColor.withOpacity(0.25)
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        stops: const [
                          0.16,
                          1,
                        ]),
                  ),
                ),
              ],
            ),
          ),
          Align(
            alignment: Alignment.bottomLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 32.w, bottom: 32.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomTextWigdet(
                    title: titleHeader,
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                  15.verticalSpace,
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 10,
                      ),
                      4.horizontalSpace,
                      CustomTextWigdet(
                        title: userName,
                        fontSize: 16,
                      )
                    ],
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
