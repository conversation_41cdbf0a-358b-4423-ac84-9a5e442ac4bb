import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../themes/colors.dart';
import '../../../custom_text_wigdet.dart';

class CustomInformationTask extends StatelessWidget {
  final String assetName;
  final String name;
  final String value;
  const CustomInformationTask(
      {super.key,
      required this.assetName,
      required this.name,
      required this.value});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          assetName,
          height: 24.h,
          color: whiteColor.withOpacity(0.4),
        ),
        10.horizontalSpace,
        Expanded(
          flex: 2,
          child: CustomTextWigdet(
            title: name,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            textColor: whiteColor.withOpacity(0.4),
          ),
        ),
        Expanded(
          flex: 3,
          child: CustomTextWigdet(
            title: value,
            fontSize: 18,
            fontWeight: FontWeight.w500,
            textColor: whiteColor,
          ),
        ),
      ],
    );
  }
}
