import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../themes/colors.dart';
import '../../../components/custom_container.dart';
import '../../../custom_text_wigdet.dart';

class CustomContentTask extends StatelessWidget {
  final String contentTitle;
  final String statusProgress;
  final List<Widget> widgets;
  const CustomContentTask(
      {super.key,
      required this.contentTitle,
      required this.statusProgress,
      required this.widgets});

  @override
  Widget build(BuildContext context) {
    final status = statusProgress.toLowerCase();
    return Column(
      children: [
        CustomContainer(
          width: double.infinity,
          radius: 10,
          widget: Padding(
            padding: EdgeInsets.all(24.r),
            child: Column(
              children: [
                Row(
                  children: [
                    CustomContainer(
                      width: 60,
                      height: 60,
                      radius: 10,
                      bgColor: baseBlueColor.withOpacity(0.5),
                      widget: Center(
                        child: SvgPicture.asset(
                          'assets/icons/question_answer.svg',
                          height: 32.h,
                        ),
                      ),
                    ),
                    20.horizontalSpace,
                    SizedBox(
                      width: Get.width / 3,
                      child: CustomTextWigdet(
                        title: contentTitle,
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    CustomContainer(
                      bgColor: status == 'not started'
                          ? greyColor
                          : status == 'in-progress' || status == 'upcoming'
                              ? goldenYellow.withOpacity(0.25)
                              : greenColor,
                      height: 40,
                      radius: 4,
                      alignment: Alignment.center,
                      widget: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 4.h),
                        child: Row(
                          children: [
                            Container(
                              width: 5.w,
                              height: 5.h,
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle, color: whiteColor),
                            ),
                            4.horizontalSpace,
                            CustomTextWigdet(
                              title: statusProgress,
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                            )
                          ],
                        ),
                      ),
                    )
                  ],
                ),
                32.verticalSpace,
                ...widgets,
              ],
            ),
          ),
        ),
      ],
    );
  }
}
