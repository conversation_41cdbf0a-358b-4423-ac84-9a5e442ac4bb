import 'package:flutter/material.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomAssigmentCourse extends StatelessWidget {
  final String? titleAssignment;

  final Function()? onPressed;

  const CustomAssigmentCourse({
    super.key,
    this.titleAssignment,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: 421,
      radius: 12,
      bgColor: redColor,
      widget: CustomTextWigdet(
        title: 'Contoh Assignment',
        fontSize: 18,
        fontWeight: FontWeight.w700,
        textColor: whiteColor,
      ),
    );
  }
}
