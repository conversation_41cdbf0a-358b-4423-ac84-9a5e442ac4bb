import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';

class CustomEssayCourse extends StatelessWidget {
  final QuillController quillController;
  const CustomEssayCourse({super.key, required this.quillController});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 24.w, left: 24.w, bottom: 24.h),
      child: CustomContainer(
        width: double.infinity,
        height: 250,
        bgColor: baseBlueColor.withOpacity(0.15),
        radius: 8,
        widget: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            QuillSimpleToolbar(
              controller: quillController,
              configurations: QuillSimpleToolbarConfigurations(
                toolbarIconAlignment: WrapAlignment.start,
                showJustifyAlignment: true,
                headerStyleType: HeaderStyleType.buttons,
                showClearFormat: false,
                showFontFamily: false,
                showFontSize: false,
                showSubscript: false,
                showSuperscript: false,
                showColorButton: false,
                showBackgroundColorButton: false,
                showRedo: false,
                showUndo: false,
                showStrikeThrough: false,
                showUnderLineButton: false,
                showSearchButton: false,
                showClipboardCut: false,
                showClipboardPaste: false,
                showClipboardCopy: false,
                showQuote: false,
                showInlineCode: false,
                showCodeBlock: false,
                showListCheck: false,
                showIndent: false,
                buttonOptions: QuillSimpleToolbarButtonOptions(
                  bold: QuillToolbarToggleStyleButtonOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  italic: QuillToolbarToggleStyleButtonOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  selectHeaderStyleButtons:
                      QuillToolbarSelectHeaderStyleButtonsOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        color: whiteColor,
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  listBullets: QuillToolbarToggleStyleButtonOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  listNumbers: QuillToolbarToggleStyleButtonOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  linkStyle: QuillToolbarLinkStyleButtonOptions(
                    iconTheme: QuillIconTheme(
                      iconButtonUnselectedData: IconButtonData(
                        color: whiteColor.withOpacity(0.5),
                      ),
                      iconButtonSelectedData: IconButtonData(
                        style: ButtonStyle(
                          backgroundColor: WidgetStatePropertyAll(
                            baseBlueColor.withOpacity(0.8),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                color: darkBlueColor.withOpacity(0.5),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                  child: QuillEditor.basic(
                    controller: quillController,
                    configurations: QuillEditorConfigurations(
                      customStyles: DefaultStyles(
                        h1: DefaultTextBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 32,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                        ),
                        h2: DefaultTextBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 24,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                        ),
                        h3: DefaultTextBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 16,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                        ),
                        paragraph: DefaultTextBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 14,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                        ),
                        lists: DefaultListBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 16,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                          null,
                        ),
                        leading: DefaultTextBlockStyle(
                          TextStyle(
                            fontFamily: "Inter",
                            color: whiteColor,
                            fontSize: 16,
                          ),
                          const HorizontalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          const VerticalSpacing(0, 0),
                          null,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
