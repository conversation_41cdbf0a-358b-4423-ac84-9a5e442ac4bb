import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mides_skadik/themes/colors.dart';

class CustomTableCourse extends StatelessWidget {
  final double? headingRowHeight;
  final List<DataColumn2> dataColumnTable;
  final List<DataRow>? dataRowTable;
  const CustomTableCourse(
      {super.key,
      required this.dataColumnTable,
      this.dataRowTable,
      this.headingRowHeight});

  @override
  Widget build(BuildContext context) {
    return DataTable2(
      headingRowHeight: headingRowHeight ?? 55.h,
      headingRowColor: WidgetStateColor.resolveWith(
          (states) => secondBlueColor.withOpacity(0.15)),
      columns: dataColumnTable.toList(),
      rows: dataRowTable ?? [],
      dividerThickness: 0,
    );
  }
}
