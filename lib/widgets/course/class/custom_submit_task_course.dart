import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/clases_course_controller.dart';
import 'package:mides_skadik/app/modules/course/clases_course/controllers/quiz_question_controller.dart';
import 'package:mides_skadik/themes/colors.dart';
import 'package:mides_skadik/widgets/components/buttons/custom_filled_button_widget.dart';
import 'package:mides_skadik/widgets/components/custom_container.dart';
import 'package:mides_skadik/widgets/custom_text_wigdet.dart';

class CustomSubmitTaskCourse extends StatelessWidget {
  final String? titleQuiz;
  final bool? isLoading;
  final String? message;
  final void Function()? onSubmit;
  final void Function()? onClose;
  final void Function()? onCancel;
  final String? type;

  CustomSubmitTaskCourse({
    super.key,
    this.titleQuiz,
    this.onSubmit,
    this.onClose,
    this.onCancel,
    this.isLoading,
    this.message,
    required this.type,
  });

  final QuizQuestionController quizQuestionController =
      Get.put(QuizQuestionController());
  final ClasesCourseController clasesCourseController =
      Get.put(ClasesCourseController());

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: 818,
      bgColor: darkBlueColor.withOpacity(0.90),
      widget: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 70, sigmaY: 70),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: CustomFilledButtonWidget(
                    onPressed: onClose,
                    withIcon: true,
                    onlyIcon: true,
                    assetName: 'assets/icons/close_course.svg',
                    widthButton: 24,
                    heightIcon: 24,
                    bgColor: Colors.transparent,
                  ),
                ),
                CustomTextWigdet(
                  title:
                      '${type == 'quiz' ? clasesCourseController.quizById.value.quiz?.title : titleQuiz}',
                  fontSize: 32,
                  fontWeight: FontWeight.w500,
                  textColor: whiteColor,
                ),
                44.verticalSpace,
                CustomContainer(
                  width: 770,
                  bgColor: baseBlueColor.withOpacity(0.10),
                  radius: 8,
                  widget: Padding(
                    padding: EdgeInsets.all(24.r),
                    child: Obx(
                      () => Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomTextWigdet(
                            title: 'Review your answers',
                            fontSize: 20,
                            fontWeight: FontWeight.w400,
                            textColor: whiteColor,
                          ),
                          32.verticalSpace,
                          Wrap(spacing: 4.w, runSpacing: 10.h, children: [
                            ...quizQuestionController.listQuizReview.map((e) {
                              return CustomContainer(
                                width: 56,
                                height: 60,
                                radius: 6,
                                bgColor:
                                    e.answer.toString().toLowerCase() == 'null'
                                        ? Colors.transparent
                                        : secondBlueColor.withOpacity(0.2),
                                widget: Center(
                                  child: CustomTextWigdet(
                                    title:
                                        '${quizQuestionController.listQuizReview.indexOf(e) + 1}',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: whiteColor,
                                  ),
                                ),
                              );
                            }),
                          ]),
                          32.verticalSpace,
                          CustomContainer(
                            width: 722,
                            height: 34,
                            radius: 4,
                            bgColor: goldenYellow.withOpacity(0.12),
                            widget: Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: 8.h,
                                horizontal: 12.w,
                              ),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    'assets/icons/warning_triangle.svg',
                                  ),
                                  5.horizontalSpace,
                                  CustomTextWigdet(
                                    title: message ?? '',
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    textColor: goldenYellow,
                                  ),
                                  const Spacer(),
                                  CustomFilledButtonWidget(
                                    onPressed: () {},
                                    withIcon: true,
                                    onlyIcon: true,
                                    assetName: 'assets/icons/close_course.svg',
                                    widthButton: 14,
                                    heightIcon: 14,
                                    bgColor: Colors.transparent,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          44.verticalSpace,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomFilledButtonWidget(
                                onPressed: onCancel,
                                title: 'Cancel',
                                fontColor: whiteColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                isOutlined: true,
                                borderColor: secondWhiteColor,
                                outlineWidth: 0.5,
                                widthButton: 350,
                                heightButton: 44,
                                radius: 4,
                              ),
                              CustomFilledButtonWidget(
                                onPressed: onSubmit,
                                isLoading: isLoading,
                                title: 'Submit',
                                fontColor: whiteColor,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                widthButton: 350,
                                heightButton: 44,
                                radius: 4,
                                bgColor: blueColor,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
