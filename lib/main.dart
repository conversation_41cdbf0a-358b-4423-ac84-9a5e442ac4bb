import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_quill/translations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:mides_skadik/app/data/services/audio_player_service.dart';
import 'package:mides_skadik/app/data/utils/orientation_controller.dart';
import 'package:mides_skadik/app/modules/login/controllers/login_controller.dart';

import 'app/routes/app_pages.dart';

// SSL bypass for development environment
class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // Allow all certificates in development
        // In production, you should implement proper certificate validation
        return true;
      };
  }
}

void main() async {
  // Ensure that the binding is initialized before running the app
  WidgetsFlutterBinding.ensureInitialized();

  await dotenv.load(mergeWith: Platform.environment);

  // SSL bypass for development environment
  HttpOverrides.global = MyHttpOverrides();

  // Automatically detect and set the preferred orientations based on the device's current orientation
  var currentOrientation =
      WidgetsBinding.instance.window.physicalSize.aspectRatio > 1
          ? DeviceOrientation.landscapeLeft
          : DeviceOrientation.portraitUp;
  await SystemChrome.setPreferredOrientations([currentOrientation]);

  // Initialize date formatting for Indonesian locale
  await initializeDateFormatting('id_ID', null);

  // Initialize any other services or plugins here if needed
  Get.put(OrientationController());
  Get.put(AudioPlayerService(), permanent: true);
  Get.put(LoginController());

  runApp(
    OrientationBuilder(builder: (context, orientation) {
      return ScreenUtilInit(
        designSize: context.isLandscape
            ? const Size(2200, 1440)
            : const Size(1440, 2200),
        minTextAdapt: true,
        splitScreenMode: true,
        child: GetMaterialApp(
          debugShowCheckedModeBanner: false,
          title: "Application",
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
          localizationsDelegates: const [
            DefaultMaterialLocalizations.delegate,
            DefaultWidgetsLocalizations.delegate,
            FlutterQuillLocalizations.delegate,
          ],
        ),
      );
    }),
  );
}
