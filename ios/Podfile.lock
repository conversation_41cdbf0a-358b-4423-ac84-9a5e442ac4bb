PODS:
  - better_player_plus (1.0.0):
    - C<PERSON> (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - Flutter (1.0.0)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - better_player_plus (from `.symlinks/plugins/better_player_plus/ios`)
  - Flutter (from `Flutter`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
    - PINOperation

EXTERNAL SOURCES:
  better_player_plus:
    :path: ".symlinks/plugins/better_player_plus/ios"
  Flutter:
    :path: Flutter
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  better_player_plus: 10794c0ed1b3b4ae058939e22a6172f850a2039b
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
