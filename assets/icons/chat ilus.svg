<svg width="340" height="335" viewBox="0 0 340 335" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_5961_172282)">
<rect y="0.5" width="340" height="334" rx="167" fill="url(#paint0_linear_5961_172282)" fill-opacity="0.15"/>
<g filter="url(#filter1_d_5961_172282)">
<path d="M80 78.5C80 67.4543 88.9543 58.5 100 58.5H240C251.046 58.5 260 67.4543 260 78.5V313.5C260 324.546 251.046 333.5 240 333.5H100C88.9543 333.5 80 324.546 80 313.5V78.5Z" fill="black" fill-opacity="0.1" shape-rendering="crispEdges"/>
<path d="M100 58.7998H240C250.88 58.7998 259.7 67.62 259.7 78.5V313.5C259.7 324.38 250.88 333.2 240 333.2H100C89.12 333.2 80.2998 324.38 80.2998 313.5V78.5C80.2998 67.62 89.12 58.7998 100 58.7998Z" stroke="white" stroke-opacity="0.1" stroke-width="0.6" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_d_5961_172282)">
<path d="M71 92.5C71 81.4543 79.9543 72.5 91 72.5H250C261.046 72.5 270 81.4543 270 92.5V327.5C270 338.546 261.046 347.5 250 347.5H91C79.9543 347.5 71 338.546 71 327.5V92.5Z" fill="black" fill-opacity="0.1" shape-rendering="crispEdges"/>
<path d="M91 72.7998H250C260.88 72.7998 269.7 81.62 269.7 92.5V327.5C269.7 338.38 260.88 347.2 250 347.2H91C80.12 347.2 71.2998 338.38 71.2998 327.5V92.5C71.2998 81.62 80.12 72.7998 91 72.7998Z" stroke="white" stroke-opacity="0.1" stroke-width="0.6" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter3_d_5961_172282)">
<rect x="60" y="86.5" width="220" height="275" rx="20" fill="black" fill-opacity="0.1" shape-rendering="crispEdges"/>
<rect x="60.3" y="86.8" width="219.4" height="274.4" rx="19.7" stroke="white" stroke-opacity="0.1" stroke-width="0.6" shape-rendering="crispEdges"/>
<g filter="url(#filter4_d_5961_172282)">
<rect x="84" y="116.5" width="118" height="48" rx="12" fill="#354E5E" shape-rendering="crispEdges"/>
<rect x="96" y="128.5" width="94" height="8" rx="4" fill="url(#paint1_linear_5961_172282)" fill-opacity="0.5"/>
<rect x="96" y="144.5" width="75" height="8" rx="4" fill="url(#paint2_linear_5961_172282)" fill-opacity="0.2"/>
<path d="M195.539 163.43L199.823 149.719C199.823 149.719 201.852 154.305 203.585 156.993C205.566 160.067 209.485 163.194 209.485 163.194C209.485 163.194 205.283 163.053 202.512 162.893C199.785 162.736 195.539 163.43 195.539 163.43Z" fill="#354E5E"/>
</g>
<rect x="214" y="119.5" width="42" height="42" rx="21" fill="white" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M231.625 137.125C231.625 136.23 231.981 135.371 232.614 134.739C233.246 134.106 234.105 133.75 235 133.75C235.895 133.75 236.754 134.106 237.386 134.739C238.019 135.371 238.375 136.23 238.375 137.125C238.375 138.02 238.019 138.879 237.386 139.511C236.754 140.144 235.895 140.5 235 140.5C234.105 140.5 233.246 140.144 232.614 139.511C231.981 138.879 231.625 138.02 231.625 137.125ZM229.375 147.25V146.688C229.375 145.345 229.908 144.057 230.858 143.108C231.807 142.158 233.095 141.625 234.438 141.625H235.562C236.905 141.625 238.193 142.158 239.142 143.108C240.092 144.057 240.625 145.345 240.625 146.688V147.25H229.375Z" fill="white" fill-opacity="0.7"/>
<rect x="84" y="178.5" width="42" height="42" rx="21" fill="#135095" fill-opacity="0.3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M101.625 196.125C101.625 195.23 101.981 194.371 102.614 193.739C103.246 193.106 104.105 192.75 105 192.75C105.895 192.75 106.754 193.106 107.386 193.739C108.019 194.371 108.375 195.23 108.375 196.125C108.375 197.02 108.019 197.879 107.386 198.511C106.754 199.144 105.895 199.5 105 199.5C104.105 199.5 103.246 199.144 102.614 198.511C101.981 197.879 101.625 197.02 101.625 196.125ZM99.375 206.25V205.688C99.375 204.345 99.9084 203.057 100.858 202.108C101.807 201.158 103.095 200.625 104.438 200.625H105.562C106.905 200.625 108.193 201.158 109.142 202.108C110.092 203.057 110.625 204.345 110.625 205.688V206.25H99.375Z" fill="white" fill-opacity="0.7"/>
<g filter="url(#filter5_d_5961_172282)">
<rect x="138" y="178.5" width="118" height="48" rx="12" fill="#135095" shape-rendering="crispEdges"/>
<rect x="150" y="190.5" width="94" height="8" rx="4" fill="url(#paint3_linear_5961_172282)" fill-opacity="0.5"/>
<rect x="150" y="206.5" width="86" height="8" rx="4" fill="url(#paint4_linear_5961_172282)" fill-opacity="0.2"/>
<path d="M143 225.43L138.716 211.719C138.716 211.719 136.687 216.305 134.954 218.993C132.973 222.067 129.054 225.194 129.054 225.194C129.054 225.194 133.256 225.053 136.027 224.893C138.754 224.736 143 225.43 143 225.43Z" fill="#135095"/>
</g>
<g filter="url(#filter6_d_5961_172282)">
<rect x="84" y="245.5" width="118" height="32" rx="12" fill="#354E5E" shape-rendering="crispEdges"/>
<rect x="96" y="257.5" width="94" height="8" rx="4" fill="url(#paint5_linear_5961_172282)" fill-opacity="0.5"/>
<path d="M195.539 276.43L199.823 262.719C199.823 262.719 201.852 267.305 203.585 269.993C205.566 273.067 209.485 276.194 209.485 276.194C209.485 276.194 205.283 276.053 202.512 275.893C199.785 275.736 195.539 276.43 195.539 276.43Z" fill="#354E5E"/>
</g>
<rect x="214" y="240.5" width="42" height="42" rx="21" fill="white" fill-opacity="0.05"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M231.625 258.125C231.625 257.23 231.981 256.371 232.614 255.739C233.246 255.106 234.105 254.75 235 254.75C235.895 254.75 236.754 255.106 237.386 255.739C238.019 256.371 238.375 257.23 238.375 258.125C238.375 259.02 238.019 259.879 237.386 260.511C236.754 261.144 235.895 261.5 235 261.5C234.105 261.5 233.246 261.144 232.614 260.511C231.981 259.879 231.625 259.02 231.625 258.125ZM229.375 268.25V267.688C229.375 266.345 229.908 265.057 230.858 264.108C231.807 263.158 233.095 262.625 234.438 262.625H235.562C236.905 262.625 238.193 263.158 239.142 264.108C240.092 265.057 240.625 266.345 240.625 267.688V268.25H229.375Z" fill="white" fill-opacity="0.7"/>
</g>
</g>
<rect x="0.3" y="0.8" width="339.4" height="333.4" rx="166.7" stroke="white" stroke-opacity="0.08" stroke-width="0.6"/>
<defs>
<filter id="filter1_d_5961_172282" x="-10" y="-31.5" width="360" height="455" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<filter id="filter2_d_5961_172282" x="-19" y="-17.5" width="379" height="455" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<filter id="filter3_d_5961_172282" x="-30" y="-3.5" width="400" height="455" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="20"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<filter id="filter4_d_5961_172282" x="54" y="90.5" width="185.484" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<filter id="filter5_d_5961_172282" x="99.0547" y="152.5" width="186.945" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<filter id="filter6_d_5961_172282" x="54" y="219.5" width="185.484" height="92" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5961_172282"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5961_172282" result="shape"/>
</filter>
<linearGradient id="paint0_linear_5961_172282" x1="170" y1="0.5" x2="170" y2="334.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.859889" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_5961_172282" x1="190" y1="132.5" x2="96" y2="132.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_5961_172282" x1="171" y1="148.5" x2="96" y2="148.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_5961_172282" x1="244" y1="194.5" x2="150" y2="194.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint4_linear_5961_172282" x1="236" y1="210.5" x2="150" y2="210.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_5961_172282" x1="190" y1="261.5" x2="96" y2="261.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<clipPath id="clip0_5961_172282">
<rect y="0.5" width="340" height="334" rx="167" fill="white"/>
</clipPath>
</defs>
</svg>
