<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_7082_105106)">
<rect width="80" height="80" rx="40" fill="white" fill-opacity="0.05"/>
<g opacity="0.8" filter="url(#filter0_d_7082_105106)">
<path d="M14 4.64102C14 1.52557 16.5256 -1 19.641 -1H61.359C64.4744 -1 67 1.52557 67 4.64103V51.359C67 54.4744 64.4744 57 61.359 57H19.641C16.5256 57 14 54.4744 14 51.359V4.64102Z" fill="url(#paint0_linear_7082_105106)" fill-opacity="0.3" shape-rendering="crispEdges"/>
<path d="M19.6406 -0.849609H61.3594C64.3917 -0.849393 66.8494 1.60833 66.8496 4.64062V51.3594C66.8494 54.3917 64.3917 56.8494 61.3594 56.8496H19.6406C16.6083 56.8494 14.1506 54.3917 14.1504 51.3594V4.64062C14.1506 1.60833 16.6083 -0.849393 19.6406 -0.849609Z" stroke="url(#paint1_linear_7082_105106)" stroke-opacity="0.1" stroke-width="0.3" shape-rendering="crispEdges"/>
<g filter="url(#filter1_d_7082_105106)">
<rect x="33.5" y="7.42188" width="14" height="14" rx="7" fill="white" fill-opacity="0.25" shape-rendering="crispEdges"/>
<rect x="33.5875" y="7.50938" width="13.825" height="13.825" rx="6.9125" stroke="url(#paint2_linear_7082_105106)" stroke-opacity="0.2" stroke-width="0.175" shape-rendering="crispEdges"/>
<g clip-path="url(#clip1_7082_105106)">
<path d="M43.125 12.3256L42.5963 11.7969L40.5 13.8931L38.4037 11.7969L37.875 12.3256L39.9713 14.4219L37.875 16.5181L38.4037 17.0469L40.5 14.9506L42.5963 17.0469L43.125 16.5181L41.0287 14.4219L43.125 12.3256Z" fill="white"/>
</g>
</g>
<g opacity="0.7">
<rect x="21" y="27.4219" width="39" height="4.23077" rx="2.11538" fill="url(#paint3_linear_7082_105106)" fill-opacity="0.8"/>
<rect x="23.5781" y="35.8828" width="33.8462" height="4.23077" rx="2.11538" fill="url(#paint4_linear_7082_105106)" fill-opacity="0.8"/>
<rect x="27.8086" y="44.3438" width="25.3846" height="4.23077" rx="2.11538" fill="url(#paint5_linear_7082_105106)" fill-opacity="0.8"/>
</g>
</g>
<rect x="22" y="38" width="36" height="36" rx="18" fill="white" fill-opacity="0.12"/>
<g filter="url(#filter3_d_7082_105106)">
<rect x="28" y="44" width="24" height="24" rx="12" fill="white" fill-opacity="0.25" shape-rendering="crispEdges"/>
<rect x="28.15" y="44.15" width="23.7" height="23.7" rx="11.85" stroke="url(#paint6_linear_7082_105106)" stroke-opacity="0.2" stroke-width="0.3" shape-rendering="crispEdges"/>
<g clip-path="url(#clip2_7082_105106)">
<path d="M43.3329 57.3333H42.8062L42.6195 57.1533C43.2729 56.3933 43.6662 55.4067 43.6662 54.3333C43.6662 51.94 41.7262 50 39.3329 50C37.0529 50 35.1862 51.76 35.0195 54H36.3662C36.5329 52.5 37.7862 51.3333 39.3329 51.3333C40.9929 51.3333 42.3329 52.6733 42.3329 54.3333C42.3329 55.9933 40.9929 57.3333 39.3329 57.3333C39.2195 57.3333 39.1129 57.3133 38.9995 57.3V58.6467C39.1129 58.66 39.2195 58.6667 39.3329 58.6667C40.4062 58.6667 41.3929 58.2733 42.1529 57.62L42.3329 57.8067V58.3333L45.6662 61.66L46.6595 60.6667L43.3329 57.3333Z" fill="white"/>
<path d="M37.3135 55.2109L35.6669 56.8576L34.0202 55.2109L33.5469 55.6843L35.1935 57.3309L33.5469 58.9776L34.0202 59.4509L35.6669 57.8043L37.3135 59.4509L37.7869 58.9776L36.1402 57.3309L37.7869 55.6843L37.3135 55.2109Z" fill="white"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_7082_105106" x="-6" y="-9" width="93" height="98" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7082_105106"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7082_105106" result="shape"/>
</filter>
<filter id="filter1_d_7082_105106" x="21.8333" y="-4.24479" width="37.3333" height="39.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="7"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7082_105106"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7082_105106" result="shape"/>
</filter>
<filter id="filter3_d_7082_105106" x="8" y="24" width="64" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7082_105106"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7082_105106" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7082_105106" x1="45" y1="-12.2987" x2="32.0746" y2="46.1999" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.405071" stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_7082_105106" x1="40.5" y1="-1" x2="40.5" y2="57" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_7082_105106" x1="40.5" y1="7.42187" x2="40.5" y2="21.4219" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_7082_105106" x1="55.5" y1="13.8203" x2="64" y2="46.3203" gradientUnits="userSpaceOnUse">
<stop offset="0.338958" stop-color="white"/>
<stop offset="0.504662" stop-color="white" stop-opacity="0.3"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint4_linear_7082_105106" x1="43.5781" y1="19.8203" x2="55.0394" y2="49.6035" gradientUnits="userSpaceOnUse">
<stop offset="0.338958" stop-color="white"/>
<stop offset="0.614292" stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint5_linear_7082_105106" x1="33.3086" y1="28.3203" x2="42.4575" y2="57.0432" gradientUnits="userSpaceOnUse">
<stop offset="0.338958" stop-color="white"/>
<stop offset="0.614292" stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_7082_105106" x1="40" y1="44" x2="40" y2="68" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_7082_105106">
<rect width="80" height="80" rx="40" fill="white"/>
</clipPath>
<clipPath id="clip1_7082_105106">
<rect width="9" height="9" fill="white" transform="translate(36 9.92188)"/>
</clipPath>
<clipPath id="clip2_7082_105106">
<rect width="16" height="16" fill="white" transform="translate(33 48)"/>
</clipPath>
</defs>
</svg>
