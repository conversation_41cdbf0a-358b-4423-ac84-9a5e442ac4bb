<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_8273_250493)">
<rect width="80" height="80" rx="40" fill="white" fill-opacity="0.1"/>
<g filter="url(#filter0_dd_8273_250493)">
<rect x="14" y="39.5" width="45" height="18" rx="5" fill="#777E82" shape-rendering="crispEdges"/>
<rect x="18" y="43.5" width="37" height="4" rx="2" fill="url(#paint0_linear_8273_250493)" fill-opacity="0.8"/>
<rect x="18" y="49.5" width="30" height="4" rx="2" fill="url(#paint1_linear_8273_250493)" fill-opacity="0.5"/>
<path d="M52.9297 56.9375L55.6938 48.0915C55.6938 48.0915 57.0029 51.0504 58.1207 52.7847C59.3991 54.7681 61.9273 56.7852 61.9273 56.7852C61.9273 56.7852 59.2162 56.6948 57.4286 56.5914C55.6694 56.4897 52.9297 56.9375 52.9297 56.9375Z" fill="#777E82"/>
</g>
<g filter="url(#filter1_dd_8273_250493)">
<rect x="21" y="23.5" width="45" height="18" rx="5" fill="#777E82" shape-rendering="crispEdges"/>
<rect x="25" y="27.5" width="37" height="4" rx="2" fill="url(#paint2_linear_8273_250493)" fill-opacity="0.8"/>
<rect x="25" y="33.5" width="30" height="4" rx="2" fill="url(#paint3_linear_8273_250493)" fill-opacity="0.5"/>
<path d="M59.9297 40.9375L62.6938 32.0915C62.6938 32.0915 64.0029 35.0504 65.1207 36.7847C66.3991 38.7681 68.9273 40.7852 68.9273 40.7852C68.9273 40.7852 66.2162 40.6948 64.4286 40.5914C62.6694 40.4897 59.9297 40.9375 59.9297 40.9375Z" fill="#777E82"/>
</g>
</g>
<defs>
<filter id="filter0_dd_8273_250493" x="-16" y="21.5" width="107.926" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8273_250493"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_8273_250493" result="effect2_dropShadow_8273_250493"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_8273_250493" result="shape"/>
</filter>
<filter id="filter1_dd_8273_250493" x="-9" y="5.5" width="107.926" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="12"/>
<feGaussianBlur stdDeviation="15"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8273_250493"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_8273_250493" result="effect2_dropShadow_8273_250493"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_8273_250493" result="shape"/>
</filter>
<linearGradient id="paint0_linear_8273_250493" x1="55" y1="45.5" x2="18" y2="45.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint1_linear_8273_250493" x1="48" y1="51.5" x2="18" y2="51.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint2_linear_8273_250493" x1="62" y1="29.5" x2="25" y2="29.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_8273_250493" x1="55" y1="35.5" x2="25" y2="35.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<clipPath id="clip0_8273_250493">
<rect width="80" height="80" rx="40" fill="white"/>
</clipPath>
</defs>
</svg>
