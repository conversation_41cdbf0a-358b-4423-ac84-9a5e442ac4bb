name: mides_skadik
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.5.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  animate_do: ^4.2.0
  audioplayers: ^6.4.0
  cached_network_image: ^3.4.1
  camera: ^0.10.5+9
  carousel_slider: ^5.0.0
  chat_bubbles: ^1.6.0
  chewie: ^1.11.3
  cupertino_icons: ^1.0.8
  data_table_2: 2.5.15
  dio: ^5.8.0+1
  dotted_border: ^2.1.0
  file_manager: ^1.0.2
  file_picker: ^10.1.2
  flutter_dotenv: ^5.2.1
  flutter_inappwebview: ^6.1.5
  flutter_osm_plugin: ^1.3.8
  flutter_pdfview: ^1.4.0+1
  flutter_quill: 10.8.5
  flutter_rating: ^2.0.2
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.0.17
  flutter_webrtc: ^0.12.12+hotfix.1
  geolocator: ^13.0.4
  get: ^4.7.2
  get_thumbnail_video: ^0.7.3
  google_maps_flutter: ^2.10.1
  http_parser: ^4.1.2
  image_picker: ^1.1.2
  intl: ^0.19.0
  just_audio: ^0.10.1
  log_service: ^1.0.0
  logger: ^2.5.0
  mediasfu_mediasoup_client: ^0.0.7
  mime: ^2.0.0
  ntp: ^2.0.0
  open_file: ^3.5.10
  path: ^1.9.1
  path_provider: ^2.1.5
  pdf: ^3.11.3
  pdfx:
    git:
      url: https://github.com/SamuelGadiel/packages.flutter.git
      path: packages/pdfx
  permission_handler: ^11.4.0
  printing: ^5.14.2
  protoo_client: ^0.3.3
  random_string: ^2.3.1
  shared_preferences: ^2.5.3
  shimmer: ^3.0.0
  skeletonizer: ^1.4.3
  socket_io_client: ^3.1.1
  swipe_to: ^1.0.6
  syncfusion_flutter_calendar: ^27.2.5
  syncfusion_flutter_datepicker: ^27.2.5
  syncfusion_flutter_pdfviewer: ^27.2.5
  url_launcher: ^6.3.1
  video_player: ^2.9.5
  video_trimmer: ^5.0.0
  web_socket_channel: ^3.0.3
  webview_flutter: ^4.13.0
  youtube_player_flutter: ^9.1.1

dev_dependencies:
  change_app_package_name: ^1.5.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter

dependency_overrides:
  webrtc_interface: 1.2.2
  intl: ^0.19.0
  path: 1.9.0
  # path: ^1.9.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets/font/Inter.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
